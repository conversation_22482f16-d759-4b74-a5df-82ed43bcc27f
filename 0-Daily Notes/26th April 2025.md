---
creation date: 2025-04-26 10:33
modification date: Saturday 26th April 2025 10:33:48
type: daily
date: 2025-04-26
day_of_week: Saturday
week: 2025-W17
month: 2025-04
tags: [daily, 2025-04]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-04-26 - Saturday

<< [[2025-04-25]] | [[2025-04-27]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- 

## Tasks
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-26)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-26
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-26
due before 2025-05-03
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-26 Meeting|Create New Meeting]]
- [[2025-04-26 Task|Create New Task]]
- [[2025-04-26 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W17|Weekly Overview]]
- [[Tasks]]
- [[Home]]
