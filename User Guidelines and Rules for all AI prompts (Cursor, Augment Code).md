---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Personal Research
tags:
  - para/resources
  - guide
  - ai
  - prompt
  - user
  - guidelines
  - augment
  - cursor
  - rules
  - cheatsheet
  - agent
  - llm
  - memory-management
related: ["Software Development Resources", "AI Prompt Engineering"]
area: Software-Development
difficulty: easy
keywords:
  - ai-assistant
  - memory-management
  - context-window
  - prompt-engineering
  - augment
  - cursor
last_used: 2025-04-16
author: jordan
---

# AI Assistant Memory Management Guidelines

## Overview
Guidelines for optimizing AI assistant memory management in Augment, Cursor, and other LLM-based tools to maximize context efficiency and personalization.

## Key Points

### Memory Management Principles
- **Prioritize Critical Context**: At the end of each response, update memory with only the most essential information needed for future interactions
- **Dynamic Memory Pruning**: Flag outdated information for removal while temporarily preserving it for potential rollbacks
- **Contextual Awareness**: Maintain awareness of the current project state, tools being used, and user preferences

### When to Update Memory
Update memory when:
1. **Project scope changes** (new requirements, pivoting direction)
2. **Implementation changes** (bug fixes, refactoring, new approaches)
3. **Tool or framework switches** occur
4. **Security vulnerabilities** are discovered
5. **User preferences** are expressed

### Memory Lifecycle Management
When significant changes occur:
1. **Flag outdated memories** with a `[PENDING-DELETION:reason]` prefix
2. **Retain flagged memories** for 3-5 interactions
3. **Create new memory entries** with updated information
4. **Remove flagged entries** after confirming the new approach is stable

## Details

### Memory Entry Structure
Each memory entry should follow this structure for consistency and clarity:

```
[CATEGORY] Key information in concise form
```

Categories include:
- `[PREFERENCE]` - User preferences for code style, tools, etc.
- `[PROJECT]` - Project-specific details like architecture, requirements
- `[WORKFLOW]` - User's preferred workflow patterns
- `[CONTEXT]` - Current state of work or conversation

For entries pending deletion:
```
[PENDING-DELETION:reason] [CATEGORY] Original memory content
```

### Context Window Optimization
- **Summarize, Don't Duplicate**: Condense information rather than repeating it
- **Prioritize Recency**: More recent interactions generally have higher relevance
- **Maintain Core Requirements**: Always preserve fundamental project requirements
- **Track User Preferences**: Maintain a clear record of user's stated preferences

### Personalization Strategy
- Observe and record user's communication style
- Note preferred code patterns and conventions
- Track frequently used tools and workflows
- Remember past solutions that were well-received

## Examples

### Effective Memory Updates

**Original Memory:**
```
[PROJECT] User wants a React application with Material UI components and Redux for state management
```

**Updated Memory (After Framework Change):**
```
[PENDING-DELETION:framework-change] [PROJECT] User wants a React application with Material UI components and Redux for state management
[PROJECT] User switched to Next.js with Tailwind CSS and React Context API for state management
```

**Final Memory (After Confirmation):**
```
[PROJECT] User is building a Next.js application with Tailwind CSS and React Context API for state management
```

### Memory Pruning Example

**Before Pruning:**
```
[PREFERENCE] User prefers 2-space indentation
[PROJECT] Building a REST API with Express and MongoDB
[WORKFLOW] User likes to write tests before implementation
[CONTEXT] Currently implementing user authentication
[PENDING-DELETION:outdated] [CONTEXT] Working on database schema design
```

**After Pruning:**
```
[PREFERENCE] User prefers 2-space indentation
[PROJECT] Building a REST API with Express and MongoDB
[WORKFLOW] User likes to write tests before implementation
[CONTEXT] Currently implementing user authentication
```

## Use Cases

- **Long-Running Projects**: Maintain context across multiple sessions without bloating the context window
- **Collaborative Work**: Track different team members' preferences when switching between users
- **Iterative Development**: Preserve history of design decisions while focusing on current approach
- **Learning Sessions**: Remember user's skill level and previously explained concepts
- **Troubleshooting**: Track what solutions have already been attempted

# Original Rules for Augment AI, fed into LLM to improve, resulting in the [[#AI Assistant Memory Management Guidelines|resources above]]

*Augment AI User Guidelines*
Make sure you update critical context information in the conclusion of your responses to maintain relevant information for your actions. Be sure to *very carefully* remove information that becomes redundant, such as:
-when the project scope changes
-when we fixed a bug, causing previous implementations to update
-switching implemented tools or programs
-when we have discovered a vulnerability in our software
When any of these get triggered, you shuold revise your memory and make an informed decision on what to do with it. Howvever instead of deleting it at that moment, please retain this memory entry temporarily (for a few prompts, or until context windows are too large) by flagging the memory for deletion (scheduling it with a marker. Please add this marker to your memory so you can identify scheduled memories for deletion). This way, we can keep the memory in case we must revert back to previous implementations in case of a breaking change or regressive updates.

Overview
Guidelines for optimizing AI assistant memory management in Augment, Cursor, and other LLM-based tools to maximize context efficiency and personalization.


## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(tags, "ai") OR contains(tags, "llm")
SORT priority ASC
LIMIT 5
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category",
  status as "Status"
FROM "2-Areas"
WHERE contains(tags, "ai") OR contains(tags, "llm")
LIMIT 5
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "ai") OR contains(tags, "prompt") OR contains(tags, "llm")
AND file.name != "AI Assistant Memory Management Guidelines"
LIMIT 5
```

## Best Practices

1. **Regularly Review Memory**: Periodically check what's being stored to ensure relevance
2. **Be Explicit About Changes**: When pivoting, clearly state that previous approach is being abandoned
3. **Confirm Before Purging**: Verify new approaches work before completely removing old information
4. **Balance Detail and Brevity**: Include enough detail to be useful without unnecessary verbosity
5. **Use Consistent Formatting**: Maintain a consistent structure for memory entries

## Create Related Notes
- [[LLM Memory Management Project|Create Related Project]]
- [[LLM Memory Management Implementation|Create Implementation Guide]]
- [[LLM Memory Management Quick Reference|Create Quick Reference]]

## Related
- [[3-Resources]]
- [[Resources TOC]]
- [[Software Development Resources]]
- [[AI Prompt Engineering]]
