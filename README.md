# Obsidian Vault - PARA Structure

This Obsidian vault is organized using the PARA method (Projects, Areas, Resources, Archive) with a focus on software development and administration workflows.

## Repository Information

```
git@ucanotes:fjord-an/notes-uca-obsidian.git
```

Make sure to add custom hostname for key.
Edit ~/.ssh/config and add these lines for the ssh key:

```
Host ucanotes
 HostName github.com
 IdentityFile C:\Users\<USER>\.ssh\notes-uca-obsidian
```

## Vault Structure

- **1-Projects**: Time-limited goals with defined outcomes
- **2-Areas**: Ongoing responsibilities to maintain
- **3-Resources**: Information and tools for reference
- **4-Archive**: Completed or inactive items
- **Daily**: Daily notes for journaling and task tracking
- **People**: Contact information and interaction logs
- **Templates**: Templates for different note types
- **Attachments**: Images and other media files

## Navigation

- Use the **Home** dashboard as your starting point
- Use the PARA index pages (**1-Projects**, **2-Areas**, **3-Resources**, **4-Archive**) to navigate to specific sections
- Use the **Tags MOC** to navigate by tags
- Use the **People MOC** to navigate contacts
- Use the **Tasks** page to view all tasks across the vault
- Use the **Search** page for advanced search techniques

## Creating New Notes

1. Use templates for consistent note structure:
   - Project template for new projects
   - Area template for new areas
   - Resource template for new resources
   - Person template for new contacts
   - Meeting template for meeting notes
   - Daily template for daily notes

2. Always add appropriate frontmatter:
   - `type` - The type of note (project, area, resource, etc.)
   - `tags` - Relevant tags for categorization
   - `status` - For projects (active, on-hold, completed)
   - `priority` - For projects (high, medium, low)
   - `deadline` - For projects

3. Place notes in the appropriate folder:
   - Projects in `1-Projects`
   - Areas in `2-Areas`
   - Resources in `3-Resources`
   - Archived items in `4-Archive`

## Recommended Plugins

- **Dataview**: For creating dynamic views of your notes
- **Tasks**: For enhanced task management
- **Templater**: For advanced templates
- **Omnisearch**: For enhanced search capabilities
- **Calendar**: For navigating daily notes
- **Periodic Notes**: For daily, weekly, monthly notes
- **Tag Wrangler**: For managing tags
- **QuickAdd**: For quickly adding content
- **Kanban**: For visual project management

## Guides

For more detailed information, refer to these guides:

- [[Vault Organization Guide]]
- [[Dataview Guide]]
- [[Recommended Plugins]]

## Best Practices

1. **Minimize Folder Nesting** - Keep the folder structure flat and use tags and links for organization
2. **Link Liberally** - Create connections between related notes
3. **Use Templates** - Ensure consistency across similar types of notes
4. **Regular Maintenance** - Archive completed projects and review areas regularly
5. **Use Dataview** - Create dynamic views instead of manual lists
6. **Consistent Tagging** - Use a consistent tagging system for better organization