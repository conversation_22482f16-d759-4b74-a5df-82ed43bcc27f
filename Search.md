---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: index
aliases: [Search, Find]
tags: [search, index]
---

# Search

> A guide to effectively searching your vault.

## Quick Search
- Use `Ctrl+P` (or `Cmd+P` on Mac) to open the command palette
- Use `Ctrl+O` (or `Cmd+O` on Mac) to search for files
- Use `Ctrl+Shift+F` (or `Cmd+Shift+F` on Mac) for global search

## Omnisearch
- Use Omnisearch for fuzzy searching across your vault
- Access Omnisearch from the left sidebar or with the hotkey `Ctrl+Shift+O` (or `Cmd+Shift+O` on Mac)

## Search Operators
- Use quotes for exact matches: `"exact phrase"`
- Use `-` to exclude terms: `-exclude`
- Use `OR` for alternatives: `term1 OR term2`
- Use `file:` to search in filenames: `file:project`
- Use `path:` to search in specific paths: `path:1-Projects`
- Use `tag:` to search for tags: `tag:#software-dev`

## Search by Tags
- Click on any tag to see all notes with that tag
- Use the [[Tags MOC]] to browse all tags

## Search by Project
- Use the [[1-Projects]] page to browse all projects
- Use dataview queries to find specific projects

## Search by Area
- Use the [[2-Areas]] page to browse all areas
- Use dataview queries to find specific areas

## Search by Resource
- Use the [[3-Resources]] page to browse all resources
- Use dataview queries to find specific resources

## Related
- [[Home]]
- [[Tags MOC]]
- [[1-Projects]]
- [[2-Areas]]
- [[3-Resources]]
