Change the logging level back to sensitive before deploying to production!

In appsettings.json (backend CabUCA.Api)
**Original value:**

  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }

**Changed to value:**
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    }
  }
}
