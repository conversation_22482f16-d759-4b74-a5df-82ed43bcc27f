**19/02/25:**
nginx-1  | ************** - - [18/Feb/2025:23:17:01 +0000] "GET / HTTP/1.1" 499 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.1.2 Safari/605.1.15"
nginx-1  | ************** - - [18/Feb/2025:23:17:06 +0000] "GET / HTTP/1.1" 400 255 "-" "WebZIP/3.5 (http://www.spidersoft.com)"
nginx-1  | ************ - - [18/Feb/2025:23:17:42 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | *************** - - [18/Feb/2025:23:19:49 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | *************** - - [18/Feb/2025:23:23:03 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | ************* - - [18/Feb/2025:23:24:41 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | ************* - - [18/Feb/2025:23:25:55 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | 175.30.48.130 - - [18/Feb/2025:23:25:57 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | 61.52.76.172 - - [18/Feb/2025:23:27:44 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | 140.228.21.193 - - [18/Feb/2025:23:27:56 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/89.0.4389.90 Safari/537.36"
nginx-1  | 140.228.21.193 - - [18/Feb/2025:23:27:58 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/89.0.4389.90 Safari/537.36"
nginx-1  | 140.228.21.193 - - [18/Feb/2025:23:27:58 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/89.0.4389.90 Safari/537.36"
nginx-1  | *************** - - [18/Feb/2025:23:31:02 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | *************** - - [18/Feb/2025:23:31:03 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36"
nginx-1  | ************* - - [18/Feb/2025:23:31:37 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/605.1.15"
nginx-1  | ************** - - [18/Feb/2025:23:39:19 +0000] "GET / HTTP/1.1" 200 3019 "-" "Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>"
nginx-1  | ************ - - [18/Feb/2025:23:45:11 +0000] "POST /GponForm/diag_Form?images/ HTTP/1.1" 301 169 "-" "Hello, World"
nginx-1  | ************ - - [18/Feb/2025:23:45:13 +0000] "h+/tmp/gpon80&ipv=0" 400 157 "-" "-"
nginx-1  | ************** - - [18/Feb/2025:23:51:24 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (compatible; InternetMeasurement/1.0; +https://internet-measurement.com/)"
nginx-1  | ************ - - [18/Feb/2025:23:51:26 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (compatible; InternetMeasurement/1.0; +https://internet-measurement.com/)"
nginx-1  | ************ - - [19/Feb/2025:00:02:38 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [19/Feb/2025:00:02:56 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:00:02:57 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:00:10:02 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [19/Feb/2025:00:10:04 +0000] "GET / HTTP/1.1" 200 3019 "http://cruca.org" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [19/Feb/2025:00:11:37 +0000] "GET /.git/config HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.92 Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:12:23 +0000] "GET / HTTP/1.1" 301 169 "-" "Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>"
nginx-1  | *************** - - [19/Feb/2025:00:12:24 +0000] "GET / HTTP/1.1" 200 3019 "http://*************:80/" "Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>"
nginx-1  | ************** - - [19/Feb/2025:00:14:01 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:00:14:02 +0000] "POST / HTTP/1.1" 405 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:00:30:21 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Linux; Android 9; SM-N960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:35:21 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:21 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10829 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:22 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:23 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:23 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:23 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:27 +0000] "GET /calendar.html HTTP/1.1" 200 1968 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:27 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 15035 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:35:56 +0000] "GET /whatson.html HTTP/1.1" 200 14130 "https://cruca.org/calendar" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:06 +0000] "GET /about.html HTTP/1.1" 200 2195 "https://cruca.org/whatson" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:09 +0000] "GET /contact.html HTTP/1.1" 200 6269 "https://cruca.org/about" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:13 +0000] "GET / HTTP/1.1" 200 674 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:13 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 200 108495 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:13 +0000] "GET /static/css/main.d73024fc.css HTTP/1.1" 200 46703 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:13 +0000] "GET /favicon.ico HTTP/1.1" 200 6463 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:13 +0000] "GET /manifest.json HTTP/1.1" 200 492 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:14 +0000] "GET /logo192.png HTTP/1.1" 200 5347 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:22 +0000] "OPTIONS /api/auth/Login HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:23 +0000] "POST /api/auth/Login HTTP/1.1" 200 348 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:23 +0000] "OPTIONS /api/events/admin HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:23 +0000] "GET /api/events/admin HTTP/1.1" 200 5963 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:29 +0000] "GET /static/media/bootstrap-icons.b7bcc075b395c14ce8c2.woff2 HTTP/1.1" 200 130396 "https://admin.cruca.org/static/css/main.d73024fc.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:32 +0000] "OPTIONS /api/users/?searchTerm= HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:32 +0000] "GET /api/users/?searchTerm= HTTP/1.1" 200 207 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:37 +0000] "GET /users/1a1246ac-dea8-4b15-ae59-84833b3ddb6a/edit HTTP/1.1" 200 674 "https://admin.cruca.org/users" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:37 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 304 0 "https://admin.cruca.org/users/1a1246ac-dea8-4b15-ae59-84833b3ddb6a/edit" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:37 +0000] "GET /static/css/main.d73024fc.css HTTP/1.1" 304 0 "https://admin.cruca.org/users/1a1246ac-dea8-4b15-ae59-84833b3ddb6a/edit" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:37 +0000] "OPTIONS /api/users/1a1246ac-dea8-4b15-ae59-84833b3ddb6a/roles HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:37 +0000] "GET /manifest.json HTTP/1.1" 304 0 "https://admin.cruca.org/users/1a1246ac-dea8-4b15-ae59-84833b3ddb6a/edit" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "OPTIONS /api/users/roles HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "OPTIONS /api/auth/User HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "GET /api/users/1a1246ac-dea8-4b15-ae59-84833b3ddb6a/roles HTTP/1.1" 200 25 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "GET /logo192.png HTTP/1.1" 304 0 "https://admin.cruca.org/users/1a1246ac-dea8-4b15-ae59-84833b3ddb6a/edit" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "GET /api/users/roles HTTP/1.1" 200 70 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "GET /api/auth/User HTTP/1.1" 200 63 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "OPTIONS /api/auth//users/current HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:38 +0000] "GET /api/auth//users/current HTTP/1.1" 200 3019 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:39 +0000] "OPTIONS /api/events/admin HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:39 +0000] "GET /api/events/admin HTTP/1.1" 200 5969 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:40 +0000] "OPTIONS /api/users/?searchTerm= HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:40 +0000] "GET /api/users/?searchTerm= HTTP/1.1" 200 207 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:41 +0000] "GET /api/users/?searchTerm= HTTP/1.1" 200 207 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:36:41 +0000] "GET /api/events/admin HTTP/1.1" 200 5963 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
nginx-1  | *************** - - [19/Feb/2025:00:37:16 +0000] "GET / HTTP/1.1" 304 0 "-" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:16 +0000] "GET /css/styles.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:16 +0000] "GET /css/components/calendar.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:17 +0000] "GET /css/components/footer.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:17 +0000] "GET /css/components/navbar.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:17 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:17 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:17 +0000] "GET /js/calendar.js HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:17 +0000] "GET /js/main.js HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:17 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 304 0 "https://cruca.org/css/styles.css" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:20 +0000] "GET /home.html HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:20 +0000] "GET /css/components/main.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:24 +0000] "GET /whatson.html HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:37:28 +0000] "GET /care.html HTTP/1.1" 200 3230 "https://cruca.org/whatson" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************ - - [19/Feb/2025:00:43:57 +0000] "GET /.git/config HTTP/1.1" 200 3019 "-" "Opera/9.80 (J2ME/MIDP; Opera Mini/5.0.16823/1428; U; en) Presto/2.2.0"
nginx-1  | *************** - - [19/Feb/2025:00:50:41 +0000] "GET /about.html HTTP/1.1" 304 0 "https://cruca.org/care" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:50:45 +0000] "GET /calendar.html HTTP/1.1" 304 0 "https://cruca.org/about" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:00:50:45 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 15035 "https://cruca.org/about" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | 34.222.251.122 - - [19/Feb/2025:00:54:58 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19582"
nginx-1  | 34.222.251.122 - - [19/Feb/2025:00:54:59 +0000] "GET / HTTP/1.1" 200 674 "http://admin.cruca.org" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19582"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:01:03:32 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:01:03:33 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 64.62.156.33 - - [19/Feb/2025:01:33:12 +0000] "\x16\x03\x01\x00{\x01\x00\x00w\x03\x03M\x1C\x8C*\x9C\x7F\x8C\xA0\x936y/>\x19\x058W\x9FW\x1D/\xE0\xDA\xAAb\xDB\xC3\x9B>^\x16\xBA\x00\x00\x1A\xC0/\xC0+\xC0\x11\xC0\x07\xC0\x13\xC0\x09\xC0\x14\xC0" 400 157 "-" "-"
nginx-1  | 93.174.93.12 - - [19/Feb/2025:01:43:17 +0000] "GET / HTTP/1.0" 301 169 "-" "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.67 Safari/537.36"
nginx-1  | ************ - - [19/Feb/2025:01:46:54 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [19/Feb/2025:01:52:37 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.104 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:01:52:38 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.104 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:01:52:38 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.104 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:01:52:40 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:41 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:41 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:41 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10829 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:42 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:42 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:42 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:42 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:42 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:42 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:43 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:43 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:43 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5"
nginx-1  | ************** - - [19/Feb/2025:01:52:48 +0000] "GET / HTTP/1.1" 301 169 "-" "python-requests/2.32.3"
nginx-1  | ************** - - [19/Feb/2025:01:52:49 +0000] "GET / HTTP/1.1" 200 3019 "-" "python-requests/2.32.3"
nginx-1  | ************** - - [19/Feb/2025:02:03:37 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:02:03:38 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:02:06:08 +0000] "GET /wp-login.php HTTP/1.1" 301 169 "-" "Mozilla/5.0"
nginx-1  | ************** - - [19/Feb/2025:02:06:09 +0000] "GET /wp-login.php HTTP/1.1" 404 0 "-" "Mozilla/5.0"
nginx-1  | ************ - - [19/Feb/2025:02:15:50 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | ************ - - [19/Feb/2025:02:22:45 +0000] "GET / HTTP/1.0" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12.10240"
nginx-1  | 216.24.87.112 - - [19/Feb/2025:02:27:09 +0000] "GET / HTTP/1.0" 301 169 "-" "curl/7.88.1"
nginx-1  | ************* - - [19/Feb/2025:02:43:47 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:02:44:33 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36"
nginx-1  | ************ - - [19/Feb/2025:02:46:49 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [19/Feb/2025:02:46:52 +0000] "GET / HTTP/1.1" 200 3019 "http://cruca.org" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:02:49:47 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:02:49:48 +0000] "POST / HTTP/1.1" 405 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:02:52:49 +0000] "GET /owa/ HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:03:24:28 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:29 +0000] "GET  HTTP/1.1" 400 157 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:30 +0000] "GET /.env_example HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:30 +0000] "GET /core/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:30 +0000] "GET /app/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:31 +0000] "GET /laravel/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:31 +0000] "GET /web/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:31 +0000] "GET /crm/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:32 +0000] "GET /backend/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:32 +0000] "GET /backend HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:32 +0000] "GET /local/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:32 +0000] "GET /api/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:33 +0000] "GET /admin/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:33 +0000] "GET /application/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:33 +0000] "GET /env.bak HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:34 +0000] "GET /phpinfo HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:34 +0000] "GET /_profiler/phpinfo HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:34 +0000] "GET /phpinfo.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:34 +0000] "GET /.env.bak HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:35 +0000] "GET /.env.backup HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:35 +0000] "GET /.env_sample HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:35 +0000] "GET /.env.old HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:35 +0000] "GET /.env.www HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:36 +0000] "GET /.docker/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:36 +0000] "GET /.env.dev HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:36 +0000] "GET /.env.example HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:36 +0000] "GET /config.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:37 +0000] "GET /.environment HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:37 +0000] "GET /.env.production HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:37 +0000] "GET /.env.production.local HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:38 +0000] "GET /.env.prod HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:38 +0000] "GET /.env.test HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:38 +0000] "GET /.env.sample.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:38 +0000] "GET /.env.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:39 +0000] "GET /.env1 HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:39 +0000] "GET /.venv HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:39 +0000] "GET /env.prod.js HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:40 +0000] "GET /config/settings.ini HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:40 +0000] "GET /info HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:40 +0000] "GET /env.test.js HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:40 +0000] "GET /info.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:41 +0000] "GET /portal/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:41 +0000] "GET /env/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:41 +0000] "GET /dev/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:41 +0000] "GET /new/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:42 +0000] "GET /new/.env.local HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:42 +0000] "GET /new/.env.production HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:42 +0000] "GET /new/.env.staging HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:42 +0000] "GET /_phpinfo.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:43 +0000] "GET /_profiler/phpinfo/info.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:43 +0000] "GET /_profiler/phpinfo/phpinfo.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:43 +0000] "GET /wp-config HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:43 +0000] "GET /env.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:44 +0000] "GET /aws-secret.yaml HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:44 +0000] "GET /awstats/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:44 +0000] "GET /conf/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:45 +0000] "GET /cron/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:45 +0000] "GET /www/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:45 +0000] "GET /docker/.env HTTP/1.1" 404 153 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:45 +0000] "GET /docker/app/.env HTTP/1.1" 404 153 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:46 +0000] "GET /env.backup HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:46 +0000] "GET /xampp/phpinfo.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:46 +0000] "GET /lara/info.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:46 +0000] "GET /lara/phpinfo.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:47 +0000] "GET /laravel/info.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:47 +0000] "GET /.vscode/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:47 +0000] "GET /js/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:48 +0000] "GET /laravel/core/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:48 +0000] "GET /mail/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:48 +0000] "GET /mailer/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:48 +0000] "GET /nginx/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:49 +0000] "GET /public/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:49 +0000] "GET /site/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:49 +0000] "GET /xampp/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:49 +0000] "GET /.docker/laravel/app/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:50 +0000] "GET /laravel/.env.local HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:50 +0000] "GET /laravel/.env.production HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:50 +0000] "GET /laravel/.env.staging HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:51 +0000] "GET /laravel/core/.env.local HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:51 +0000] "GET /laravel/core/.env.production HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:51 +0000] "GET /laravel/core/.env.staging HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:51 +0000] "GET /main/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:52 +0000] "GET /.aws/credentials HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:52 +0000] "GET /config.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:52 +0000] "GET /sendgrid.json HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:52 +0000] "GET /bootstrap/cache/config.php HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:53 +0000] "GET /storage/app/private/.env HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:53 +0000] "GET /storage/logs/laravel.log HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:53 +0000] "GET /composer.lock HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:53 +0000] "GET /server.key HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:54 +0000] "GET /dump.sh HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:54 +0000] "GET /php5.ini HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:54 +0000] "GET /config.php.bak HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:55 +0000] "GET /src/app.js HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:55 +0000] "GET /server-info HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:55 +0000] "GET /docker.sh HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:55 +0000] "GET /config.php.save HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:56 +0000] "GET /config.ini.old HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:56 +0000] "GET /~backup HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:56 +0000] "GET /config.xml HTTP/1.1" 200 674 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:03:24:57 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 200 345473 "-" "-"
nginx-1  | ************ - - [19/Feb/2025:03:33:40 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | 93.174.93.12 - - [19/Feb/2025:03:36:29 +0000] "\x16\x03\x02\x01o\x01\x00\x01k\x03\x02RH\xC5\x1A#\xF7:N\xDF\xE2\xB4\x82/\xFF\x09T\x9F\xA7\xC4y\xB0h\xC6\x13\x8C\xA4\x1C=\x22\xE1\x1A\x98 \x84\xB4,\x85\xAFn\xE3Y\xBBbhl\xFF(=':\xA9\x82\xD9o\xC8\xA2\xD7\x93\x98\xB4\xEF\x80\xE5\xB9\x90\x00(\xC0" 400 157 "-" "-"
nginx-1  | 141.98.11.205 - - [19/Feb/2025:03:44:51 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | 89.248.168.222 - - [19/Feb/2025:03:54:55 +0000] "GET / HTTP/1.1" 200 3019 "-" "curl/7.68.0"
nginx-1  | 89.248.168.222 - - [19/Feb/2025:03:54:56 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,like Gecko) Chrome/95.0.4638.69 Safari/537.36"
nginx-1  | 89.248.168.222 - - [19/Feb/2025:03:54:57 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,like Gecko) Chrome/95.0.4638.69 Safari/537.36"
nginx-1  | 89.248.168.222 - - [19/Feb/2025:03:54:58 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML,like Gecko) Chrome/95.0.4638.69 Safari/537.36"
nginx-1  | 206.168.34.218 - - [19/Feb/2025:04:04:02 +0000] "\x00\x0E\x08\x1F\x02\xEE\xBB\xB3\xD0%\xF8\x00\x00\x00\x00\x00" 400 157 "-" "-"
nginx-1  | 206.168.34.218 - - [19/Feb/2025:04:04:04 +0000] "\x00\x0E8\x1F\x02\xEE\xBB\xB3\xD0%\xF8\x00\x00\x00\x00\x00" 400 157 "-" "-"
nginx-1  | 64.62.197.200 - - [19/Feb/2025:04:10:22 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
nginx-1  | 185.242.226.10 - - [19/Feb/2025:04:10:40 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36"
nginx-1  | 185.242.226.10 - - [19/Feb/2025:04:10:42 +0000] "GET / HTTP/1.1" 200 3019 "http://*************:80/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36"
nginx-1  | 64.62.197.197 - - [19/Feb/2025:04:12:12 +0000] "GET /webui/ HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
nginx-1  | 64.62.197.202 - - [19/Feb/2025:04:12:34 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
nginx-1  | 64.62.197.209 - - [19/Feb/2025:04:14:09 +0000] "GET /geoserver/web/ HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
nginx-1  | 17.241.75.210 - - [19/Feb/2025:04:19:37 +0000] "GET /robots.txt HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15 (Applebot/0.1; +http://www.apple.com/go/applebot)"
nginx-1  | 17.241.75.210 - - [19/Feb/2025:04:19:37 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15 (Applebot/0.1; +http://www.apple.com/go/applebot)"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:04:23:39 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:04:23:40 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 104.197.69.115 - - [19/Feb/2025:04:37:26 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:29 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:29 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:29 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10829 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:29 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 104.197.69.115 - - [19/Feb/2025:04:37:29 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:29 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 104.197.69.115 - - [19/Feb/2025:04:37:29 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:30 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:30 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:30 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:30 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 34.123.170.104 - - [19/Feb/2025:04:37:30 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/125.0.6422.60 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:45 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:46 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:46 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:46 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:53 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:58 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:58 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:58 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10829 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:58 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:58 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:58 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:58 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:59 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:59 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:59 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:59 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 205.169.39.179 - - [19/Feb/2025:04:37:59 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.79 Safari/537.36"
nginx-1  | 17.241.227.175 - - [19/Feb/2025:04:38:31 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Safari/605.1.15 (Applebot/0.1; +http://www.apple.com/go/applebot)"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:26 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:27 +0000] "GET / HTTP/1.1" 304 0 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:27 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10829 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /css/components/footer.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /js/calendar.js HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /css/components/navbar.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /js/main.js HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:28 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:29 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:30 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:30 +0000] "GET /data/img/favicon.ico HTTP/1.1" 499 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:31 +0000] "GET /home.html HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:31 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:32 +0000] "GET /calendar.html HTTP/1.1" 200 1968 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"
nginx-1  | 49.191.50.169 - - [19/Feb/2025:04:46:32 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 15035 "https://cruca.org/" "Mozilla/5.0 (X11; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0"

=
#copied@ 8:50, febuary 19
=
---

nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:21 +0000] "GET /query?name=example.com&type=A HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:21 +0000] "GET /resolve?dns=IOIBAAABAAAAAAAAB2V4YW1wbGUDY29tAAABAAE HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:21 +0000] "POST /resolve HTTP/1.1" 405 0 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:21 +0000] "GET /resolve?name=example.com&type=A HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:22 +0000] "GET /resolve?dns=lSkBAAABAAAAAAAAB2V4YW1wbGUDY29tAAABAAE HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:22 +0000] "POST /resolve HTTP/1.1" 405 0 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:22 +0000] "GET /resolve?name=example.com&type=A HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:22 +0000] "GET /?dns=cmwBAAABAAAAAAAAB2V4YW1wbGUDY29tAAABAAE HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:23 +0000] "POST / HTTP/1.1" 405 0 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:23 +0000] "GET /?name=example.com&type=A HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:23 +0000] "GET /?dns=svgBAAABAAAAAAAAB2V4YW1wbGUDY29tAAABAAE HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:23 +0000] "POST / HTTP/1.1" 405 0 "-" "Go-http-client/1.1"
nginx-1  | 47.91.125.252 - - [19/Feb/2025:05:16:23 +0000] "GET /?name=example.com&type=A HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | ************ - - [19/Feb/2025:05:19:15 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [19/Feb/2025:05:21:18 +0000] "\x16\x03\x01\x00{\x01\x00\x00w\x03\x03\xD5\x9C\x01\x1E<\x1B\xD4\x1F\xC5\x95\xAE\x0B\xAD\xA0*\x9FC\xD1\xB4\x00GP\x84A7\x0F5\x81Z\x99\xFD\xC3\x00\x00\x1A\xC0/\xC0+\xC0\x11\xC0\x07\xC0\x13\xC0\x09\xC0\x14\xC0" 400 157 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:05:21:18 +0000] "\x16\x03\x01\x00{\x01\x00\x00w\x03\x03\x15\xC72-\xD0=Uv\xE93\xCC\xED\x17\xDE\x88\xBC\xEA\xE2\xB7\xD9\x1Egv\x91\xB3\xB5?d\x81k\x1C\xF6\x00\x00\x1A\xC0/\xC0+\xC0\x11\xC0\x07\xC0\x13\xC0\x09\xC0\x14\xC0" 400 157 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:05:21:18 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:21:19 +0000] "GET / HTTP/1.1" 200 3019 "http://*************/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:21:19 +0000] "GET /form.html HTTP/1.1" 301 169 "-" "curl/8.1.2"
nginx-1  | ************** - - [19/Feb/2025:05:21:20 +0000] "GET /upl.php HTTP/1.1" 301 169 "-" "Mozilla/5.0"
nginx-1  | ************** - - [19/Feb/2025:05:21:20 +0000] "GET /t4 HTTP/1.1" 301 169 "-" "Mozilla/5.0"
nginx-1  | ************** - - [19/Feb/2025:05:21:20 +0000] "GET /geoip/ HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:21:21 +0000] "GET /favicon.ico HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:21:21 +0000] "GET /1.php HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:21:21 +0000] "GET /systembc/password.php HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:21:22 +0000] "GET /password.php HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:25:52 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:25:53 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Debian; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:28:47 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:05:28:48 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:41 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:41 +0000] "" 400 0 "-" "-"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:42 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:42 +0000] "GET //wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:42 +0000] "GET //xmlrpc.php?rsd HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:42 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:43 +0000] "GET //blog/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:43 +0000] "GET //web/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:43 +0000] "GET //wordpress/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:43 +0000] "GET //website/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:43 +0000] "GET //wp/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:44 +0000] "GET //news/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:44 +0000] "GET //2018/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:44 +0000] "GET //2019/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:44 +0000] "GET //shop/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:44 +0000] "GET //wp1/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:45 +0000] "GET //test/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:45 +0000] "GET //media/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:45 +0000] "GET //wp2/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:45 +0000] "GET //site/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:45 +0000] "GET //cms/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:45 +0000] "GET //sito/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 34.169.192.164 - - [19/Feb/2025:05:30:46 +0000] "" 400 0 "-" "-"
nginx-1  | 217.24.151.240 - - [19/Feb/2025:05:57:10 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36"
nginx-1  | ************ - - [19/Feb/2025:05:57:33 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:34 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:39 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:39 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:40 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10832 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:40 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:40 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:40 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:40 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:40 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:40 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:41 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:41 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:41 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 205.169.39.58 - - [19/Feb/2025:06:01:41 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.5938.132 Safari/537.36"
nginx-1  | 47.237.115.100 - - [19/Feb/2025:06:01:47 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.95 Safari/537.36"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:06:03:41 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:06:03:41 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 154.223.139.83 - - [19/Feb/2025:06:15:20 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36"
nginx-1  | 176.53.216.10 - - [19/Feb/2025:06:15:21 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36"
nginx-1  | 176.53.220.136 - - [19/Feb/2025:06:15:22 +0000] "GET /favicon.ico HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36"
nginx-1  | 176.53.219.162 - - [19/Feb/2025:06:15:23 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36"
nginx-1  | 176.53.219.162 - - [19/Feb/2025:06:15:23 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:06:22:23 +0000] "GET /owa/ HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:06:38:40 +0000] "GET / HTTP/1.1" 301 169 "-" "Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>"
nginx-1  | ************** - - [19/Feb/2025:06:38:41 +0000] "GET / HTTP/1.1" 200 3019 "http://*************:80/" "Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>"
nginx-1  | ************* - - [19/Feb/2025:06:42:20 +0000] "\xD9\x00\x00\x00" 400 157 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:06:43:21 +0000] "GET /.git/HEAD HTTP/1.1" 200 3019 "-" "Go-http-client/1.1"
nginx-1  | ************ - - [19/Feb/2025:06:46:33 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [19/Feb/2025:06:58:55 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:06:58:57 +0000] "POST / HTTP/1.1" 405 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | *************** - - [19/Feb/2025:07:00:40 +0000] "GET /cgi-bin/luci/;stok=/locale HTTP/1.1" 301 169 "-" "-"
nginx-1  | *************** - - [19/Feb/2025:07:00:41 +0000] "GET /cgi-bin/luci/;stok=/locale HTTP/1.1" 200 3019 "http://*************:80/cgi-bin/luci/;stok=/locale" "-"
nginx-1  | ************** - - [19/Feb/2025:07:11:46 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:07:11:47 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | 34.220.137.115 - - [19/Feb/2025:07:13:34 +0000] "GET / HTTP/1.1" 200 674 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19582"
nginx-1  | 34.220.137.115 - - [19/Feb/2025:07:13:34 +0000] "GET /static/css/main.d73024fc.css HTTP/1.1" 200 46703 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19582"
nginx-1  | 34.220.137.115 - - [19/Feb/2025:07:13:35 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 200 108495 "https://admin.cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19582"
nginx-1  | 4.255.101.65 - - [19/Feb/2025:07:30:11 +0000] "GET /manager/html HTTP/1.1" 301 169 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:07:43:35 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:07:43:36 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:26 +0000] "GET /ab2g HTTP/1.1" 200 3019 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:26 +0000] "GET /ab2h HTTP/1.1" 200 3019 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:27 +0000] "GET /alive.php HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:30 +0000] "GET / HTTP/1.1" 400 657 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:31 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:31 +0000] "GET /t4 HTTP/1.1" 400 255 "-" "Mozilla/5.0"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:32 +0000] "GET /t4 HTTP/1.1" 200 3019 "-" "Mozilla/5.0"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:32 +0000] "GET /favicon.ico HTTP/1.1" 400 657 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:33 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:33 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/4.0 (compatible; MSIE 9.0; Windows NT 10.0; .NET4.0C; .NET4.0E; .NET CLR 2.0.50727; .NET CLR 3.0.30729; .NET CLR 3.5.30729)"
nginx-1  | 24.199.113.57 - - [19/Feb/2025:07:49:34 +0000] "GET /teorema505?t=1 HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 161.77.134.56 - - [19/Feb/2025:07:53:57 +0000] "GET / HTTP/1.1" 200 3019 "-" "Apache-HttpClient/5.1.4 (Java/11.0.18)"
nginx-1  | 209.127.252.230 - - [19/Feb/2025:07:53:59 +0000] "HEAD / HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 104.252.66.29 - - [19/Feb/2025:07:53:59 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 64.43.118.221 - - [19/Feb/2025:07:54:00 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 64.43.118.221 - - [19/Feb/2025:07:54:01 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 93.120.113.97 - - [19/Feb/2025:07:54:03 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 38.154.198.153 - - [19/Feb/2025:07:54:05 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:11 +0000] "POST /cgi-bin/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/bin/sh HTTP/1.1" 400 157 "-" "-"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:12 +0000] "POST /cgi-bin/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/bin/sh HTTP/1.1" 400 157 "-" "-"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:12 +0000] "POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input HTTP/1.1" 301 169 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:13 +0000] "POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input HTTP/1.1" 405 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:14 +0000] "GET /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:14 +0000] "GET /vendor/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:14 +0000] "GET /vendor/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:15 +0000] "GET /vendor/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:15 +0000] "GET /vendor/phpunit/phpunit/LICENSE/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:15 +0000] "GET /vendor/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:16 +0000] "GET /phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:16 +0000] "GET /phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:16 +0000] "GET /phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:17 +0000] "GET /phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:17 +0000] "GET /lib/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:17 +0000] "GET /lib/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:18 +0000] "GET /lib/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:18 +0000] "GET /lib/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:18 +0000] "GET /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:19 +0000] "GET /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:19 +0000] "GET /www/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:19 +0000] "GET /ws/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:20 +0000] "GET /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:20 +0000] "GET /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:20 +0000] "GET /ws/ec/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:21 +0000] "GET /V2/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:21 +0000] "GET /tests/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:21 +0000] "GET /test/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:22 +0000] "GET /testing/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:22 +0000] "GET /api/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:22 +0000] "GET /demo/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:23 +0000] "GET /cms/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:23 +0000] "GET /crm/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:23 +0000] "GET /admin/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:24 +0000] "GET /backup/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:24 +0000] "GET /blog/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:24 +0000] "GET /workspace/drupal/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:25 +0000] "GET /panel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:25 +0000] "GET /public/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:25 +0000] "GET /apps/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:26 +0000] "GET /app/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:26 +0000] "GET /index.php?s=/index/\x5Cthink\x5Capp/invokefunction&function=call_user_func_array&vars[0]=md5&vars[1][]=Hello HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:26 +0000] "GET /public/index.php?s=/index/\x5Cthink\x5Capp/invokefunction&function=call_user_func_array&vars[0]=md5&vars[1][]=Hello HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:27 +0000] "GET /index.php?lang=../../../../../../../../usr/local/lib/php/pearcmd&+config-create+/&/<?echo(md5(\x22hi\x22));?>+/tmp/index1.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:27 +0000] "GET /index.php?lang=../../../../../../../../tmp/index1 HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 219.132.37.52 - - [19/Feb/2025:08:18:28 +0000] "GET /containers/json HTTP/1.1" 404 153 "-" "Custom-AsyncHttpClient"
nginx-1  | ************* - - [19/Feb/2025:08:19:10 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Linux; Android 8.0.0; HTC U11 plus) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Mobile Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:08:19:58 +0000] "GET /main.js HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Linux; Android 9; Pixel XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.89 Mobile Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:08:20:03 +0000] "GET //kit.fontawesome.com/269065907b.js HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Linux; Android 8.1.0; Redmi Y2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.89 Mobile Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:08:20:03 +0000] "GET /calendar.js HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Linux; Android 9; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Mobile Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:51 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:52 +0000] "" 400 0 "-" "-"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:53 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:53 +0000] "GET //wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:53 +0000] "GET //xmlrpc.php?rsd HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:53 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:54 +0000] "GET //blog/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:54 +0000] "GET //web/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:54 +0000] "GET //wordpress/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:54 +0000] "GET //website/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:55 +0000] "GET //wp/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:55 +0000] "GET //news/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:55 +0000] "GET //2020/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:55 +0000] "GET //2019/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:56 +0000] "GET //shop/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:56 +0000] "GET //wp1/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:56 +0000] "GET //test/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:57 +0000] "GET //wp2/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:57 +0000] "GET //site/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:57 +0000] "GET //cms/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:57 +0000] "GET //sito/wp-includes/wlwmanifest.xml HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36"
nginx-1  | 2.58.56.48 - - [19/Feb/2025:08:22:58 +0000] "" 400 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:24:38 +0000] "GET /.git/HEAD HTTP/1.1" 301 169 "-" "curl/8.5.0"
nginx-1  | ************* - - [19/Feb/2025:08:24:51 +0000] "GET /.git/HEAD HTTP/1.1" 200 3019 "-" "curl/8.5.0"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:01 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:08 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:08 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10832 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:08 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:08 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:08 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:08 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:08 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:09 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:09 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:09 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.245.97.69 - - [19/Feb/2025:08:27:09 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:08:28:55 +0000] "\x00\x0E\x08,\xE3\x5C\x91@\xF0}N\x00\x00\x00\x00\x00" 400 157 "-" "-"
nginx-1  | ************** - - [19/Feb/2025:08:28:57 +0000] "\x00\x0E8,\xE3\x5C\x91@\xF0}N\x00\x00\x00\x00\x00" 400 157 "-" "-"
nginx-1  | ************ - - [19/Feb/2025:08:30:05 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [19/Feb/2025:08:30:13 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:14 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:14 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:14 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 10832 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:14 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:14 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:14 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:14 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:15 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:15 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:15 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/css/styles.css" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [19/Feb/2025:08:30:15 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [19/Feb/2025:08:38:58 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [19/Feb/2025:08:46:14 +0000] "GET / HTTP/1.1" 400 255 "-" "python-requests/2.32.3"
nginx-1  | ************* - - [19/Feb/2025:08:51:46 +0000] "GET /httpd.conf HTTP/1.1" 301 169 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:51:47 +0000] "GET httpd.conf HTTP/1.1" 400 157 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:51:48 +0000] "GET /php_info.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************ - - [19/Feb/2025:08:52:03 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [19/Feb/2025:08:52:07 +0000] "GET / HTTP/1.1" 200 3019 "http://cruca.org" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [19/Feb/2025:08:52:18 +0000] "GET /api/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:52:49 +0000] "GET /blog/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:53:19 +0000] "GET /.env.production HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:53:50 +0000] "GET /protected/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:54:20 +0000] "GET /config/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:54:51 +0000] "GET /base/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:55:22 +0000] "GET /.env.development HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:55:52 +0000] "GET /v2/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:56:23 +0000] "GET /database/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:56:53 +0000] "GET /config.yaml.bak HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:57:24 +0000] "GET /.env.bak HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:57:54 +0000] "GET /package.json HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:58:25 +0000] "GET /conf/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:58:55 +0000] "GET /application-dev.properties HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:59:26 +0000] "GET /.AWS_/credentials HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:08:59:26 +0000] "GET /karma.conf.json HTTP/1.1" 404 0 "-" "-"
nginx-1  | *************** - - [19/Feb/2025:08:59:29 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [19/Feb/2025:08:59:49 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:08:59:50 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:08:59:56 +0000] "GET /wp-config.php.save HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:00:27 +0000] "GET /app/config/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:00:57 +0000] "GET /backend/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:01:28 +0000] "GET /config/php.ini HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:01:58 +0000] "GET /.env.save HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:02:29 +0000] "GET /db_config.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:03:00 +0000] "GET /crm/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:03:13 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 12_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/76.0.3809.81 Mobile/15E148 Safari/605.1"
nginx-1  | ************* - - [19/Feb/2025:09:03:30 +0000] "GET /laravel/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:03:41 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Linux; Android 8.0.0; LG-H873) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.89 Mobile Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:09:04:01 +0000] "GET /system/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:04:31 +0000] "GET /.htaccess HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:05:02 +0000] "GET /api/config/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:05:32 +0000] "GET /app/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:06:03 +0000] "GET /admin/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:06:33 +0000] "GET /cgi-bin/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:07:04 +0000] "GET /_profiler/phpinfo HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:07:04 +0000] "GET /server/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:07:34 +0000] "GET /old/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:08:05 +0000] "GET /.env.json HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:08:35 +0000] "GET /api/env HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:08:36 +0000] "GET /resources/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:09:06 +0000] "GET /info.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:09:37 +0000] "GET /wp-config.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:10:07 +0000] "GET /dev.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:10:38 +0000] "GET /wp-content/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:11:08 +0000] "GET /phpinfo.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:11:39 +0000] "GET /.json HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:12:09 +0000] "GET /.env.backup HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:12:40 +0000] "GET / HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:12:40 +0000] "GET /php.ini HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:13:11 +0000] "GET /web/app_dev.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | 48.217.212.104 - - [19/Feb/2025:09:13:20 +0000] "GET /druid/index.html HTTP/1.1" 301 169 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | ************* - - [19/Feb/2025:09:13:41 +0000] "GET /.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:14:12 +0000] "GET /test HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:14:12 +0000] "GET /library/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:14:42 +0000] "GET /settings.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:15:13 +0000] "GET /config/env.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:15:43 +0000] "GET /db.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:16:14 +0000] "GET /config.php.save HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:16:44 +0000] "GET /v1/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:17:15 +0000] "GET /wp-admin/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:17:46 +0000] "GET /.env.testing HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:18:16 +0000] "GET /info HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:18:16 +0000] "GET /.env.old HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:18:47 +0000] "GET /public/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:19:17 +0000] "GET /app_dev.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:19:48 +0000] "GET /.aws/credentials HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:19:48 +0000] "GET /.env.local HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:20:19 +0000] "GET /admin/config HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:20:19 +0000] "GET /core/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:20:49 +0000] "GET /storage/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:21:20 +0000] "GET /phpinfo HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:21:20 +0000] "GET /config.properties HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:21:51 +0000] "GET /config/settings.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:22:21 +0000] "GET /index.html HTTP/1.1" 200 3019 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:22:21 +0000] "GET /settings/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:22:52 +0000] "GET /php_errors.log HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:23:23 +0000] "GET /db.php.bak HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:23:53 +0000] "GET /database.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:09:24:15 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 101.32.218.31 - - [19/Feb/2025:09:24:15 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************* - - [19/Feb/2025:09:24:24 +0000] "GET /src/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:24:54 +0000] "GET /adminer.php HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:25:25 +0000] "GET /new/.env HTTP/1.1" 404 0 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:25:55 +0000] "GET /js/calendar.js HTTP/1.1" 200 10553 "-" "-"
nginx-1  | ************* - - [19/Feb/2025:09:25:55 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "-" "-"
nginx-1  | 65.49.1.20 - - [19/Feb/2025:09:51:20 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.50"
nginx-1  | 45.137.194.167 - - [19/Feb/2025:09:52:25 +0000] "\x16\x03\x01\x02\x00\x01\x00\x01\xFC\x03\x03Hh\xAE\xC3UJ3\x1D\xB3\x1E\x87\xC8\xB7\xF0~ \x93\x00\x04\xD5\xB9J\xC3\xE2\xCB\x19\x90Dg\xB2b? K\xBDJy\x86\x5Ct\xF1\xC2\xA6\xC0\xEE\xCCE\xF3\xCD\xF6n\x92v\x0F\xD7\x03\x95\x95\x1Cd\xE0\x99\xC4t\xDE\x00V\x13\x02\x13\x03\x13\x01\xC0,\xC00\xC0+\xC0/\xCC\xA9\xCC\xA8\x00\x9F\x00\x9E\xCC\xAA\xC0\xAF\xC0\xAD\xC0\xAE\xC0\xAC\xC0$\xC0(\xC0#\xC0'\xC0" 400 157 "-" "-"
nginx-1  | 65.49.1.17 - - [19/Feb/2025:09:55:13 +0000] "GET /webui/ HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 14.153.144.47 - - [19/Feb/2025:09:57:23 +0000] "GET /cdn-cgi/trace HTTP/1.1" 400 255 "-" "Python/3.9 aiohttp/3.11.12"
nginx-1  | 65.49.1.15 - - [19/Feb/2025:09:58:41 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36"
nginx-1  | 65.49.1.17 - - [19/Feb/2025:09:59:40 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.50"
nginx-1  | 65.49.1.14 - - [19/Feb/2025:10:03:22 +0000] "GET /geoserver/web/ HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 65.49.1.18 - - [19/Feb/2025:10:05:57 +0000] "GET /.git/config HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.0"
nginx-1  | 143.137.166.95 - - [19/Feb/2025:10:06:39 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "-" "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 192.67.163.2 - - [19/Feb/2025:10:06:41 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:10:10:45 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [19/Feb/2025:10:10:47 +0000] "POST / HTTP/1.1" 405 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************ - - [19/Feb/2025:10:17:48 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | 186.179.39.113 - - [19/Feb/2025:10:19:30 +0000] "GET / HTTP/1.1" 200 3019 "-" "Apache-HttpClient/5.1.4 (Java/11.0.18)"
nginx-1  | 191.102.157.39 - - [19/Feb/2025:10:19:32 +0000] "HEAD / HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 209.127.107.101 - - [19/Feb/2025:10:19:33 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 161.77.133.225 - - [19/Feb/2025:10:19:34 +0000] "GET / HTTP/1.1" 200 3019 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 209.127.110.8 - - [19/Feb/2025:10:19:36 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 54.226.32.36 - - [19/Feb/2025:10:19:38 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 54.242.214.56 - - [19/Feb/2025:10:19:40 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 47.129.246.175 - - [19/Feb/2025:10:32:33 +0000] "GET / HTTP/1.1" 301 169 "-" "Go-http-client/1.1"

===
Release date 23/feb 
==
148 Safari/604.1"
nginx-1  | 139.59.18.224 - - [22/Feb/2025:18:28:57 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 Keydrop"
nginx-1  | 139.59.18.224 - - [22/Feb/2025:18:28:57 +0000] "GET /.env HTTP/1.1" 400 255 "-" "Mozilla/5.0 Keydrop"
nginx-1  | 199.45.154.129 - - [22/Feb/2025:18:30:21 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | 199.45.154.129 - - [22/Feb/2025:18:30:30 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 199.45.154.129 - - [22/Feb/2025:18:30:31 +0000] "PRI * HTTP/2.0" 400 157 "-" "-"
nginx-1  | 199.45.154.129 - - [22/Feb/2025:18:30:35 +0000] "GET /favicon.ico HTTP/1.1" 301 169 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 199.45.154.129 - - [22/Feb/2025:18:30:36 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "http://*************/favicon.ico" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 199.45.154.129 - - [22/Feb/2025:18:30:37 +0000] "\x16\x03\x01\x00\xF7\x01\x00\x00\xF3\x03\x03\xE4?\x10\xD2\xCC*\xB3\xBE\xD3\xCEJ\x8C\xEC?D\xCF5\x94\x95\x08\xF8\x9C\x15D\xD3\x87\xE3\xE6\x05\xE8\xAB\x82 \xC6\x07\xEB\x9C%\x04\xD0m\x88\x90'9\x5C@\xD6\xFC\xA8&\xCAB\x8Fv\x11\xDF]\xB9\x10X\xCB\x9C\x0E\xF9\x00&\xCC\xA8\xCC\xA9\xC0/\xC00\xC0+\xC0,\xC0\x13\xC0\x09\xC0\x14\xC0" 400 157 "-" "-"
nginx-1  | ************* - - [22/Feb/2025:18:31:29 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.101 Safari/537.36 OPR/40.0.2308.62"
nginx-1  | 4.43.184.114 - - [22/Feb/2025:18:32:13 +0000] "GET / HTTP/1.0" 301 169 "-" "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50728)"
nginx-1  | 4.43.184.114 - - [22/Feb/2025:18:32:14 +0000] "GET / HTTP/1.0" 200 3027 "-" "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50728)"
nginx-1  | ************ - - [22/Feb/2025:18:36:23 +0000] "GET / HTTP/1.0" 301 169 "-" "Mozilla/5.0 (Linux; Android 7.1.2; Redmi 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.157 Mobile Safari/537.36"
nginx-1  | ************ - - [22/Feb/2025:18:38:34 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [22/Feb/2025:18:50:20 +0000] "GET / HTTP/1.0" 301 169 "-" "Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>"
nginx-1  | ************* - - [22/Feb/2025:18:56:33 +0000] "POST /GponForm/diag_Form?images/ HTTP/1.1" 400 157 "-" "Hello, World"
nginx-1  | ************** - - [22/Feb/2025:19:11:44 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:19:11:45 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************ - - [22/Feb/2025:19:13:55 +0000] "GET /.git/config HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Linux; Android 8.0.0; G8441) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.99 YaBrowser/**********.00 Mobile Safari/537.36"
nginx-1  | ************* - - [22/Feb/2025:19:20:04 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 12_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [22/Feb/2025:19:21:21 +0000] "GET /main.js HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Linux; Android 9; Moto Z3 Play) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Mobile Safari/537.36"
nginx-1  | ************* - - [22/Feb/2025:19:21:21 +0000] "GET //kit.fontawesome.com/269065907b.js HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Linux; Android 9; Pixel 3 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.111 Mobile Safari/537.36"
nginx-1  | ************* - - [22/Feb/2025:19:21:22 +0000] "GET /calendar.js HTTP/1.1" 404 0 "-" "Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.34 (KHTML, like Gecko) QupZilla/1.2.0 Safari/534.34"
nginx-1  | ************ - - [22/Feb/2025:19:29:29 +0000] "\x16\x03\x02\x01o\x01\x00\x01k\x03\x02RH\xC5\x1A#\xF7:N\xDF\xE2\xB4\x82/\xFF\x09T\x9F\xA7\xC4y\xB0h\xC6\x13\x8C\xA4\x1C=\x22\xE1\x1A\x98 \x84\xB4,\x85\xAFn\xE3Y\xBBbhl\xFF(=':\xA9\x82\xD9o\xC8\xA2\xD7\x93\x98\xB4\xEF\x80\xE5\xB9\x90\x00(\xC0" 400 157 "-" "-"
nginx-1  | *************** - - [22/Feb/2025:19:36:57 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************** - - [22/Feb/2025:19:40:32 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:19:40:33 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:19:42:40 +0000] "\x00\x0E\x08\x15O\xC4\x84\x9074\xDF\x00\x00\x00\x00\x00" 400 157 "-" "-"
nginx-1  | ************** - - [22/Feb/2025:19:42:42 +0000] "\x00\x0E8\x15O\xC4\x84\x9074\xDF\x00\x00\x00\x00\x00" 400 157 "-" "-"
nginx-1  | ************** - - [22/Feb/2025:19:44:50 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [22/Feb/2025:19:44:52 +0000] "GET / HTTP/1.1" 200 3027 "http://*************" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [22/Feb/2025:19:49:51 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:19:49:53 +0000] "POST / HTTP/1.1" 405 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************ - - [22/Feb/2025:19:58:47 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | ************* - - [22/Feb/2025:20:06:37 +0000] "\xFF\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xF1\x03%\x00\x00\x00{ \x22Ret\x22 : 100, \x22SessionID\x22 : \x220x0\x22 }" 400 157 "-" "-"
nginx-1  | *************** - - [22/Feb/2025:20:06:54 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | ************** - - [22/Feb/2025:20:17:12 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
nginx-1  | ************** - - [22/Feb/2025:20:17:12 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15"
nginx-1  | ************ - - [22/Feb/2025:20:18:35 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | 47.100.201.178 - - [22/Feb/2025:20:27:01 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36"
nginx-1  | 47.100.201.178 - - [22/Feb/2025:20:27:01 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36"
nginx-1  | ************ - - [22/Feb/2025:20:28:42 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | *************** - - [22/Feb/2025:20:29:58 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | 92.255.57.58 - - [22/Feb/2025:20:31:48 +0000] "GET /actuator/gateway/routes HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36"
nginx-1  | 194.187.176.196 - - [22/Feb/2025:20:35:41 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:65.0) Gecko/20100101 Firefox/65.0"
nginx-1  | 44.243.104.220 - - [22/Feb/2025:20:36:04 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 44.243.104.220 - - [22/Feb/2025:20:36:05 +0000] "GET / HTTP/1.1" 200 674 "http://admin.cruca.org" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | 206.168.34.41 - - [22/Feb/2025:20:37:13 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 206.168.34.41 - - [22/Feb/2025:20:37:18 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 206.168.34.41 - - [22/Feb/2025:20:37:20 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 206.168.34.41 - - [22/Feb/2025:20:37:46 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 35.203.211.66 - - [22/Feb/2025:20:38:19 +0000] "\x00\x00\x001\xFFSMBr\x00\x00\x00\x00\x18Eh\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xB5}\x00\x00\x01\x00\x00\x0E\x00\x02NT LM 0.12\x00\x02\x00" 400 157 "-" "-"
nginx-1  | 35.203.211.66 - - [22/Feb/2025:20:38:19 +0000] "\x00\x00\x00f\xFESMB@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x001234567890123456$\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x001234567890123456\x00\x00\x00\x00\x00\x00\x00\x00\x02\x02" 400 157 "-" "-"
nginx-1  | 35.203.211.66 - - [22/Feb/2025:20:38:20 +0000] "\x00\x00\x00f\xFESMB@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x001234567890123456$\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x001234567890123456\x00\x00\x00\x00\x00\x00\x00\x00\x10\x02" 400 157 "-" "-"
nginx-1  | 35.203.211.66 - - [22/Feb/2025:20:38:21 +0000] "\x00\x00\x00f\xFESMB@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x001234567890123456$\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x001234567890123456\x00\x00\x00\x00\x00\x00\x00\x00\x00\x03" 400 157 "-" "-"
nginx-1  | 35.203.211.66 - - [22/Feb/2025:20:38:21 +0000] "\x00\x00\x00f\xFESMB@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x001234567890123456$\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x001234567890123456\x00\x00\x00\x00\x00\x00\x00\x00\x02\x03" 400 157 "-" "-"
nginx-1  | 35.203.211.66 - - [22/Feb/2025:20:38:22 +0000] "\x00\x00\x00\xAC\xFESMB@\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x001234567890123456$\x00\x01\x00\x01\x00\x00\x00\x00\x00\x00\x001234567890123456h\x00\x00\x00\x02\x00\x00\x00\x11\x03\x00\x00\x02\x00\x06\x00\x00\x00\x00\x00\x02\x00\x02\x00\x01\x00\x00\x00\x01\x00,\x00\x00\x00\x00\x00\x02\x00\x02\x00\x01\x00\x01\x00 \x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00" 400 157 "-" "-"
nginx-1  | 206.168.34.64 - - [22/Feb/2025:20:41:20 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 206.168.34.64 - - [22/Feb/2025:20:41:32 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 206.168.34.64 - - [22/Feb/2025:20:42:01 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 206.168.34.64 - - [22/Feb/2025:20:42:24 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | 92.255.57.86 - - [22/Feb/2025:20:49:47 +0000] "GET /remote/login HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.203"
nginx-1  | 92.255.57.86 - - [22/Feb/2025:20:49:48 +0000] "GET /login HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.203"
nginx-1  | ************* - - [22/Feb/2025:20:59:24 +0000] "GET / HTTP/1.1" 200 3027 "-" "curl/8.6.0"
nginx-1  | ************ - - [22/Feb/2025:21:08:27 +0000] "GET / HTTP/1.1" 301 169 "-" "python-requests/2.32.3"
nginx-1  | *************** - - [22/Feb/2025:21:10:29 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:29 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:29 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 11232 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /js/calendar.js HTTP/1.1" 200 14375 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:30 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:31 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:31 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:10:31 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:11:29 +0000] "GET /calendar.html HTTP/1.1" 200 1937 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:11:30 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 16797 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:12:34 +0000] "GET /whatson.html HTTP/1.1" 200 14130 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:15:49 +0000] "GET /about.html HTTP/1.1" 200 2195 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:15:55 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 16819 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:16:00 +0000] "GET /care.html HTTP/1.1" 200 3897 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [22/Feb/2025:21:16:22 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [22/Feb/2025:21:16:24 +0000] "GET / HTTP/1.1" 200 3027 "http://cruca.org" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [22/Feb/2025:21:17:09 +0000] "GET /contact.html HTTP/1.1" 200 6269 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [22/Feb/2025:21:29:58 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:29:59 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:55 +0000] "GET / HTTP/1.1" 304 0 "-" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:55 +0000] "GET /css/components/calendar.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:55 +0000] "GET /css/styles.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:55 +0000] "GET /js/calendar.js HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:55 +0000] "GET /js/main.js HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:59 +0000] "GET / HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:59 +0000] "GET /static/css/main.d73024fc.css HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:59 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:32:59 +0000] "GET /manifest.json HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:00 +0000] "OPTIONS /api/auth/User HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:00 +0000] "GET /logo192.png HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:00 +0000] "GET /api/auth/User HTTP/1.1" 401 37 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:04 +0000] "GET /css/components/calendar.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:05 +0000] "GET /css/styles.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:05 +0000] "GET /css/components/navbar.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:05 +0000] "GET /js/main.js HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:06 +0000] "GET /css/components/main.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:09 +0000] "GET / HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:09 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:10 +0000] "GET /static/css/main.d73024fc.css HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:10 +0000] "GET /manifest.json HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:10 +0000] "GET /logo192.png HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:23 +0000] "OPTIONS /api/auth/Login HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:23 +0000] "POST /api/auth/Login HTTP/1.1" 200 348 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:23 +0000] "OPTIONS /api/events/admin HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:21:33:23 +0000] "GET /api/events/admin HTTP/1.1" 200 14585 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | 13.83.41.6 - - [22/Feb/2025:21:44:44 +0000] "GET /manager/html HTTP/1.1" 400 255 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:07 +0000] "POST /cgi-bin/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/bin/sh HTTP/1.1" 400 157 "-" "-"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:07 +0000] "POST /cgi-bin/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/bin/sh HTTP/1.1" 400 157 "-" "-"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:08 +0000] "POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input HTTP/1.1" 301 169 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:09 +0000] "POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input HTTP/1.1" 405 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:09 +0000] "GET /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:10 +0000] "GET /vendor/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:10 +0000] "GET /vendor/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:10 +0000] "GET /vendor/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:11 +0000] "GET /vendor/phpunit/phpunit/LICENSE/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:11 +0000] "GET /vendor/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:11 +0000] "GET /phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:12 +0000] "GET /phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:12 +0000] "GET /phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:12 +0000] "GET /phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:13 +0000] "GET /lib/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:13 +0000] "GET /lib/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:13 +0000] "GET /lib/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:14 +0000] "GET /lib/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:14 +0000] "GET /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:14 +0000] "GET /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:15 +0000] "GET /www/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:15 +0000] "GET /ws/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:15 +0000] "GET /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:16 +0000] "GET /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:16 +0000] "GET /ws/ec/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:16 +0000] "GET /V2/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:17 +0000] "GET /tests/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:17 +0000] "GET /test/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:17 +0000] "GET /testing/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:18 +0000] "GET /api/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:18 +0000] "GET /demo/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:18 +0000] "GET /cms/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:18 +0000] "GET /crm/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:19 +0000] "GET /admin/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:19 +0000] "GET /backup/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:19 +0000] "GET /blog/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:20 +0000] "GET /workspace/drupal/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:20 +0000] "GET /panel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:20 +0000] "GET /public/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:21 +0000] "GET /apps/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:21 +0000] "GET /app/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:21 +0000] "GET /index.php?s=/index/\x5Cthink\x5Capp/invokefunction&function=call_user_func_array&vars[0]=md5&vars[1][]=Hello HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:22 +0000] "GET /public/index.php?s=/index/\x5Cthink\x5Capp/invokefunction&function=call_user_func_array&vars[0]=md5&vars[1][]=Hello HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:22 +0000] "GET /index.php?lang=../../../../../../../../usr/local/lib/php/pearcmd&+config-create+/&/<?echo(md5(\x22hi\x22));?>+/tmp/index1.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:22 +0000] "GET /index.php?lang=../../../../../../../../tmp/index1 HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | 36.189.253.253 - - [22/Feb/2025:21:49:23 +0000] "GET /containers/json HTTP/1.1" 404 153 "-" "Custom-AsyncHttpClient"
nginx-1  | ************* - - [22/Feb/2025:21:52:26 +0000] "GET / HTTP/1.1" 301 169 "-" "iTunes/9.0.3 (Macintosh; U; Intel Mac OS X 10_6_2; en-ca)"
nginx-1  | ************ - - [22/Feb/2025:21:56:49 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************* - - [22/Feb/2025:22:06:43 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [22/Feb/2025:22:08:17 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/28.0.1469.0 Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:22:10:13 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
nginx-1  | ************** - - [22/Feb/2025:22:10:14 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
nginx-1  | ************** - - [22/Feb/2025:22:10:15 +0000] "HEAD /aspnet_webadmin HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"
nginx-1  | ************** - - [22/Feb/2025:22:22:29 +0000] "GET / HTTP/1.1" 200 3027 "-" "-"
nginx-1  | ************** - - [22/Feb/2025:22:22:51 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | ************** - - [22/Feb/2025:22:22:55 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | ************** - - [22/Feb/2025:22:22:59 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | ************** - - [22/Feb/2025:22:23:37 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"
nginx-1  | ************** - - [22/Feb/2025:22:34:23 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [22/Feb/2025:22:34:25 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:47 +0000] "GET / HTTP/1.1" 301 169 "android-app://com.google.android.googlequicksearchbox/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:48 +0000] "GET / HTTP/1.1" 200 3027 "android-app://com.google.android.googlequicksearchbox/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:48 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:48 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 11232 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /js/calendar.js HTTP/1.1" 200 14375 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:49 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:50 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:50 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:58 +0000] "GET /calendar.html HTTP/1.1" 200 1937 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:54:58 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 16819 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 1.128.107.158 - - [22/Feb/2025:22:56:50 +0000] "GET /api/Events?month=3&year=2025 HTTP/1.1" 200 19560 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
nginx-1  | 74.125.77.19 - - [22/Feb/2025:22:57:33 +0000] "GET / HTTP/1.1" 200 3027 "https://www.google.com/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.19 - - [22/Feb/2025:22:57:33 +0000] "GET /css/styles.css HTTP/1.1" 200 6949 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.19 - - [22/Feb/2025:22:57:33 +0000] "GET /css/components/calendar.css HTTP/1.1" 200 11232 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.26 - - [22/Feb/2025:22:57:34 +0000] "GET /css/components/navbar.css HTTP/1.1" 200 1218 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.20 - - [22/Feb/2025:22:57:34 +0000] "GET /css/components/footer.css HTTP/1.1" 200 576 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.25 - - [22/Feb/2025:22:57:34 +0000] "GET /js/calendar.js HTTP/1.1" 200 14375 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.22 - - [22/Feb/2025:22:57:34 +0000] "GET /js/main.js HTTP/1.1" 200 5015 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.24 - - [22/Feb/2025:22:57:34 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.19 - - [22/Feb/2025:22:57:34 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.24 - - [22/Feb/2025:22:57:34 +0000] "GET /home.html HTTP/1.1" 200 2336 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.25 - - [22/Feb/2025:22:57:34 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 200 199870 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 74.125.77.24 - - [22/Feb/2025:22:57:34 +0000] "GET /css/components/main.css HTTP/1.1" 200 2637 "https://cruca.org/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 64.233.173.233 - - [22/Feb/2025:22:57:49 +0000] "GET /.well-known/assetlinks.json HTTP/1.1" 404 0 "-" "GoogleAssociationService"
nginx-1  | 64.233.173.235 - - [22/Feb/2025:22:57:50 +0000] "GET /.well-known/assetlinks.json HTTP/1.1" 404 0 "-" "GoogleAssociationService"
nginx-1  | ************** - - [22/Feb/2025:23:00:05 +0000] "POST /boaform/admin/formLogin HTTP/1.1" 301 169 "http://*************:80/admin/login.asp" "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:71.0) Gecko/20100101 Firefox/71.0"
nginx-1  | ************** - - [22/Feb/2025:23:00:05 +0000] "" 400 0 "-" "-"
nginx-1  | ************ - - [22/Feb/2025:23:08:41 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | ************* - - [22/Feb/2025:23:37:49 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [22/Feb/2025:23:37:51 +0000] "GET / HTTP/1.1" 200 3027 "http://*************" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [22/Feb/2025:23:45:27 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | ************ - - [22/Feb/2025:23:52:43 +0000] "POST /cgi-bin/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/bin/sh HTTP/1.1" 400 157 "-" "-"
nginx-1  | ************ - - [22/Feb/2025:23:52:45 +0000] "POST /cgi-bin/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/%%32%65%%32%65/bin/sh HTTP/1.1" 400 157 "-" "-"
nginx-1  | ************ - - [22/Feb/2025:23:52:47 +0000] "POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input HTTP/1.1" 301 169 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:52:51 +0000] "POST /hello.world?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input HTTP/1.1" 405 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:52:53 +0000] "GET /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:52:55 +0000] "GET /vendor/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:52:57 +0000] "GET /vendor/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:52:59 +0000] "GET /vendor/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:00 +0000] "GET /vendor/phpunit/phpunit/LICENSE/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:02 +0000] "GET /vendor/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:04 +0000] "GET /phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:05 +0000] "GET /phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:07 +0000] "GET /phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:08 +0000] "GET /phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:09 +0000] "GET /lib/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:11 +0000] "GET /lib/phpunit/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:12 +0000] "GET /lib/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:13 +0000] "GET /lib/phpunit/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:14 +0000] "GET /lib/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:15 +0000] "GET /laravel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:17 +0000] "GET /www/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:18 +0000] "GET /ws/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:19 +0000] "GET /yii/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:20 +0000] "GET /zend/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:21 +0000] "GET /ws/ec/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:22 +0000] "GET /V2/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:23 +0000] "GET /tests/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:24 +0000] "GET /test/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:26 +0000] "GET /testing/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:27 +0000] "GET /api/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:28 +0000] "GET /demo/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:28 +0000] "GET /cms/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:30 +0000] "GET /crm/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:31 +0000] "GET /admin/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:32 +0000] "GET /backup/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:33 +0000] "GET /blog/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:35 +0000] "GET /workspace/drupal/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:36 +0000] "GET /panel/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:37 +0000] "GET /public/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:38 +0000] "GET /apps/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:39 +0000] "GET /app/vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:41 +0000] "GET /index.php?s=/index/\x5Cthink\x5Capp/invokefunction&function=call_user_func_array&vars[0]=md5&vars[1][]=Hello HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:42 +0000] "GET /public/index.php?s=/index/\x5Cthink\x5Capp/invokefunction&function=call_user_func_array&vars[0]=md5&vars[1][]=Hello HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:43 +0000] "GET /index.php?lang=../../../../../../../../usr/local/lib/php/pearcmd&+config-create+/&/<?echo(md5(\x22hi\x22));?>+/tmp/index1.php HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:44 +0000] "GET /index.php?lang=../../../../../../../../tmp/index1 HTTP/1.1" 404 0 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [22/Feb/2025:23:53:45 +0000] "GET /containers/json HTTP/1.1" 404 153 "-" "Custom-AsyncHttpClient"
nginx-1  | ************ - - [23/Feb/2025:00:06:24 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 16797 "https://cruca.org/" "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [23/Feb/2025:00:08:49 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************ - - [23/Feb/2025:00:08:50 +0000] "GET / HTTP/1.1" 200 3027 "http://cruca.org" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************* - - [23/Feb/2025:00:10:37 +0000] "GET / HTTP/1.1" 200 3027 "-" "python-requests/2.32.3"
nginx-1  | ************** - - [23/Feb/2025:00:13:42 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:00:13:44 +0000] "HEAD /Core/Skin/Login.aspx HTTP/1.1" 404 0 "http://*************:80/Core/Skin/Login.aspx" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:20:40 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:41 +0000] "GET / HTTP/1.1" 304 0 "-" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:42 +0000] "GET /css/components/footer.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:42 +0000] "GET /js/calendar.js HTTP/1.1" 200 14375 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:43 +0000] "GET /data/img/bkg/CRUC_Logo_and_trim.webp HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:44 +0000] "GET /calendar.html HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:44 +0000] "GET /api/Events?month=2&year=2025 HTTP/1.1" 200 16797 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:46 +0000] "GET /api/Events?month=3&year=2025 HTTP/1.1" 200 19560 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:49 +0000] "GET /api/Events?month=4&year=2025 HTTP/1.1" 200 14926 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:29:50 +0000] "GET /api/Events?month=5&year=2025 HTTP/1.1" 200 14833 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************ - - [23/Feb/2025:00:30:05 +0000] "\x16\x03\x02\x01o\x01\x00\x01k\x03\x02RH\xC5\x1A#\xF7:N\xDF\xE2\xB4\x82/\xFF\x09T\x9F\xA7\xC4y\xB0h\xC6\x13\x8C\xA4\x1C=\x22\xE1\x1A\x98 \x84\xB4,\x85\xAFn\xE3Y\xBBbhl\xFF(=':\xA9\x82\xD9o\xC8\xA2\xD7\x93\x98\xB4\xEF\x80\xE5\xB9\x90\x00(\xC0" 400 157 "-" "-"
nginx-1  | ********** - - [23/Feb/2025:00:30:54 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (iPad; CPU OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6,2 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [23/Feb/2025:00:31:55 +0000] "GET /ab2g HTTP/1.1" 200 3027 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | *************** - - [23/Feb/2025:00:31:55 +0000] "GET /ab2h HTTP/1.1" 200 3027 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | *************** - - [23/Feb/2025:00:31:55 +0000] "GET /alive.php HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:31:57 +0000] "GET / HTTP/1.1" 400 657 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:31:58 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:31:58 +0000] "GET /t4 HTTP/1.1" 400 255 "-" "Mozilla/5.0"
nginx-1  | *************** - - [23/Feb/2025:00:31:58 +0000] "GET /t4 HTTP/1.1" 200 3027 "-" "Mozilla/5.0"
nginx-1  | *************** - - [23/Feb/2025:00:31:59 +0000] "GET /favicon.ico HTTP/1.1" 400 657 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:31:59 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:00:31:59 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/4.0 (compatible; MSIE 9.0; Windows NT 10.0; .NET4.0C; .NET4.0E; .NET CLR 2.0.50727; .NET CLR 3.0.30729; .NET CLR 3.5.30729)"
nginx-1  | *************** - - [23/Feb/2025:00:32:00 +0000] "GET /teorema505?t=1 HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ********** - - [23/Feb/2025:00:34:24 +0000] "GET /webui/ HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
nginx-1  | 193.34.73.149 - - [23/Feb/2025:00:35:29 +0000] "GET / HTTP/1.1" 200 3027 "-" "Apache-HttpClient/5.1.4 (Java/11.0.18)"
nginx-1  | 64.43.119.250 - - [23/Feb/2025:00:35:31 +0000] "HEAD / HTTP/1.1" 301 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 143.137.166.64 - - [23/Feb/2025:00:35:32 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 143.137.165.120 - - [23/Feb/2025:00:35:33 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 64.43.108.170 - - [23/Feb/2025:00:35:34 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 170.246.53.24 - - [23/Feb/2025:00:35:35 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 192.67.160.88 - - [23/Feb/2025:00:35:37 +0000] "GET /data/img/favicon.ico HTTP/1.1" 200 32038 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ********** - - [23/Feb/2025:00:37:28 +0000] "GET /favicon.ico HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
nginx-1  | ********** - - [23/Feb/2025:00:38:54 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (iPad; CPU OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6,2 Mobile/15E148 Safari/604.1"
nginx-1  | ********** - - [23/Feb/2025:00:42:41 +0000] "GET /geoserver/web/ HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
nginx-1  | ************** - - [23/Feb/2025:00:43:51 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 Keydrop"
nginx-1  | ************** - - [23/Feb/2025:00:43:52 +0000] "GET /.env HTTP/1.1" 400 255 "-" "Mozilla/5.0 Keydrop"
nginx-1  | ********** - - [23/Feb/2025:00:44:32 +0000] "GET /.git/config HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; rv:102.0) Gecko/20100101 Firefox/102.0"
nginx-1  | ************ - - [23/Feb/2025:00:46:26 +0000] "GET / HTTP/1.1" 200 674 "-" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | ************ - - [23/Feb/2025:00:46:27 +0000] "GET /static/css/main.d73024fc.css HTTP/1.1" 200 46688 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | ************ - - [23/Feb/2025:00:46:27 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 200 108495 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:00:52:16 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:00:52:18 +0000] "POST / HTTP/1.1" 405 0 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | *************** - - [23/Feb/2025:01:00:20 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | *************** - - [23/Feb/2025:01:00:21 +0000] "GET / HTTP/1.1" 200 3027 "http://cruca.org" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [23/Feb/2025:01:06:00 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:01:06:00 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************ - - [23/Feb/2025:01:06:51 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.0"
nginx-1  | ************** - - [23/Feb/2025:01:22:18 +0000] "GET /.env HTTP/1.1" 404 0 "-" "Links (2.1pre15; FreeBSD 5.3-RELEASE i386; 196x84)"
nginx-1  | ************ - - [23/Feb/2025:01:22:56 +0000] "GET / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36 Edg/90.0.818.46"
nginx-1  | 193.34.73.235 - - [23/Feb/2025:01:32:10 +0000] "GET /data/img/logo/logo_nbk.png HTTP/1.1" 200 20029 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_0_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | 186.65.120.171 - - [23/Feb/2025:01:32:12 +0000] "GET /data/img/logo/cross.png HTTP/1.1" 200 23276 "-" "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
nginx-1  | ************ - - [23/Feb/2025:01:42:52 +0000] "GET / HTTP/1.1" 301 169 "-" "-"
nginx-1  | 78.153.140.151 - - [23/Feb/2025:01:47:52 +0000] "\x16\x03\x01\x00\xAC\x01\x00\x00\xA8\x03\x03m\xF0u\x7F\x0C\x92\xD4\xD4\x16\x1F(5Q*E\x7Ff\xC8\xEC\x90\xA3\xDA\xD0XoE__\x8C^\xF4\x00\x00\x008\xC0,\xC0" 400 157 "-" "-"
nginx-1  | ************** - - [23/Feb/2025:01:52:22 +0000] "GET /.env HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:01:52:22 +0000] "POST / HTTP/1.1" 301 169 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.140 Safari/537.36"
nginx-1  | ************* - - [23/Feb/2025:01:54:26 +0000] "GET /ecp/Current/exporttool/microsoft.exchange.ediscovery.exporttool.application HTTP/1.1" 404 0 "-" "Mozilla/5.0 zgrab/0.x"
nginx-1  | ************ - - [23/Feb/2025:02:06:32 +0000] "GET / HTTP/1.1" 200 3027 "-" "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"
nginx-1  | ************** - - [23/Feb/2025:02:11:33 +0000] "GET / HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:34 +0000] "GET /static/css/main.d73024fc.css HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:34 +0000] "GET /static/js/main.cefe4e5a.js HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:34 +0000] "OPTIONS /api/auth/User HTTP/1.1" 204 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:35 +0000] "GET /manifest.json HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:35 +0000] "GET /api/auth/User HTTP/1.1" 401 37 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:35 +0000] "GET /logo192.png HTTP/1.1" 304 0 "https://admin.cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:41 +0000] "GET /whatson.html HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
nginx-1  | ************** - - [23/Feb/2025:02:11:41 +0000] "GET /css/styles.css HTTP/1.1" 304 0 "https://cruca.org/" "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/********* Mobile Safari/537.36"
