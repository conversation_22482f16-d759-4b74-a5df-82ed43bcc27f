---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: project
status: active
priority: medium
deadline: <% tp.date.now("YYYY-MM-DD", 30) %>
project_owner: <% await tp.system.suggester(["Jordan", "Other"], ["Jordan", await tp.system.prompt("Project Owner")], false, "Project Owner") %>
project_client: <% await tp.system.suggester(["Personal", "Church", "University", "Client"], ["Personal", "Church", "University", await tp.system.prompt("Client Name")], false, "Project Client") %>
completion_percentage: 0
estimated_hours: <% await tp.system.suggester(["10", "20", "40", "80", "Other"], ["10", "20", "40", "80", await tp.system.prompt("Estimated Hours")], false, "Estimated Hours") %>
tags: [para/projects, <% await tp.system.suggester(["software-dev", "church", "personal", "university", "other"], ["software-dev", "church", "personal", "university", await tp.system.prompt("Custom Tag")], false, "Primary Tag") %>]
area: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "Other"], ["Software-Development", "Administration", "Personal", "Church", await tp.system.prompt("Area Name")], false, "Related Area") %>
start_date: <% tp.date.now("YYYY-MM-DD") %>
---

# <% tp.file.title %>

## Overview
<!-- Brief description of the project -->

## Objectives
<!-- What are you trying to achieve? -->
-

## Success Criteria
<!-- How will you know when the project is successful? -->
-

## Tasks
<!-- List of tasks to complete -->
- [ ]

## Timeline
- **Start Date**: <% tp.date.now("YYYY-MM-DD") %>
- **Deadline**: <% tp.date.now("YYYY-MM-DD", 30) %>
- **Milestones**:
  - [ ] Initial Planning - <% tp.date.now("YYYY-MM-DD", 7) %>
  - [ ] Development - <% tp.date.now("YYYY-MM-DD", 14) %>
  - [ ] Testing - <% tp.date.now("YYYY-MM-DD", 21) %>
  - [ ] Completion - <% tp.date.now("YYYY-MM-DD", 30) %>

## Resources
<!-- Links to relevant resources -->
-

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "<% tp.file.title %>") OR area = "<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area") %>"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR contains(tags, "<% await tp.system.suggester(["software-dev", "church", "personal", "university"], ["software-dev", "church", "personal", "university"], false, "Filter Tag") %>")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "<% tp.file.title %>") OR contains(file.name, "<% tp.file.title %>")
SORT date DESC
```

## Progress Updates
<!-- Regular updates on project progress -->
### <% tp.date.now("YYYY-MM-DD") %> - Initial Setup
- Project created
- Initial planning started

## Quick Links
- [[<% tp.file.title %> Meeting|New Meeting]]
- [[<% tp.file.title %> Resource|New Resource]]
- [[1-Projects|All Projects]]
