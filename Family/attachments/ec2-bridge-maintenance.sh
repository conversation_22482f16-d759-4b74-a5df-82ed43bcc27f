#!/bin/bash

# EC2 Bridge Server Maintenance Script
# This script sets up and maintains an EC2 bridge server that forwards
# HTTP/HTTPS traffic from the internet to a local Nginx server over ZeroTier

# =============================================================================
# Configuration Variables (adjust these to match your environment)
# =============================================================================

# Network interfaces
INTERNET_IFACE="enX0"         # Internet-facing interface (typically eth0 or enX0)
ZEROTIER_IFACE="ztmjfcpubr"   # ZeroTier interface name

# IP addresses
LOCAL_SERVER_IP="*************"  # ZeroTier IP of your local Nginx Proxy Manager

# ZeroTier Network ID
ZEROTIER_NETWORK_ID="8056c2e21ce5322d"  # Your ZeroTier network ID

# Directories for logs and configs
LOG_DIR="/var/log/bridge-maintenance"
CONFIG_DIR="/etc/bridge-maintenance"

# =============================================================================
# Functions
# =============================================================================

# Function to log messages
log_message() {
    local message="$1"
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $message"
    echo "[$timestamp] $message" >> "${LOG_DIR}/maintenance.log"
}

# Function to check if a package is installed
is_installed() {
    dpkg -l "$1" &>/dev/null
    return $?
}

# Function to check internet connectivity
check_internet() {
    log_message "Checking internet connectivity..."
    if ping -c 3 ******* &>/dev/null; then
        log_message "Internet connectivity: OK"
        return 0
    else
        log_message "Internet connectivity: FAILED"
        return 1
    fi
}

# Function to check ZeroTier status
check_zerotier() {
    log_message "Checking ZeroTier status..."
    
    # Check if ZeroTier is running
    if ! systemctl is-active --quiet zerotier-one; then
        log_message "ZeroTier service is not running. Attempting to start..."
        systemctl start zerotier-one
        sleep 5
    fi
    
    # Check ZeroTier connection status
    local zt_status=$(zerotier-cli info)
    if [[ $zt_status == *"ONLINE"* ]]; then
        log_message "ZeroTier status: ONLINE"
        
        # Check if connected to the required network
        local networks=$(zerotier-cli listnetworks)
        if [[ $networks == *"${ZEROTIER_NETWORK_ID}"* ]]; then
            if [[ $networks == *"${ZEROTIER_NETWORK_ID}"*"OK"* ]]; then
                log_message "ZeroTier network connection: OK"
                return 0
            else
                log_message "ZeroTier network status issue. Current status:"
                log_message "$networks"
                return 1
            fi
        else
            log_message "Not connected to required ZeroTier network. Joining network..."
            zerotier-cli join "${ZEROTIER_NETWORK_ID}"
            return 1
        fi
    else
        log_message "ZeroTier status: OFFLINE"
        return 1
    fi
}

# Function to check for the ZeroTier interface and its IP
check_zerotier_interface() {
    log_message "Checking ZeroTier interface..."
    
    # Check if the ZeroTier interface exists
    if ! ip link show "$ZEROTIER_IFACE" &>/dev/null; then
        log_message "ZeroTier interface $ZEROTIER_IFACE not found. Available interfaces:"
        ip -o link | grep zt | awk -F': ' '{print $2}'
        return 1
    fi
    
    # Check if the interface has an IPv4 address
    local ip_addr=$(ip -4 addr show dev "$ZEROTIER_IFACE" | grep inet | awk '{print $2}')
    if [[ -z "$ip_addr" ]]; then
        log_message "No IPv4 address assigned to $ZEROTIER_IFACE"
        return 1
    else
        log_message "ZeroTier interface $ZEROTIER_IFACE has IP: $ip_addr"
        return 0
    fi
}

# Function to enable IP forwarding
enable_ip_forwarding() {
    log_message "Enabling IP forwarding..."
    
    # Check current IP forwarding status
    local current_status=$(sysctl -n net.ipv4.ip_forward)
    
    if [[ "$current_status" == "1" ]]; then
        log_message "IP forwarding is already enabled."
    else
        sysctl -w net.ipv4.ip_forward=1
        echo "net.ipv4.ip_forward=1" > /etc/sysctl.d/99-ip-forward.conf
        sysctl -p /etc/sysctl.d/99-ip-forward.conf
        log_message "IP forwarding has been enabled."
    fi
    
    return 0
}

# Function to set up iptables rules
setup_iptables_rules() {
    log_message "Setting up iptables rules..."
    
    # Create iptables directory if it doesn't exist
    mkdir -p /etc/iptables
    
    # Flush existing iptables rules
    iptables -F
    iptables -t nat -F
    iptables -X
    
    # Set default policies
    iptables -P INPUT ACCEPT
    iptables -P FORWARD ACCEPT
    iptables -P OUTPUT ACCEPT
    
    # Allow established and related traffic
    iptables -A FORWARD -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
    
    # Allow incoming HTTP and HTTPS traffic
    iptables -A INPUT -i "$INTERNET_IFACE" -p tcp --dport 80 -m conntrack --ctstate NEW -j ACCEPT
    iptables -A INPUT -i "$INTERNET_IFACE" -p tcp --dport 443 -m conntrack --ctstate NEW -j ACCEPT
    
    # Forward HTTP and HTTPS traffic to the local server via ZeroTier
    iptables -t nat -A PREROUTING -i "$INTERNET_IFACE" -p tcp --dport 80 -j DNAT --to-destination "${LOCAL_SERVER_IP}:80"
    iptables -t nat -A PREROUTING -i "$INTERNET_IFACE" -p tcp --dport 443 -j DNAT --to-destination "${LOCAL_SERVER_IP}:443"
    
    # Allow forwarding of traffic to the local server
    iptables -A FORWARD -i "$INTERNET_IFACE" -o "$ZEROTIER_IFACE" -p tcp --dport 80 -d "$LOCAL_SERVER_IP" -j ACCEPT
    iptables -A FORWARD -i "$INTERNET_IFACE" -o "$ZEROTIER_IFACE" -p tcp --dport 443 -d "$LOCAL_SERVER_IP" -j ACCEPT
    
    # Masquerade outgoing traffic on the ZeroTier interface
    iptables -t nat -A POSTROUTING -o "$ZEROTIER_IFACE" -j MASQUERADE
    
    # Save iptables rules
    log_message "Saving iptables rules..."
    mkdir -p /etc/iptables
    iptables-save > /etc/iptables/rules.v4
    
    log_message "iptables rules have been set up and saved."
    return 0
}

# Function to ensure iptables rules are loaded on boot
ensure_iptables_persistence() {
    log_message "Ensuring iptables rules persistence..."
    
    # Install iptables-persistent if not already installed
    if ! is_installed iptables-persistent; then
        log_message "Installing iptables-persistent..."
        DEBIAN_FRONTEND=noninteractive apt-get update
        DEBIAN_FRONTEND=noninteractive apt-get install -y iptables-persistent
    fi
    
    # Create systemd service to restore iptables rules on boot
    cat <<EOF > /etc/systemd/system/iptables-restore.service
[Unit]
Description=Restore iptables firewall rules
DefaultDependencies=no
After=network-pre.target
Before=network-online.target
Wants=network-pre.target

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore /etc/iptables/rules.v4
ExecReload=/sbin/iptables-restore /etc/iptables/rules.v4
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable the service
    systemctl daemon-reload
    systemctl enable iptables-restore.service
    
    log_message "iptables persistence has been configured."
    return 0
}

# Function to test connectivity to the local server
test_connectivity() {
    log_message "Testing connectivity to local server (${LOCAL_SERVER_IP})..."
    
    if ping -c 3 "$LOCAL_SERVER_IP" &>/dev/null; then
        log_message "Connectivity to local server: OK"
        
        # Try to connect to HTTP port
        if nc -z -w 5 "$LOCAL_SERVER_IP" 80; then
            log_message "HTTP port (80) on local server is accessible."
        else
            log_message "WARNING: HTTP port (80) on local server is not accessible."
        fi
        
        # Try to connect to HTTPS port
        if nc -z -w 5 "$LOCAL_SERVER_IP" 443; then
            log_message "HTTPS port (443) on local server is accessible."
        else
            log_message "WARNING: HTTPS port (443) on local server is not accessible."
        fi
        
        return 0
    else
        log_message "Connectivity to local server: FAILED"
        return 1
    fi
}

# Function to check AWS security group configuration (informational only)
check_aws_security_group() {
    log_message "Reminder: Ensure AWS security groups allow:"
    log_message "  - Inbound TCP port 80 (HTTP) from 0.0.0.0/0"
    log_message "  - Inbound TCP port 443 (HTTPS) from 0.0.0.0/0"
}

# Function to verify DNS configuration (informational only)
check_dns_configuration() {
    log_message "Reminder: Ensure DNS records are properly configured:"
    log_message "  - A record for your domain points to this server's Elastic IP"
    log_message "  - Wait for DNS propagation if recently changed"
}

# Function to create a status report
create_status_report() {
    local report_file="${LOG_DIR}/status_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "======================================================="
        echo "           EC2 Bridge Server Status Report"
        echo "======================================================="
        echo "Date: $(date)"
        echo ""
        
        echo "System Information:"
        echo "-------------------"
        echo "Hostname: $(hostname)"
        echo "Kernel: $(uname -r)"
        echo "Uptime: $(uptime -p)"
        echo ""
        
        echo "Network Interfaces:"
        echo "------------------"
        ip -br addr
        echo ""
        
        echo "ZeroTier Status:"
        echo "---------------"
        zerotier-cli info
        zerotier-cli listnetworks
        echo ""
        
        echo "iptables Rules:"
        echo "--------------"
        echo "Filter table:"
        iptables -L -v -n
        echo ""
        echo "NAT table:"
        iptables -t nat -L -v -n
        echo ""
        
        echo "Service Status:"
        echo "--------------"
        echo "ZeroTier-One: $(systemctl is-active zerotier-one)"
        echo "iptables-restore: $(systemctl is-active iptables-restore.service)"
        echo ""
        
        echo "Connectivity Tests:"
        echo "------------------"
        echo "Ping to Google (*******): $(ping -c 1 ******* &>/dev/null && echo "Success" || echo "Failed")"
        echo "Ping to Local Server (${LOCAL_SERVER_IP}): $(ping -c 1 "$LOCAL_SERVER_IP" &>/dev/null && echo "Success" || echo "Failed")"
        echo "HTTP port check (${LOCAL_SERVER_IP}:80): $(nc -z -w 5 "$LOCAL_SERVER_IP" 80 &>/dev/null && echo "Accessible" || echo "Not accessible")"
        echo "HTTPS port check (${LOCAL_SERVER_IP}:443): $(nc -z -w 5 "$LOCAL_SERVER_IP" 443 &>/dev/null && echo "Accessible" || echo "Not accessible")"
        echo ""
        
        echo "======================================================="
    } > "$report_file"
    
    log_message "Status report created: $report_file"
    
    # Display the report
    cat "$report_file"
}

# Function to install required packages
install_required_packages() {
    log_message "Checking and installing required packages..."
    
    # Update package lists
    apt-get update
    
    # Install required packages
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
        iptables \
        iptables-persistent \
        netcat \
        net-tools \
        tcpdump \
        traceroute \
        curl \
        wget \
        htop
    
    log_message "Required packages have been installed."
    return 0
}

# =============================================================================
# Main Script
# =============================================================================

# Ensure running as root
if [[ $EUID -ne 0 ]]; then
    echo "This script must be run as root. Use: sudo $0"
    exit 1
fi

# Create necessary directories
mkdir -p "$LOG_DIR"
mkdir -p "$CONFIG_DIR"

# Start logging
log_message "============ Bridge Maintenance Script Started ============"

# Install required packages
install_required_packages

# Check internet connectivity
check_internet || log_message "WARNING: Internet connectivity issues detected."

# Check and configure ZeroTier
check_zerotier || log_message "WARNING: ZeroTier issues detected."

# Check ZeroTier interface
check_zerotier_interface || log_message "WARNING: ZeroTier interface issues detected."

# Enable IP forwarding
enable_ip_forwarding || log_message "ERROR: Failed to enable IP forwarding."

# Set up iptables rules
setup_iptables_rules || log_message "ERROR: Failed to set up iptables rules."

# Ensure iptables rules persistence
ensure_iptables_persistence || log_message "ERROR: Failed to configure iptables persistence."

# Test connectivity to local server
test_connectivity || log_message "WARNING: Connectivity issues detected."

# Display AWS security group reminder
check_aws_security_group

# Display DNS configuration reminder
check_dns_configuration

# Create a status report
create_status_report

# End logging
log_message "============ Bridge Maintenance Script Completed ============"

# Add cron job for automated maintenance (if specified)
if [[ "$1" == "--add-cron" ]]; then
    log_message "Adding maintenance script to cron (daily at 2 AM)..."
    echo "0 2 * * * root $(readlink -f "$0") --run-maintenance > /dev/null 2>&1" > /etc/cron.d/bridge-maintenance
    chmod 644 /etc/cron.d/bridge-maintenance
    log_message "Cron job added."
fi

# Exit
exit 0 