---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - Simplified ZeroTier Setup
  - Single Network Architecture
tags:
  - networking
  - zerotier
  - nginx
  - security
  - simplification
  - docker
  - ufw
  - iptables
area: Infrastructure
project: Bridge Network
status: active
priority: high
links:
  - "[[Minimal Gateway For ZeroTier Networks]]"
  - "[[Family/Network Architecture Overview]]"
  - "[[Troubleshooting ZeroTier and Docker Networking]]"
related:
  - "[[EC2 Bridge Server Maintenance]]"
  - "[[ZeroTier Flow Rules Placement]]"
---

# Simplified ZeroTier Network Setup

This document outlines a simplified approach for accessing internal services over the internet using a single ZeroTier network. This approach prioritizes convenience while maintaining reasonable security through careful configuration of firewalls and network rules.

## Architecture Overview

```mermaid
graph LR
    subgraph Internet
        User[User Browser]
    end
    
    subgraph AWS EC2 Bridge
        EC2[EC2 Instance (Public IP)]
        EC2ZT[ZeroTier Client (************)]
        IPT[iptables Forwarding]
    end

    subgraph ZeroTier Network (***********/24)
        ZTN[Overlay Network]
    end
    
    subgraph Home Server (Pop-OS)
        direction LR
        PopOS[Host OS]
        subgraph Docker
            NPM[Nginx Proxy Manager (**********)]
        end
        PopOSZT[ZeroTier Client (*************)]
        UFW[UFW Firewall]
        IPT2[iptables Forwarding]
        Services[Backend Services (e.g., qBittorrent 8055, Prowlarr 9696)]
    end

    User -- HTTPS --> EC2;
    EC2 -- Port Forward (iptables) --> EC2ZT;
    EC2ZT -- ZeroTier --> PopOSZT;
    PopOSZT -- UFW Allows --> IPT2;
    IPT2 -- Forwards to Docker --> NPM;
    NPM -- Proxies to --> Services;
```

### Trade-offs

| Approach | Pros | Cons |
|----------|------|------|
| **Single ZeroTier Network** (This Document) | • Simplicity<br>• Easier to set up<br>• Less infrastructure to maintain<br>• Lower resource requirements | • Less isolation between internet-facing and internal components<br>• Higher risk if bridge server is compromised |
| **Dual ZeroTier Networks with Gateway** ([Alternative Approach](Minimal%20Gateway%20For%20ZeroTier%20Networks.md)) | • Better isolation<br>• Defense in depth<br>• Expendable gateway<br>• Limited exposure | • More complex setup<br>• More infrastructure to maintain<br>• Higher resource requirements |

## Implementation Steps

### 1. Set Up the EC2 Bridge Server

*   Install ZeroTier (`curl -s https://install.zerotier.com | sudo bash`).
*   Join the ZeroTier network (`sudo zerotier-cli join <network_id>`).
*   Authorize the EC2 server in ZeroTier Central and assign a static IP (e.g., `************`).
*   Enable IP forwarding:
    ```bash
    sudo sysctl -w net.ipv4.ip_forward=1
    echo "net.ipv4.ip_forward=1" | sudo tee -a /etc/sysctl.conf
    ```
*   Install `iptables-persistent`.

### 2. Set Up Nginx Proxy Manager Host (Pop-OS)

*   Install ZeroTier (`curl -s https://install.zerotier.com | sudo bash`).
*   Join the same ZeroTier network (`sudo zerotier-cli join <network_id>`).
*   Authorize the Pop-OS server in ZeroTier Central and assign a static IP (e.g., `*************`).
*   Install Docker and Docker Compose.
*   Configure Nginx Proxy Manager using `docker-compose.yml` (see Docker Configuration below).
*   Enable IP forwarding (same commands as EC2).
*   Install `iptables-persistent`.

### 3. Configure EC2 Bridge Server iptables Forwarding

These rules forward incoming public traffic on ports 80/443 to the Nginx host's ZeroTier IP.

```bash
# Variables (replace with your actual values)
NGINX_ZEROTIER_IP="*************"  # Nginx host's ZeroTier IP
ZEROTIER_INTERFACE="ztmjfcpubr"  # ZeroTier interface name on EC2
PUBLIC_INTERFACE="enX0"            # Public facing interface on EC2

# --- Filter Table ---
# Allow established connections
sudo iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
sudo iptables -A FORWARD -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# Allow incoming HTTP/HTTPS on public interface (for initial connection)
sudo iptables -A INPUT -i $PUBLIC_INTERFACE -p tcp --dport 80 -m conntrack --ctstate NEW -j ACCEPT
sudo iptables -A INPUT -i $PUBLIC_INTERFACE -p tcp --dport 443 -m conntrack --ctstate NEW -j ACCEPT

# Allow forwarding from public interface to ZeroTier interface for Nginx
sudo iptables -A FORWARD -i $PUBLIC_INTERFACE -o $ZEROTIER_INTERFACE -d $NGINX_ZEROTIER_IP -p tcp --dport 80 -j ACCEPT
sudo iptables -A FORWARD -i $PUBLIC_INTERFACE -o $ZEROTIER_INTERFACE -d $NGINX_ZEROTIER_IP -p tcp --dport 443 -j ACCEPT

# --- NAT Table ---
# Forward incoming HTTP/HTTPS to Nginx ZeroTier IP
sudo iptables -t nat -A PREROUTING -i $PUBLIC_INTERFACE -p tcp --dport 80 -j DNAT --to-destination $NGINX_ZEROTIER_IP:80
sudo iptables -t nat -A PREROUTING -i $PUBLIC_INTERFACE -p tcp --dport 443 -j DNAT --to-destination $NGINX_ZEROTIER_IP:443

# Masquerade traffic going out the ZeroTier interface (allows return traffic)
sudo iptables -t nat -A POSTROUTING -o $ZEROTIER_INTERFACE -j MASQUERADE

# --- Save Rules ---
sudo netfilter-persistent save
```

### 4. Configure Nginx Host Docker Setup

Nginx Proxy Manager runs in Docker. We need to ensure it can communicate with both the ZeroTier network and the host's backend services.

#### 4.1 Docker Compose (`docker-compose.yml`)

Use a custom bridge network for better isolation than the default `docker0`.

```yaml
version: '3.8'
services:
  app:
    image: 'jc21/nginx-proxy-manager:latest'
    container_name: nginx-proxy-manager # Assign a fixed name
    restart: unless-stopped
    ports:
      # Map host ports to container ports
      - '80:80'   # HTTP Port
      - '443:443' # HTTPS Port
      - '81:81'   # Admin Web Port
    volumes:
      - npm_data:/data
      - npm_letsencrypt:/etc/letsencrypt
    networks:
      # Connect to the custom bridge network
      - proxy-manager-network 

networks:
  proxy-manager-network: # Define the custom network
    driver: bridge
    # Optionally define subnet if needed, otherwise Docker assigns one
    # ipam:
    #  config:
    #    - subnet: **********/16 # Example - verify yours

volumes:
  npm_data:
  npm_letsencrypt:
```

#### 4.2 Identify Docker Network Details

Find the network name, bridge interface, and subnet used by your Nginx container.

```bash
# Find container name/ID
docker ps | grep nginx-proxy-manager

# Inspect the container to find its network
docker inspect nginx-proxy-manager --format '{{json .NetworkSettings.Networks}}' | jq

# Inspect the specific network (e.g., torrents_proxy-manager)
docker network inspect torrents_proxy-manager 
# Note the "Subnet" (e.g., **********/16) and the "Id" (first 12 chars used for bridge name)
# The bridge interface name will be "br-<network_id_prefix>" (e.g., br-fc93a4406e3f)
ip a # Confirm the bridge interface name
```

### 5. Configure Nginx Host iptables (Docker <-> ZeroTier)

Allow traffic to flow between the Docker bridge network and the ZeroTier interface. Replace `br-fc93a4406e3f` and `**********/16` with your actual values found above. Replace `ztmjfcpubr` with your ZeroTier interface name on the Nginx host.

```bash
# Variables
DOCKER_BRIDGE="br-fc93a4406e3f" # Example bridge interface
DOCKER_SUBNET="**********/16" # Example Docker subnet
ZEROTIER_IF="ztmjfcpubr"     # Example ZeroTier interface

# Allow forwarding between Docker bridge and ZeroTier interface
sudo iptables -A FORWARD -i $DOCKER_BRIDGE -o $ZEROTIER_IF -j ACCEPT
sudo iptables -A FORWARD -i $ZEROTIER_IF -o $DOCKER_BRIDGE -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT # Allow return traffic

# Masquerade traffic from Docker subnet going to ZeroTier network
# This allows containers to initiate connections to ZeroTier IPs
sudo iptables -t nat -A POSTROUTING -s $DOCKER_SUBNET -o $ZEROTIER_IF -j MASQUERADE

# Save rules
sudo netfilter-persistent save
```
*Explanation*:
*   The `FORWARD` rules explicitly permit packets moving between the Docker network interface and the ZeroTier interface.
*   The `MASQUERADE` rule in the `nat` table rewrites the source IP address of packets originating from the Docker containers to be the IP address of the host's ZeroTier interface (`$ZEROTIER_IF`). This is crucial for return traffic to find its way back to the container.

### 6. Configure Nginx Host Firewall (UFW)

Set up restrictive UFW rules on the Nginx host (Pop-OS).

```bash
# Reset UFW to start fresh
sudo ufw --force reset

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Basic Allow Rules (adjust SSH port/IPs if needed)
sudo ufw allow proto tcp from ***********/16 to any port 22 comment 'Allow SSH from Local LAN'
sudo ufw allow proto tcp from ************** to any port 22 comment 'Allow SSH from Specific Static IP' 
# Be cautious allowing SSH from ZeroTier subnet - only if needed
# sudo ufw allow proto tcp from ***********/24 to any port 22 comment 'Allow SSH from ZeroTier'

# Allow Incoming Traffic from ZeroTier to Nginx Proxy Manager (running in Docker)
# Docker maps ports 80, 443, 81 to the host. Traffic arrives on ZT interface.
sudo ufw allow in on ztmjfcpubr proto tcp from ************ to any port 80,443 comment 'Allow HTTP/S from EC2 Bridge via ZeroTier'

# Allow Nginx Admin UI only from Local LAN
sudo ufw allow proto tcp from ***********/16 to any port 81 comment 'Allow NPM Admin UI from Local LAN'

# Allow Docker Network -> Host Services Communication
# Replace **********/16 with your actual Docker subnet
DOCKER_SUBNET="**********/16" 
sudo ufw allow proto tcp from $DOCKER_SUBNET to any port 8055,8096,9696 comment 'Allow Docker NPM to reach backend services'

# Allow established/related traffic on ZeroTier interface (handles return traffic)
sudo ufw route allow in on ztmjfcpubr out on $DOCKER_BRIDGE from any to $DOCKER_SUBNET
sudo ufw route allow in on $DOCKER_BRIDGE out on ztmjfcpubr from $DOCKER_SUBNET to any

# Allow traffic on the Docker bridge interface itself (necessary for iptables rules to work with UFW)
DOCKER_BRIDGE="br-fc93a4406e3f" # Example bridge interface
sudo ufw allow in on $DOCKER_BRIDGE 
sudo ufw allow out on $DOCKER_BRIDGE

# Enable UFW
sudo ufw enable
sudo ufw status verbose
```
*Explanation*:
*   We deny incoming traffic by default.
*   SSH access is restricted.
*   Traffic from the EC2 bridge's ZeroTier IP (`************`) is allowed to the host's ports 80 and 443, which Docker maps to the Nginx container.
*   The Nginx admin UI (port 81) is restricted to the local LAN.
*   The Docker subnet is allowed to reach the specific backend service ports (8055, 8096, 9696) on the host.
*   Allowing traffic `in on $DOCKER_BRIDGE` is crucial because UFW processes rules *before* iptables FORWARD rules in some cases. This ensures UFW doesn't block traffic intended for the Docker network.

### 7. Configure Nginx Proxy Manager Hosts

*   Access the Nginx Proxy Manager Admin UI (http://<local_ip>:81 or http://<zerotier_ip>:81 from allowed networks).
*   For each service you want to expose:
    *   Add a Proxy Host.
    *   Domain Names: Your desired domain/subdomain (e.g., `prowlarr.yourdomain.com`).
    *   Scheme: `http` (Nginx handles HTTPS).
    *   Forward Hostname / IP: `*************` (Your Nginx host's ZeroTier IP).
    *   Forward Port: The port the service runs on (e.g., `9696` for Prowlarr).
    *   Enable `Block Common Exploits`.
    *   Enable `Websockets Support` if needed (e.g., for Jellyfin).
    *   Go to the `SSL` tab.
    *   SSL Certificate: Select "Request a new SSL Certificate" with Let's Encrypt.
    *   Enable `Force SSL` and `HTTP/2 Support`.
    *   Save.

### 8. Configure ZeroTier Flow Rules

Restrict traffic flow at the ZeroTier network level for added security.

```
# Default: Drop non-IP traffic & allow basics
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;
accept ethertype arp;
accept ipprotocol icmp; # Allow ping

# Define IPs
# Note: Using tags is generally more robust than hardcoding IPs if they might change.
# tag bridge id <ec2_node_id> name bridge;
# tag nginx id <popos_node_id> name nginx;

# Allow HTTP/HTTPS from Bridge to Nginx Host
accept
  ipprotocol tcp
  and dport 80,443
# and ipdest *************/32  # Nginx Host ZT IP
# and ipsrc ************/32    # Bridge ZT IP
  and ztdest *************    # Nginx Host ZT IP
  and ztsrc ************     # Bridge ZT IP
# or use tags:
# and tdest name nginx
# and tsrc name bridge
;

# Allow return traffic for HTTP/HTTPS
accept
  ipprotocol tcp
  and sport 80,443
# and ipsrc *************/32   # Nginx Host ZT IP
# and ipdest ************/32   # Bridge ZT IP
  and ztsrc *************    # Nginx Host ZT IP
  and ztdest ************     # Bridge ZT IP
# or use tags:
# and tsrc name nginx
# and tdest name bridge
;

# Allow general traffic between other authorized members (adjust as needed)
# accept; 

# Default drop for all other traffic between members
drop;
```

### 9. Security Hardening & Maintenance

*   **Restrict SSH Access**: Use key-based authentication, disable root login, consider Fail2ban.
*   **Automatic Updates**: Enable unattended upgrades on both servers.
    ```bash
    sudo apt install -y unattended-upgrades apt-listchanges
    sudo dpkg-reconfigure -plow unattended-upgrades
    ```
*   **Monitoring**: Set up basic monitoring (see previous `check-services.sh` example) or use more robust tools.
*   **Regular Reviews**: Periodically check logs (UFW, Nginx, Auth), review firewall rules, and audit ZeroTier members.
*   **Backups**: Regularly back up Nginx Proxy Manager data (`/data` volume) and system configurations.

## Troubleshooting Summary

Common issues and solutions encountered:

1.  **Cannot Ping Between ZeroTier Nodes**:
    *   **Check Authorization**: Ensure both nodes are authorized in ZeroTier Central. (`sudo zerotier-cli listnetworks`).
    *   **Check ZeroTier Flow Rules**: Temporarily set to `accept;` for testing. Ensure ICMP is allowed.
    *   **Check Host Firewalls (UFW/iptables)**: Ensure firewalls on *both* ends allow ICMP and traffic from the specific ZeroTier source IP/subnet. Temporarily disable for testing (`sudo ufw disable` / `sudo iptables -F`). (`sudo ufw status verbose`, `sudo iptables -L -v -n`).
    *   **Check Routes**: Ensure routes exist for the ZeroTier subnet (`ip route`). see: [[Troubleshooting ZeroTier and Docker Networking#Troubleshooting ZeroTier and Docker Networking]] the first section for Zerotier Managed Routes.
    *   **Restart ZeroTier**: (`sudo systemctl restart zerotier-one`).

2.  **Services Inaccessible via Domain (e.g., 504 Gateway Timeout)**:
    *   **Verify EC2 -> Nginx Host Connectivity**: Ping and curl the Nginx host's ZeroTier IP and service ports *from the EC2 bridge*. (`ping *************`, `curl -v *************:9696`). If this fails, revisit step 1.
    *   **Verify Nginx Host -> Service Connectivity**: Curl the service ports using `localhost` *on the Nginx host*. (`curl -v localhost:9696`). If this fails, the service itself or its binding is the issue.
    *   **Check Nginx Proxy Manager Config**: Ensure the Forward Hostname/IP is correct (`*************`), port is correct, scheme is `http`. Check Nginx logs (`docker logs nginx-proxy-manager`).
    *   **Check Docker Networking & Firewall**:
        *   Ensure the Nginx container can reach the host's ZeroTier IP (`*************`).
        *   Verify UFW rules allow the Docker subnet (`**********/16`) to reach the backend service ports on the host.
        *   Verify UFW allows traffic `in on <docker_bridge_interface>`.
        *   Verify iptables `FORWARD` and `nat MASQUERADE` rules are correct for the Docker subnet and ZeroTier interface.
    *   **Check DNS**: Ensure domain points to EC2 public IP and propagation is complete.
    *   **Check EC2 Security Group**: Ensure inbound ports 80/443 are open from `0.0.0.0/0`.

3.  **HTTPS/SSL Issues**:
    *   Ensure Nginx Proxy Manager handles SSL termination. Set scheme to `http` when forwarding to backend services.
    *   Verify the correct SSL certificate is selected and active in Nginx Proxy Manager for the domain.
    *   Check certificate validity and expiration.

See [[Troubleshooting ZeroTier and Docker Networking]] for more detailed steps. 