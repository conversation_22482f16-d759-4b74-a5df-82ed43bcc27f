---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - ZeroTier Troubleshooting
  - Docker Network Diagnostics
tags:
  - networking
  - zerotier
  - docker
  - troubleshooting
  - diagnostics
  - ufw
  - iptables
area: Infrastructure
project: Bridge Network
status: reference
priority: medium
links:
  - "[[Simplified ZeroTier Network Setup]]"
  - "[[Family/Network Architecture Overview]]"
related:
  - "[[EC2 Bridge Server Maintenance]]"
  - "[[ZeroTier Flow Rules Placement]]"
---

# Troubleshooting ZeroTier and Docker Networking

This document outlines common issues and diagnostic steps when setting up a ZeroTier network with Docker containers, specifically for the [[Simplified ZeroTier Network Setup]] architecture.

## Issue 1: Cannot Ping Between ZeroTier Nodes

**Symptoms**: `ping <zerotier_ip>` fails between EC2 bridge and Nginx host.

**Troubleshooting Steps**:

1.  **Check ZeroTier Authorization**:
    *   Command: `sudo zerotier-cli listnetworks` (on both nodes)
    *   Verify: Status is `OK`, correct Network ID, assigned IP addresses are present.
    *   Action: Authorize nodes in ZeroTier Central if needed.

2.  **Check ZeroTier Flow Rules**:
    *   Location: ZeroTier Central -> Network -> Flow Rules.
    *   Verify: Rules allow ICMP (`accept ipprotocol icmp;`).
    *   Action: Temporarily set rules to `accept;` for testing. If ping works, refine rules to allow necessary traffic (ICMP, specific ports/IPs).

3.  **Check Host Firewalls (UFW/iptables)**:
    *   Commands:
        *   `sudo ufw status verbose`
        *   `sudo iptables -L INPUT -v -n --line-numbers`
        *   `sudo iptables -L FORWARD -v -n --line-numbers`
    *   Verify: Firewalls on *both* nodes allow incoming ICMP echo-requests (`proto icmp`) from the source ZeroTier IP/subnet (e.g., `***********/24`).
    *   Action: Add appropriate UFW/iptables rules. Temporarily disable for testing (`sudo ufw disable` or `sudo iptables -P INPUT ACCEPT && sudo iptables -P FORWARD ACCEPT`). Remember to re-enable/restore rules.

4.  **Check Routes**:
    *   Command: `ip route` (on both nodes)
    *   Verify: A route exists for the ZeroTier subnet (e.g., `***********/24 dev ztmjfcpubr`).
    * Managed Route setup was originally:
|   |   |   |   |
|---|---|---|---|
|************/32|via|*************||
|**********/16|->|(LAN)|
|--------------------|


    *   Action: Check ZeroTier network configuration in ZeroTier Central (Managed Routes). Restart ZeroTier service.

5.  **Check ZeroTier Service Status & Peers**:
    *   Commands:
        *   `sudo systemctl status zerotier-one`
        *   `sudo zerotier-cli info` (Should show `ONLINE`)
        *   `sudo zerotier-cli peers` (Check if the other node is listed as a `LEAF`)
    *   Action: Restart ZeroTier service (`sudo systemctl restart zerotier-one`). Check logs (`sudo journalctl -u zerotier-one`).

## Issue 2: Services Inaccessible via Domain (e.g., 504 Gateway Timeout)

**Symptoms**: Accessing `https://yourdomain.com` results in a timeout or error, but direct access to services via IP might work.

**Troubleshooting Steps (Follow in Order)**:

1.  **Verify EC2 -> Nginx Host Connectivity**: *Rule out ZeroTier issues.*
    *   Commands (on EC2 bridge):
        *   `ping *************`
        *   `curl -v http://*************:80` (Should get Nginx default page or redirect)
        *   `curl -v http://*************:9696` (Should get response from Prowlarr)
    *   If Fails: Go back to **Issue 1** troubleshooting.

2.  **Verify Nginx Host -> Backend Service Connectivity**: *Rule out service issues.*
    *   Commands (on Nginx host):
        *   `curl -v localhost:9696`
        *   `curl -v localhost:8055`
        *   `curl -v localhost:8096`
    *   Verify: Services respond correctly (e.g., 200 OK, 302 Found, *not* Connection Refused).
    *   Action: Check if the service is running (`docker ps`, `systemctl status <service>`). Check service logs. Ensure service is binding to `0.0.0.0` or `localhost`, not just a specific IP.

3.  **Check Nginx Proxy Manager Configuration**: *Rule out proxy errors.*
    *   Verify (in NPM UI):
        *   Domain name is correct.
        *   Scheme is `http`.
        *   Forward Hostname/IP is the Nginx host's **ZeroTier IP** (`*************`).
        *   Forward Port matches the backend service port.
        *   SSL is enabled, correct certificate selected, `Force SSL` is on.
    *   Check Logs: `docker logs nginx-proxy-manager` for specific errors related to the domain or backend connection.

4.  **Check Docker Networking & Firewall (Nginx Host)**: *Rule out container connectivity issues.*
    *   Verify Container -> Host ZT IP Access:
        *   `docker exec nginx-proxy-manager curl -v *************:9696` (Should succeed).
    *   Verify Firewall Rules:
        *   UFW allows Docker subnet (`**********/16`) to destination service ports (`sudo ufw status verbose`).
        *   UFW allows traffic `in on <docker_bridge>` (e.g., `br-fc93a4406e3f`).
        *   iptables `FORWARD` rules exist between Docker bridge and ZeroTier interface (`sudo iptables -L FORWARD -v -n`).
        *   iptables `nat POSTROUTING` `MASQUERADE` rule exists for Docker subnet -> ZeroTier interface (`sudo iptables -t nat -L POSTROUTING -v -n`).
    *   Action: Add/correct UFW and iptables rules as per [[Simplified ZeroTier Network Setup#Configure Nginx Host Firewall (UFW)]] and [[Simplified ZeroTier Network Setup#Configure Nginx Host iptables (Docker <-> ZeroTier)]].

5.  **Check EC2 Bridge Forwarding & Firewall**: *Rule out bridge errors.*
    *   Verify iptables Rules:
        *   `nat PREROUTING` DNAT rules exist for ports 80/443 to `*************` (`sudo iptables -t nat -L PREROUTING -v -n`).
        *   `filter FORWARD` rules allow traffic from public interface to ZeroTier interface for destination `*************` ports 80/443 (`sudo iptables -L FORWARD -v -n`).
        *   `nat POSTROUTING` `MASQUERADE` rule exists for traffic going out the ZeroTier interface.
    *   Verify AWS Security Group: Inbound rules allow TCP 80 and 443 from `0.0.0.0/0`.

6.  **Check DNS**: *Rule out domain resolution issues.*
    *   Command: `dig yourdomain.com +short` (Should return EC2 Public IP).
    *   Verify: DNS A record is correct. Allow time for propagation.

## Issue 3: HTTPS/SSL Errors

**Symptoms**: Browser shows certificate warnings or connection errors.

**Troubleshooting Steps**:

1.  **Verify Certificate Application**: In Nginx Proxy Manager, ensure the correct Let's Encrypt certificate is selected for the Proxy Host and `Force SSL` is enabled.
2.  **Check Certificate Validity**: In NPM, view the certificate details. Check expiration date. Renew if necessary.
3.  **Ensure Backend Uses HTTP**: Verify the Proxy Host forwards using `http` scheme, as NPM handles the SSL termination.
4.  **Clear Browser Cache**: Sometimes browsers cache old SSL states.

## Useful Diagnostic Commands

*   **Check Listening Ports**: `sudo ss -tulpn`
*   **Trace Network Path**: `mtr <destination_ip_or_domain>`
*   **Capture Packets**: `sudo tcpdump -i <interface> -n <filter_expression>` (e.g., `sudo tcpdump -i ztmjfcpubr -n host ************`) 