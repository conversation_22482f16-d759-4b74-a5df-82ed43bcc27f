---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: index
aliases: [Areas]
tags: [para/areas, index]
---

# Areas of Responsibility

> Areas represent ongoing responsibilities with no end date that require regular attention.

## Administration Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(tags, "admin") OR contains(tags, "administration")
SORT file.name ASC
```

## Software Development Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(tags, "software-dev") OR contains(tags, "programming")
SORT file.name ASC
```

## Personal Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(tags, "personal")
SORT file.name ASC
```

## Professional Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(tags, "professional") OR contains(tags, "work")
SORT file.name ASC
```

## All Areas
```dataview
TABLE
  area as "Area",
  status as "Status"
FROM "2-Areas"
SORT file.name ASC
```

## Related
- [[1-Projects]]
- [[3-Resources]]
- [[4-Archive]]
- [[Home]]