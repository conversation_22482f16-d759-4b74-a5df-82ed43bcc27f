---
creation_date: <% tp.date.now("YYYY-<PERSON>M-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: index
aliases: [Tasks, Todo]
tags: [tasks, index]
---

# Tasks

> A central place to view and manage all tasks across the vault.

## Active Tasks

```tasks
not done
sort by due
group by path
```

## Due Today

```tasks
not done
due today
```

## Overdue Tasks

```tasks
not done
due before today
```

## No Due Date

```tasks
not done
no due date
```

## Software Development Tasks

```tasks
not done
path includes 1-Projects
tag includes #software-dev OR #programming
```

## Administration Tasks

```tasks
not done
path includes 1-Projects OR 2-Areas
tag includes #admin OR #administration
```

## Recently Completed Tasks

```tasks
done
sort by done reverse
limit 10
```

## Related
- [[Home]]
- [[1-Projects]]
- [[2-Areas]]
