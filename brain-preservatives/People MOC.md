---
creation_date: <% tp.date.now("YYYY-<PERSON>M-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: index
aliases: [People, Contacts]
tags: [people, index]
---

# People Map of Content

> A central place to view and navigate contacts and people.

## Recent Interactions
```dataview
TABLE 
  last_contact as "Last Contact",
  role as "Role",
  organization as "Organization"
FROM #person
SORT last_contact DESC
LIMIT 10
```

## By Organization
```dataview
TABLE 
  role as "Role",
  last_contact as "Last Contact"
FROM #person
WHERE organization
GROUP BY organization
SORT organization ASC
```

## By Role
```dataview
TABLE 
  organization as "Organization",
  last_contact as "Last Contact"
FROM #person
WHERE role
GROUP BY role
SORT role ASC
```

## All People
```dataview
TABLE 
  organization as "Organization",
  role as "Role",
  last_contact as "Last Contact"
FROM #person
SORT file.name ASC
```

## Related
- [[Home]]
- [[1-Projects]]
- [[2-Areas]]
