we need to make a visually appealing and streamlined gallery focused website for a maine coon cat breeder. frontend utilising plain javascript for DOM manipulation. css heavy frontend to make this image heavy gallery site more apealing and resource efficient. Backend is using kestrel server with .NET 8. Ensure good security practises are enforced. Use verbose comments to help inexperienced developers, adding rich documentation. Deployment is hosted on aws ec2, with aws ECR for docker images and aws RDS for the Postgre SQL server. i've had explicit instruction not to add prices for cats. This website should just serve as a gallery for the cats, with a system to easily add new photos and updates on the cats themselves. the available cats section should list all the cats and have a section about them