---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: index
aliases: [Projects]
tags: [para/projects, index]
---

# Projects

> Projects are series of tasks linked to a goal, with a deadline.

## Active Projects
```dataview
LIST
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT file.name ASC
```

## On Hold Projects
```dataview
LIST
FROM "1-Projects"
WHERE status = "on-hold"
SORT file.name ASC
```

## Software Development Projects
```dataview
TABLE
  status as "Status",
  priority as "Priority",
  deadline as "Deadline"
FROM "1-Projects"
WHERE contains(tags, "software-dev") OR contains(tags, "programming")
SORT status ASC, priority ASC
```

## Administration Projects
```dataview
TABLE
  status as "Status",
  priority as "Priority",
  deadline as "Deadline"
FROM "1-Projects"
WHERE contains(tags, "admin") OR contains(tags, "administration")
SORT status ASC, priority ASC
```

## All Projects
```dataview
TABLE
  status as "Status",
  priority as "Priority",
  deadline as "Deadline"
FROM "1-Projects"
SORT status ASC, priority ASC
```

## Related
- [[2-Areas]]
- [[3-Resources]]
- [[4-Archive]]
- [[Home]]