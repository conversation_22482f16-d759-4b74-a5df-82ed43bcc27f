<%*
// Improved Template Selector Script
// Streamlined for faster note creation with fewer prompts

// Define template options with default subfolders
const templates = [
  { name: "Project", path: "Templates/Enhanced Project.md", folder: "1-Projects", subfolder_options: ["Software", "Personal", "Church", "University"] },
  { name: "Area", path: "Templates/Enhanced Area.md", folder: "2-Areas", subfolder_options: ["Software-Development", "Administration", "Personal", "Church"] },
  { name: "Resource", path: "Templates/Enhanced Resource.md", folder: "3-Resources", subfolder_options: ["Guides", "References", "Tools", "Learning"] },
  { name: "Meeting", path: "Templates/Meeting.md", folder: "", subfolder_options: [] },
  { name: "Person", path: "Templates/Person.md", folder: "People", subfolder_options: [] }
];

// Prompt user to select a template
const templateChoice = await tp.system.suggester(
  templates.map(t => t.name),
  templates,
  false,
  "Select a template"
);

if (!templateChoice) return; // User cancelled

// Prompt for filename
const filename = await tp.system.prompt("Enter a name for your note", "");
if (!filename) return; // User cancelled

// Determine the folder path
let folder = templateChoice.folder;

// Only ask for subfolder if we have options and it's a PARA template
if (folder && templateChoice.subfolder_options.length > 0) {
  // Add "None" option to the subfolder list
  const options = ["None"].concat(templateChoice.subfolder_options);

  // Prompt for subfolder using suggester instead of free text
  const subfolder = await tp.system.suggester(
    options,
    options,
    false,
    "Select a subfolder"
  );

  // Only modify folder path if user didn't select "None"
  if (subfolder && subfolder !== "None") {
    folder = `${folder}/${subfolder}`;
  }
}

// Create the full path
const fullPath = folder ? `${folder}/${filename}` : filename;

// Create the note with the selected template
await tp.file.create_new(tp.file.find_tfile(templateChoice.path), filename, false, fullPath);
%>
