---
creation_date: <% tp.file.creation_date() %>
modification_date: <% tp.file.last_modified_date() %>
type: resource
source: 
tags: [para/resources, yendorcats, software-dev]
area: Software-Development
difficulty: 
url: 
---

# <% tp.file.title %>

## Overview


## Key Points
- 
- 
- 

## Details


## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[<% tp.file.title %>]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "yendorcats") OR contains(tags, "software-dev")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[3-Resources|All Resources]]
