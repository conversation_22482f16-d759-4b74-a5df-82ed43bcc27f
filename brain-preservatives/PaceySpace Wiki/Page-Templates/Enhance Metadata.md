<%*
// Metadata Enhancer Script
// This script helps you enhance the metadata of the current note

// Get current note information
const currentTitle = tp.file.title;
const currentPath = tp.file.path(true);
const currentFolder = currentPath.substring(0, currentPath.lastIndexOf("/"));
const currentType = tp.frontmatter.type || "note";

// Define metadata fields based on note type
let metadataFields = [];

if (currentType === "project") {
  metadataFields = [
    { name: "project_owner", prompt: "Project Owner", default: "Jordan" },
    { name: "project_client", prompt: "Project Client", default: "Personal" },
    { name: "completion_percentage", prompt: "Completion Percentage (0-100)", default: "0" },
    { name: "estimated_hours", prompt: "Estimated Hours", default: "20" },
    { name: "area", prompt: "Related Area", default: "Software-Development" },
    { name: "start_date", prompt: "Start Date", default: tp.date.now("YYYY-MM-DD") },
    { name: "stakeholders", prompt: "Stakeholders (comma-separated)", default: "" }
  ];
} else if (currentType === "area") {
  metadataFields = [
    { name: "area_owner", prompt: "Area Owner", default: "Jordan" },
    { name: "responsibility_level", prompt: "Responsibility Level (high, medium, low)", default: "medium" },
    { name: "review_frequency", prompt: "Review Frequency (daily, weekly, monthly, quarterly)", default: "monthly" },
    { name: "organization", prompt: "Organization (if applicable)", default: "" },
    { name: "key_contacts", prompt: "Key Contacts (comma-separated)", default: "" },
    { name: "last_review_date", prompt: "Last Review Date", default: tp.date.now("YYYY-MM-DD") },
    { name: "next_review_date", prompt: "Next Review Date", default: tp.date.now("YYYY-MM-DD", 30) }
  ];
} else if (currentType === "resource") {
  metadataFields = [
    { name: "source", prompt: "Source", default: "Personal research" },
    { name: "area", prompt: "Related Area", default: "Software-Development" },
    { name: "difficulty", prompt: "Difficulty (easy, medium, hard)", default: "medium" },
    { name: "keywords", prompt: "Keywords (comma-separated)", default: "guide, reference" },
    { name: "last_used", prompt: "Last Used Date", default: tp.date.now("YYYY-MM-DD") },
    { name: "url", prompt: "URL (if applicable)", default: "" },
    { name: "author", prompt: "Author (if applicable)", default: "" }
  ];
} else if (currentType === "person") {
  metadataFields = [
    { name: "first_name", prompt: "First Name", default: "" },
    { name: "last_name", prompt: "Last Name", default: "" },
    { name: "organization", prompt: "Organization", default: "" },
    { name: "role", prompt: "Role", default: "" },
    { name: "email", prompt: "Email", default: "" },
    { name: "phone", prompt: "Phone", default: "" },
    { name: "relationship", prompt: "Relationship (colleague, client, friend, family, service provider)", default: "" }
  ];
} else if (currentType === "meeting") {
  metadataFields = [
    { name: "time", prompt: "Meeting Time", default: tp.date.now("HH:mm") },
    { name: "end_time", prompt: "End Time", default: "" },
    { name: "location", prompt: "Location", default: "Online" },
    { name: "participants", prompt: "Participants (comma-separated)", default: "" },
    { name: "project", prompt: "Related Project (if applicable)", default: "" },
    { name: "area", prompt: "Related Area (if applicable)", default: "" },
    { name: "status", prompt: "Status (scheduled, completed, cancelled)", default: "scheduled" }
  ];
} else {
  metadataFields = [
    { name: "type", prompt: "Note Type (project, area, resource, person, meeting, daily)", default: "note" },
    { name: "tags", prompt: "Tags (comma-separated)", default: "" },
    { name: "related", prompt: "Related Notes (comma-separated)", default: "" },
    { name: "area", prompt: "Related Area", default: "" },
    { name: "project", prompt: "Related Project", default: "" }
  ];
}

// Add common fields
metadataFields.push({ name: "tags", prompt: "Additional Tags (comma-separated)", default: "" });
metadataFields.push({ name: "related", prompt: "Related Notes (comma-separated)", default: "" });

// Get the current file content
const currentFile = tp.file.find_tfile(currentPath);
const currentContent = await app.vault.read(currentFile);

// Parse the frontmatter
const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
const match = currentContent.match(frontmatterRegex);

if (match) {
  let frontmatter = match[1];
  
  // Process each metadata field
  for (const field of metadataFields) {
    // Check if the field already exists
    const fieldRegex = new RegExp(`${field.name}:\\s*(.*)`);
    const fieldMatch = frontmatter.match(fieldRegex);
    
    let currentValue = "";
    if (fieldMatch) {
      currentValue = fieldMatch[1].trim();
      // Remove quotes if present
      if (currentValue.startsWith('"') && currentValue.endsWith('"')) {
        currentValue = currentValue.substring(1, currentValue.length - 1);
      }
      // Handle arrays
      if (currentValue.startsWith('[') && currentValue.endsWith(']')) {
        currentValue = currentValue.substring(1, currentValue.length - 1);
      }
    }
    
    // Prompt for the new value
    const newValue = await tp.system.prompt(
      `${field.prompt} (current: ${currentValue || "none"})`,
      currentValue || field.default
    );
    
    if (newValue !== null) {  // Only update if the user didn't cancel
      // Format the value based on the field type
      let formattedValue = newValue;
      
      // Handle arrays
      if (field.name === "tags" || field.name === "related" || field.name === "stakeholders" || 
          field.name === "key_contacts" || field.name === "keywords" || field.name === "participants") {
        // Split by commas, trim each value, and wrap in quotes if needed
        const values = newValue.split(",").map(v => {
          v = v.trim();
          return v.includes(" ") && !v.startsWith('"') ? `"${v}"` : v;
        });
        formattedValue = `[${values.join(", ")}]`;
      } 
      // Handle strings that might need quotes
      else if (newValue.includes(" ") && !newValue.startsWith('"')) {
        formattedValue = `"${newValue}"`;
      }
      
      // Update or add the field
      if (fieldMatch) {
        frontmatter = frontmatter.replace(fieldRegex, `${field.name}: ${formattedValue}`);
      } else {
        frontmatter += `\n${field.name}: ${formattedValue}`;
      }
    }
  }
  
  // Update the file content
  const updatedContent = currentContent.replace(frontmatterRegex, `---\n${frontmatter}\n---`);
  await app.vault.modify(currentFile, updatedContent);
  
  new Notice("Metadata enhanced successfully!");
} else {
  new Notice("No frontmatter found in this note!");
}
%>
