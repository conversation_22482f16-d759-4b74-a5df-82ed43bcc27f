---
creation_date: 2025-01-27
modification_date: 2025-01-27
type: project
status: active
priority: high
deadline: 2025-06-30
project_owner: Jordan
project_client: YendorCats
completion_percentage: 0
estimated_hours: 280
actual_hours: 0
tags: [para/projects, finance, web-development, yendorcats, client-project]
related: [YendorCats Service Agreement, YendorCats Project Quote, YendorCats SLA]
area: Finance
start_date: 2025-01-27
stakeholders: [YendorCats, PaceySpace Digital]
---

# YendorCats Project Financial Tracking

## Project Overview

**Client:** YendorCats - Maine Coon Breeder
**Project Type:** Simple Cat Gallery Website
**Contract Value:** $3,300 AUD (including GST)
**Project Duration:** 4-6 weeks
**Start Date:** 2025-01-27
**Expected Completion:** 2025-03-15

## Financial Summary

### Revenue Breakdown
| Component | Amount (AUD) | Status |
|-----------|--------------|--------|
| Development Services | $3,000.00 | Contracted |
| GST (10%) | $300.00 | Contracted |
| **Total Project Value** | **$3,300.00** | **Contracted** |

### Payment Schedule
| Payment | Amount (AUD) | Due Date | Status | Date Received |
|---------|--------------|----------|--------|---------------|
| Deposit (50%) | $1,650.00 | Contract Signing | Pending | - |
| Final Payment (50%) | $1,650.00 | Project Completion | Pending | - |

### Ongoing Revenue (Post-Launch)
| Service | Monthly Rate (AUD) | Annual Value (AUD) | Status |
|---------|-------------------|-------------------|--------|
| Basic Hosting & Maintenance | $50.00 | $600.00 | Proposed |
| Additional Photo Management | $25.00 | $300.00 | Optional |
| Enhanced Support | $30.00 | $360.00 | Optional |

## Cost Analysis

### Direct Project Costs
| Category | Estimated Hours | Rate (AUD/hr) | Total Cost (AUD) |
|----------|----------------|---------------|------------------|
| Simple Website Development | 25 | $50 | $1,250 |
| Photo Upload System | 15 | $50 | $750 |
| Basic Hosting Setup | 10 | $50 | $500 |
| Testing & Launch | 10 | $50 | $500 |
| **Total** | **60** | **$50** | **$3,000** |

### Infrastructure Costs (Monthly)
| Service | Provider | Monthly Cost (AUD) | Annual Cost (AUD) |
|---------|----------|-------------------|-------------------|
| Shared Hosting | Various | $15.00 | $180.00 |
| Domain Registration | Various | $2.00 | $24.00 |
| SSL Certificate | Let's Encrypt | $0.00 | $0.00 |
| **Total Infrastructure** | | **$17.00** | **$204.00** |

### Tools & Software Costs
| Tool | Purpose | Monthly Cost (AUD) | Annual Cost (AUD) |
|------|---------|-------------------|-------------------|
| Visual Studio | Development | $0.00 | $0.00 |
| Docker | Containerization | $0.00 | $0.00 |
| Git/GitHub | Version Control | $0.00 | $0.00 |
| Postman | API Testing | $0.00 | $0.00 |
| **Total Tools** | | **$0.00** | **$0.00** |

## Profitability Analysis

### Project Profitability
| Metric | Amount (AUD) | Percentage |
|--------|--------------|------------|
| Gross Revenue | $3,300.00 | 100% |
| GST (to be remitted) | $300.00 | 9.1% |
| Net Revenue | $3,000.00 | 90.9% |
| Direct Labor Costs | $3,000.00 | 90.9% |
| Infrastructure Costs (2 months) | $34.00 | 1.0% |
| **Net Profit (Project)** | **-$34.00** | **-1.0%** |

### Annual Profitability (Including Hosting)
| Metric | Amount (AUD) | Notes |
|--------|--------------|-------|
| Project Revenue | $3,000.00 | One-time |
| Annual Hosting Revenue | $600.00 | Recurring |
| Total Annual Revenue | $3,600.00 | |
| Annual Infrastructure | $204.00 | Recurring |
| **Annual Net Profit** | **$3,396.00** | **94.3% margin** |

### Learning Investment Analysis
This project represents a learning investment as a new developer:
- **Experience Value:** Invaluable first professional project
- **Portfolio Value:** Real client work for future proposals
- **Skill Development:** Practical application of web development skills
- **Client Relationship:** Potential for referrals and future work
- **Market Entry:** Establishing presence in local market

## Time Tracking

### Estimated vs Actual Hours
| Phase | Estimated Hours | Actual Hours | Variance | Status |
|-------|----------------|--------------|----------|--------|
| Planning & Design | 20 | 0 | 0 | Not Started |
| Backend Development | 50 | 0 | 0 | Not Started |
| Frontend Development | 40 | 0 | 0 | Not Started |
| Testing & QA | 20 | 0 | 0 | Not Started |
| Deployment | 25 | 0 | 0 | Not Started |
| Documentation | 15 | 0 | 0 | Not Started |
| Project Management | 20 | 0 | 0 | Not Started |
| **Total** | **280** | **0** | **0** | **0% Complete** |

### Hourly Rate Analysis
| Metric | Value |
|--------|-------|
| Average Hourly Rate | $86.52 AUD |
| Highest Rate (DevOps) | $95.00 AUD |
| Lowest Rate (PM) | $70.00 AUD |
| Target Utilization | 85% |

## Cash Flow Projection

### Monthly Cash Flow (Development Phase)
| Month | Income (AUD) | Expenses (AUD) | Net Cash Flow (AUD) | Cumulative (AUD) |
|-------|--------------|----------------|-------------------|-------------------|
| Month 1 | $7,994.25 | $32.00 | $7,962.25 | $7,962.25 |
| Month 2 | $10,659.00 | $32.00 | $10,627.00 | $18,589.25 |
| Month 3 | $7,994.25 | $32.00 | $7,962.25 | $26,551.50 |

### Ongoing Cash Flow (Post-Launch)
| Month | Maintenance Income | Infrastructure Costs | Net Monthly | Annual Total |
|-------|-------------------|---------------------|-------------|--------------|
| Monthly | $350.00 | $32.00 | $318.00 | $3,816.00 |

## Key Performance Indicators (KPIs)

### Financial KPIs
```dataview
TABLE WITHOUT ID
  "Metric" as Metric,
  "Value" as Value,
  "Target" as Target,
  "Status" as Status
FROM ""
WHERE file = this.file
LIMIT 0
```

| Metric | Current Value | Target | Status |
|--------|---------------|--------|--------|
| Project Margin | -0.4% | 20% | ⚠️ Below Target |
| Annual Margin | 98.6% | 80% | ✅ Above Target |
| Payment Collection | 0% | 100% | 🔄 In Progress |
| Time Utilization | 0% | 85% | 🔄 Not Started |

### Project Milestones
- [ ] Contract Signed - Target: 2025-01-30
- [ ] Deposit Received - Target: 2025-02-15
- [ ] 25% Complete - Target: 2025-03-15
- [ ] 50% Complete (Progress Payment) - Target: 2025-04-15
- [ ] 75% Complete - Target: 2025-05-15
- [ ] Project Complete (Final Payment) - Target: 2025-06-30

## Risk Analysis

### Financial Risks
| Risk | Probability | Impact | Mitigation Strategy |
|------|-------------|--------|-------------------|
| Scope Creep | Medium | High | Clear change management process |
| Payment Delays | Low | Medium | Clear payment terms and follow-up |
| Technical Complexity | Medium | Medium | Buffer time in estimates |
| Third-party Service Issues | Low | Low | Multiple provider options |

### Opportunities
| Opportunity | Potential Value | Probability | Action Required |
|-------------|----------------|-------------|-----------------|
| Additional Features | $2,000-5,000 | Medium | Present options during development |
| Referrals | $20,000+ | High | Deliver exceptional service |
| Ongoing Maintenance | $4,200/year | High | Demonstrate value post-launch |
| E-commerce Integration | $3,000-5,000 | Medium | Discuss future expansion |

## Expense Tracking

### Development Expenses
```dataview
TABLE
  date as "Date",
  description as "Description",
  amount as "Amount (AUD)",
  category as "Category"
FROM "Expenses"
WHERE project = "YendorCats"
SORT date DESC
```

### Monthly Recurring Expenses
- [ ] Hosting: $25.00 AUD
- [ ] Domain: $2.00 AUD
- [ ] Storage: $5.00 AUD
- [ ] Total: $32.00 AUD

## Invoice Tracking

### Invoices Issued
| Invoice # | Date Issued | Amount (AUD) | Due Date | Status | Date Paid |
|-----------|-------------|--------------|----------|--------|-----------|
| YC-001 | TBD | $7,994.25 | TBD | Draft | - |
| YC-002 | TBD | $10,659.00 | TBD | Pending | - |
| YC-003 | TBD | $7,994.25 | TBD | Pending | - |

### Payment Follow-up
```dataview
TASK
FROM "Invoices"
WHERE project = "YendorCats" AND status != "Paid"
```

## Tax Considerations

### GST Obligations
- **GST Collected:** $2,422.50 AUD
- **GST on Expenses:** $0.00 AUD (not GST registered)
- **Net GST Payable:** $2,422.50 AUD

### Income Tax
- **Taxable Income:** $24,225.00 AUD
- **Estimated Tax (30%):** $7,267.50 AUD
- **Net After Tax:** $16,957.50 AUD

## Related Documents
- [[YendorCats Service Agreement]]
- [[YendorCats Project Quote]]
- [[YendorCats Service Level Agreement]]
- [[2-Areas/Finances/Finances]]

## Quick Actions
- [ ] Send contract to client
- [ ] Set up project tracking system
- [ ] Create invoice templates
- [ ] Schedule milestone reviews
- [ ] Set up expense tracking

## Notes
- Client specializes in Maine Coon cats
- High-quality photography will be important
- Consider upselling e-commerce features
- Potential for ongoing relationship and referrals

---

*This financial tracking document will be updated throughout the project lifecycle to maintain accurate records for accounting and business analysis.*
