---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: index
aliases:
  - Resources
tags:
  - para/resources
  - index
links:
  - "[[Resources TOC]]"
---
<<[[Home]]||<[[Resources TOC]]|■■■■■■■■■■■■■■■■|[[#Resources]]>||[[#All Resources]]>>
--- 
# Resources

> Resources are topics or themes of ongoing interest that may be referenced across projects and areas.

## Software Development Resources
```dataview
TABLE
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "software-dev") OR contains(tags, "programming")
SORT file.name ASC
```

## Administration Resources
```dataview
TABLE
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "admin") OR contains(tags, "administration")
SORT file.name ASC
```

## Guides & Cheatsheets
```dataview
TABLE
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "guide") OR contains(tags, "cheatsheet")
SORT file.name ASC
```

## Templates
```dataview
LIST
FROM "Templates"
SORT file.name ASC
```

## All Resources
```dataview
TABLE
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
SORT file.name ASC
```

## Related
- [[1-Projects]]
- [[2-Areas]]
- [[4-Archive]]
- [[Home]]