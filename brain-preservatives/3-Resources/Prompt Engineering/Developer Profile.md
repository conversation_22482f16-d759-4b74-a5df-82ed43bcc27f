---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Personal Information
tags: [para/resources, personal, development, environment, profile, context]
related: ["Tech Stack Reference", "Preferred Coding Styles", "MCP Setup Guide"]
area: Software-Development
keywords: [profile, personal, development, environment, context]
last_used: 2025-04-16
---

# Developer Profile

## Overview
Personal development environment and background information for use in LLM prompts and agent configurations.

## Development Environment

### Operating System
- **Primary OS**: Arch Linux
- **Desktop Environment**: GNOME
- **Display Manager**: GDM
- **Architecture**: 64-bit

### Hardware
- **Device Type**: Desktop
- **Processor**: [Your processor details]
- **RAM**: [Your RAM details]
- **Storage**: [Your storage details]

### Terminal Setup
- **Shell**: zsh (oh my zsh)
- **Terminal Emulator**: zellij and kitty[Your terminal emulator]
- **Package Manager**: <PERSON>man (with optional AUR helpers)

### Editor/IDE
- **Primary Code Editor**: [Your preferred editor/IDE]
- **Theme**: [Your preferred theme]
- **Extensions**: [Key extensions you use]

## Personal Background

### Location and Culture
- **Country**: Australia
- **Timezone**: [Your timezone, e.g., AEST/AEDT]
- **Language**: English (Australian)

### Developer Experience
- **Level**: New Developer
- **Background**: [Any previous experience or career transition details]
- **Learning Style**: [How you prefer to learn - hands-on, tutorial-based, etc.]

### Current Focus
- Building practical projects to reinforce learning
- Developing cloud-native applications
- Improving JavaScript skills
- Learning more advanced bash scripting

## Current Tech Stack

### Primary Technologies
- **.NET & C#**: Building backend services and applications
- **ASP.NET Core**: Web application development
- **AWS**: Cloud infrastructure and services
- **Git**: Version control

### Web Development
- **HTML/CSS**: Frontend structure and styling
- **JavaScript**: Currently learning, building interactive elements

### DevOps & Infrastructure
- **Bash**: Command-line scripting and automation
- **SSH**: Secure remote access
- **Git**: Version control and collaboration

### Secondary Skills
- **Python**: Scripting and automation
- **Docker**: Containerization (basic understanding)
- **Linux Administration**: System configuration and management

## Learning Priorities

### Short-term Goals
- Strengthen JavaScript fundamentals
- Improve AWS service knowledge
- Enhance bash scripting capabilities
- Build more complex ASP.NET Core applications

### Medium-term Goals
- Learn a JavaScript framework (React or Vue)
- Implement CI/CD pipelines
- Develop serverless applications on AWS
- Improve database design and optimization

### Long-term Goals
- Full-stack proficiency
- Cloud architecture expertise
- DevOps integration
- Contribution to open-source projects

## Preferred Learning Resources

### Documentation
- Microsoft Learn
- AWS Documentation
- MDN Web Docs

### Courses
- [Your preferred learning platforms]
- [Specific courses you've found helpful]

### Communities
- Stack Overflow
- Reddit programming communities
- GitHub discussions

## Notes for LLM Assistants

When providing assistance, please consider:

1. **Code Examples**: Prefer C# and ASP.NET Core examples when applicable
2. **Explanations**: Include explanations with code to aid learning
3. **AWS Context**: Frame cloud solutions in AWS terminology and services
4. **Skill Level**: Provide beginner to intermediate level guidance
5. **Terminal Commands**: Include bash commands for Linux (Arch specifically)
6. **Australian Context**: Use Australian English spelling and conventions when relevant

## Related
- [[Prompt Engineering TOC]]
- [[Tech Stack Reference]]
- [[Preferred Coding Styles]]
- [[MCP Setup Guide]]
