---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Personal Research
tags: [para/resources, prompt-engineering, ai, llm, code-generation, programming]
related: ["Prompt Engineering Fundamentals", "System Prompt Templates", "Tech Stack Reference"]
area: Software-Development
difficulty: medium
keywords: [code-generation, prompts, programming, c#, javascript, python, bash]
last_used: 2025-04-16
---

# Code Generation Prompts

## Overview
Specialized prompts for effective code generation across different programming languages and use cases, optimized for clarity, correctness, and best practices.

## General Code Generation Principles

### 1. Specify Context and Requirements
- **Language and Version**: Explicitly state the programming language and version
- **Framework/Library**: Specify any frameworks or libraries to use
- **Purpose**: Clearly describe what the code should accomplish
- **Constraints**: Mention any limitations or requirements (performance, compatibility, etc.)

### 2. Request Explanations
- Ask for comments within the code
- Request explanations of key concepts or complex logic
- Ask for reasoning behind design decisions

### 3. Specify Code Style
- Mention coding conventions to follow
- Request specific error handling approaches
- Specify logging or documentation requirements

### 4. Request Tests
- Ask for unit tests or test cases
- Specify testing framework if applicable
- Request examples of how to use the code

## Language-Specific Templates

### C# Code Generation

#### Basic C# Function
```
Write a C# function that [SPECIFIC TASK].

Requirements:
- Target .NET 8
- Include XML documentation comments
- Follow Microsoft's C# coding conventions
- Include proper error handling
- Make the function async if appropriate

Please explain any non-obvious parts of the implementation and provide an example of how to call this function.
```

#### ASP.NET Core API Endpoint
```
Create an ASP.NET Core API controller endpoint that [SPECIFIC TASK].

Requirements:
- Target ASP.NET Core 8
- Use the minimal API approach (or controller-based if preferred)
- Include proper model validation
- Implement appropriate error handling
- Return correct status codes
- Follow RESTful principles

Please explain the key components and provide an example of how to test this endpoint using curl or Postman.
```

#### Entity Framework Core Query
```
Write an Entity Framework Core query that [SPECIFIC TASK].

Requirements:
- Target EF Core 8
- Optimize for performance
- Use LINQ syntax (or query syntax if preferred)
- Include proper null checking
- Implement pagination if returning multiple items

Please explain how the query works and any performance considerations.
```

### JavaScript Code Generation

#### Basic JavaScript Function
```
Write a JavaScript function that [SPECIFIC TASK].

Requirements:
- Use modern JavaScript (ES6+) features
- Include JSDoc comments
- Handle edge cases and errors
- Make the function async if appropriate

Please explain any non-obvious parts and provide an example of how to use this function.
```

#### DOM Manipulation
```
Write JavaScript code that [SPECIFIC DOM MANIPULATION TASK].

Requirements:
- Use vanilla JavaScript (no jQuery or other libraries)
- Work across modern browsers
- Follow best practices for performance
- Include error handling

Please explain how the code works and any browser compatibility considerations.
```

#### Fetch API Request
```
Write JavaScript code using the Fetch API to [SPECIFIC API INTERACTION].

Requirements:
- Handle success and error responses
- Include proper error handling
- Use async/await syntax
- Parse JSON response

Please explain how to handle different response scenarios and provide an example of how to use this code.
```

### Python Code Generation

#### Basic Python Function
```
Write a Python function that [SPECIFIC TASK].

Requirements:
- Target Python 3.9+
- Include docstrings
- Follow PEP 8 style guidelines
- Include proper error handling
- Use type hints

Please explain any non-obvious parts and provide an example of how to use this function.
```

#### Data Processing Script
```
Write a Python script that [SPECIFIC DATA PROCESSING TASK].

Requirements:
- Use pandas for data manipulation
- Optimize for performance with large datasets
- Include error handling and logging
- Follow best practices for memory management

Please explain the key steps in the data processing workflow and any performance considerations.
```

#### AWS Lambda Function
```
Write a Python AWS Lambda function that [SPECIFIC TASK].

Requirements:
- Target Python 3.9+
- Include proper AWS SDK usage
- Handle Lambda context and event objects
- Implement error handling and logging
- Follow AWS Lambda best practices

Please explain how the function works and how to configure it in AWS.
```

### Bash Script Generation

#### Basic Bash Script
```
Write a bash script that [SPECIFIC TASK].

Requirements:
- Include proper shebang line
- Add helpful comments
- Implement error handling
- Follow shell scripting best practices
- Make it compatible with bash 4.0+

Please explain any non-obvious parts and provide examples of how to use this script.
```

#### System Administration Script
```
Write a bash script for [SPECIFIC SYSTEM ADMINISTRATION TASK] on Arch Linux.

Requirements:
- Include proper error handling and logging
- Check for required permissions
- Validate inputs
- Provide usage information
- Make it safe to run multiple times

Please explain how the script works and any system-specific considerations.
```

## Use Case Specific Templates

### Authentication Implementation

#### JWT Authentication in ASP.NET Core
```
Create the necessary code for implementing JWT authentication in an ASP.NET Core 8 application.

Include:
1. Configuration in Program.cs
2. JWT service implementation
3. Authentication middleware setup
4. Example of a protected endpoint
5. How to generate and validate tokens

Please explain the key components and security considerations.
```

#### User Registration and Login in C#
```
Create the necessary code for user registration and login functionality in an ASP.NET Core 8 application.

Include:
1. User model with proper password handling
2. Registration endpoint with validation
3. Login endpoint with authentication
4. Password hashing implementation
5. Example of how to use Identity (or custom solution)

Please explain security best practices and potential vulnerabilities to avoid.
```

### Database Operations

#### CRUD Operations with Entity Framework Core
```
Create a complete set of CRUD operations for a [SPECIFIC ENTITY] using Entity Framework Core 8.

Include:
1. Entity class with proper annotations
2. DbContext configuration
3. Create operation with validation
4. Read operation with filtering and pagination
5. Update operation with concurrency handling
6. Delete operation with proper checks

Please explain any performance considerations and best practices.
```

#### Database Migration Script
```
Create a database migration script for [SPECIFIC SCHEMA CHANGE] using Entity Framework Core 8.

Include:
1. Migration class
2. Up and Down methods
3. Any necessary data seeding
4. How to apply the migration

Please explain any potential issues during migration and how to handle them.
```

### API Integration

#### AWS S3 Integration in C#
```
Create code for integrating with AWS S3 in a C# application to [SPECIFIC S3 OPERATION].

Include:
1. AWS SDK setup and configuration
2. S3 client initialization
3. Implementation of the specific operation
4. Error handling and retry logic
5. Example of how to use the code

Please explain best practices for AWS credential management and performance optimization.
```

#### External API Client in C#
```
Create a C# client for interacting with [SPECIFIC API].

Include:
1. HttpClient setup with proper configuration
2. Methods for each API endpoint
3. Request/response models
4. Error handling and retry logic
5. Authentication implementation

Please explain how to use the client and any considerations for production use.
```

## Best Practices for Code Generation Prompts

1. **Start Simple, Then Refine**: Begin with a basic prompt and iterate based on the results
2. **Provide Examples**: When possible, include examples of expected input/output
3. **Specify Edge Cases**: Mention specific edge cases you want handled
4. **Request Alternatives**: Ask for multiple approaches to solve the problem
5. **Ask for Trade-offs**: Request explanations of pros and cons for the chosen approach

## Example Workflow

### Initial Prompt
```
Write a C# function that validates an Australian phone number.

Requirements:
- Target .NET 8
- Include XML documentation comments
- Follow Microsoft's C# coding conventions
- Include proper error handling
```

### Refined Prompt
```
Write a C# function that validates an Australian phone number.

Requirements:
- Target .NET 8
- Support both mobile (04xx xxx xxx) and landline formats ((0x) xxxx xxxx)
- Handle international format (+61)
- Remove spaces, dashes, and parentheses before validation
- Return a standardized format if valid, or throw a descriptive exception if invalid
- Include XML documentation comments
- Follow Microsoft's C# coding conventions
- Include unit tests with various test cases

Please explain the regex pattern (if used) and any validation logic.
```

## Related
- [[Prompt Engineering TOC]]
- [[Prompt Engineering Fundamentals]]
- [[3System Prompt Templates]]
- [[Tech Stack Reference]]
