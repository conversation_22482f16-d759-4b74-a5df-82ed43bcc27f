---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Personal Research
tags: [para/resources, prompt-engineering, ai, llm, mcp, server, python, integration, cursor, augment]
related: ["MCP Server Setup and Utilization", "MCP Setup Guide", "MCP Customization", "Developer Profile"]
area: Software-Development
difficulty: medium
keywords: [mcp, model-context-protocol, server, setup, integration, cursor, augment, context-management, python]
last_used: 2025-04-16
---

# Detailed Plan for Setting Up a Personalized MCP Server

## Overview
A comprehensive, step-by-step plan for setting up a personalized Model Context Protocol (MCP) server using Python to achieve unified context history across development environments like Cursor AI and Augment AI.

## Prerequisites

- Python 3.8+ installed on Arch Linux
- pip package manager
- Basic understanding of Python programming
- Access to Cursor AI and/or Augment AI
- Git installed for documentation ingestion

## Phase 1: Environment Setup

### 1. Create Project Directory
```bash
# Create a dedicated project directory
mkdir -p ~/projects/mcp-server
cd ~/projects/mcp-server
```

### 2. Set Up Virtual Environment
```bash
# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate

# Verify Python version
python --version  # Should be 3.8+
```

### 3. Install Required Packages
```bash
# Install core MCP package
pip install mcp

# Install additional dependencies
pip install requests beautifulsoup4 sqlite3 pyyaml
```

### 4. Create Basic Directory Structure
```bash
# Create directory structure
mkdir -p src/tools
mkdir -p data/context
mkdir -p config
mkdir -p logs
```

## Phase 2: Basic MCP Server Implementation

### 1. Create Configuration File
Create `config/config.yaml`:

```yaml
server:
  host: 127.0.0.1
  port: 8000
  debug: true

context:
  storage_type: sqlite
  db_path: data/context/context.db

logging:
  level: INFO
  file: logs/mcp_server.log
```

### 2. Create Context Storage Module
Create `src/context_storage.py`:

```python
import sqlite3
import json
import logging
from typing import Any, Dict, Optional, List

class ContextStorage:
    def __init__(self, db_path: str = "data/context/context.db"):
        """
        Initialize the context storage with a SQLite database.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self.logger = logging.getLogger("mcp.context")
        self._init_db()
    
    def _init_db(self) -> None:
        """
        Initialize the database schema if it doesn't exist.
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS context (
            key TEXT PRIMARY KEY,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        conn.commit()
        conn.close()
        self.logger.info(f"Initialized context database at {self.db_path}")
    
    def store(self, key: str, value: Any) -> None:
        """
        Store a value in the context database.
        
        Args:
            key: The key to store the value under
            value: The value to store (will be JSON serialized)
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        serialized_value = json.dumps(value)
        cursor.execute("""
        INSERT OR REPLACE INTO context (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
        """, (key, serialized_value))
        conn.commit()
        conn.close()
        self.logger.debug(f"Stored context: {key}")
    
    def retrieve(self, key: str) -> Optional[Any]:
        """
        Retrieve a value from the context database.
        
        Args:
            key: The key to retrieve the value for
            
        Returns:
            The deserialized value, or None if the key doesn't exist
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT value FROM context WHERE key = ?", (key,))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            self.logger.debug(f"Retrieved context: {key}")
            return json.loads(result[0])
        
        self.logger.debug(f"Context not found: {key}")
        return None
    
    def delete(self, key: str) -> bool:
        """
        Delete a value from the context database.
        
        Args:
            key: The key to delete
            
        Returns:
            True if the key was deleted, False if it didn't exist
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM context WHERE key = ?", (key,))
        deleted = cursor.rowcount > 0
        conn.commit()
        conn.close()
        
        if deleted:
            self.logger.debug(f"Deleted context: {key}")
        else:
            self.logger.debug(f"Context not found for deletion: {key}")
        
        return deleted
    
    def list_keys(self) -> List[str]:
        """
        List all keys in the context database.
        
        Returns:
            A list of all keys
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT key FROM context")
        keys = [row[0] for row in cursor.fetchall()]
        conn.close()
        return keys
```

### 3. Create Basic MCP Server
Create `src/mcp_server.py`:

```python
from mcp import MCP
import logging
import yaml
import os
from typing import Dict, Any, Optional
from context_storage import ContextStorage

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/mcp_server.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("mcp.server")

# Load configuration
def load_config() -> Dict[str, Any]:
    """Load configuration from YAML file."""
    config_path = os.path.join("config", "config.yaml")
    with open(config_path, "r") as f:
        return yaml.safe_load(f)

config = load_config()
logger.info("Configuration loaded")

# Initialize context storage
context_db = ContextStorage(config["context"]["db_path"])
logger.info("Context storage initialized")

# Initialize MCP server
mcp = MCP()
logger.info("MCP server initialized")

# Define context management tools
@mcp.tool("Store context")
def store_context(key: str, value: Any) -> str:
    """
    Stores context information in the database.
    
    Args:
        key: The key to store the context under
        value: The context value to store
        
    Returns:
        Confirmation message
    """
    context_db.store(key, value)
    return f"Context stored under key: {key}"

@mcp.tool("Retrieve context")
def retrieve_context(key: str) -> Any:
    """
    Retrieves context information from the database.
    
    Args:
        key: The key to retrieve context for
        
    Returns:
        The stored context value, or None if not found
    """
    value = context_db.retrieve(key)
    if value is None:
        return "No context found for this key"
    return value

@mcp.tool("List context keys")
def list_context_keys() -> List[str]:
    """
    Lists all keys in the context database.
    
    Returns:
        A list of all context keys
    """
    return context_db.list_keys()

# Define a simple example tool
@mcp.tool("Echo message")
def echo_message(message: str) -> str:
    """
    Echoes back the provided message.
    
    Args:
        message: The message to echo
        
    Returns:
        The same message
    """
    return f"Echo: {message}"

# Run the server
if __name__ == "__main__":
    logger.info("Starting MCP server...")
    host = config["server"]["host"]
    port = config["server"]["port"]
    debug = config["server"]["debug"]
    
    logger.info(f"Server running at {host}:{port} (debug: {debug})")
    mcp.run(host=host, port=port, debug=debug)
```

## Phase 3: Implement Advanced Tools

### 1. Create Documentation Tools
Create `src/tools/documentation_tools.py`:

```python
from mcp import MCP
import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger("mcp.tools.documentation")

def register_documentation_tools(mcp: MCP) -> None:
    """Register documentation tools with the MCP server."""
    
    @mcp.tool("Ingest GitHub repository")
    def ingest_repo(repo_url: str, branch: str = "main", path: str = "") -> str:
        """
        Converts a GitHub repository into LLM-readable data using Git-Ingest.
        
        Args:
            repo_url: The URL of the GitHub repository
            branch: The branch to ingest (default: main)
            path: Optional specific path within the repository
            
        Returns:
            The processed repository data in a format suitable for LLMs
        """
        # Validate the URL format
        if not repo_url.startswith("https://github.com/"):
            raise ValueError("URL must be a valid GitHub repository URL")
        
        # Replace github.com with gitingest.com
        ingest_url = repo_url.replace("github.com", "gitingest.com")
        
        # Add branch and path parameters if provided
        if branch != "main":
            ingest_url += f"/tree/{branch}"
        
        if path:
            # Ensure path doesn't start with a slash
            path = path.lstrip("/")
            if branch == "main":
                ingest_url += f"/tree/main/{quote(path)}"
            else:
                ingest_url += f"/{quote(path)}"
        
        logger.info(f"Ingesting repository: {repo_url} (via {ingest_url})")
        
        # Fetch the processed data
        headers = {
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        }
        
        try:
            response = requests.get(ingest_url, headers=headers, timeout=60)
            response.raise_for_status()
            logger.info(f"Successfully ingested repository: {repo_url}")
            return response.text
        except requests.RequestException as e:
            logger.error(f"Error ingesting repository: {str(e)}")
            return f"Error fetching repository data: {str(e)}"
    
    @mcp.tool("Convert webpage to markdown")
    def convert_webpage_to_markdown(url: str) -> str:
        """
        Converts a webpage to markdown format.
        
        Args:
            url: The URL of the webpage to convert
            
        Returns:
            The webpage content in markdown format
        """
        logger.info(f"Converting webpage to markdown: {url}")
        
        try:
            # Fetch the webpage
            headers = {
                "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
            }
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.extract()
            
            # Extract the title
            title = soup.title.text if soup.title else "No title"
            markdown = f"# {title}\n\n"
            
            # Find the main content
            main_content = soup.find("main") or soup.find("article") or soup.find("div", class_=["content", "main", "article"])
            
            if not main_content:
                main_content = soup.body
            
            # Process headings
            for level in range(1, 7):
                for heading in main_content.find_all(f"h{level}"):
                    text = heading.get_text().strip()
                    markdown += f"{'#' * (level + 1)} {text}\n\n"
            
            # Process paragraphs
            for p in main_content.find_all("p"):
                text = p.get_text().strip()
                if text:
                    markdown += f"{text}\n\n"
            
            # Process lists
            for ul in main_content.find_all("ul"):
                for li in ul.find_all("li"):
                    text = li.get_text().strip()
                    markdown += f"- {text}\n"
                markdown += "\n"
            
            for ol in main_content.find_all("ol"):
                for i, li in enumerate(ol.find_all("li"), 1):
                    text = li.get_text().strip()
                    markdown += f"{i}. {text}\n"
                markdown += "\n"
            
            # Process code blocks
            for pre in main_content.find_all("pre"):
                code = pre.get_text()
                markdown += f"```\n{code}\n```\n\n"
            
            logger.info(f"Successfully converted webpage to markdown: {url}")
            return markdown
            
        except Exception as e:
            logger.error(f"Error converting webpage to markdown: {str(e)}")
            return f"Error converting webpage: {str(e)}"
```

### 2. Create API Integration Tools
Create `src/tools/api_tools.py`:

```python
from mcp import MCP
import requests
import os
import re
import json
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger("mcp.tools.api")

def register_api_tools(mcp: MCP) -> None:
    """Register API integration tools with the MCP server."""
    
    @mcp.tool("Get GitHub repository info")
    def get_github_repo_info(repo_url: str) -> Dict[str, Any]:
        """
        Gets information about a GitHub repository.
        
        Args:
            repo_url: The URL of the GitHub repository
            
        Returns:
            Repository information
        """
        # Extract owner and repo from URL
        match = re.search(r'github\.com/([^/]+)/([^/]+)', repo_url)
        if not match:
            raise ValueError("Invalid GitHub repository URL")
            
        owner, repo = match.groups()
        repo = repo.split('/')[0]  # Remove any trailing path
        
        logger.info(f"Fetching GitHub repository info: {owner}/{repo}")
        
        # Make API request
        url = f"https://api.github.com/repos/{owner}/{repo}"
        headers = {"Accept": "application/vnd.github.v3+json"}
        
        # Add token if available
        github_token = os.environ.get("GITHUB_TOKEN")
        if github_token:
            headers["Authorization"] = f"token {github_token}"
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            # Extract relevant information
            result = {
                "name": data["name"],
                "full_name": data["full_name"],
                "description": data["description"],
                "stars": data["stargazers_count"],
                "forks": data["forks_count"],
                "open_issues": data["open_issues_count"],
                "language": data["language"],
                "created_at": data["created_at"],
                "updated_at": data["updated_at"],
                "license": data["license"]["name"] if data["license"] else None,
                "url": data["html_url"]
            }
            
            logger.info(f"Successfully fetched GitHub repository info: {owner}/{repo}")
            return result
            
        except Exception as e:
            logger.error(f"Error fetching GitHub repository info: {str(e)}")
            return {"error": str(e)}
    
    @mcp.tool("Search documentation")
    def search_documentation(query: str, source: str = "python") -> List[Dict[str, Any]]:
        """
        Searches documentation for a given query.
        
        Args:
            query: The search query
            source: The documentation source (python, javascript, etc.)
            
        Returns:
            List of search results
        """
        logger.info(f"Searching {source} documentation for: {query}")
        
        # Define search endpoints for different sources
        endpoints = {
            "python": "https://docs.python.org/3/search.html",
            "javascript": "https://developer.mozilla.org/api/v1/search",
            "aws": "https://docs.aws.amazon.com/search/doc-search.html"
        }
        
        if source not in endpoints:
            return [{"error": f"Unsupported documentation source: {source}"}]
        
        try:
            if source == "python":
                # Python documentation search
                params = {"q": query, "check_keywords": "yes", "area": "default"}
                response = requests.get(endpoints[source], params=params)
                
                # This is a simplified example - actual implementation would parse the HTML response
                return [{"title": f"Python search results for '{query}'", "url": response.url}]
                
            elif source == "javascript":
                # MDN documentation search
                params = {"q": query, "locale": "en-US"}
                response = requests.get(endpoints[source], params=params)
                data = response.json()
                
                results = []
                for doc in data.get("documents", [])[:5]:
                    results.append({
                        "title": doc.get("title", ""),
                        "summary": doc.get("summary", ""),
                        "url": f"https://developer.mozilla.org{doc.get('mdn_url', '')}"
                    })
                
                return results
                
            elif source == "aws":
                # AWS documentation search
                params = {"searchPath": "documentation-guide", "searchQuery": query}
                response = requests.get(endpoints[source], params=params)
                
                # This is a simplified example - actual implementation would parse the HTML response
                return [{"title": f"AWS search results for '{query}'", "url": response.url}]
            
        except Exception as e:
            logger.error(f"Error searching documentation: {str(e)}")
            return [{"error": str(e)}]
```

### 3. Update Main Server File
Update `src/mcp_server.py` to include the new tools:

```python
# Add these imports
from tools.documentation_tools import register_documentation_tools
from tools.api_tools import register_api_tools

# After initializing the MCP server
register_documentation_tools(mcp)
register_api_tools(mcp)
logger.info("Advanced tools registered")
```

## Phase 4: Deployment and Integration

### 1. Create Systemd Service
Create `mcp-server.service`:

```ini
[Unit]
Description=MCP Server
After=network.target

[Service]
Type=simple
User=jordan
WorkingDirectory=/home/<USER>/projects/mcp-server
ExecStart=/home/<USER>/projects/mcp-server/venv/bin/python src/mcp_server.py
Restart=on-failure
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
```

### 2. Install and Start the Service
```bash
# Copy the service file
sudo cp mcp-server.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Enable and start the service
sudo systemctl enable mcp-server
sudo systemctl start mcp-server

# Check status
sudo systemctl status mcp-server
```

### 3. Configure Cursor AI Integration
1. Open Cursor AI settings
2. Navigate to the MCP section
3. Add a new MCP server:
   - Name: Personal MCP Server
   - Type: command
   - Command: `curl -X POST http://localhost:8000/mcp -H "Content-Type: application/json" -d '{"tool": "{tool}", "params": {params}}'`
4. Click "Add"

### 4. Configure Augment AI Integration
1. Open Augment AI settings
2. Navigate to the Integrations section
3. Add a new MCP integration:
   - Name: Personal MCP Server
   - URL: http://localhost:8000/mcp
   - Authentication: None (or add if configured)
4. Click "Save"

## Phase 5: Testing and Validation

### 1. Test Basic Functionality
```bash
# Activate the virtual environment
cd ~/projects/mcp-server
source venv/bin/activate

# Run a simple test
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"tool": "Echo message", "params": {"message": "Hello, MCP!"}}'
```

### 2. Test Context Storage
```bash
# Store context
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"tool": "Store context", "params": {"key": "test", "value": "This is a test value"}}'

# Retrieve context
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"tool": "Retrieve context", "params": {"key": "test"}}'
```

### 3. Test Documentation Tools
```bash
# Test GitHub repository ingestion
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"tool": "Ingest GitHub repository", "params": {"repo_url": "https://github.com/python/cpython"}}'
```

### 4. Test in Cursor AI
1. Open Cursor AI
2. Ask it to use your MCP tool:
   - "Use the Echo message tool to say 'Hello from Cursor AI'"
   - "Store the current project details in the context storage"
   - "Retrieve the project details from context storage"

### 5. Test in Augment AI
1. Open Augment AI
2. Ask it to use your MCP tool:
   - "Use the Echo message tool to say 'Hello from Augment AI'"
   - "Store my current file structure in the context storage"
   - "Retrieve the file structure from context storage"

## Phase 6: Monitoring and Maintenance

### 1. Set Up Logging
```bash
# View logs
tail -f ~/projects/mcp-server/logs/mcp_server.log

# Set up log rotation
sudo nano /etc/logrotate.d/mcp-server
```

Add the following content:
```
/home/<USER>/projects/mcp-server/logs/mcp_server.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0640 jordan jordan
}
```

### 2. Create Update Script
Create `update.sh`:

```bash
#!/bin/bash
# Script to update the MCP server

# Change to the project directory
cd ~/projects/mcp-server

# Activate the virtual environment
source venv/bin/activate

# Pull the latest changes if using Git
# git pull

# Update dependencies
pip install --upgrade mcp requests beautifulsoup4 pyyaml

# Restart the service
sudo systemctl restart mcp-server

# Check the status
sudo systemctl status mcp-server
```

Make it executable:
```bash
chmod +x update.sh
```

## Related
- [[MCP Server Setup and Utilization]]
- [[Python MCP Server Project]]
- [[Prompt Engineering TOC]]
- [[Developer Profile]]
