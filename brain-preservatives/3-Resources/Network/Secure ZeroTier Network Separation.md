---
creation_date: 2025-04-06
modification_date: 2025-04-21
type: resource
source: Personal research
tags: [para/resources, networking, zerotier, bridge, security, network-architecture]
area: Network-Administration
difficulty: hard
url:
---

# Secure ZeroTier Network Separation

This document explains how to configure a highly secure setup where the bridge server and local services are on completely separate ZeroTier networks with no direct membership overlap.

## Architecture Overview

In this security-focused architecture:

- **Network A (Bridge Network)**: Contains only the EC2 bridge server and no local services
- **Network B (Services Network)**: Contains Nginx proxy and all local services
- The bridge server is explicitly **NOT** a member of Network B
- The local services are explicitly **NOT** members of Network A

This creates an "air gap" between the bridge server and your local network, providing maximum security if the bridge server is ever compromised.

## How to Connect Networks

Since the bridge server isn't directly on the same ZeroTier network as your services, you need a secure tunnel between networks. There are three main approaches:

### Option 1: Intermediate Bridge Host (Most Secure)

In this approach, you introduce a third server that joins both ZeroTier networks and acts as the true "bridge" between them.

1. **Setup**:
   - EC2 Bridge Server: On Network A only
   - Intermediate Bridge: On both Network A and Network B
   - Local Services: On Network B only

2. **Traffic Flow**:
   - Internet → EC2 Bridge (Network A) → Intermediate Bridge → Local Services (Network B)

3. **Security Benefits**:
   - EC2 Bridge has no direct access to your local services
   - Compromise of EC2 Bridge doesn't expose your local network
   - Intermediate Bridge can implement additional security controls

### Option 2: IP Forwarding Through Another Host (Recommended)

A simpler variation where you designate one local machine that's a member of both networks to handle the routing.

1. **Setup**:
   - EC2 Bridge Server: On Network A only
   - Local Gateway Server: On both Network A and Network B
   - Local Services: On Network B only

2. **Configuration on Local Gateway**:
   ```bash
   # Enable IP forwarding
   sudo sysctl -w net.ipv4.ip_forward=1

   # Set up iptables rules to forward traffic
   sudo iptables -t nat -A PREROUTING -i zt_network_a_interface -p tcp --dport 80 -j DNAT --to-destination *************:80
   sudo iptables -t nat -A PREROUTING -i zt_network_a_interface -p tcp --dport 443 -j DNAT --to-destination *************:443

   # Allow forwarding
   sudo iptables -A FORWARD -i zt_network_a_interface -o zt_network_b_interface -p tcp -d ************* --dport 80 -j ACCEPT
   sudo iptables -A FORWARD -i zt_network_a_interface -o zt_network_b_interface -p tcp -d ************* --dport 443 -j ACCEPT

   # Set up return traffic
   sudo iptables -A FORWARD -i zt_network_b_interface -o zt_network_a_interface -m state --state ESTABLISHED,RELATED -j ACCEPT
   sudo iptables -t nat -A POSTROUTING -o zt_network_b_interface -j MASQUERADE
   ```

### Option 3: SSH Tunneling (Simple But Limited)

If you don't want to set up an intermediate bridge, you can use SSH tunneling from the EC2 bridge to a host on the local network.

1. **Setup**:
   - EC2 Bridge Server: On Network A only
   - One local server: On both Network A and Network B
   - Local Services: On Network B only

2. **SSH Tunnel Configuration**:
   ```bash
   # On EC2 Bridge Server
   ssh -N -L 80:*************:80 -L 443:*************:443 user@local_server_ip_on_network_a
   ```

3. **iptables Rules on EC2 Bridge**:
   ```bash
   # Forward incoming traffic to localhost (the SSH tunnel endpoints)
   sudo iptables -t nat -A PREROUTING -i enX0 -p tcp --dport 80 -j DNAT --to-destination 127.0.0.1:80
   sudo iptables -t nat -A PREROUTING -i enX0 -p tcp --dport 443 -j DNAT --to-destination 127.0.0.1:443
   ```

## Flow Rules Configuration

With this separated network architecture, you need flow rules on both networks.

### Network A (Bridge Network) Rules

The bridge network should be configured to:

1. Allow traffic from the internet to the bridge server on ports 80/443
2. Allow traffic from the bridge server to the local gateway on specific ports
3. Block all other traffic

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow all outbound traffic from bridge server
accept
  ipsrc ************/32  # Bridge's IP on Network A
;

# Allow inbound HTTP/HTTPS to bridge server
accept
  ipprotocol tcp
  and ipdest ************/32
  and dport 80
;

accept
  ipprotocol tcp
  and ipdest ************/32
  and dport 443
;

# Allow traffic to the local gateway server (the one on both networks)
accept
  ipprotocol tcp
  and ipdest *************/32  # Local gateway IP on Network A
  and dport 80
;

accept
  ipprotocol tcp
  and ipdest *************/32
  and dport 443
;

# Allow DNS and ICMP
accept
  ipprotocol udp
  and dport 53
;

accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

# Allow ARP
accept
  ethertype arp
;

# Drop everything else
drop;
```

### Network B (Services Network) Rules

The local services network should be configured to:

1. Allow the local gateway to communicate with your Nginx server
2. Allow communications between local services
3. Block access from any unexpected sources

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow HTTP/HTTPS from the local gateway to Nginx
accept
  ipprotocol tcp
  and dport 80
  and ipdest *************/32  # Nginx server
  and ipsrc **************/32  # Local gateway IP on Network B
;

accept
  ipprotocol tcp
  and dport 443
  and ipdest *************/32
  and ipsrc **************/32
;

# Allow return traffic
accept
  ipprotocol tcp
  and sport 80
  and ipsrc *************/32
  and ipdest **************/32
;

accept
  ipprotocol tcp
  and sport 443
  and ipsrc *************/32
  and ipdest **************/32
;

# Allow communications between local services (adjust as needed)
accept
  ipsrc **********/16
  and ipdest **********/16
;

# Allow ICMP within local network
accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

# Allow ARP
accept
  ethertype arp
;

# Drop everything else
drop;
```

## Implementation Steps

### 1. Set Up the Local Gateway Host

1. **Choose a Local Server**:
   - Select a reliable machine on your local network to act as gateway
   - This could be your Nginx server itself or another machine

2. **Join Both ZeroTier Networks**:
   ```bash
   sudo zerotier-cli join <network_a_id>  # Bridge network
   sudo zerotier-cli join <network_b_id>  # Services network
   ```

3. **Enable IP Forwarding**:
   ```bash
   sudo sysctl -w net.ipv4.ip_forward=1
   echo "net.ipv4.ip_forward=1" | sudo tee -a /etc/sysctl.conf
   ```

4. **Set Up Forwarding Rules**:
   ```bash
   # Identify interfaces
   ip a | grep zt

   # Set up iptables rules (replace interface names and IPs)
   sudo iptables -t nat -A PREROUTING -i zt_network_a_interface -p tcp --dport 80 -j DNAT --to-destination *************:80
   sudo iptables -t nat -A PREROUTING -i zt_network_a_interface -p tcp --dport 443 -j DNAT --to-destination *************:443

   # Allow forwarding
   sudo iptables -A FORWARD -i zt_network_a_interface -o zt_network_b_interface -j ACCEPT
   sudo iptables -A FORWARD -i zt_network_b_interface -o zt_network_a_interface -m state --state ESTABLISHED,RELATED -j ACCEPT

   # Set up NAT
   sudo iptables -t nat -A POSTROUTING -o zt_network_b_interface -j MASQUERADE
   ```

5. **Make iptables Rules Persistent**:
   ```bash
   sudo apt-get install iptables-persistent
   sudo netfilter-persistent save
   ```

### 2. Configure Bridge Server

1. **Join Only Network A**:
   ```bash
   sudo zerotier-cli join <network_a_id>
   ```

2. **Configure iptables on Bridge**:
   ```bash
   # Forward incoming internet traffic to local gateway
   sudo iptables -t nat -A PREROUTING -i enX0 -p tcp --dport 80 -j DNAT --to-destination <local_gateway_ip_on_network_a>:80
   sudo iptables -t nat -A PREROUTING -i enX0 -p tcp --dport 443 -j DNAT --to-destination <local_gateway_ip_on_network_a>:443

   # Allow forwarding
   sudo iptables -A FORWARD -i enX0 -o ztmjfcpubr -j ACCEPT
   sudo iptables -A FORWARD -i ztmjfcpubr -o enX0 -m state --state ESTABLISHED,RELATED -j ACCEPT

   # Set up masquerading
   sudo iptables -t nat -A POSTROUTING -o ztmjfcpubr -j MASQUERADE
   ```

3. **Make Rules Persistent**:
   ```bash
   sudo apt-get install iptables-persistent
   sudo netfilter-persistent save
   ```

### 3. Configure Flow Rules

1. **Apply Rules to Network A** (using ZeroTier Central)
2. **Apply Rules to Network B** (using ZeroTier Central)

### 4. Test the Setup

1. **From Bridge Server**:
   ```bash
   # Test connectivity to local gateway
   ping <local_gateway_ip_on_network_a>

   # Test HTTP forwarding
   curl -v http://<local_gateway_ip_on_network_a>
   ```

2. **From Internet**:
   - Try accessing the domain that points to your bridge server
   - Verify the traffic reaches your local services

## Troubleshooting

### Common Issues

1. **Traffic Not Reaching Local Gateway**:
   - Verify flow rules on Network A allow traffic to local gateway
   - Check connectivity between bridge and local gateway
   - Confirm the bridge's iptables rules are forwarding correctly

2. **Traffic Not Forwarded from Gateway to Services**:
   - Verify IP forwarding is enabled on local gateway
   - Check iptables rules on local gateway
   - Ensure flow rules on Network B allow traffic from gateway to services

3. **Return Traffic Not Working**:
   - Check if masquerading (NAT) is set up correctly
   - Verify that iptables FORWARD chain allows return traffic
   - Confirm flow rules allow return traffic

### Debugging Steps

1. **Check ZeroTier Status**:
   ```bash
   sudo zerotier-cli info
   sudo zerotier-cli listnetworks
   ```

2. **Verify IP Forwarding**:
   ```bash
   cat /proc/sys/net/ipv4/ip_forward
   ```

3. **Check iptables Rules**:
   ```bash
   sudo iptables -L -v -n
   sudo iptables -t nat -L -v -n
   ```

4. **Monitor Traffic with tcpdump**:
   ```bash
   # On the bridge server
   sudo tcpdump -i ztmjfcpubr host <local_gateway_ip_on_network_a>

   # On the local gateway
   sudo tcpdump -i <zt_network_a_interface> host <bridge_ip_on_network_a>
   sudo tcpdump -i <zt_network_b_interface> host *************
   ```

## Security Benefits

This architecture provides several significant security benefits:

1. **Network Isolation**: Complete separation between internet-facing systems and internal services
2. **Damage Limitation**: If the bridge server is compromised, attackers can't directly access your local network
3. **Defense in Depth**: Multiple network boundaries must be crossed to reach internal resources
4. **Controlled Access Points**: All traffic must flow through a designated and monitored gateway
5. **Reduced Attack Surface**: Each network component has limited exposure to potential threats

## Enhanced Security Measures

To further improve security:

1. **Local Gateway Hardening**:
   - Limit services running on the gateway to absolute minimum
   - Implement host-based firewall rules
   - Set up intrusion detection/prevention

2. **Application Layer Filtering**:
   - Consider a Web Application Firewall (WAF) at the gateway
   - Implement request filtering and rate limiting

3. **Monitoring and Logging**:
   - Log all traffic crossing between networks
   - Set up alerts for suspicious access patterns
   - Regularly review logs for unauthorized access attempts

## Tasks
- [ ] Design network separation architecture
- [ ] Set up local gateway host
- [ ] Configure bridge server
- [ ] Implement flow rules
- [ ] Test connectivity
- [ ] Document network configuration

## Related
- [[Network Architecture Overview]]
- [[Minimal Gateway For ZeroTier Networks]]
- [[EC2 Bridge Server Maintenance]]
- [[3-Resources]]
- [[Resources TOC]]
