---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: guide
tags: [para/resources, guide, dataview]
---

# Dataview Guide

This guide explains how to use Dataview effectively in our vault.

## Basic Dataview Queries

### LIST Query

The simplest query lists files matching certain criteria:

```dataview
LIST
FROM "1-Projects"
```

This lists all files in the "1-Projects" folder.

### TABLE Query

Table queries display data in columns:

```dataview
TABLE
  status as "Status",
  priority as "Priority",
  deadline as "Deadline"
FROM "1-Projects"
SORT status ASC, priority ASC
```

This creates a table with Status, Priority, and Deadline columns for all projects.

### TASK Query

Task queries list tasks from notes:

```dataview
TASK
FROM "1-Projects"
```

This lists all tasks from project notes.

## Common Filters

### Filter by Folder

```dataview
LIST
FROM "1-Projects"
```

### Filter by Tag

```dataview
LIST
FROM #software-dev
```

### Filter by Multiple Tags

```dataview
LIST
FROM #software-dev AND #priority/high
```

### Filter by Frontmatter Property

```dataview
LIST
FROM "1-Projects"
WHERE status = "active"
```

### Filter by Multiple Conditions

```dataview
LIST
FROM "1-Projects"
WHERE status = "active" AND priority = "high"
```

### Filter by Date

```dataview
LIST
FROM "1-Projects"
WHERE deadline <= date(today) + dur(7 days)
```

This lists projects due within the next 7 days.

## Sorting and Limiting

### Sort Results

```dataview
LIST
FROM "1-Projects"
SORT file.mtime DESC
```

This sorts by last modified date, newest first.

### Limit Results

```dataview
LIST
FROM "1-Projects"
SORT file.mtime DESC
LIMIT 5
```

This shows only the 5 most recently modified projects.

## Grouping

### Group by a Property

```dataview
LIST
FROM "1-Projects"
GROUP BY status
```

This groups projects by their status.

## Useful Examples for Our Vault

### Recent Projects

```dataview
TABLE
  status as "Status",
  file.mtime as "Last Modified"
FROM "1-Projects"
SORT file.mtime DESC
LIMIT 10
```

### Projects by Deadline

```dataview
TABLE
  deadline as "Deadline",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE deadline
SORT deadline ASC
```

### High Priority Tasks

```dataview
TASK
FROM "1-Projects"
WHERE contains(tags, "priority/high")
```

### Notes Linked to a Specific Project

```dataview
LIST
FROM [[Project Name]]
```

### Notes Mentioning a Person

```dataview
LIST
FROM -#person
WHERE contains(file.content, "Person Name")
```

## Advanced Techniques

### Inline Dataview

You can use inline dataview to include dynamic data within text:

```
There are `= length(filter(list FROM "1-Projects", (p) => p.status = "active"))` active projects.
```

### Custom Formatting

```dataview
TABLE
  status as "Status",
  "<span style='color: " + (priority = "high" ? "red" : (priority = "medium" ? "orange" : "green")) + "'>" + priority + "</span>" as "Priority"
FROM "1-Projects"
```

This colors the priority text based on its value.

## Best Practices

1. **Keep Queries Simple** - Start with simple queries and build up complexity
2. **Use Consistent Frontmatter** - Ensure consistent property names across notes
3. **Document Complex Queries** - Add comments to explain complex queries
4. **Test on Small Datasets** - Test complex queries on small datasets first
5. **Use Appropriate Query Type** - Choose LIST, TABLE, or TASK based on your needs
6. **Consider Performance** - Very complex queries on large vaults can be slow

## Related
- [[Vault Organization Guide]]
- [[3-Resources]]
- [[Home]]
