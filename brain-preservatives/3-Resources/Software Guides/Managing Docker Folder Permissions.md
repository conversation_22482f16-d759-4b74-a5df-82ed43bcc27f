---
creation_date: 2025-04-07
modification_date: 2025-04-16
type: resource
source: Personal research
tags: [para/resources, docker, permissions, linux, troubleshooting, mediaserver, software, containers, virtualization, security, file-permissions, uid, gid, chown, chmod]
related: ["Docker Configuration Files", "Media Streaming Server Setup", "Network Architecture Overview"]
area: "Network-Administration"
difficulty: medium
operating_system: "Linux"
key_concepts: ["PUID", "PGID", "file permissions", "group ownership", "setgid bit"]
commands: ["chown", "chmod", "id", "groupadd", "usermod", "find"]
---

# Managing Docker Folder Permissions

## Overview
When using Docker, especially with applications managing files on the host system (like media servers Radarr, Sonarr, Jellyfin, or download clients like qBittorrent), managing file and folder permissions correctly is crucial to avoid access errors.

## Key Concept: PUID and PGID

Many popular Docker images, particularly those from [LinuxServer.io](https://www.linuxserver.io/), utilize environment variables to map the container's primary process user and group to your host system's user and group IDs:

- **`PUID`**: Process User ID. Set this to the User ID (`uid`) of the host user who should own the configuration files and data managed by the container.
- **`PGID`**: Process Group ID. Set this to the Group ID (`gid`) of the host group that should have group ownership/access to the files.

Using `PUID` and `PGID` ensures that files created or modified by the container have the correct ownership on the host filesystem, allowing both the container and your host user to access them without issues.

## Finding Your Host User/Group IDs

To find the `uid` and `gid` for your current user (e.g., `jordan`), run the `id` command in your terminal:

```bash
id $(whoami)
# Or specify the user:
# id jordan
```

The output will look similar to this:

```
uid=1000(jordan) gid=1000(jordan) groups=1000(jordan),4(adm),27(sudo),...
```

In this example, `PUID=1000` and `PGID=1000`.

## Best Practices for Media Server Permissions

For applications managing a shared media library, it's often best to create a dedicated group on the host system to manage access.

### 1. Create a Dedicated Media Group

Choose a unique Group ID (e.g., `2000`) that isn't already in use (`grep ':2000:' /etc/group` to check).
```bash
sudo groupadd -g 2000 media
```

### 2. Add Your User to the Media Group

Add your primary user (`jordan`) and any other relevant users to this new group. You'll need to log out and back in for the group change to take effect fully.
```bash
sudo usermod -aG media jordan
```
Verify membership: `groups jordan` or `id jordan`.

### 3. Set Proper Directory Ownership and Permissions

Ensure your main media folder (e.g., `/mnt/storage/media`) and its subdirectories (`movies`, `tv`, `downloads`, etc.) are owned by your user and the `media` group. Use `775` permissions for directories and `664` for files.
```bash
# Example for a /mnt/storage/media structure
sudo mkdir -p /mnt/storage/media/{movies,tv,downloads}
sudo chown -R jordan:media /mnt/storage/media
sudo chmod -R 775 /mnt/storage/media # Sets drwxrwxr-x
# Optionally set files within to 664 (rw-rw-r--) after creation if needed
# sudo find /mnt/storage/media -type f -exec chmod 664 {} \;
```

### 4. Use Correct PUID/PGID in Docker Compose

Configure your Docker containers to use your user's `PUID` and the *media group's* `PGID`.
```yaml
services:
  radarr:
    image: linuxserver/radarr
    container_name: radarr
    environment:
      - PUID=1000            # Your user ID (e.g., jordan)
      - PGID=2000            # The 'media' group ID
      - TZ=Your/Timezone
    volumes:
      - ./appdata/radarr:/config   # Config owned by PUID=1000
      - /mnt/storage/media:/media  # Media library access via PGID=2000
    # ... other settings
```

### 5. (Optional but Recommended) Set the `setgid` Bit

Applying the `setgid` bit (`g+s`) to the parent media directories ensures that new files and directories created within them automatically inherit the group ownership (`media`) from the parent directory.
```bash
sudo chmod g+s /mnt/storage/media
sudo chmod g+s /mnt/storage/media/movies
sudo chmod g+s /mnt/storage/media/tv
sudo chmod g+s /mnt/storage/media/downloads
# Verify with ls -ld /mnt/storage/media (should show drwxrwsr-x)
```

### 6. (Optional) Use a `.env` File

To avoid repeating IDs and make updates easier, define them in a `.env` file in the same directory as your `docker-compose.yml`:
```dotenv
# .env
PUID=1000
PGID=2000
TZ=Your/Timezone
MEDIA_ROOT=/mnt/storage/media
APPDATA_ROOT=./appdata
```

Then reference these in your `docker-compose.yml`:
```yaml
services:
  radarr:
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${APPDATA_ROOT}/radarr:/config
      - ${MEDIA_ROOT}:/media
# ... etc
```

## Troubleshooting Common Issues

- **"Permission Denied" errors in container logs**: Double-check `PUID`/`PGID` settings and host directory permissions (`ls -la /path/to/media`). Ensure the group ID used (`PGID`) actually has write permissions (`775` or `rwx` for the group).
- **Files created by container not accessible by host user**: Verify the container is using the correct `PUID`/`PGID`. Check the `umask` setting (often configurable via environment variable `UMASK`, default `022` is usually fine, resulting in `664`/`775`).
- **Container cannot access media files**: Ensure volume mounts are correct (`/host/path:/container/path`) and that the user/group defined by `PUID`/`PGID` has read/execute permissions on the host path.
- **Issues after changing PUID/PGID**: You may need to recursively `chown` the existing config/data directories on the host to match the new `PUID`/`PGID` before restarting the container.
  ```bash
  # Example: Fix ownership for Radarr config
  sudo chown -R 1000:2000 ./appdata/radarr
  ```

By consistently applying `PUID`/`PGID` and managing host directory permissions carefully, especially using a shared group, you can create a robust and trouble-free Docker setup for your media server and related applications.

## Related
- [[Brain/3-Resources/Software Guides/Docker Configuration Files]]
- [[Media Streaming Server Setup]]
- [[3-Resources]]
- [[Resources TOC]]
