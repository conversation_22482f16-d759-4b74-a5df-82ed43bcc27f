---
creation_date: 2025-05-06
modification_date: 2025-05-06
type: resource
source: Personal research
tags:
  - para/resources
  - reference
area: Software-Development
difficulty: easy
url: https://unix.stackexchange.com/questions/106561/finding-the-pid-of-the-process-using-a-specific-port
---

# Untitled

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
-


## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->

## Find the application using a specific Port
using SS, the standard way to achievee this on linux
```
sudo ss -lptn 'sport = :80'
```

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[Untitled]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[Untitled]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "reference") AND file.name != "Untitled"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Quick Links
- [[Untitled Project|New Project]]
- [[Untitled Reference|Quick Reference]]
- [[3-Resources|All Resources]]
