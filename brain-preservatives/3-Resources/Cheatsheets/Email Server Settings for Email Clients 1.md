---
creation_date: 2025-04-11
modification_date: 2025-04-11
type: index
aliases:
  - Resources
tags:
  - para/resources
  - index
  - email
  - admin/documentation
  - communication
  - guide
---
# SMTP Settings:
SSL:
server: mail.privateemail.com
port: 465
<EMAIL>
password:
(in bitwarden)

TLS:
server: mail.privateemail.com
port:
587
<EMAIL>
password:
(in bitwarden)

# General Private Email configuration for mail clients and mobile devices
https://www.namecheap.com/support/knowledgebase/article.aspx/1179/2175/general-private-email-configuration-for-mail-clients-and-mobile-devices/

It is possible to use the service just as ordinary email account and configure mail client to retrieve and send messages through Namecheap Private Email server.

Only encrypted connections are supported by our mail servers, so please use the following settings:

Username: your email address
Password: password for this email account
Incoming/outgoing servers name: mail.privateemail.com (please note that it should not be changed)

Incoming server type: IMAP or POP3
Incoming server (IMAP): 993 port for SSL, 143 for TLS/STARTTLS
Incoming server (POP3): 995 port for SSL

Outgoing server (SMTP): 465 port for SSL, 587 for TLS/STARTTLS
Outgoing server authentication should be switched on, SPA (secure password authentication) must be disabled.

More specific instructions can be found here.

Namecheap Private Email (Powered by Open-Xchange) User Guide can be found here.

You may also connect your mailbox from the Windows/Apple/Android device directly. Feel free to check this guide.
Associated articles
**Private Email account setup in Outlook 2016**
https://www.namecheap.com/support/knowledgebase/article.aspx/10031/2175/private-email-account-setup-in-outlook-2016/ 
**Private Email account setup in Thunderbird**
Private Email account setup in Mail on macOS Sierra/Mojave/Catalina/Big Sur (SMTP/IMAP)
##
- [ ] 


###
- 