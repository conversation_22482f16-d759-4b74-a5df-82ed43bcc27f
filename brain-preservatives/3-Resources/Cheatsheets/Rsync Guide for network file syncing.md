---
creation_date: 2025-04-17
modification_date: 2025-04-17
type: resource
source: Personal research
tags:
  - para/resources
  - guide
  - cheetsheet
  - media
  - server
  - commands
  - rsync
  - transferring
  - remote
  - jellyfin
  - pop-os
related: 
area: media, lifestyle
difficulty: easy
keywords:
  - guide
  - reference
  - cheatsheet
  - commands
  - bash
  - cli
  - command line
  - rsync
  - sync
  - sharing
  - file
last_used: 2025-04-17
url: 
author: jordan
---

# RSYNC Quick Start

## Overview
<!-- Brief description of this resource -->


#### Key Points/Snippets
<!-- Main takeaways or important information -->
```
rsync -avvzP -e "ssh -i ~/.ssh/popserver -P 41066" ~/Videos/QT/ jordan@*************:/srv/media/QT
```
###### ARCHIVE MODE: preserve file ownership and permissions: 
You want the -p flag:

    -p, --perms                 preserve permissions
I tend to always use the -a flag, which is an aggregation of -p and several other useful ones:

    -a, --archive               archive mode; equals -rlptgoD (no -H,-A,-X)
Both taken straight from the rsync manpage.

### To use Rsync to completely backup a remote filesystem

```bash
sudo rsync -avzP -e "ssh -i ~/.ssh/cruca-org-ssh" --rsync-path="sudo rsync" --exclude='/proc/*' --exclude='/sys/*' --exclude='/dev/*' --exclude='/mnt/*' --exclude='/media/*' <EMAIL>:/ /run/media/Jordan/c41e0e6e-4981-4a0c-bbd1-3740b0ab749d/crucaorg
```

**EXAMPLE:** To backup the cruca ec2 instance to thee local 1tb hard drive ("export medium"): 
```bash
sudo rsync -avzP -e "ssh -i /home/<USER>/.ssh/cruca-org-ssh" --rsync-path="sudo rsync" --exclude='/proc/*' --exclude='/sys/*' --exclude='/dev/*' --exclude='/mnt/*' --exclude='/media/*' <EMAIL>:/ /run/media/Jordan/c41e0e6e-4981-4a0c-bbd1-3740b0ab749d/crucaorg
```
## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## Use Cases
<!-- When and how to use this resource -->
- 

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(related, "Untitled") OR contains(file.content, "[[Untitled]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category",
  status as "Status"
FROM "2-Areas"
WHERE contains(related, "Untitled") OR contains(file.content, "[[Untitled]]")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "guide") AND file.name != "Untitled"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Create Related Notes
- [[Untitled Project|Create Related Project]]
- [[Untitled Implementation|Create Implementation Guide]]
- [[Untitled Reference|Create Quick Reference]]

## Related
- [[3-Resources]]
- [[Resources TOC]]
- [[Guides TOC]]
