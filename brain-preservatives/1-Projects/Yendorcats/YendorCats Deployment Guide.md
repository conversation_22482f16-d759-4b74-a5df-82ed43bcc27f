---
creation_date: 2025-04-28
modification_date: 2025-04-28
type: resource
source: Internal documentation
tags: [para/resources, yendorcats, software-dev, deployment, enhance-control-panel]
area: Software-Development
difficulty: medium
url: 
---

# YendorCats Deployment Guide for Enhance Control Panel

## Overview
This guide provides comprehensive instructions for deploying the YendorCats application on Enhance Control Panel. It covers setting up the server environment, configuring services, and implementing best practices for security and performance.

## Key Points
- Enhance Control Panel simplifies deployment and management
- Docker containers are used for all services
- Backblaze B2 provides S3-compatible storage for images
- MariaDB is used for the database
- Cloudflare integration provides CDN, WAF, and DNS services

## Prerequisites

### Accounts and Services
- Enhance Control Panel account with a VPS
- Backblaze B2 account for S3-compatible storage
- Domain name with DNS access
- Cloudflare account (optional but recommended)
- Doppler account for secrets management (optional)

### Required Information
- Enhance Control Panel credentials
- Backblaze B2 credentials (access key, secret key)
- Domain name and DNS settings
- SSL certificate (Let's Encrypt can be used)

## Server Setup

### 1. Initial Server Configuration

1. Log in to your Enhance Control Panel
2. Navigate to the "Servers" section
3. Select your VPS or create a new one
4. Ensure the server has at least:
   - 2 CPU cores
   - 4GB RAM
   - 50GB SSD storage
5. Choose Ubuntu 24.04 as the operating system
6. Set up SSH keys for secure access

### 2. DNS Configuration

1. Log in to your domain registrar
2. Create the following DNS records:
   - A record: `yendorcats.yourdomain.com` → Your server IP
   - A record: `api.yendorcats.yourdomain.com` → Your server IP
   - A record: `upload.yendorcats.yourdomain.com` → Your server IP
3. If using Cloudflare:
   - Add your domain to Cloudflare
   - Set the DNS records in Cloudflare
   - Enable proxy for the records (orange cloud)
   - Configure SSL/TLS to "Full" mode

### 3. SSL Certificate Setup

1. In Enhance Control Panel, navigate to "SSL/TLS"
2. Select "Let's Encrypt" for free SSL certificates
3. Add your domains:
   - `yendorcats.yourdomain.com`
   - `api.yendorcats.yourdomain.com`
   - `upload.yendorcats.yourdomain.com`
4. Generate the certificates
5. Verify the certificates are active

## Docker Setup

### 1. Install Docker and Docker Compose

Docker should be pre-installed on your Enhance Control Panel VPS. If not:

```bash
# Update package lists
sudo apt update

# Install Docker
sudo apt install -y docker.io

# Install Docker Compose
sudo apt install -y docker-compose

# Add your user to the docker group
sudo usermod -aG docker $USER

# Apply changes
newgrp docker
```

### 2. Create Docker Network

```bash
# Create a network for YendorCats services
docker network create yendorcats-network
```

## Database Setup

### 1. Deploy MariaDB Container

```bash
# Create a directory for MariaDB data
mkdir -p ~/yendorcats/mariadb-data

# Create a docker-compose.yml file for MariaDB
cat > ~/yendorcats/docker-compose.mariadb.yml << 'EOF'
version: '3'

services:
  mariadb:
    image: mariadb:10.6
    container_name: yendorcats-mariadb
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - ./mariadb-data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - yendorcats-network

networks:
  yendorcats-network:
    external: true
EOF

# Create a .env file for MariaDB
cat > ~/yendorcats/.env.mariadb << 'EOF'
MYSQL_ROOT_PASSWORD=your-secure-root-password
MYSQL_DATABASE=yendorcats
MYSQL_USER=yendorcats
MYSQL_PASSWORD=your-secure-password
EOF

# Start MariaDB container
cd ~/yendorcats
docker-compose -f docker-compose.mariadb.yml --env-file .env.mariadb up -d
```

### 2. Initialize Database Schema

```bash
# Connect to MariaDB container
docker exec -it yendorcats-mariadb bash

# Connect to MySQL
mysql -u root -p

# Enter your root password when prompted

# Create database schema (if not already created)
CREATE DATABASE IF NOT EXISTS yendorcats;
USE yendorcats;

# Create tables (example)
CREATE TABLE cats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  breed VARCHAR(255),
  gender CHAR(1) NOT NULL,
  date_of_birth DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

# Exit MySQL and container
EXIT;
exit
```

## Secrets Management Setup

### Option 1: Using Environment Files

```bash
# Create a directory for secrets
mkdir -p ~/yendorcats/secrets

# Create environment files for each service
cat > ~/yendorcats/secrets/api.env << 'EOF'
DB_CONNECTION_STRING=Server=yendorcats-mariadb;Database=yendorcats;User=yendorcats;Password=your-secure-password;
AWS_S3_ACCESS_KEY=your-backblaze-access-key
AWS_S3_SECRET_KEY=your-backblaze-secret-key
AWS_S3_BUCKET_NAME=your-backblaze-bucket-name
AWS_S3_REGION=us-west-004
AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
JWT_SECRET=your-secure-jwt-secret
JWT_ISSUER=YendorCatsApi
JWT_AUDIENCE=YendorCatsClients
JWT_EXPIRY_MINUTES=60
EOF

cat > ~/yendorcats/secrets/uploader.env << 'EOF'
AWS_S3_ACCESS_KEY=your-backblaze-access-key
AWS_S3_SECRET_KEY=your-backblaze-secret-key
AWS_S3_BUCKET_NAME=your-backblaze-bucket-name
AWS_S3_REGION=us-west-004
AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
API_BASE_URL=https://api.yendorcats.yourdomain.com
PORT=80
EOF
```

### Option 2: Using Doppler (Recommended)

1. Set up Doppler as described in the [Doppler Self-Hosted Installation Guide](link-to-doppler-guide)
2. Create a project for YendorCats
3. Add the necessary secrets for each service
4. Use the Doppler CLI or Docker integration to inject secrets into your containers

## Application Deployment

### 1. Deploy API Service

```bash
# Create a docker-compose.yml file for the API
cat > ~/yendorcats/docker-compose.api.yml << 'EOF'
version: '3'

services:
  api:
    image: yendorcats/api:latest
    container_name: yendorcats-api
    restart: always
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    env_file:
      - ./secrets/api.env
    networks:
      - yendorcats-network
    depends_on:
      - mariadb

networks:
  yendorcats-network:
    external: true
EOF

# Pull or build the API image
docker pull yendorcats/api:latest
# Or build from source
# docker build -t yendorcats/api:latest -f Dockerfile ./backend/YendorCats.API

# Start the API container
cd ~/yendorcats
docker-compose -f docker-compose.api.yml up -d
```

### 2. Deploy File Uploader Service

```bash
# Create a docker-compose.yml file for the File Uploader
cat > ~/yendorcats/docker-compose.uploader.yml << 'EOF'
version: '3'

services:
  uploader:
    image: yendorcats/uploader:latest
    container_name: yendorcats-uploader
    restart: always
    ports:
      - "5002:80"
    env_file:
      - ./secrets/uploader.env
    networks:
      - yendorcats-network

networks:
  yendorcats-network:
    external: true
EOF

# Pull or build the Uploader image
docker pull yendorcats/uploader:latest
# Or build from source
# docker build -t yendorcats/uploader:latest -f Dockerfile ./tools/file-uploader

# Start the Uploader container
cd ~/yendorcats
docker-compose -f docker-compose.uploader.yml up -d
```

### 3. Deploy Frontend

```bash
# Create a docker-compose.yml file for the Frontend
cat > ~/yendorcats/docker-compose.frontend.yml << 'EOF'
version: '3'

services:
  frontend:
    image: nginx:alpine
    container_name: yendorcats-frontend
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - yendorcats-network

networks:
  yendorcats-network:
    external: true
EOF

# Create directories for frontend files and Nginx configuration
mkdir -p ~/yendorcats/frontend
mkdir -p ~/yendorcats/nginx/conf.d
mkdir -p ~/yendorcats/nginx/ssl

# Copy SSL certificates from Enhance Control Panel
cp /etc/letsencrypt/live/yendorcats.yourdomain.com/fullchain.pem ~/yendorcats/nginx/ssl/
cp /etc/letsencrypt/live/yendorcats.yourdomain.com/privkey.pem ~/yendorcats/nginx/ssl/

# Create Nginx configuration
cat > ~/yendorcats/nginx/conf.d/default.conf << 'EOF'
server {
    listen 80;
    server_name yendorcats.yourdomain.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name yendorcats.yourdomain.com;

    ssl_certificate /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://yendorcats-api:80/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /upload/ {
        proxy_pass http://yendorcats-uploader:80/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Copy frontend files
# (Assuming you have the frontend files in a local directory)
# scp -r ./frontend/* user@your-server-ip:~/yendorcats/frontend/

# Start the Frontend container
cd ~/yendorcats
docker-compose -f docker-compose.frontend.yml up -d
```

## Backup Configuration

### 1. Set Up Automated Backups

1. In Enhance Control Panel, navigate to "Backup"
2. Configure backup settings:
   - Schedule: Daily
   - Retention: 7 days
   - Include: Database, Files, Configuration
3. Choose backup destination:
   - Local storage
   - Remote FTP/SFTP
   - Cloud storage (if available)
4. Enable email notifications for backup status

### 2. Manual Backup Script

```bash
# Create a backup script
cat > ~/yendorcats/backup.sh << 'EOF'
#!/bin/bash

# Set variables
BACKUP_DIR="/home/<USER>/backups/yendorcats"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
MYSQL_USER="yendorcats"
MYSQL_PASSWORD="your-secure-password"
MYSQL_DATABASE="yendorcats"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Backup MariaDB database
docker exec yendorcats-mariadb mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE > $BACKUP_DIR/yendorcats_db_$TIMESTAMP.sql

# Backup environment files
tar -czf $BACKUP_DIR/yendorcats_env_$TIMESTAMP.tar.gz ~/yendorcats/secrets

# Backup frontend files
tar -czf $BACKUP_DIR/yendorcats_frontend_$TIMESTAMP.tar.gz ~/yendorcats/frontend

# Backup Nginx configuration
tar -czf $BACKUP_DIR/yendorcats_nginx_$TIMESTAMP.tar.gz ~/yendorcats/nginx

# Keep only the last 7 backups
find $BACKUP_DIR -name "yendorcats_db_*.sql" -type f -mtime +7 -delete
find $BACKUP_DIR -name "yendorcats_env_*.tar.gz" -type f -mtime +7 -delete
find $BACKUP_DIR -name "yendorcats_frontend_*.tar.gz" -type f -mtime +7 -delete
find $BACKUP_DIR -name "yendorcats_nginx_*.tar.gz" -type f -mtime +7 -delete

echo "Backup completed at $(date)"
EOF

# Make the script executable
chmod +x ~/yendorcats/backup.sh

# Add to crontab to run daily
(crontab -l 2>/dev/null; echo "0 2 * * * ~/yendorcats/backup.sh >> ~/yendorcats/backup.log 2>&1") | crontab -
```

## Monitoring and Maintenance

### 1. Set Up Monitoring

1. In Enhance Control Panel, navigate to "Monitoring"
2. Enable monitoring for:
   - CPU usage
   - Memory usage
   - Disk space
   - Network traffic
3. Configure alerts for critical thresholds
4. Set up email notifications for alerts

### 2. Log Management

```bash
# Create a log directory
mkdir -p ~/yendorcats/logs

# Update docker-compose files to mount log volumes
# Example for API:
volumes:
  - ./logs/api:/app/logs

# Install a log viewer (optional)
sudo apt install -y goaccess

# Create a log rotation configuration
cat > /etc/logrotate.d/yendorcats << 'EOF'
/home/<USER>/yendorcats/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0640 root root
}
EOF
```

## Security Considerations

### 1. Firewall Configuration

1. In Enhance Control Panel, navigate to "Firewall"
2. Configure the firewall to allow only necessary ports:
   - 22 (SSH)
   - 80 (HTTP)
   - 443 (HTTPS)
3. Block all other incoming traffic

### 2. Regular Updates

```bash
# Create an update script
cat > ~/yendorcats/update.sh << 'EOF'
#!/bin/bash

# Pull latest images
docker pull yendorcats/api:latest
docker pull yendorcats/uploader:latest
docker pull nginx:alpine
docker pull mariadb:10.6

# Restart containers
cd ~/yendorcats
docker-compose -f docker-compose.api.yml down
docker-compose -f docker-compose.api.yml up -d
docker-compose -f docker-compose.uploader.yml down
docker-compose -f docker-compose.uploader.yml up -d
docker-compose -f docker-compose.frontend.yml down
docker-compose -f docker-compose.frontend.yml up -d

echo "Update completed at $(date)"
EOF

# Make the script executable
chmod +x ~/yendorcats/update.sh

# Add to crontab to run weekly
(crontab -l 2>/dev/null; echo "0 3 * * 0 ~/yendorcats/update.sh >> ~/yendorcats/update.log 2>&1") | crontab -
```

## Troubleshooting

### Common Issues and Solutions

1. **Container fails to start**:
   - Check logs: `docker logs yendorcats-api`
   - Verify environment variables: `docker exec yendorcats-api env`
   - Check for port conflicts: `netstat -tuln`

2. **Database connection issues**:
   - Verify MariaDB is running: `docker ps | grep mariadb`
   - Check connection string: `cat ~/yendorcats/secrets/api.env | grep DB_CONNECTION`
   - Test connection: `docker exec -it yendorcats-mariadb mysql -uyendorcats -p`

3. **SSL certificate issues**:
   - Check certificate expiration: `openssl x509 -in ~/yendorcats/nginx/ssl/fullchain.pem -noout -dates`
   - Renew certificates if needed: `certbot renew`
   - Verify Nginx configuration: `docker exec yendorcats-frontend nginx -t`

4. **S3 storage issues**:
   - Verify credentials: `cat ~/yendorcats/secrets/api.env | grep AWS`
   - Test S3 connection: `aws s3 ls --endpoint-url $AWS_S3_ENDPOINT`
   - Check bucket permissions in Backblaze B2 console

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats Deployment Guide]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats Deployment Guide]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "yendorcats") OR contains(tags, "deployment") OR contains(tags, "enhance-control-panel")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats S3 Metadata Implementation|S3 Metadata Documentation]]
- [[YendorCats File Uploader Service|File Uploader Documentation]]
- [[3-Resources|All Resources]]
