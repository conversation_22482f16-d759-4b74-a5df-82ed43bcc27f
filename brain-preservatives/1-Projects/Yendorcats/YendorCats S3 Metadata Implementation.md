---
creation_date: 2025-04-28
modification_date: 2025-04-28
type: resource
source: Internal documentation
tags: [para/resources, yendorcats, software-dev, s3, metadata]
area: Software-Development
difficulty: medium
url: 
---

# YendorCats S3 Metadata Implementation

## Overview
This document details the implementation of rich metadata for cat gallery images in the YendorCats project. The system uses Backblaze B2 S3-compatible storage with object metadata to store and retrieve detailed information about each cat image.

## Key Points
- S3 object metadata is the primary source of information for cat images
- Filename parsing is used as a fallback for backward compatibility
- Rich metadata enables advanced filtering and search capabilities
- The implementation supports both required and optional metadata fields
- System-generated metadata provides additional context for analytics and security

## Metadata Fields

### Required Fields
| Field | Description | Example |
|-------|-------------|---------|
| name | Name of the cat | "Fluffy" |
| gender | Gender of the cat (M/F) | "F" |
| date_uploaded | Date the image was uploaded (set automatically) | "2025-04-28T14:30:00Z" |
| file_format | File format/extension | "jpg" |
| content_type | MIME type of the image | "image/jpeg" |

### Optional User-Inputted Fields
| Field | Description | Example |
|-------|-------------|---------|
| description | Description of the cat or image | "Fluffy playing with a toy" |
| age | Age of the cat | "2.5" |
| bloodline | Bloodline of the cat | "Champion lineage" |
| breed | Breed of the cat | "Maine Coon" |
| hair_color | Hair/fur color of the cat | "Brown Tabby" |
| date_taken | Date the photo was taken | "2025-03-15T10:00:00Z" |
| personality | Description of the cat's personality | "Playful, Affectionate" |
| category | Category the cat belongs to | "queens" |
| tags | Tags for filtering and searching | "kitten, playful, adoption" |
| mother | Parent/mother of the cat | "Duchess" |
| father | Parent/father of the cat | "King" |

### System-Generated Fields
| Field | Description | Example |
|-------|-------------|---------|
| file_size | Size of the file in bytes | "1048576" |
| width | Width of the image in pixels | "1920" |
| height | Height of the image in pixels | "1080" |
| uploader_ip | IP address of the uploader | "***********" |
| uploader_user_agent | Browser/client information | "Mozilla/5.0..." |
| upload_session_id | Unique identifier for the upload session | "session-1234567890" |

## Implementation Details

### CatImageMetadata Model
The `CatImageMetadata` class is the core model for handling image metadata. It provides:
- Properties for all metadata fields
- Methods to convert between S3 metadata and the model
- Support for additional custom metadata

```csharp
public class CatImageMetadata
{
    // Required fields
    public string Name { get; set; } = string.Empty;
    public string Gender { get; set; } = string.Empty;
    public DateTime DateUploaded { get; set; } = DateTime.UtcNow;
    public string FileFormat { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    
    // Optional fields
    public string? Description { get; set; }
    public string? Age { get; set; }
    public string? Bloodline { get; set; }
    // ... other properties ...
    
    // Conversion methods
    public Dictionary<string, string> ToS3Metadata() { ... }
    public static CatImageMetadata FromS3Metadata(Dictionary<string, string> metadata) { ... }
}
```

### CatGalleryImage Model
The `CatGalleryImage` model represents images in the gallery view and includes:
- Basic image information (ID, URL, etc.)
- Metadata extracted from S3 or filenames
- Methods to create gallery images from S3 metadata

```csharp
public class CatGalleryImage
{
    public string Id { get; set; } = string.Empty;
    public string CatName { get; set; } = string.Empty;
    public float Age { get; set; }
    public DateTime DateTaken { get; set; }
    // ... other properties ...
    
    public static CatGalleryImage FromS3Metadata(
        Dictionary<string, string> metadata,
        string relativePath,
        string category,
        CatGalleryImage? fallbackImage = null) { ... }
}
```

### S3StorageService
The `S3StorageService` handles interactions with the S3-compatible storage:
- Uploading images with metadata
- Retrieving images and their metadata
- Listing images in a directory
- Deleting images

```csharp
public class S3StorageService : IS3StorageService
{
    // ... constructor and fields ...
    
    public async Task<string> UploadFileAsync(
        string key, 
        Stream fileStream, 
        string contentType, 
        Dictionary<string, string> metadata) { ... }
        
    public async Task<Dictionary<string, string>> GetObjectMetadataAsync(string key) { ... }
    
    // ... other methods ...
}
```

### File Uploader Service
The file uploader service is a Node.js application that:
- Provides a web interface for uploading images
- Collects metadata from the user
- Uploads images to S3 with metadata
- Handles file validation and error handling

```javascript
// Extract metadata from the request
const { 
  gender, 
  breed, 
  bloodline, 
  hair_color, 
  personality, 
  traits, 
  date_taken, 
  tags, 
  mother, 
  father 
} = req.body;

// Upload to S3 with metadata
const params = {
  Bucket: process.env.AWS_S3_BUCKET_NAME,
  Key: s3Key,
  Body: fileContent,
  ContentType: req.file.mimetype,
  ACL: 'public-read',
  Metadata: {
    // Required fields
    'name': name,
    'gender': gender || '',
    // ... other metadata fields ...
  }
};
```

### CatGalleryController
The `CatGalleryController` provides API endpoints for:
- Getting images for a specific category
- Getting detailed metadata for a specific image
- Getting available metadata fields for filtering
- Searching images based on metadata criteria

```csharp
[HttpGet("search")]
public ActionResult<IEnumerable<CatGalleryImage>> SearchImages(
    [FromQuery] string? category = null,
    [FromQuery] string? gender = null,
    [FromQuery] string? breed = null,
    // ... other filter parameters ...
) { ... }
```

## Usage Examples

### Uploading an Image with Metadata
1. Navigate to the file uploader interface
2. Select an image file
3. Fill in the metadata fields (name, gender, breed, etc.)
4. Click "Upload"

### Retrieving Images with Metadata
```csharp
// Get all images in the "queens" category
var images = await _catGalleryService.GetCategoryImagesAsync("queens");

// Get detailed metadata for a specific image
var metadata = await _s3StorageService.GetObjectMetadataAsync("resources/queens/fluffy.jpg");
var catMetadata = CatImageMetadata.FromS3Metadata(metadata);

// Search for images with specific criteria
var filteredImages = await _catGalleryService.SearchImagesAsync(
    category: "queens",
    gender: "F",
    breed: "Maine Coon",
    minAge: 2.0,
    maxAge: 5.0
);
```

## Testing

### Unit Tests
Unit tests for the metadata implementation cover:
- Converting between S3 metadata and the model
- Creating gallery images from metadata
- Handling missing or invalid metadata

### Integration Tests
Integration tests verify:
- Uploading images with metadata
- Retrieving images and their metadata
- Searching and filtering based on metadata

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats S3 Metadata Implementation]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats S3 Metadata Implementation]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "yendorcats") OR contains(tags, "s3") OR contains(tags, "metadata")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats File Uploader Service|File Uploader Documentation]]
- [[3-Resources|All Resources]]
