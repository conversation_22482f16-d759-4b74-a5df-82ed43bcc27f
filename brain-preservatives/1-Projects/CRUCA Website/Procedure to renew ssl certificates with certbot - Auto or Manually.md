```bash
### Requesting Let's Encrypt certificate for cruca.org ...
### Requesting Let's Encrypt certificate for admin.cruca.org ...
### Requesting Let's Encrypt certificate for domains: cruca.org admin.cruca.org ...
Saving debug log to /var/log/letsencrypt/letsencrypt.log
Renewing an existing certificate for cruca.org and admin.cruca.org
	
Successfully received certificate.
Certificate is saved at: /etc/letsencrypt/live/cruca.org/fullchain.pem
Key is saved at:         /etc/letsencrypt/live/cruca.org/privkey.pem
This certificate expires on 2025-08-20.
These files will be updated when the certificate renews.

NEXT STEPS:
- The certificate will need to be renewed before it expires. Cert<PERSON> can automatically renew the certificate in the background, but you may need to take steps to enable that functionality. See https://certbot.org/renewal-setup for instructions.

#- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
#If you like <PERSON><PERSON><PERSON>, please consider supporting our work by:
# * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
# * Donating to EFF:                    https://eff.org/donate-le
#- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
#### Reloading Nginx ...
#2025/05/22 15:07:06 [warn] 10#10: "ssl_stapling" ignored, no OCSP responder URL in the certificate "/etc/letsencrypt/live/cruca.org/fullchain.pem"
#nginx: [warn] "ssl_stapling" ignored, no OCSP responder URL in the certificate "/etc/letsencrypt/live/cruca.org/fullchain.pem"
#2025/05/22 15:07:06 [notice] 10#10: signal process started
```

script:
init-letencrypt.sh
