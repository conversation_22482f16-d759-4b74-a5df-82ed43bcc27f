---
creation date: 2025-04-23 03:16
modification date: Wednesday 23rd April 2025 03:16:20
type: daily
date: 2025-04-23
day_of_week: Wednesday
week: 2025-W17
month: 2025-04
tags: [daily, 2025-04]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-04-23 - Wednesday

<< [[2025-04-22]] | [[2025-04-24]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
**yendorcats**
important phone call with instructions needed todo. see [[#Tasks]]

**Linphone -> Groundwire**
- sip details:

| username     | 10207780    |     |     |     |     |
| ------------ | ----------- | --- | --- | --- | --- |
| Display Name | PaceySpace  |     |     |     |     |
|              |             |     |     |     |     |
|              |             |     |     |     |     |

## Tasks
 

**YendorCats Phone Call Correspondence 3:25pm** 
 - [ ] [[#Tasks|see tasks section]]
- [ ]  Wants to release the website as soon as possible in order to provide update information for her new litter. 
	- Wants to specifically provide dates that the litter will be available on the website (~july)
	- People have been calling her alot asking about when the new litter will be available, there needs to be information up for the website link from facebook
- [ ]  Need to point the domain name from cybersolutions to yendorcats as soon as the website is at a MVP (minimum viable product)
- [x]  Need to draft a letter/email for margeret to send to cybersol stating services no longer needsed in order to cancel the subscription and ensure contract is terminated. Ensure to mention this phrase, temrinating contract. Research what needs to be said in case there are legally binding agreement/contracts that must be stopped properly
- [ ]  update/polish the website ready for an MVP 
## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-23)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-23
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-23
due before 2025-04-30
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-23 Meeting|Create New Meeting]]
- [[2025-04-23 Task|Create New Task]]
- [[2025-04-23 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W17|Weekly Overview]]
- [[Tasks]]
- [[Home]]
