/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var c9=Object.create;var x1=Object.defineProperty;var f9=Object.getOwnPropertyDescriptor;var h9=Object.getOwnPropertyNames;var d9=Object.getPrototypeOf,g9=Object.prototype.hasOwnProperty;var we=(s,r)=>()=>(r||s((r={exports:{}}).exports,r),r.exports),p9=(s,r)=>{for(var l in r)x1(s,l,{get:r[l],enumerable:!0})},wl=(s,r,l,c)=>{if(r&&typeof r=="object"||typeof r=="function")for(let d of h9(r))!g9.call(s,d)&&d!==l&&x1(s,d,{get:()=>r[d],enumerable:!(c=f9(r,d))||c.enumerable});return s};var y1=(s,r,l)=>(l=s!=null?c9(d9(s)):{},wl(r||!s||!s.__esModule?x1(l,"default",{value:s,enumerable:!0}):l,s)),m9=s=>wl(x1({},"__esModule",{value:!0}),s);var O1=we(L1=>{"use strict";Object.defineProperty(L1,"__esModule",{value:!0});L1.Point=void 0;var s3=class{constructor(r,l){this.row=r,this.column=l}equals(r){return this.row===r.row&&this.column===r.column}};L1.Point=s3});var P1=we(S1=>{"use strict";Object.defineProperty(S1,"__esModule",{value:!0});S1.Range=void 0;var l3=class{constructor(r,l){this.start=r,this.end=l}};S1.Range=l3});var W1=we(I1=>{"use strict";Object.defineProperty(I1,"__esModule",{value:!0});I1.Focus=void 0;var u3=class s{constructor(r,l,c){this.row=r,this.column=l,this.offset=c}posEquals(r){return this.row===r.row&&this.column===r.column}setRow(r){return new s(r,this.column,this.offset)}setColumn(r){return new s(this.row,r,this.offset)}setOffset(r){return new s(this.row,this.column,r)}};I1.Focus=u3});var vi=we(Dn=>{"use strict";Object.defineProperty(Dn,"__esModule",{value:!0});Dn.HeaderAlignment=Dn.DefaultAlignment=Dn.Alignment=void 0;var _l;(function(s){s.NONE="none",s.LEFT="left",s.RIGHT="right",s.CENTER="center"})(_l||(Dn.Alignment=_l={}));var bl;(function(s){s.LEFT="left",s.RIGHT="right",s.CENTER="center"})(bl||(Dn.DefaultAlignment=bl={}));var El;(function(s){s.FOLLOW="follow",s.LEFT="left",s.RIGHT="right",s.CENTER="center"})(El||(Dn.HeaderAlignment=El={}))});var Pr=we(D1=>{"use strict";Object.defineProperty(D1,"__esModule",{value:!0});D1.TableCell=void 0;var M1=vi(),a3=class{constructor(r){this.rawContent=r,this.content=r.trim(),this.paddingLeft=this.content===""?this.rawContent===""?0:1:this.rawContent.length-this.rawContent.trimLeft().length,this.paddingRight=this.rawContent.length-this.content.length-this.paddingLeft}toText(){return this.rawContent}isDelimiter(){return/^\s*:?-+:?\s*$/.test(this.rawContent)}getAlignment(){if(this.isDelimiter())return this.content[0]===":"?this.content[this.content.length-1]===":"?M1.Alignment.CENTER:M1.Alignment.LEFT:this.content[this.content.length-1]===":"?M1.Alignment.RIGHT:M1.Alignment.NONE}computeContentOffset(r){return this.content===""||r<this.paddingLeft?0:r<this.paddingLeft+this.content.length?r-this.paddingLeft:this.content.length}computeRawOffset(r){return r+this.paddingLeft}};D1.TableCell=a3});var _i=we(F1=>{"use strict";Object.defineProperty(F1,"__esModule",{value:!0});F1.TableRow=void 0;var w9=Pr(),c3=class s{constructor(r,l,c){this._cells=r.slice(),this.marginLeft=l,this.marginRight=c}getWidth(){return this._cells.length}getCells(){return this._cells.slice()}getCellAt(r){return this._cells[r]}setCellAt(r,l){let c=this.getCells();return c[r]=new w9.TableCell(l),new s(c,this.marginLeft,this.marginRight)}toText(){if(this._cells.length===0)return this.marginLeft;let r=this._cells.map(l=>l.toText()).join("|");return`${this.marginLeft}|${r}|${this.marginRight}`}isDelimiter(){return this._cells.every(r=>r.isDelimiter())}};F1.TableRow=c3});var Xt=we(ct=>{"use strict";Object.defineProperty(ct,"__esModule",{value:!0});ct.Err=ct.Ok=ct.err=ct.ok=void 0;var C9=s=>new k1(s);ct.ok=C9;var v9=s=>new U1(s);ct.err=v9;var k1=class{constructor(r){this.value=r,this.match=(l,c)=>l(this.value)}isOk(){return!0}isErr(){return!this.isOk()}map(r){return(0,ct.ok)(r(this.value))}mapErr(r){return(0,ct.ok)(this.value)}andThen(r){return r(this.value)}unwrapOr(r){return this.value}_unsafeUnwrap(){return this.value}_unsafeUnwrapErr(){throw new Error("Called `_unsafeUnwrapErr` on an Ok")}};ct.Ok=k1;var U1=class{constructor(r){this.error=r,this.match=(l,c)=>c(this.error)}isOk(){return!1}isErr(){return!this.isOk()}map(r){return(0,ct.err)(this.error)}mapErr(r){return(0,ct.err)(r(this.error))}andThen(r){return(0,ct.err)(this.error)}unwrapOr(r){return r}_unsafeUnwrap(){throw new Error("Called `_unsafeUnwrap` on an Err")}_unsafeUnwrapErr(){return this.error}};ct.Err=U1});var $t=we(St=>{"use strict";Object.defineProperty(St,"__esModule",{value:!0});St.prettyPrintAST=St.checkChildLength=St.checkType=St.errRelativeReferenceIndex=St.errIndex0=void 0;St.errIndex0=new Error("Index 0 used to create a reference");St.errRelativeReferenceIndex=new Error("Can not use relative reference where absolute reference is required");var _9=(s,...r)=>{if(!(r.indexOf(s.type)>=0))return new Error(`Formula element '${s.text}' is a ${s.type} but expected one of ${r} in this position.`)};St.checkType=_9;var b9=(s,r)=>{if(s.children.length!==r)return new Error(`Formula element '${s.text}' was expected to have ${r} elements, but had ${s.children.length}`)};St.checkChildLength=b9;var E9=(s,r=0)=>{console.log("  ".repeat(r)+`|-${s.type}${s.children.length===0?"="+s.text:""}`),s.children&&s.children.forEach(l=>{(0,St.prettyPrintAST)(l,r+1)})};St.prettyPrintAST=E9});var f3=we((Al,q1)=>{(function(s){"use strict";var r=9e15,l=1e9,c="0123456789abcdef",d="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",C="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",_={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-r,maxE:r,crypto:!1},m,A,P,y,S=!0,V="[DecimalError] ",M=V+"Invalid argument: ",Y=V+"Precision limit exceeded",se=V+"crypto unavailable",ge="[object Decimal]",D=Math.floor,$=Math.pow,U=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,K=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,te=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,J=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Q=1e7,G=7,Ce=9007199254740991,Pe=d.length-1,ot=C.length-1,B={toStringTag:ge};B.absoluteValue=B.abs=function(){var o=new this.constructor(this);return o.s<0&&(o.s=1),j(o)},B.ceil=function(){return j(new this.constructor(this),this.e+1,2)},B.clampedTo=B.clamp=function(o,u){var a,f=this,g=f.constructor;if(o=new g(o),u=new g(u),!o.s||!u.s)return new g(NaN);if(o.gt(u))throw Error(M+u);return a=f.cmp(o),a<0?o:f.cmp(u)>0?u:new g(f)},B.comparedTo=B.cmp=function(o){var u,a,f,g,p=this,E=p.d,T=(o=new p.constructor(o)).d,L=p.s,x=o.s;if(!E||!T)return!L||!x?NaN:L!==x?L:E===T?0:!E^L<0?1:-1;if(!E[0]||!T[0])return E[0]?L:T[0]?-x:0;if(L!==x)return L;if(p.e!==o.e)return p.e>o.e^L<0?1:-1;for(f=E.length,g=T.length,u=0,a=f<g?f:g;u<a;++u)if(E[u]!==T[u])return E[u]>T[u]^L<0?1:-1;return f===g?0:f>g^L<0?1:-1},B.cosine=B.cos=function(){var o,u,a=this,f=a.constructor;return a.d?a.d[0]?(o=f.precision,u=f.rounding,f.precision=o+Math.max(a.e,a.sd())+G,f.rounding=1,a=Lo(f,Fi(f,a)),f.precision=o,f.rounding=u,j(y==2||y==3?a.neg():a,o,u,!0)):new f(1):new f(NaN)},B.cubeRoot=B.cbrt=function(){var o,u,a,f,g,p,E,T,L,x,I=this,F=I.constructor;if(!I.isFinite()||I.isZero())return new F(I);for(S=!1,p=I.s*$(I.s*I,1/3),!p||Math.abs(p)==1/0?(a=Oe(I.d),o=I.e,(p=(o-a.length+1)%3)&&(a+=p==1||p==-2?"0":"00"),p=$(a,1/3),o=D((o+1)/3)-(o%3==(o<0?-1:2)),p==1/0?a="5e"+o:(a=p.toExponential(),a=a.slice(0,a.indexOf("e")+1)+o),f=new F(a),f.s=I.s):f=new F(p.toString()),E=(o=F.precision)+3;;)if(T=f,L=T.times(T).times(T),x=L.plus(I),f=Ae(x.plus(I).times(T),x.plus(L),E+2,1),Oe(T.d).slice(0,E)===(a=Oe(f.d)).slice(0,E))if(a=a.slice(E-3,E+1),a=="9999"||!g&&a=="4999"){if(!g&&(j(T,o+1,0),T.times(T).times(T).eq(I))){f=T;break}E+=4,g=1}else{(!+a||!+a.slice(1)&&a.charAt(0)=="5")&&(j(f,o+1,1),u=!f.times(f).times(f).eq(I));break}return S=!0,j(f,o,F.rounding,u)},B.decimalPlaces=B.dp=function(){var o,u=this.d,a=NaN;if(u){if(o=u.length-1,a=(o-D(this.e/G))*G,o=u[o],o)for(;o%10==0;o/=10)a--;a<0&&(a=0)}return a},B.dividedBy=B.div=function(o){return Ae(this,new this.constructor(o))},B.dividedToIntegerBy=B.divToInt=function(o){var u=this,a=u.constructor;return j(Ae(u,new a(o),0,1,1),a.precision,a.rounding)},B.equals=B.eq=function(o){return this.cmp(o)===0},B.floor=function(){return j(new this.constructor(this),this.e+1,3)},B.greaterThan=B.gt=function(o){return this.cmp(o)>0},B.greaterThanOrEqualTo=B.gte=function(o){var u=this.cmp(o);return u==1||u===0},B.hyperbolicCosine=B.cosh=function(){var o,u,a,f,g,p=this,E=p.constructor,T=new E(1);if(!p.isFinite())return new E(p.s?1/0:NaN);if(p.isZero())return T;a=E.precision,f=E.rounding,E.precision=a+Math.max(p.e,p.sd())+4,E.rounding=1,g=p.d.length,g<32?(o=Math.ceil(g/3),u=(1/Zn(4,o)).toString()):(o=16,u="2.3283064365386962890625e-10"),p=et(E,1,p.times(u),new E(1),!0);for(var L,x=o,I=new E(8);x--;)L=p.times(p),p=T.minus(L.times(I.minus(L.times(I))));return j(p,E.precision=a,E.rounding=f,!0)},B.hyperbolicSine=B.sinh=function(){var o,u,a,f,g=this,p=g.constructor;if(!g.isFinite()||g.isZero())return new p(g);if(u=p.precision,a=p.rounding,p.precision=u+Math.max(g.e,g.sd())+4,p.rounding=1,f=g.d.length,f<3)g=et(p,2,g,g,!0);else{o=1.4*Math.sqrt(f),o=o>16?16:o|0,g=g.times(1/Zn(5,o)),g=et(p,2,g,g,!0);for(var E,T=new p(5),L=new p(16),x=new p(20);o--;)E=g.times(g),g=g.times(T.plus(E.times(L.times(E).plus(x))))}return p.precision=u,p.rounding=a,j(g,u,a,!0)},B.hyperbolicTangent=B.tanh=function(){var o,u,a=this,f=a.constructor;return a.isFinite()?a.isZero()?new f(a):(o=f.precision,u=f.rounding,f.precision=o+7,f.rounding=1,Ae(a.sinh(),a.cosh(),f.precision=o,f.rounding=u)):new f(a.s)},B.inverseCosine=B.acos=function(){var o,u=this,a=u.constructor,f=u.abs().cmp(1),g=a.precision,p=a.rounding;return f!==-1?f===0?u.isNeg()?Ze(a,g,p):new a(0):new a(NaN):u.isZero()?Ze(a,g+4,p).times(.5):(a.precision=g+6,a.rounding=1,u=u.asin(),o=Ze(a,g+4,p).times(.5),a.precision=g,a.rounding=p,o.minus(u))},B.inverseHyperbolicCosine=B.acosh=function(){var o,u,a=this,f=a.constructor;return a.lte(1)?new f(a.eq(1)?0:NaN):a.isFinite()?(o=f.precision,u=f.rounding,f.precision=o+Math.max(Math.abs(a.e),a.sd())+4,f.rounding=1,S=!1,a=a.times(a).minus(1).sqrt().plus(a),S=!0,f.precision=o,f.rounding=u,a.ln()):new f(a)},B.inverseHyperbolicSine=B.asinh=function(){var o,u,a=this,f=a.constructor;return!a.isFinite()||a.isZero()?new f(a):(o=f.precision,u=f.rounding,f.precision=o+2*Math.max(Math.abs(a.e),a.sd())+6,f.rounding=1,S=!1,a=a.times(a).plus(1).sqrt().plus(a),S=!0,f.precision=o,f.rounding=u,a.ln())},B.inverseHyperbolicTangent=B.atanh=function(){var o,u,a,f,g=this,p=g.constructor;return g.isFinite()?g.e>=0?new p(g.abs().eq(1)?g.s/0:g.isZero()?g:NaN):(o=p.precision,u=p.rounding,f=g.sd(),Math.max(f,o)<2*-g.e-1?j(new p(g),o,u,!0):(p.precision=a=f-g.e,g=Ae(g.plus(1),new p(1).minus(g),a+o,1),p.precision=o+4,p.rounding=1,g=g.ln(),p.precision=o,p.rounding=u,g.times(.5))):new p(NaN)},B.inverseSine=B.asin=function(){var o,u,a,f,g=this,p=g.constructor;return g.isZero()?new p(g):(u=g.abs().cmp(1),a=p.precision,f=p.rounding,u!==-1?u===0?(o=Ze(p,a+4,f).times(.5),o.s=g.s,o):new p(NaN):(p.precision=a+6,p.rounding=1,g=g.div(new p(1).minus(g.times(g)).sqrt().plus(1)).atan(),p.precision=a,p.rounding=f,g.times(2)))},B.inverseTangent=B.atan=function(){var o,u,a,f,g,p,E,T,L,x=this,I=x.constructor,F=I.precision,z=I.rounding;if(x.isFinite()){if(x.isZero())return new I(x);if(x.abs().eq(1)&&F+4<=ot)return E=Ze(I,F+4,z).times(.25),E.s=x.s,E}else{if(!x.s)return new I(NaN);if(F+4<=ot)return E=Ze(I,F+4,z).times(.5),E.s=x.s,E}for(I.precision=T=F+10,I.rounding=1,a=Math.min(28,T/G+2|0),o=a;o;--o)x=x.div(x.times(x).plus(1).sqrt().plus(1));for(S=!1,u=Math.ceil(T/G),f=1,L=x.times(x),E=new I(x),g=x;o!==-1;)if(g=g.times(L),p=E.minus(g.div(f+=2)),g=g.times(L),E=p.plus(g.div(f+=2)),E.d[u]!==void 0)for(o=u;E.d[o]===p.d[o]&&o--;);return a&&(E=E.times(2<<a-1)),S=!0,j(E,I.precision=F,I.rounding=z,!0)},B.isFinite=function(){return!!this.d},B.isInteger=B.isInt=function(){return!!this.d&&D(this.e/G)>this.d.length-2},B.isNaN=function(){return!this.s},B.isNegative=B.isNeg=function(){return this.s<0},B.isPositive=B.isPos=function(){return this.s>0},B.isZero=function(){return!!this.d&&this.d[0]===0},B.lessThan=B.lt=function(o){return this.cmp(o)<0},B.lessThanOrEqualTo=B.lte=function(o){return this.cmp(o)<1},B.logarithm=B.log=function(o){var u,a,f,g,p,E,T,L,x=this,I=x.constructor,F=I.precision,z=I.rounding,ie=5;if(o==null)o=new I(10),u=!0;else{if(o=new I(o),a=o.d,o.s<0||!a||!a[0]||o.eq(1))return new I(NaN);u=o.eq(10)}if(a=x.d,x.s<0||!a||!a[0]||x.eq(1))return new I(a&&!a[0]?-1/0:x.s!=1?NaN:a?0:1/0);if(u)if(a.length>1)p=!0;else{for(g=a[0];g%10===0;)g/=10;p=g!==1}if(S=!1,T=F+ie,E=bt(x,T),f=u?pr(I,T+10):bt(o,T),L=Ae(E,f,T,1),vt(L.d,g=F,z))do if(T+=10,E=bt(x,T),f=u?pr(I,T+10):bt(o,T),L=Ae(E,f,T,1),!p){+Oe(L.d).slice(g+1,g+15)+1==1e14&&(L=j(L,F+1,0));break}while(vt(L.d,g+=10,z));return S=!0,j(L,F,z)},B.minus=B.sub=function(o){var u,a,f,g,p,E,T,L,x,I,F,z,ie=this,_e=ie.constructor;if(o=new _e(o),!ie.d||!o.d)return!ie.s||!o.s?o=new _e(NaN):ie.d?o.s=-o.s:o=new _e(o.d||ie.s!==o.s?ie:NaN),o;if(ie.s!=o.s)return o.s=-o.s,ie.plus(o);if(x=ie.d,z=o.d,T=_e.precision,L=_e.rounding,!x[0]||!z[0]){if(z[0])o.s=-o.s;else if(x[0])o=new _e(ie);else return new _e(L===3?-0:0);return S?j(o,T,L):o}if(a=D(o.e/G),I=D(ie.e/G),x=x.slice(),p=I-a,p){for(F=p<0,F?(u=x,p=-p,E=z.length):(u=z,a=I,E=x.length),f=Math.max(Math.ceil(T/G),E)+2,p>f&&(p=f,u.length=1),u.reverse(),f=p;f--;)u.push(0);u.reverse()}else{for(f=x.length,E=z.length,F=f<E,F&&(E=f),f=0;f<E;f++)if(x[f]!=z[f]){F=x[f]<z[f];break}p=0}for(F&&(u=x,x=z,z=u,o.s=-o.s),E=x.length,f=z.length-E;f>0;--f)x[E++]=0;for(f=z.length;f>p;){if(x[--f]<z[f]){for(g=f;g&&x[--g]===0;)x[g]=Q-1;--x[g],x[f]+=Q}x[f]-=z[f]}for(;x[--E]===0;)x.pop();for(;x[0]===0;x.shift())--a;return x[0]?(o.d=x,o.e=gr(x,a),S?j(o,T,L):o):new _e(L===3?-0:0)},B.modulo=B.mod=function(o){var u,a=this,f=a.constructor;return o=new f(o),!a.d||!o.s||o.d&&!o.d[0]?new f(NaN):!o.d||a.d&&!a.d[0]?j(new f(a),f.precision,f.rounding):(S=!1,f.modulo==9?(u=Ae(a,o.abs(),0,3,1),u.s*=o.s):u=Ae(a,o,0,f.modulo,1),u=u.times(o),S=!0,a.minus(u))},B.naturalExponential=B.exp=function(){return Tn(this)},B.naturalLogarithm=B.ln=function(){return bt(this)},B.negated=B.neg=function(){var o=new this.constructor(this);return o.s=-o.s,j(o)},B.plus=B.add=function(o){var u,a,f,g,p,E,T,L,x,I,F=this,z=F.constructor;if(o=new z(o),!F.d||!o.d)return!F.s||!o.s?o=new z(NaN):F.d||(o=new z(o.d||F.s===o.s?F:NaN)),o;if(F.s!=o.s)return o.s=-o.s,F.minus(o);if(x=F.d,I=o.d,T=z.precision,L=z.rounding,!x[0]||!I[0])return I[0]||(o=new z(F)),S?j(o,T,L):o;if(p=D(F.e/G),f=D(o.e/G),x=x.slice(),g=p-f,g){for(g<0?(a=x,g=-g,E=I.length):(a=I,f=p,E=x.length),p=Math.ceil(T/G),E=p>E?p+1:E+1,g>E&&(g=E,a.length=1),a.reverse();g--;)a.push(0);a.reverse()}for(E=x.length,g=I.length,E-g<0&&(g=E,a=I,I=x,x=a),u=0;g;)u=(x[--g]=x[g]+I[g]+u)/Q|0,x[g]%=Q;for(u&&(x.unshift(u),++f),E=x.length;x[--E]==0;)x.pop();return o.d=x,o.e=gr(x,f),S?j(o,T,L):o},B.precision=B.sd=function(o){var u,a=this;if(o!==void 0&&o!==!!o&&o!==1&&o!==0)throw Error(M+o);return a.d?(u=zn(a.d),o&&a.e+1>u&&(u=a.e+1)):u=NaN,u},B.round=function(){var o=this,u=o.constructor;return j(new u(o),o.e+1,u.rounding)},B.sine=B.sin=function(){var o,u,a=this,f=a.constructor;return a.isFinite()?a.isZero()?new f(a):(o=f.precision,u=f.rounding,f.precision=o+Math.max(a.e,a.sd())+G,f.rounding=1,a=Oo(f,Fi(f,a)),f.precision=o,f.rounding=u,j(y>2?a.neg():a,o,u,!0)):new f(NaN)},B.squareRoot=B.sqrt=function(){var o,u,a,f,g,p,E=this,T=E.d,L=E.e,x=E.s,I=E.constructor;if(x!==1||!T||!T[0])return new I(!x||x<0&&(!T||T[0])?NaN:T?E:1/0);for(S=!1,x=Math.sqrt(+E),x==0||x==1/0?(u=Oe(T),(u.length+L)%2==0&&(u+="0"),x=Math.sqrt(u),L=D((L+1)/2)-(L<0||L%2),x==1/0?u="5e"+L:(u=x.toExponential(),u=u.slice(0,u.indexOf("e")+1)+L),f=new I(u)):f=new I(x.toString()),a=(L=I.precision)+3;;)if(p=f,f=p.plus(Ae(E,p,a+2,1)).times(.5),Oe(p.d).slice(0,a)===(u=Oe(f.d)).slice(0,a))if(u=u.slice(a-3,a+1),u=="9999"||!g&&u=="4999"){if(!g&&(j(p,L+1,0),p.times(p).eq(E))){f=p;break}a+=4,g=1}else{(!+u||!+u.slice(1)&&u.charAt(0)=="5")&&(j(f,L+1,1),o=!f.times(f).eq(E));break}return S=!0,j(f,L,I.rounding,o)},B.tangent=B.tan=function(){var o,u,a=this,f=a.constructor;return a.isFinite()?a.isZero()?new f(a):(o=f.precision,u=f.rounding,f.precision=o+10,f.rounding=1,a=a.sin(),a.s=1,a=Ae(a,new f(1).minus(a.times(a)).sqrt(),o+10,0),f.precision=o,f.rounding=u,j(y==2||y==4?a.neg():a,o,u,!0)):new f(NaN)},B.times=B.mul=function(o){var u,a,f,g,p,E,T,L,x,I=this,F=I.constructor,z=I.d,ie=(o=new F(o)).d;if(o.s*=I.s,!z||!z[0]||!ie||!ie[0])return new F(!o.s||z&&!z[0]&&!ie||ie&&!ie[0]&&!z?NaN:!z||!ie?o.s/0:o.s*0);for(a=D(I.e/G)+D(o.e/G),L=z.length,x=ie.length,L<x&&(p=z,z=ie,ie=p,E=L,L=x,x=E),p=[],E=L+x,f=E;f--;)p.push(0);for(f=x;--f>=0;){for(u=0,g=L+f;g>f;)T=p[g]+ie[f]*z[g-f-1]+u,p[g--]=T%Q|0,u=T/Q|0;p[g]=(p[g]+u)%Q|0}for(;!p[--E];)p.pop();return u?++a:p.shift(),o.d=p,o.e=gr(p,a),S?j(o,F.precision,F.rounding):o},B.toBinary=function(o,u){return cn(this,2,o,u)},B.toDecimalPlaces=B.toDP=function(o,u){var a=this,f=a.constructor;return a=new f(a),o===void 0?a:(je(o,0,l),u===void 0?u=f.rounding:je(u,0,8),j(a,o+a.e+1,u))},B.toExponential=function(o,u){var a,f=this,g=f.constructor;return o===void 0?a=Mt(f,!0):(je(o,0,l),u===void 0?u=g.rounding:je(u,0,8),f=j(new g(f),o+1,u),a=Mt(f,!0,o+1)),f.isNeg()&&!f.isZero()?"-"+a:a},B.toFixed=function(o,u){var a,f,g=this,p=g.constructor;return o===void 0?a=Mt(g):(je(o,0,l),u===void 0?u=p.rounding:je(u,0,8),f=j(new p(g),o+g.e+1,u),a=Mt(f,!1,o+f.e+1)),g.isNeg()&&!g.isZero()?"-"+a:a},B.toFraction=function(o){var u,a,f,g,p,E,T,L,x,I,F,z,ie=this,_e=ie.d,ce=ie.constructor;if(!_e)return new ce(ie);if(x=a=new ce(1),f=L=new ce(0),u=new ce(f),p=u.e=zn(_e)-ie.e-1,E=p%G,u.d[0]=$(10,E<0?G+E:E),o==null)o=p>0?u:x;else{if(T=new ce(o),!T.isInt()||T.lt(x))throw Error(M+T);o=T.gt(u)?p>0?u:x:T}for(S=!1,T=new ce(Oe(_e)),I=ce.precision,ce.precision=p=_e.length*G*2;F=Ae(T,u,0,1,1),g=a.plus(F.times(f)),g.cmp(o)!=1;)a=f,f=g,g=x,x=L.plus(F.times(g)),L=g,g=u,u=T.minus(F.times(g)),T=g;return g=Ae(o.minus(a),f,0,1,1),L=L.plus(g.times(x)),a=a.plus(g.times(f)),L.s=x.s=ie.s,z=Ae(x,f,p,1).minus(ie).abs().cmp(Ae(L,a,p,1).minus(ie).abs())<1?[x,f]:[L,a],ce.precision=I,S=!0,z},B.toHexadecimal=B.toHex=function(o,u){return cn(this,16,o,u)},B.toNearest=function(o,u){var a=this,f=a.constructor;if(a=new f(a),o==null){if(!a.d)return a;o=new f(1),u=f.rounding}else{if(o=new f(o),u===void 0?u=f.rounding:je(u,0,8),!a.d)return o.s?a:o;if(!o.d)return o.s&&(o.s=a.s),o}return o.d[0]?(S=!1,a=Ae(a,o,0,u,1).times(o),S=!0,j(a)):(o.s=a.s,a=o),a},B.toNumber=function(){return+this},B.toOctal=function(o,u){return cn(this,8,o,u)},B.toPower=B.pow=function(o){var u,a,f,g,p,E,T=this,L=T.constructor,x=+(o=new L(o));if(!T.d||!o.d||!T.d[0]||!o.d[0])return new L($(+T,x));if(T=new L(T),T.eq(1))return T;if(f=L.precision,p=L.rounding,o.eq(1))return j(T,f,p);if(u=D(o.e/G),u>=o.d.length-1&&(a=x<0?-x:x)<=Ce)return g=En(L,T,a,f),o.s<0?new L(1).div(g):j(g,f,p);if(E=T.s,E<0){if(u<o.d.length-1)return new L(NaN);if(o.d[u]&1||(E=1),T.e==0&&T.d[0]==1&&T.d.length==1)return T.s=E,T}return a=$(+T,x),u=a==0||!isFinite(a)?D(x*(Math.log("0."+Oe(T.d))/Math.LN10+T.e+1)):new L(a+"").e,u>L.maxE+1||u<L.minE-1?new L(u>0?E/0:0):(S=!1,L.rounding=T.s=1,a=Math.min(12,(u+"").length),g=Tn(o.times(bt(T,f+a)),f),g.d&&(g=j(g,f+5,1),vt(g.d,f,p)&&(u=f+10,g=j(Tn(o.times(bt(T,u+a)),u),u+5,1),+Oe(g.d).slice(f+1,f+15)+1==1e14&&(g=j(g,f+1,0)))),g.s=E,S=!0,L.rounding=p,j(g,f,p))},B.toPrecision=function(o,u){var a,f=this,g=f.constructor;return o===void 0?a=Mt(f,f.e<=g.toExpNeg||f.e>=g.toExpPos):(je(o,1,l),u===void 0?u=g.rounding:je(u,0,8),f=j(new g(f),o,u),a=Mt(f,o<=f.e||f.e<=g.toExpNeg,o)),f.isNeg()&&!f.isZero()?"-"+a:a},B.toSignificantDigits=B.toSD=function(o,u){var a=this,f=a.constructor;return o===void 0?(o=f.precision,u=f.rounding):(je(o,1,l),u===void 0?u=f.rounding:je(u,0,8)),j(new f(a),o,u)},B.toString=function(){var o=this,u=o.constructor,a=Mt(o,o.e<=u.toExpNeg||o.e>=u.toExpPos);return o.isNeg()&&!o.isZero()?"-"+a:a},B.truncated=B.trunc=function(){return j(new this.constructor(this),this.e+1,1)},B.valueOf=B.toJSON=function(){var o=this,u=o.constructor,a=Mt(o,o.e<=u.toExpNeg||o.e>=u.toExpPos);return o.isNeg()?"-"+a:a};function Oe(o){var u,a,f,g=o.length-1,p="",E=o[0];if(g>0){for(p+=E,u=1;u<g;u++)f=o[u]+"",a=G-f.length,a&&(p+=tn(a)),p+=f;E=o[u],f=E+"",a=G-f.length,a&&(p+=tn(a))}else if(E===0)return"0";for(;E%10===0;)E/=10;return p+E}function je(o,u,a){if(o!==~~o||o<u||o>a)throw Error(M+o)}function vt(o,u,a,f){var g,p,E,T;for(p=o[0];p>=10;p/=10)--u;return--u<0?(u+=G,g=0):(g=Math.ceil((u+1)/G),u%=G),p=$(10,G-u),T=o[g]%p|0,f==null?u<3?(u==0?T=T/100|0:u==1&&(T=T/10|0),E=a<4&&T==99999||a>3&&T==49999||T==5e4||T==0):E=(a<4&&T+1==p||a>3&&T+1==p/2)&&(o[g+1]/p/100|0)==$(10,u-2)-1||(T==p/2||T==0)&&(o[g+1]/p/100|0)==0:u<4?(u==0?T=T/1e3|0:u==1?T=T/100|0:u==2&&(T=T/10|0),E=(f||a<4)&&T==9999||!f&&a>3&&T==4999):E=((f||a<4)&&T+1==p||!f&&a>3&&T+1==p/2)&&(o[g+1]/p/1e3|0)==$(10,u-3)-1,E}function _t(o,u,a){for(var f,g=[0],p,E=0,T=o.length;E<T;){for(p=g.length;p--;)g[p]*=u;for(g[0]+=c.indexOf(o.charAt(E++)),f=0;f<g.length;f++)g[f]>a-1&&(g[f+1]===void 0&&(g[f+1]=0),g[f+1]+=g[f]/a|0,g[f]%=a)}return g.reverse()}function Lo(o,u){var a,f,g;if(u.isZero())return u;f=u.d.length,f<32?(a=Math.ceil(f/3),g=(1/Zn(4,a)).toString()):(a=16,g="2.3283064365386962890625e-10"),o.precision+=a,u=et(o,1,u.times(g),new o(1));for(var p=a;p--;){var E=u.times(u);u=E.times(E).minus(E).times(8).plus(1)}return o.precision-=a,u}var Ae=function(){function o(f,g,p){var E,T=0,L=f.length;for(f=f.slice();L--;)E=f[L]*g+T,f[L]=E%p|0,T=E/p|0;return T&&f.unshift(T),f}function u(f,g,p,E){var T,L;if(p!=E)L=p>E?1:-1;else for(T=L=0;T<p;T++)if(f[T]!=g[T]){L=f[T]>g[T]?1:-1;break}return L}function a(f,g,p,E){for(var T=0;p--;)f[p]-=T,T=f[p]<g[p]?1:0,f[p]=T*E+f[p]-g[p];for(;!f[0]&&f.length>1;)f.shift()}return function(f,g,p,E,T,L){var x,I,F,z,ie,_e,ce,Ye,Me,Et,Re,Be,Yn,At,ri,Jn,nn,Cr,Tt,Xn,Qn=f.constructor,jn=f.s==g.s?1:-1,Ge=f.d,ye=g.d;if(!Ge||!Ge[0]||!ye||!ye[0])return new Qn(!f.s||!g.s||(Ge?ye&&Ge[0]==ye[0]:!ye)?NaN:Ge&&Ge[0]==0||!ye?jn*0:jn/0);for(L?(ie=1,I=f.e-g.e):(L=Q,ie=G,I=D(f.e/ie)-D(g.e/ie)),Tt=ye.length,nn=Ge.length,Me=new Qn(jn),Et=Me.d=[],F=0;ye[F]==(Ge[F]||0);F++);if(ye[F]>(Ge[F]||0)&&I--,p==null?(At=p=Qn.precision,E=Qn.rounding):T?At=p+(f.e-g.e)+1:At=p,At<0)Et.push(1),_e=!0;else{if(At=At/ie+2|0,F=0,Tt==1){for(z=0,ye=ye[0],At++;(F<nn||z)&&At--;F++)ri=z*L+(Ge[F]||0),Et[F]=ri/ye|0,z=ri%ye|0;_e=z||F<nn}else{for(z=L/(ye[0]+1)|0,z>1&&(ye=o(ye,z,L),Ge=o(Ge,z,L),Tt=ye.length,nn=Ge.length),Jn=Tt,Re=Ge.slice(0,Tt),Be=Re.length;Be<Tt;)Re[Be++]=0;Xn=ye.slice(),Xn.unshift(0),Cr=ye[0],ye[1]>=L/2&&++Cr;do z=0,x=u(ye,Re,Tt,Be),x<0?(Yn=Re[0],Tt!=Be&&(Yn=Yn*L+(Re[1]||0)),z=Yn/Cr|0,z>1?(z>=L&&(z=L-1),ce=o(ye,z,L),Ye=ce.length,Be=Re.length,x=u(ce,Re,Ye,Be),x==1&&(z--,a(ce,Tt<Ye?Xn:ye,Ye,L))):(z==0&&(x=z=1),ce=ye.slice()),Ye=ce.length,Ye<Be&&ce.unshift(0),a(Re,ce,Be,L),x==-1&&(Be=Re.length,x=u(ye,Re,Tt,Be),x<1&&(z++,a(Re,Tt<Be?Xn:ye,Be,L))),Be=Re.length):x===0&&(z++,Re=[0]),Et[F++]=z,x&&Re[0]?Re[Be++]=Ge[Jn]||0:(Re=[Ge[Jn]],Be=1);while((Jn++<nn||Re[0]!==void 0)&&At--);_e=Re[0]!==void 0}Et[0]||Et.shift()}if(ie==1)Me.e=I,A=_e;else{for(F=1,z=Et[0];z>=10;z/=10)F++;Me.e=F+I*ie-1,j(Me,T?p+Me.e+1:p,E,_e)}return Me}}();function j(o,u,a,f){var g,p,E,T,L,x,I,F,z,ie=o.constructor;e:if(u!=null){if(F=o.d,!F)return o;for(g=1,T=F[0];T>=10;T/=10)g++;if(p=u-g,p<0)p+=G,E=u,I=F[z=0],L=I/$(10,g-E-1)%10|0;else if(z=Math.ceil((p+1)/G),T=F.length,z>=T)if(f){for(;T++<=z;)F.push(0);I=L=0,g=1,p%=G,E=p-G+1}else break e;else{for(I=T=F[z],g=1;T>=10;T/=10)g++;p%=G,E=p-G+g,L=E<0?0:I/$(10,g-E-1)%10|0}if(f=f||u<0||F[z+1]!==void 0||(E<0?I:I%$(10,g-E-1)),x=a<4?(L||f)&&(a==0||a==(o.s<0?3:2)):L>5||L==5&&(a==4||f||a==6&&(p>0?E>0?I/$(10,g-E):0:F[z-1])%10&1||a==(o.s<0?8:7)),u<1||!F[0])return F.length=0,x?(u-=o.e+1,F[0]=$(10,(G-u%G)%G),o.e=-u||0):F[0]=o.e=0,o;if(p==0?(F.length=z,T=1,z--):(F.length=z+1,T=$(10,G-p),F[z]=E>0?(I/$(10,g-E)%$(10,E)|0)*T:0),x)for(;;)if(z==0){for(p=1,E=F[0];E>=10;E/=10)p++;for(E=F[0]+=T,T=1;E>=10;E/=10)T++;p!=T&&(o.e++,F[0]==Q&&(F[0]=1));break}else{if(F[z]+=T,F[z]!=Q)break;F[z--]=0,T=1}for(p=F.length;F[--p]===0;)F.pop()}return S&&(o.e>ie.maxE?(o.d=null,o.e=NaN):o.e<ie.minE&&(o.e=0,o.d=[0])),o}function Mt(o,u,a){if(!o.isFinite())return Zr(o);var f,g=o.e,p=Oe(o.d),E=p.length;return u?(a&&(f=a-E)>0?p=p.charAt(0)+"."+p.slice(1)+tn(f):E>1&&(p=p.charAt(0)+"."+p.slice(1)),p=p+(o.e<0?"e":"e+")+o.e):g<0?(p="0."+tn(-g-1)+p,a&&(f=a-E)>0&&(p+=tn(f))):g>=E?(p+=tn(g+1-E),a&&(f=a-g-1)>0&&(p=p+"."+tn(f))):((f=g+1)<E&&(p=p.slice(0,f)+"."+p.slice(f)),a&&(f=a-E)>0&&(g+1===E&&(p+="."),p+=tn(f))),p}function gr(o,u){var a=o[0];for(u*=G;a>=10;a/=10)u++;return u}function pr(o,u,a){if(u>Pe)throw S=!0,a&&(o.precision=a),Error(Y);return j(new o(d),u,1,!0)}function Ze(o,u,a){if(u>ot)throw Error(Y);return j(new o(C),u,a,!0)}function zn(o){var u=o.length-1,a=u*G+1;if(u=o[u],u){for(;u%10==0;u/=10)a--;for(u=o[0];u>=10;u/=10)a++}return a}function tn(o){for(var u="";o--;)u+="0";return u}function En(o,u,a,f){var g,p=new o(1),E=Math.ceil(f/G+4);for(S=!1;;){if(a%2&&(p=p.times(u),ht(p.d,E)&&(g=!0)),a=D(a/2),a===0){a=p.d.length-1,g&&p.d[a]===0&&++p.d[a];break}u=u.times(u),ht(u.d,E)}return S=!0,p}function An(o){return o.d[o.d.length-1]&1}function Di(o,u,a){for(var f,g=new o(u[0]),p=0;++p<u.length;)if(f=new o(u[p]),f.s)g[a](f)&&(g=f);else{g=f;break}return g}function Tn(o,u){var a,f,g,p,E,T,L,x=0,I=0,F=0,z=o.constructor,ie=z.rounding,_e=z.precision;if(!o.d||!o.d[0]||o.e>17)return new z(o.d?o.d[0]?o.s<0?0:1/0:1:o.s?o.s<0?0:o:NaN);for(u==null?(S=!1,L=_e):L=u,T=new z(.03125);o.e>-2;)o=o.times(T),F+=5;for(f=Math.log($(2,F))/Math.LN10*2+5|0,L+=f,a=p=E=new z(1),z.precision=L;;){if(p=j(p.times(o),L,1),a=a.times(++I),T=E.plus(Ae(p,a,L,1)),Oe(T.d).slice(0,L)===Oe(E.d).slice(0,L)){for(g=F;g--;)E=j(E.times(E),L,1);if(u==null)if(x<3&&vt(E.d,L-f,ie,x))z.precision=L+=10,a=p=T=new z(1),I=0,x++;else return j(E,z.precision=_e,ie,S=!0);else return z.precision=_e,E}E=T}}function bt(o,u){var a,f,g,p,E,T,L,x,I,F,z,ie=1,_e=10,ce=o,Ye=ce.d,Me=ce.constructor,Et=Me.rounding,Re=Me.precision;if(ce.s<0||!Ye||!Ye[0]||!ce.e&&Ye[0]==1&&Ye.length==1)return new Me(Ye&&!Ye[0]?-1/0:ce.s!=1?NaN:Ye?0:ce);if(u==null?(S=!1,I=Re):I=u,Me.precision=I+=_e,a=Oe(Ye),f=a.charAt(0),Math.abs(p=ce.e)<15e14){for(;f<7&&f!=1||f==1&&a.charAt(1)>3;)ce=ce.times(o),a=Oe(ce.d),f=a.charAt(0),ie++;p=ce.e,f>1?(ce=new Me("0."+a),p++):ce=new Me(f+"."+a.slice(1))}else return x=pr(Me,I+2,Re).times(p+""),ce=bt(new Me(f+"."+a.slice(1)),I-_e).plus(x),Me.precision=Re,u==null?j(ce,Re,Et,S=!0):ce;for(F=ce,L=E=ce=Ae(ce.minus(1),ce.plus(1),I,1),z=j(ce.times(ce),I,1),g=3;;){if(E=j(E.times(z),I,1),x=L.plus(Ae(E,new Me(g),I,1)),Oe(x.d).slice(0,I)===Oe(L.d).slice(0,I))if(L=L.times(2),p!==0&&(L=L.plus(pr(Me,I+2,Re).times(p+""))),L=Ae(L,new Me(ie),I,1),u==null)if(vt(L.d,I-_e,Et,T))Me.precision=I+=_e,x=E=ce=Ae(F.minus(1),F.plus(1),I,1),z=j(ce.times(ce),I,1),g=T=1;else return j(L,Me.precision=Re,Et,S=!0);else return Me.precision=Re,L;L=x,g+=2}}function Zr(o){return String(o.s*o.s/0)}function st(o,u){var a,f,g;for((a=u.indexOf("."))>-1&&(u=u.replace(".","")),(f=u.search(/e/i))>0?(a<0&&(a=f),a+=+u.slice(f+1),u=u.substring(0,f)):a<0&&(a=u.length),f=0;u.charCodeAt(f)===48;f++);for(g=u.length;u.charCodeAt(g-1)===48;--g);if(u=u.slice(f,g),u){if(g-=f,o.e=a=a-f-1,o.d=[],f=(a+1)%G,a<0&&(f+=G),f<g){for(f&&o.d.push(+u.slice(0,f)),g-=G;f<g;)o.d.push(+u.slice(f,f+=G));u=u.slice(f),f=G-u.length}else f-=g;for(;f--;)u+="0";o.d.push(+u),S&&(o.e>o.constructor.maxE?(o.d=null,o.e=NaN):o.e<o.constructor.minE&&(o.e=0,o.d=[0]))}else o.e=0,o.d=[0];return o}function $n(o,u){var a,f,g,p,E,T,L,x,I;if(u.indexOf("_")>-1){if(u=u.replace(/(\d)_(?=\d)/g,"$1"),J.test(u))return st(o,u)}else if(u==="Infinity"||u==="NaN")return+u||(o.s=NaN),o.e=NaN,o.d=null,o;if(K.test(u))a=16,u=u.toLowerCase();else if(U.test(u))a=2;else if(te.test(u))a=8;else throw Error(M+u);for(p=u.search(/p/i),p>0?(L=+u.slice(p+1),u=u.substring(2,p)):u=u.slice(2),p=u.indexOf("."),E=p>=0,f=o.constructor,E&&(u=u.replace(".",""),T=u.length,p=T-p,g=En(f,new f(a),p,p*2)),x=_t(u,a,Q),I=x.length-1,p=I;x[p]===0;--p)x.pop();return p<0?new f(o.s*0):(o.e=gr(x,I),o.d=x,S=!1,E&&(o=Ae(o,g,T*4)),L&&(o=o.times(Math.abs(L)<54?$(2,L):m.pow(2,L))),S=!0,o)}function Oo(o,u){var a,f=u.d.length;if(f<3)return u.isZero()?u:et(o,2,u,u);a=1.4*Math.sqrt(f),a=a>16?16:a|0,u=u.times(1/Zn(5,a)),u=et(o,2,u,u);for(var g,p=new o(5),E=new o(16),T=new o(20);a--;)g=u.times(u),u=u.times(p.plus(g.times(E.times(g).minus(T))));return u}function et(o,u,a,f,g){var p,E,T,L,x=1,I=o.precision,F=Math.ceil(I/G);for(S=!1,L=a.times(a),T=new o(f);;){if(E=Ae(T.times(L),new o(u++*u++),I,1),T=g?f.plus(E):f.minus(E),f=Ae(E.times(L),new o(u++*u++),I,1),E=T.plus(f),E.d[F]!==void 0){for(p=F;E.d[p]===T.d[p]&&p--;);if(p==-1)break}p=T,T=f,f=E,E=p,x++}return S=!0,E.d.length=F+1,E}function Zn(o,u){for(var a=o;--u;)a*=o;return a}function Fi(o,u){var a,f=u.s<0,g=Ze(o,o.precision,1),p=g.times(.5);if(u=u.abs(),u.lte(p))return y=f?4:1,u;if(a=u.divToInt(g),a.isZero())y=f?3:2;else{if(u=u.minus(a.times(g)),u.lte(p))return y=An(a)?f?2:3:f?4:1,u;y=An(a)?f?1:4:f?3:2}return u.minus(g).abs()}function cn(o,u,a,f){var g,p,E,T,L,x,I,F,z,ie=o.constructor,_e=a!==void 0;if(_e?(je(a,1,l),f===void 0?f=ie.rounding:je(f,0,8)):(a=ie.precision,f=ie.rounding),!o.isFinite())I=Zr(o);else{for(I=Mt(o),E=I.indexOf("."),_e?(g=2,u==16?a=a*4-3:u==8&&(a=a*3-2)):g=u,E>=0&&(I=I.replace(".",""),z=new ie(1),z.e=I.length-E,z.d=_t(Mt(z),10,g),z.e=z.d.length),F=_t(I,10,g),p=L=F.length;F[--L]==0;)F.pop();if(!F[0])I=_e?"0p+0":"0";else{if(E<0?p--:(o=new ie(o),o.d=F,o.e=p,o=Ae(o,z,a,f,0,g),F=o.d,p=o.e,x=A),E=F[a],T=g/2,x=x||F[a+1]!==void 0,x=f<4?(E!==void 0||x)&&(f===0||f===(o.s<0?3:2)):E>T||E===T&&(f===4||x||f===6&&F[a-1]&1||f===(o.s<0?8:7)),F.length=a,x)for(;++F[--a]>g-1;)F[a]=0,a||(++p,F.unshift(1));for(L=F.length;!F[L-1];--L);for(E=0,I="";E<L;E++)I+=c.charAt(F[E]);if(_e){if(L>1)if(u==16||u==8){for(E=u==16?4:3,--L;L%E;L++)I+="0";for(F=_t(I,g,u),L=F.length;!F[L-1];--L);for(E=1,I="1.";E<L;E++)I+=c.charAt(F[E])}else I=I.charAt(0)+"."+I.slice(1);I=I+(p<0?"p":"p+")+p}else if(p<0){for(;++p;)I="0"+I;I="0."+I}else if(++p>L)for(p-=L;p--;)I+="0";else p<L&&(I=I.slice(0,p)+"."+I.slice(p))}I=(u==16?"0x":u==2?"0b":u==8?"0o":"")+I}return o.s<0?"-"+I:I}function ht(o,u){if(o.length>u)return o.length=u,!0}function Gn(o){return new this(o).abs()}function mr(o){return new this(o).acos()}function So(o){return new this(o).acosh()}function Vn(o,u){return new this(o).plus(u)}function Po(o){return new this(o).asin()}function Kn(o){return new this(o).asinh()}function Rn(o){return new this(o).atan()}function Gr(o){return new this(o).atanh()}function Vr(o,u){o=new this(o),u=new this(u);var a,f=this.precision,g=this.rounding,p=f+4;return!o.s||!u.s?a=new this(NaN):!o.d&&!u.d?(a=Ze(this,p,1).times(u.s>0?.25:.75),a.s=o.s):!u.d||o.isZero()?(a=u.s<0?Ze(this,f,g):new this(0),a.s=o.s):!o.d||u.isZero()?(a=Ze(this,p,1).times(.5),a.s=o.s):u.s<0?(this.precision=p,this.rounding=1,a=this.atan(Ae(o,u,p,1)),u=Ze(this,p,1),this.precision=f,this.rounding=g,a=o.s<0?a.minus(u):a.plus(u)):a=this.atan(Ae(o,u,p,1)),a}function Kr(o){return new this(o).cbrt()}function Yr(o){return j(o=new this(o),o.e+1,2)}function Jr(o,u,a){return new this(o).clamp(u,a)}function Xr(o){if(!o||typeof o!="object")throw Error(V+"Object expected");var u,a,f,g=o.defaults===!0,p=["precision",1,l,"rounding",0,8,"toExpNeg",-r,0,"toExpPos",0,r,"maxE",0,r,"minE",-r,0,"modulo",0,9];for(u=0;u<p.length;u+=3)if(a=p[u],g&&(this[a]=_[a]),(f=o[a])!==void 0)if(D(f)===f&&f>=p[u+1]&&f<=p[u+2])this[a]=f;else throw Error(M+a+": "+f);if(a="crypto",g&&(this[a]=_[a]),(f=o[a])!==void 0)if(f===!0||f===!1||f===0||f===1)if(f)if(typeof crypto!="undefined"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[a]=!0;else throw Error(se);else this[a]=!1;else throw Error(M+a+": "+f);return this}function Qr(o){return new this(o).cos()}function jr(o){return new this(o).cosh()}function wr(o){var u,a,f;function g(p){var E,T,L,x=this;if(!(x instanceof g))return new g(p);if(x.constructor=g,ei(p)){x.s=p.s,S?!p.d||p.e>g.maxE?(x.e=NaN,x.d=null):p.e<g.minE?(x.e=0,x.d=[0]):(x.e=p.e,x.d=p.d.slice()):(x.e=p.e,x.d=p.d?p.d.slice():p.d);return}if(L=typeof p,L==="number"){if(p===0){x.s=1/p<0?-1:1,x.e=0,x.d=[0];return}if(p<0?(p=-p,x.s=-1):x.s=1,p===~~p&&p<1e7){for(E=0,T=p;T>=10;T/=10)E++;S?E>g.maxE?(x.e=NaN,x.d=null):E<g.minE?(x.e=0,x.d=[0]):(x.e=E,x.d=[p]):(x.e=E,x.d=[p]);return}else if(p*0!==0){p||(x.s=NaN),x.e=NaN,x.d=null;return}return st(x,p.toString())}else if(L!=="string")throw Error(M+p);return(T=p.charCodeAt(0))===45?(p=p.slice(1),x.s=-1):(T===43&&(p=p.slice(1)),x.s=1),J.test(p)?st(x,p):$n(x,p)}if(g.prototype=B,g.ROUND_UP=0,g.ROUND_DOWN=1,g.ROUND_CEIL=2,g.ROUND_FLOOR=3,g.ROUND_HALF_UP=4,g.ROUND_HALF_DOWN=5,g.ROUND_HALF_EVEN=6,g.ROUND_HALF_CEIL=7,g.ROUND_HALF_FLOOR=8,g.EUCLID=9,g.config=g.set=Xr,g.clone=wr,g.isDecimal=ei,g.abs=Gn,g.acos=mr,g.acosh=So,g.add=Vn,g.asin=Po,g.asinh=Kn,g.atan=Rn,g.atanh=Gr,g.atan2=Vr,g.cbrt=Kr,g.ceil=Yr,g.clamp=Jr,g.cos=Qr,g.cosh=jr,g.div=Io,g.exp=Wo,g.floor=Mo,g.hypot=ki,g.ln=Do,g.log=Fo,g.log10=Uo,g.log2=ko,g.max=Ui,g.min=qo,g.mod=Ho,g.mul=Bo,g.pow=ti,g.random=zo,g.round=ni,g.sign=$o,g.sin=Zo,g.sinh=Go,g.sqrt=Vo,g.sub=Ko,g.sum=Yo,g.tan=Jo,g.tanh=Xo,g.trunc=qi,o===void 0&&(o={}),o&&o.defaults!==!0)for(f=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],u=0;u<f.length;)o.hasOwnProperty(a=f[u++])||(o[a]=this[a]);return g.config(o),g}function Io(o,u){return new this(o).div(u)}function Wo(o){return new this(o).exp()}function Mo(o){return j(o=new this(o),o.e+1,3)}function ki(){var o,u,a=new this(0);for(S=!1,o=0;o<arguments.length;)if(u=new this(arguments[o++]),u.d)a.d&&(a=a.plus(u.times(u)));else{if(u.s)return S=!0,new this(1/0);a=u}return S=!0,a.sqrt()}function ei(o){return o instanceof m||o&&o.toStringTag===ge||!1}function Do(o){return new this(o).ln()}function Fo(o,u){return new this(o).log(u)}function ko(o){return new this(o).log(2)}function Uo(o){return new this(o).log(10)}function Ui(){return Di(this,arguments,"lt")}function qo(){return Di(this,arguments,"gt")}function Ho(o,u){return new this(o).mod(u)}function Bo(o,u){return new this(o).mul(u)}function ti(o,u){return new this(o).pow(u)}function zo(o){var u,a,f,g,p=0,E=new this(1),T=[];if(o===void 0?o=this.precision:je(o,1,l),f=Math.ceil(o/G),this.crypto)if(crypto.getRandomValues)for(u=crypto.getRandomValues(new Uint32Array(f));p<f;)g=u[p],g>=429e7?u[p]=crypto.getRandomValues(new Uint32Array(1))[0]:T[p++]=g%1e7;else if(crypto.randomBytes){for(u=crypto.randomBytes(f*=4);p<f;)g=u[p]+(u[p+1]<<8)+(u[p+2]<<16)+((u[p+3]&127)<<24),g>=214e7?crypto.randomBytes(4).copy(u,p):(T.push(g%1e7),p+=4);p=f/4}else throw Error(se);else for(;p<f;)T[p++]=Math.random()*1e7|0;for(f=T[--p],o%=G,f&&o&&(g=$(10,G-o),T[p]=(f/g|0)*g);T[p]===0;p--)T.pop();if(p<0)a=0,T=[0];else{for(a=-1;T[0]===0;a-=G)T.shift();for(f=1,g=T[0];g>=10;g/=10)f++;f<G&&(a-=G-f)}return E.e=a,E.d=T,E}function ni(o){return j(o=new this(o),o.e+1,this.rounding)}function $o(o){return o=new this(o),o.d?o.d[0]?o.s:0*o.s:o.s||NaN}function Zo(o){return new this(o).sin()}function Go(o){return new this(o).sinh()}function Vo(o){return new this(o).sqrt()}function Ko(o,u){return new this(o).sub(u)}function Yo(){var o=0,u=arguments,a=new this(u[o]);for(S=!1;a.s&&++o<u.length;)a=a.plus(u[o]);return S=!0,j(a,this.precision,this.rounding)}function Jo(o){return new this(o).tan()}function Xo(o){return new this(o).tanh()}function qi(o){return j(o=new this(o),o.e+1,1)}m=wr(_),m.prototype.constructor=m,m.default=m.Decimal=m,d=new m(d),C=new m(C),typeof define=="function"&&define.amd?define(function(){return m}):typeof q1!="undefined"&&q1.exports?(typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"&&(B[Symbol.for("nodejs.util.inspect.custom")]=B.toString,B[Symbol.toStringTag]="Decimal"),q1.exports=m):(s||(s=typeof self!="undefined"&&self&&self.self==self?self:window),P=s.Decimal,m.noConflict=function(){return s.Decimal=P,m},s.Decimal=m)})(Al)});var Wr=we((Ir,bi)=>{(function(){var s,r="4.17.21",l=200,c="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",d="Expected a function",C="Invalid `variable` option passed into `_.template`",_="__lodash_hash_undefined__",m=500,A="__lodash_placeholder__",P=1,y=2,S=4,V=1,M=2,Y=1,se=2,ge=4,D=8,$=16,U=32,K=64,te=128,J=256,Q=512,G=30,Ce="...",Pe=800,ot=16,B=1,Oe=2,je=3,vt=1/0,_t=9007199254740991,Lo=17976931348623157e292,Ae=NaN,j=4294967295,Mt=j-1,gr=j>>>1,pr=[["ary",te],["bind",Y],["bindKey",se],["curry",D],["curryRight",$],["flip",Q],["partial",U],["partialRight",K],["rearg",J]],Ze="[object Arguments]",zn="[object Array]",tn="[object AsyncFunction]",En="[object Boolean]",An="[object Date]",Di="[object DOMException]",Tn="[object Error]",bt="[object Function]",Zr="[object GeneratorFunction]",st="[object Map]",$n="[object Number]",Oo="[object Null]",et="[object Object]",Zn="[object Promise]",Fi="[object Proxy]",cn="[object RegExp]",ht="[object Set]",Gn="[object String]",mr="[object Symbol]",So="[object Undefined]",Vn="[object WeakMap]",Po="[object WeakSet]",Kn="[object ArrayBuffer]",Rn="[object DataView]",Gr="[object Float32Array]",Vr="[object Float64Array]",Kr="[object Int8Array]",Yr="[object Int16Array]",Jr="[object Int32Array]",Xr="[object Uint8Array]",Qr="[object Uint8ClampedArray]",jr="[object Uint16Array]",wr="[object Uint32Array]",Io=/\b__p \+= '';/g,Wo=/\b(__p \+=) '' \+/g,Mo=/(__e\(.*?\)|\b__t\)) \+\n'';/g,ki=/&(?:amp|lt|gt|quot|#39);/g,ei=/[&<>"']/g,Do=RegExp(ki.source),Fo=RegExp(ei.source),ko=/<%-([\s\S]+?)%>/g,Uo=/<%([\s\S]+?)%>/g,Ui=/<%=([\s\S]+?)%>/g,qo=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ho=/^\w*$/,Bo=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ti=/[\\^$.*+?()[\]{}|]/g,zo=RegExp(ti.source),ni=/^\s+/,$o=/\s/,Zo=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Go=/\{\n\/\* \[wrapped with (.+)\] \*/,Vo=/,? & /,Ko=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Yo=/[()=,{}\[\]\/\s]/,Jo=/\\(\\)?/g,Xo=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,qi=/\w*$/,o=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,a=/^\[object .+?Constructor\]$/,f=/^0o[0-7]+$/i,g=/^(?:0|[1-9]\d*)$/,p=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,E=/($^)/,T=/['\n\r\u2028\u2029\\]/g,L="\\ud800-\\udfff",x="\\u0300-\\u036f",I="\\ufe20-\\ufe2f",F="\\u20d0-\\u20ff",z=x+I+F,ie="\\u2700-\\u27bf",_e="a-z\\xdf-\\xf6\\xf8-\\xff",ce="\\xac\\xb1\\xd7\\xf7",Ye="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",Me="\\u2000-\\u206f",Et=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Re="A-Z\\xc0-\\xd6\\xd8-\\xde",Be="\\ufe0e\\ufe0f",Yn=ce+Ye+Me+Et,At="['\u2019]",ri="["+L+"]",Jn="["+Yn+"]",nn="["+z+"]",Cr="\\d+",Tt="["+ie+"]",Xn="["+_e+"]",Qn="[^"+L+Yn+Cr+ie+_e+Re+"]",jn="\\ud83c[\\udffb-\\udfff]",Ge="(?:"+nn+"|"+jn+")",ye="[^"+L+"]",Qo="(?:\\ud83c[\\udde6-\\uddff]){2}",jo="[\\ud800-\\udbff][\\udc00-\\udfff]",vr="["+Re+"]",r0="\\u200d",i0="(?:"+Xn+"|"+Qn+")",du="(?:"+vr+"|"+Qn+")",o0="(?:"+At+"(?:d|ll|m|re|s|t|ve))?",s0="(?:"+At+"(?:D|LL|M|RE|S|T|VE))?",l0=Ge+"?",u0="["+Be+"]?",gu="(?:"+r0+"(?:"+[ye,Qo,jo].join("|")+")"+u0+l0+")*",pu="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",mu="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",a0=u0+l0+gu,wu="(?:"+[Tt,Qo,jo].join("|")+")"+a0,Cu="(?:"+[ye+nn+"?",nn,Qo,jo,ri].join("|")+")",vu=RegExp(At,"g"),_u=RegExp(nn,"g"),e2=RegExp(jn+"(?="+jn+")|"+Cu+a0,"g"),bu=RegExp([vr+"?"+Xn+"+"+o0+"(?="+[Jn,vr,"$"].join("|")+")",du+"+"+s0+"(?="+[Jn,vr+i0,"$"].join("|")+")",vr+"?"+i0+"+"+o0,vr+"+"+s0,mu,pu,Cr,wu].join("|"),"g"),Eu=RegExp("["+r0+L+z+Be+"]"),Au=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Tu=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Ru=-1,De={};De[Gr]=De[Vr]=De[Kr]=De[Yr]=De[Jr]=De[Xr]=De[Qr]=De[jr]=De[wr]=!0,De[Ze]=De[zn]=De[Kn]=De[En]=De[Rn]=De[An]=De[Tn]=De[bt]=De[st]=De[$n]=De[et]=De[cn]=De[ht]=De[Gn]=De[Vn]=!1;var Ie={};Ie[Ze]=Ie[zn]=Ie[Kn]=Ie[Rn]=Ie[En]=Ie[An]=Ie[Gr]=Ie[Vr]=Ie[Kr]=Ie[Yr]=Ie[Jr]=Ie[st]=Ie[$n]=Ie[et]=Ie[cn]=Ie[ht]=Ie[Gn]=Ie[mr]=Ie[Xr]=Ie[Qr]=Ie[jr]=Ie[wr]=!0,Ie[Tn]=Ie[bt]=Ie[Vn]=!1;var xu={\u00C0:"A",\u00C1:"A",\u00C2:"A",\u00C3:"A",\u00C4:"A",\u00C5:"A",\u00E0:"a",\u00E1:"a",\u00E2:"a",\u00E3:"a",\u00E4:"a",\u00E5:"a",\u00C7:"C",\u00E7:"c",\u00D0:"D",\u00F0:"d",\u00C8:"E",\u00C9:"E",\u00CA:"E",\u00CB:"E",\u00E8:"e",\u00E9:"e",\u00EA:"e",\u00EB:"e",\u00CC:"I",\u00CD:"I",\u00CE:"I",\u00CF:"I",\u00EC:"i",\u00ED:"i",\u00EE:"i",\u00EF:"i",\u00D1:"N",\u00F1:"n",\u00D2:"O",\u00D3:"O",\u00D4:"O",\u00D5:"O",\u00D6:"O",\u00D8:"O",\u00F2:"o",\u00F3:"o",\u00F4:"o",\u00F5:"o",\u00F6:"o",\u00F8:"o",\u00D9:"U",\u00DA:"U",\u00DB:"U",\u00DC:"U",\u00F9:"u",\u00FA:"u",\u00FB:"u",\u00FC:"u",\u00DD:"Y",\u00FD:"y",\u00FF:"y",\u00C6:"Ae",\u00E6:"ae",\u00DE:"Th",\u00FE:"th",\u00DF:"ss",\u0100:"A",\u0102:"A",\u0104:"A",\u0101:"a",\u0103:"a",\u0105:"a",\u0106:"C",\u0108:"C",\u010A:"C",\u010C:"C",\u0107:"c",\u0109:"c",\u010B:"c",\u010D:"c",\u010E:"D",\u0110:"D",\u010F:"d",\u0111:"d",\u0112:"E",\u0114:"E",\u0116:"E",\u0118:"E",\u011A:"E",\u0113:"e",\u0115:"e",\u0117:"e",\u0119:"e",\u011B:"e",\u011C:"G",\u011E:"G",\u0120:"G",\u0122:"G",\u011D:"g",\u011F:"g",\u0121:"g",\u0123:"g",\u0124:"H",\u0126:"H",\u0125:"h",\u0127:"h",\u0128:"I",\u012A:"I",\u012C:"I",\u012E:"I",\u0130:"I",\u0129:"i",\u012B:"i",\u012D:"i",\u012F:"i",\u0131:"i",\u0134:"J",\u0135:"j",\u0136:"K",\u0137:"k",\u0138:"k",\u0139:"L",\u013B:"L",\u013D:"L",\u013F:"L",\u0141:"L",\u013A:"l",\u013C:"l",\u013E:"l",\u0140:"l",\u0142:"l",\u0143:"N",\u0145:"N",\u0147:"N",\u014A:"N",\u0144:"n",\u0146:"n",\u0148:"n",\u014B:"n",\u014C:"O",\u014E:"O",\u0150:"O",\u014D:"o",\u014F:"o",\u0151:"o",\u0154:"R",\u0156:"R",\u0158:"R",\u0155:"r",\u0157:"r",\u0159:"r",\u015A:"S",\u015C:"S",\u015E:"S",\u0160:"S",\u015B:"s",\u015D:"s",\u015F:"s",\u0161:"s",\u0162:"T",\u0164:"T",\u0166:"T",\u0163:"t",\u0165:"t",\u0167:"t",\u0168:"U",\u016A:"U",\u016C:"U",\u016E:"U",\u0170:"U",\u0172:"U",\u0169:"u",\u016B:"u",\u016D:"u",\u016F:"u",\u0171:"u",\u0173:"u",\u0174:"W",\u0175:"w",\u0176:"Y",\u0177:"y",\u0178:"Y",\u0179:"Z",\u017B:"Z",\u017D:"Z",\u017A:"z",\u017C:"z",\u017E:"z",\u0132:"IJ",\u0133:"ij",\u0152:"Oe",\u0153:"oe",\u0149:"'n",\u017F:"s"},yu={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Nu={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Lu={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Ou=parseFloat,Su=parseInt,c0=typeof global=="object"&&global&&global.Object===Object&&global,Pu=typeof self=="object"&&self&&self.Object===Object&&self,Ve=c0||Pu||Function("return this")(),t2=typeof Ir=="object"&&Ir&&!Ir.nodeType&&Ir,er=t2&&typeof bi=="object"&&bi&&!bi.nodeType&&bi,f0=er&&er.exports===t2,n2=f0&&c0.process,Dt=function(){try{var N=er&&er.require&&er.require("util").types;return N||n2&&n2.binding&&n2.binding("util")}catch(k){}}(),h0=Dt&&Dt.isArrayBuffer,d0=Dt&&Dt.isDate,g0=Dt&&Dt.isMap,p0=Dt&&Dt.isRegExp,m0=Dt&&Dt.isSet,w0=Dt&&Dt.isTypedArray;function Rt(N,k,W){switch(W.length){case 0:return N.call(k);case 1:return N.call(k,W[0]);case 2:return N.call(k,W[0],W[1]);case 3:return N.call(k,W[0],W[1],W[2])}return N.apply(k,W)}function Iu(N,k,W,ee){for(var fe=-1,Te=N==null?0:N.length;++fe<Te;){var ze=N[fe];k(ee,ze,W(ze),N)}return ee}function Ft(N,k){for(var W=-1,ee=N==null?0:N.length;++W<ee&&k(N[W],W,N)!==!1;);return N}function Wu(N,k){for(var W=N==null?0:N.length;W--&&k(N[W],W,N)!==!1;);return N}function C0(N,k){for(var W=-1,ee=N==null?0:N.length;++W<ee;)if(!k(N[W],W,N))return!1;return!0}function xn(N,k){for(var W=-1,ee=N==null?0:N.length,fe=0,Te=[];++W<ee;){var ze=N[W];k(ze,W,N)&&(Te[fe++]=ze)}return Te}function Hi(N,k){var W=N==null?0:N.length;return!!W&&_r(N,k,0)>-1}function r2(N,k,W){for(var ee=-1,fe=N==null?0:N.length;++ee<fe;)if(W(k,N[ee]))return!0;return!1}function Fe(N,k){for(var W=-1,ee=N==null?0:N.length,fe=Array(ee);++W<ee;)fe[W]=k(N[W],W,N);return fe}function yn(N,k){for(var W=-1,ee=k.length,fe=N.length;++W<ee;)N[fe+W]=k[W];return N}function i2(N,k,W,ee){var fe=-1,Te=N==null?0:N.length;for(ee&&Te&&(W=N[++fe]);++fe<Te;)W=k(W,N[fe],fe,N);return W}function Mu(N,k,W,ee){var fe=N==null?0:N.length;for(ee&&fe&&(W=N[--fe]);fe--;)W=k(W,N[fe],fe,N);return W}function o2(N,k){for(var W=-1,ee=N==null?0:N.length;++W<ee;)if(k(N[W],W,N))return!0;return!1}var Du=s2("length");function Fu(N){return N.split("")}function ku(N){return N.match(Ko)||[]}function v0(N,k,W){var ee;return W(N,function(fe,Te,ze){if(k(fe,Te,ze))return ee=Te,!1}),ee}function Bi(N,k,W,ee){for(var fe=N.length,Te=W+(ee?1:-1);ee?Te--:++Te<fe;)if(k(N[Te],Te,N))return Te;return-1}function _r(N,k,W){return k===k?Ju(N,k,W):Bi(N,_0,W)}function Uu(N,k,W,ee){for(var fe=W-1,Te=N.length;++fe<Te;)if(ee(N[fe],k))return fe;return-1}function _0(N){return N!==N}function b0(N,k){var W=N==null?0:N.length;return W?u2(N,k)/W:Ae}function s2(N){return function(k){return k==null?s:k[N]}}function l2(N){return function(k){return N==null?s:N[k]}}function E0(N,k,W,ee,fe){return fe(N,function(Te,ze,Se){W=ee?(ee=!1,Te):k(W,Te,ze,Se)}),W}function qu(N,k){var W=N.length;for(N.sort(k);W--;)N[W]=N[W].value;return N}function u2(N,k){for(var W,ee=-1,fe=N.length;++ee<fe;){var Te=k(N[ee]);Te!==s&&(W=W===s?Te:W+Te)}return W}function a2(N,k){for(var W=-1,ee=Array(N);++W<N;)ee[W]=k(W);return ee}function Hu(N,k){return Fe(k,function(W){return[W,N[W]]})}function A0(N){return N&&N.slice(0,y0(N)+1).replace(ni,"")}function xt(N){return function(k){return N(k)}}function c2(N,k){return Fe(k,function(W){return N[W]})}function ii(N,k){return N.has(k)}function T0(N,k){for(var W=-1,ee=N.length;++W<ee&&_r(k,N[W],0)>-1;);return W}function R0(N,k){for(var W=N.length;W--&&_r(k,N[W],0)>-1;);return W}function Bu(N,k){for(var W=N.length,ee=0;W--;)N[W]===k&&++ee;return ee}var zu=l2(xu),$u=l2(yu);function Zu(N){return"\\"+Lu[N]}function Gu(N,k){return N==null?s:N[k]}function br(N){return Eu.test(N)}function Vu(N){return Au.test(N)}function Ku(N){for(var k,W=[];!(k=N.next()).done;)W.push(k.value);return W}function f2(N){var k=-1,W=Array(N.size);return N.forEach(function(ee,fe){W[++k]=[fe,ee]}),W}function x0(N,k){return function(W){return N(k(W))}}function Nn(N,k){for(var W=-1,ee=N.length,fe=0,Te=[];++W<ee;){var ze=N[W];(ze===k||ze===A)&&(N[W]=A,Te[fe++]=W)}return Te}function zi(N){var k=-1,W=Array(N.size);return N.forEach(function(ee){W[++k]=ee}),W}function Yu(N){var k=-1,W=Array(N.size);return N.forEach(function(ee){W[++k]=[ee,ee]}),W}function Ju(N,k,W){for(var ee=W-1,fe=N.length;++ee<fe;)if(N[ee]===k)return ee;return-1}function Xu(N,k,W){for(var ee=W+1;ee--;)if(N[ee]===k)return ee;return ee}function Er(N){return br(N)?ju(N):Du(N)}function Vt(N){return br(N)?e4(N):Fu(N)}function y0(N){for(var k=N.length;k--&&$o.test(N.charAt(k)););return k}var Qu=l2(Nu);function ju(N){for(var k=e2.lastIndex=0;e2.test(N);)++k;return k}function e4(N){return N.match(e2)||[]}function t4(N){return N.match(bu)||[]}var n4=function N(k){k=k==null?Ve:Ln.defaults(Ve.Object(),k,Ln.pick(Ve,Tu));var W=k.Array,ee=k.Date,fe=k.Error,Te=k.Function,ze=k.Math,Se=k.Object,h2=k.RegExp,r4=k.String,kt=k.TypeError,$i=W.prototype,i4=Te.prototype,Ar=Se.prototype,Zi=k["__core-js_shared__"],Gi=i4.toString,Ne=Ar.hasOwnProperty,o4=0,N0=function(){var e=/[^.]+$/.exec(Zi&&Zi.keys&&Zi.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Vi=Ar.toString,s4=Gi.call(Se),l4=Ve._,u4=h2("^"+Gi.call(Ne).replace(ti,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ki=f0?k.Buffer:s,On=k.Symbol,Yi=k.Uint8Array,L0=Ki?Ki.allocUnsafe:s,Ji=x0(Se.getPrototypeOf,Se),O0=Se.create,S0=Ar.propertyIsEnumerable,Xi=$i.splice,P0=On?On.isConcatSpreadable:s,oi=On?On.iterator:s,tr=On?On.toStringTag:s,Qi=function(){try{var e=sr(Se,"defineProperty");return e({},"",{}),e}catch(t){}}(),a4=k.clearTimeout!==Ve.clearTimeout&&k.clearTimeout,c4=ee&&ee.now!==Ve.Date.now&&ee.now,f4=k.setTimeout!==Ve.setTimeout&&k.setTimeout,ji=ze.ceil,e1=ze.floor,d2=Se.getOwnPropertySymbols,h4=Ki?Ki.isBuffer:s,I0=k.isFinite,d4=$i.join,g4=x0(Se.keys,Se),$e=ze.max,tt=ze.min,p4=ee.now,m4=k.parseInt,W0=ze.random,w4=$i.reverse,g2=sr(k,"DataView"),si=sr(k,"Map"),p2=sr(k,"Promise"),Tr=sr(k,"Set"),li=sr(k,"WeakMap"),ui=sr(Se,"create"),t1=li&&new li,Rr={},C4=lr(g2),v4=lr(si),_4=lr(p2),b4=lr(Tr),E4=lr(li),n1=On?On.prototype:s,ai=n1?n1.valueOf:s,M0=n1?n1.toString:s;function w(e){if(Ue(e)&&!he(e)&&!(e instanceof be)){if(e instanceof Ut)return e;if(Ne.call(e,"__wrapped__"))return Ds(e)}return new Ut(e)}var xr=function(){function e(){}return function(t){if(!ke(t))return{};if(O0)return O0(t);e.prototype=t;var n=new e;return e.prototype=s,n}}();function r1(){}function Ut(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=s}w.templateSettings={escape:ko,evaluate:Uo,interpolate:Ui,variable:"",imports:{_:w}},w.prototype=r1.prototype,w.prototype.constructor=w,Ut.prototype=xr(r1.prototype),Ut.prototype.constructor=Ut;function be(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=j,this.__views__=[]}function A4(){var e=new be(this.__wrapped__);return e.__actions__=dt(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=dt(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=dt(this.__views__),e}function T4(){if(this.__filtered__){var e=new be(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function R4(){var e=this.__wrapped__.value(),t=this.__dir__,n=he(e),i=t<0,h=n?e.length:0,v=F6(0,h,this.__views__),b=v.start,R=v.end,O=R-b,q=i?R:b-1,H=this.__iteratees__,Z=H.length,X=0,ne=tt(O,this.__takeCount__);if(!n||!i&&h==O&&ne==O)return os(e,this.__actions__);var ue=[];e:for(;O--&&X<ne;){q+=t;for(var pe=-1,ae=e[q];++pe<Z;){var ve=H[pe],Ee=ve.iteratee,Lt=ve.type,at=Ee(ae);if(Lt==Oe)ae=at;else if(!at){if(Lt==B)continue e;break e}}ue[X++]=ae}return ue}be.prototype=xr(r1.prototype),be.prototype.constructor=be;function nr(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function x4(){this.__data__=ui?ui(null):{},this.size=0}function y4(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}function N4(e){var t=this.__data__;if(ui){var n=t[e];return n===_?s:n}return Ne.call(t,e)?t[e]:s}function L4(e){var t=this.__data__;return ui?t[e]!==s:Ne.call(t,e)}function O4(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=ui&&t===s?_:t,this}nr.prototype.clear=x4,nr.prototype.delete=y4,nr.prototype.get=N4,nr.prototype.has=L4,nr.prototype.set=O4;function fn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function S4(){this.__data__=[],this.size=0}function P4(e){var t=this.__data__,n=i1(t,e);if(n<0)return!1;var i=t.length-1;return n==i?t.pop():Xi.call(t,n,1),--this.size,!0}function I4(e){var t=this.__data__,n=i1(t,e);return n<0?s:t[n][1]}function W4(e){return i1(this.__data__,e)>-1}function M4(e,t){var n=this.__data__,i=i1(n,e);return i<0?(++this.size,n.push([e,t])):n[i][1]=t,this}fn.prototype.clear=S4,fn.prototype.delete=P4,fn.prototype.get=I4,fn.prototype.has=W4,fn.prototype.set=M4;function hn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function D4(){this.size=0,this.__data__={hash:new nr,map:new(si||fn),string:new nr}}function F4(e){var t=m1(this,e).delete(e);return this.size-=t?1:0,t}function k4(e){return m1(this,e).get(e)}function U4(e){return m1(this,e).has(e)}function q4(e,t){var n=m1(this,e),i=n.size;return n.set(e,t),this.size+=n.size==i?0:1,this}hn.prototype.clear=D4,hn.prototype.delete=F4,hn.prototype.get=k4,hn.prototype.has=U4,hn.prototype.set=q4;function rr(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new hn;++t<n;)this.add(e[t])}function H4(e){return this.__data__.set(e,_),this}function B4(e){return this.__data__.has(e)}rr.prototype.add=rr.prototype.push=H4,rr.prototype.has=B4;function Kt(e){var t=this.__data__=new fn(e);this.size=t.size}function z4(){this.__data__=new fn,this.size=0}function $4(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}function Z4(e){return this.__data__.get(e)}function G4(e){return this.__data__.has(e)}function V4(e,t){var n=this.__data__;if(n instanceof fn){var i=n.__data__;if(!si||i.length<l-1)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new hn(i)}return n.set(e,t),this.size=n.size,this}Kt.prototype.clear=z4,Kt.prototype.delete=$4,Kt.prototype.get=Z4,Kt.prototype.has=G4,Kt.prototype.set=V4;function D0(e,t){var n=he(e),i=!n&&ur(e),h=!n&&!i&&Mn(e),v=!n&&!i&&!h&&Or(e),b=n||i||h||v,R=b?a2(e.length,r4):[],O=R.length;for(var q in e)(t||Ne.call(e,q))&&!(b&&(q=="length"||h&&(q=="offset"||q=="parent")||v&&(q=="buffer"||q=="byteLength"||q=="byteOffset")||mn(q,O)))&&R.push(q);return R}function F0(e){var t=e.length;return t?e[x2(0,t-1)]:s}function K4(e,t){return w1(dt(e),ir(t,0,e.length))}function Y4(e){return w1(dt(e))}function m2(e,t,n){(n!==s&&!Yt(e[t],n)||n===s&&!(t in e))&&dn(e,t,n)}function ci(e,t,n){var i=e[t];(!(Ne.call(e,t)&&Yt(i,n))||n===s&&!(t in e))&&dn(e,t,n)}function i1(e,t){for(var n=e.length;n--;)if(Yt(e[n][0],t))return n;return-1}function J4(e,t,n,i){return Sn(e,function(h,v,b){t(i,h,n(h),b)}),i}function k0(e,t){return e&&on(t,Ke(t),e)}function X4(e,t){return e&&on(t,pt(t),e)}function dn(e,t,n){t=="__proto__"&&Qi?Qi(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function w2(e,t){for(var n=-1,i=t.length,h=W(i),v=e==null;++n<i;)h[n]=v?s:X2(e,t[n]);return h}function ir(e,t,n){return e===e&&(n!==s&&(e=e<=n?e:n),t!==s&&(e=e>=t?e:t)),e}function qt(e,t,n,i,h,v){var b,R=t&P,O=t&y,q=t&S;if(n&&(b=h?n(e,i,h,v):n(e)),b!==s)return b;if(!ke(e))return e;var H=he(e);if(H){if(b=U6(e),!R)return dt(e,b)}else{var Z=nt(e),X=Z==bt||Z==Zr;if(Mn(e))return us(e,R);if(Z==et||Z==Ze||X&&!h){if(b=O||X?{}:ys(e),!R)return O?N6(e,X4(b,e)):y6(e,k0(b,e))}else{if(!Ie[Z])return h?e:{};b=q6(e,Z,R)}}v||(v=new Kt);var ne=v.get(e);if(ne)return ne;v.set(e,b),nl(e)?e.forEach(function(ae){b.add(qt(ae,t,n,ae,e,v))}):el(e)&&e.forEach(function(ae,ve){b.set(ve,qt(ae,t,n,ve,e,v))});var ue=q?O?F2:D2:O?pt:Ke,pe=H?s:ue(e);return Ft(pe||e,function(ae,ve){pe&&(ve=ae,ae=e[ve]),ci(b,ve,qt(ae,t,n,ve,e,v))}),b}function Q4(e){var t=Ke(e);return function(n){return U0(n,e,t)}}function U0(e,t,n){var i=n.length;if(e==null)return!i;for(e=Se(e);i--;){var h=n[i],v=t[h],b=e[h];if(b===s&&!(h in e)||!v(b))return!1}return!0}function q0(e,t,n){if(typeof e!="function")throw new kt(d);return wi(function(){e.apply(s,n)},t)}function fi(e,t,n,i){var h=-1,v=Hi,b=!0,R=e.length,O=[],q=t.length;if(!R)return O;n&&(t=Fe(t,xt(n))),i?(v=r2,b=!1):t.length>=l&&(v=ii,b=!1,t=new rr(t));e:for(;++h<R;){var H=e[h],Z=n==null?H:n(H);if(H=i||H!==0?H:0,b&&Z===Z){for(var X=q;X--;)if(t[X]===Z)continue e;O.push(H)}else v(t,Z,i)||O.push(H)}return O}var Sn=ds(rn),H0=ds(v2,!0);function j4(e,t){var n=!0;return Sn(e,function(i,h,v){return n=!!t(i,h,v),n}),n}function o1(e,t,n){for(var i=-1,h=e.length;++i<h;){var v=e[i],b=t(v);if(b!=null&&(R===s?b===b&&!Nt(b):n(b,R)))var R=b,O=v}return O}function e6(e,t,n,i){var h=e.length;for(n=de(n),n<0&&(n=-n>h?0:h+n),i=i===s||i>h?h:de(i),i<0&&(i+=h),i=n>i?0:il(i);n<i;)e[n++]=t;return e}function B0(e,t){var n=[];return Sn(e,function(i,h,v){t(i,h,v)&&n.push(i)}),n}function Je(e,t,n,i,h){var v=-1,b=e.length;for(n||(n=B6),h||(h=[]);++v<b;){var R=e[v];t>0&&n(R)?t>1?Je(R,t-1,n,i,h):yn(h,R):i||(h[h.length]=R)}return h}var C2=gs(),z0=gs(!0);function rn(e,t){return e&&C2(e,t,Ke)}function v2(e,t){return e&&z0(e,t,Ke)}function s1(e,t){return xn(t,function(n){return wn(e[n])})}function or(e,t){t=In(t,e);for(var n=0,i=t.length;e!=null&&n<i;)e=e[sn(t[n++])];return n&&n==i?e:s}function $0(e,t,n){var i=t(e);return he(e)?i:yn(i,n(e))}function lt(e){return e==null?e===s?So:Oo:tr&&tr in Se(e)?D6(e):Y6(e)}function _2(e,t){return e>t}function t6(e,t){return e!=null&&Ne.call(e,t)}function n6(e,t){return e!=null&&t in Se(e)}function r6(e,t,n){return e>=tt(t,n)&&e<$e(t,n)}function b2(e,t,n){for(var i=n?r2:Hi,h=e[0].length,v=e.length,b=v,R=W(v),O=1/0,q=[];b--;){var H=e[b];b&&t&&(H=Fe(H,xt(t))),O=tt(H.length,O),R[b]=!n&&(t||h>=120&&H.length>=120)?new rr(b&&H):s}H=e[0];var Z=-1,X=R[0];e:for(;++Z<h&&q.length<O;){var ne=H[Z],ue=t?t(ne):ne;if(ne=n||ne!==0?ne:0,!(X?ii(X,ue):i(q,ue,n))){for(b=v;--b;){var pe=R[b];if(!(pe?ii(pe,ue):i(e[b],ue,n)))continue e}X&&X.push(ue),q.push(ne)}}return q}function i6(e,t,n,i){return rn(e,function(h,v,b){t(i,n(h),v,b)}),i}function hi(e,t,n){t=In(t,e),e=Ss(e,t);var i=e==null?e:e[sn(Bt(t))];return i==null?s:Rt(i,e,n)}function Z0(e){return Ue(e)&&lt(e)==Ze}function o6(e){return Ue(e)&&lt(e)==Kn}function s6(e){return Ue(e)&&lt(e)==An}function di(e,t,n,i,h){return e===t?!0:e==null||t==null||!Ue(e)&&!Ue(t)?e!==e&&t!==t:l6(e,t,n,i,di,h)}function l6(e,t,n,i,h,v){var b=he(e),R=he(t),O=b?zn:nt(e),q=R?zn:nt(t);O=O==Ze?et:O,q=q==Ze?et:q;var H=O==et,Z=q==et,X=O==q;if(X&&Mn(e)){if(!Mn(t))return!1;b=!0,H=!1}if(X&&!H)return v||(v=new Kt),b||Or(e)?Ts(e,t,n,i,h,v):W6(e,t,O,n,i,h,v);if(!(n&V)){var ne=H&&Ne.call(e,"__wrapped__"),ue=Z&&Ne.call(t,"__wrapped__");if(ne||ue){var pe=ne?e.value():e,ae=ue?t.value():t;return v||(v=new Kt),h(pe,ae,n,i,v)}}return X?(v||(v=new Kt),M6(e,t,n,i,h,v)):!1}function u6(e){return Ue(e)&&nt(e)==st}function E2(e,t,n,i){var h=n.length,v=h,b=!i;if(e==null)return!v;for(e=Se(e);h--;){var R=n[h];if(b&&R[2]?R[1]!==e[R[0]]:!(R[0]in e))return!1}for(;++h<v;){R=n[h];var O=R[0],q=e[O],H=R[1];if(b&&R[2]){if(q===s&&!(O in e))return!1}else{var Z=new Kt;if(i)var X=i(q,H,O,e,t,Z);if(!(X===s?di(H,q,V|M,i,Z):X))return!1}}return!0}function G0(e){if(!ke(e)||$6(e))return!1;var t=wn(e)?u4:a;return t.test(lr(e))}function a6(e){return Ue(e)&&lt(e)==cn}function c6(e){return Ue(e)&&nt(e)==ht}function f6(e){return Ue(e)&&A1(e.length)&&!!De[lt(e)]}function V0(e){return typeof e=="function"?e:e==null?mt:typeof e=="object"?he(e)?J0(e[0],e[1]):Y0(e):pl(e)}function A2(e){if(!mi(e))return g4(e);var t=[];for(var n in Se(e))Ne.call(e,n)&&n!="constructor"&&t.push(n);return t}function h6(e){if(!ke(e))return K6(e);var t=mi(e),n=[];for(var i in e)i=="constructor"&&(t||!Ne.call(e,i))||n.push(i);return n}function T2(e,t){return e<t}function K0(e,t){var n=-1,i=gt(e)?W(e.length):[];return Sn(e,function(h,v,b){i[++n]=t(h,v,b)}),i}function Y0(e){var t=U2(e);return t.length==1&&t[0][2]?Ls(t[0][0],t[0][1]):function(n){return n===e||E2(n,e,t)}}function J0(e,t){return H2(e)&&Ns(t)?Ls(sn(e),t):function(n){var i=X2(n,e);return i===s&&i===t?Q2(n,e):di(t,i,V|M)}}function l1(e,t,n,i,h){e!==t&&C2(t,function(v,b){if(h||(h=new Kt),ke(v))d6(e,t,b,n,l1,i,h);else{var R=i?i(z2(e,b),v,b+"",e,t,h):s;R===s&&(R=v),m2(e,b,R)}},pt)}function d6(e,t,n,i,h,v,b){var R=z2(e,n),O=z2(t,n),q=b.get(O);if(q){m2(e,n,q);return}var H=v?v(R,O,n+"",e,t,b):s,Z=H===s;if(Z){var X=he(O),ne=!X&&Mn(O),ue=!X&&!ne&&Or(O);H=O,X||ne||ue?he(R)?H=R:qe(R)?H=dt(R):ne?(Z=!1,H=us(O,!0)):ue?(Z=!1,H=as(O,!0)):H=[]:Ci(O)||ur(O)?(H=R,ur(R)?H=ol(R):(!ke(R)||wn(R))&&(H=ys(O))):Z=!1}Z&&(b.set(O,H),h(H,O,i,v,b),b.delete(O)),m2(e,n,H)}function X0(e,t){var n=e.length;if(n)return t+=t<0?n:0,mn(t,n)?e[t]:s}function Q0(e,t,n){t.length?t=Fe(t,function(v){return he(v)?function(b){return or(b,v.length===1?v[0]:v)}:v}):t=[mt];var i=-1;t=Fe(t,xt(le()));var h=K0(e,function(v,b,R){var O=Fe(t,function(q){return q(v)});return{criteria:O,index:++i,value:v}});return qu(h,function(v,b){return x6(v,b,n)})}function g6(e,t){return j0(e,t,function(n,i){return Q2(e,i)})}function j0(e,t,n){for(var i=-1,h=t.length,v={};++i<h;){var b=t[i],R=or(e,b);n(R,b)&&gi(v,In(b,e),R)}return v}function p6(e){return function(t){return or(t,e)}}function R2(e,t,n,i){var h=i?Uu:_r,v=-1,b=t.length,R=e;for(e===t&&(t=dt(t)),n&&(R=Fe(e,xt(n)));++v<b;)for(var O=0,q=t[v],H=n?n(q):q;(O=h(R,H,O,i))>-1;)R!==e&&Xi.call(R,O,1),Xi.call(e,O,1);return e}function es(e,t){for(var n=e?t.length:0,i=n-1;n--;){var h=t[n];if(n==i||h!==v){var v=h;mn(h)?Xi.call(e,h,1):L2(e,h)}}return e}function x2(e,t){return e+e1(W0()*(t-e+1))}function m6(e,t,n,i){for(var h=-1,v=$e(ji((t-e)/(n||1)),0),b=W(v);v--;)b[i?v:++h]=e,e+=n;return b}function y2(e,t){var n="";if(!e||t<1||t>_t)return n;do t%2&&(n+=e),t=e1(t/2),t&&(e+=e);while(t);return n}function me(e,t){return $2(Os(e,t,mt),e+"")}function w6(e){return F0(Sr(e))}function C6(e,t){var n=Sr(e);return w1(n,ir(t,0,n.length))}function gi(e,t,n,i){if(!ke(e))return e;t=In(t,e);for(var h=-1,v=t.length,b=v-1,R=e;R!=null&&++h<v;){var O=sn(t[h]),q=n;if(O==="__proto__"||O==="constructor"||O==="prototype")return e;if(h!=b){var H=R[O];q=i?i(H,O,R):s,q===s&&(q=ke(H)?H:mn(t[h+1])?[]:{})}ci(R,O,q),R=R[O]}return e}var ts=t1?function(e,t){return t1.set(e,t),e}:mt,v6=Qi?function(e,t){return Qi(e,"toString",{configurable:!0,enumerable:!1,value:e3(t),writable:!0})}:mt;function _6(e){return w1(Sr(e))}function Ht(e,t,n){var i=-1,h=e.length;t<0&&(t=-t>h?0:h+t),n=n>h?h:n,n<0&&(n+=h),h=t>n?0:n-t>>>0,t>>>=0;for(var v=W(h);++i<h;)v[i]=e[i+t];return v}function b6(e,t){var n;return Sn(e,function(i,h,v){return n=t(i,h,v),!n}),!!n}function u1(e,t,n){var i=0,h=e==null?i:e.length;if(typeof t=="number"&&t===t&&h<=gr){for(;i<h;){var v=i+h>>>1,b=e[v];b!==null&&!Nt(b)&&(n?b<=t:b<t)?i=v+1:h=v}return h}return N2(e,t,mt,n)}function N2(e,t,n,i){var h=0,v=e==null?0:e.length;if(v===0)return 0;t=n(t);for(var b=t!==t,R=t===null,O=Nt(t),q=t===s;h<v;){var H=e1((h+v)/2),Z=n(e[H]),X=Z!==s,ne=Z===null,ue=Z===Z,pe=Nt(Z);if(b)var ae=i||ue;else q?ae=ue&&(i||X):R?ae=ue&&X&&(i||!ne):O?ae=ue&&X&&!ne&&(i||!pe):ne||pe?ae=!1:ae=i?Z<=t:Z<t;ae?h=H+1:v=H}return tt(v,Mt)}function ns(e,t){for(var n=-1,i=e.length,h=0,v=[];++n<i;){var b=e[n],R=t?t(b):b;if(!n||!Yt(R,O)){var O=R;v[h++]=b===0?0:b}}return v}function rs(e){return typeof e=="number"?e:Nt(e)?Ae:+e}function yt(e){if(typeof e=="string")return e;if(he(e))return Fe(e,yt)+"";if(Nt(e))return M0?M0.call(e):"";var t=e+"";return t=="0"&&1/e==-vt?"-0":t}function Pn(e,t,n){var i=-1,h=Hi,v=e.length,b=!0,R=[],O=R;if(n)b=!1,h=r2;else if(v>=l){var q=t?null:P6(e);if(q)return zi(q);b=!1,h=ii,O=new rr}else O=t?[]:R;e:for(;++i<v;){var H=e[i],Z=t?t(H):H;if(H=n||H!==0?H:0,b&&Z===Z){for(var X=O.length;X--;)if(O[X]===Z)continue e;t&&O.push(Z),R.push(H)}else h(O,Z,n)||(O!==R&&O.push(Z),R.push(H))}return R}function L2(e,t){return t=In(t,e),e=Ss(e,t),e==null||delete e[sn(Bt(t))]}function is(e,t,n,i){return gi(e,t,n(or(e,t)),i)}function a1(e,t,n,i){for(var h=e.length,v=i?h:-1;(i?v--:++v<h)&&t(e[v],v,e););return n?Ht(e,i?0:v,i?v+1:h):Ht(e,i?v+1:0,i?h:v)}function os(e,t){var n=e;return n instanceof be&&(n=n.value()),i2(t,function(i,h){return h.func.apply(h.thisArg,yn([i],h.args))},n)}function O2(e,t,n){var i=e.length;if(i<2)return i?Pn(e[0]):[];for(var h=-1,v=W(i);++h<i;)for(var b=e[h],R=-1;++R<i;)R!=h&&(v[h]=fi(v[h]||b,e[R],t,n));return Pn(Je(v,1),t,n)}function ss(e,t,n){for(var i=-1,h=e.length,v=t.length,b={};++i<h;){var R=i<v?t[i]:s;n(b,e[i],R)}return b}function S2(e){return qe(e)?e:[]}function P2(e){return typeof e=="function"?e:mt}function In(e,t){return he(e)?e:H2(e,t)?[e]:Ms(xe(e))}var E6=me;function Wn(e,t,n){var i=e.length;return n=n===s?i:n,!t&&n>=i?e:Ht(e,t,n)}var ls=a4||function(e){return Ve.clearTimeout(e)};function us(e,t){if(t)return e.slice();var n=e.length,i=L0?L0(n):new e.constructor(n);return e.copy(i),i}function I2(e){var t=new e.constructor(e.byteLength);return new Yi(t).set(new Yi(e)),t}function A6(e,t){var n=t?I2(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}function T6(e){var t=new e.constructor(e.source,qi.exec(e));return t.lastIndex=e.lastIndex,t}function R6(e){return ai?Se(ai.call(e)):{}}function as(e,t){var n=t?I2(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function cs(e,t){if(e!==t){var n=e!==s,i=e===null,h=e===e,v=Nt(e),b=t!==s,R=t===null,O=t===t,q=Nt(t);if(!R&&!q&&!v&&e>t||v&&b&&O&&!R&&!q||i&&b&&O||!n&&O||!h)return 1;if(!i&&!v&&!q&&e<t||q&&n&&h&&!i&&!v||R&&n&&h||!b&&h||!O)return-1}return 0}function x6(e,t,n){for(var i=-1,h=e.criteria,v=t.criteria,b=h.length,R=n.length;++i<b;){var O=cs(h[i],v[i]);if(O){if(i>=R)return O;var q=n[i];return O*(q=="desc"?-1:1)}}return e.index-t.index}function fs(e,t,n,i){for(var h=-1,v=e.length,b=n.length,R=-1,O=t.length,q=$e(v-b,0),H=W(O+q),Z=!i;++R<O;)H[R]=t[R];for(;++h<b;)(Z||h<v)&&(H[n[h]]=e[h]);for(;q--;)H[R++]=e[h++];return H}function hs(e,t,n,i){for(var h=-1,v=e.length,b=-1,R=n.length,O=-1,q=t.length,H=$e(v-R,0),Z=W(H+q),X=!i;++h<H;)Z[h]=e[h];for(var ne=h;++O<q;)Z[ne+O]=t[O];for(;++b<R;)(X||h<v)&&(Z[ne+n[b]]=e[h++]);return Z}function dt(e,t){var n=-1,i=e.length;for(t||(t=W(i));++n<i;)t[n]=e[n];return t}function on(e,t,n,i){var h=!n;n||(n={});for(var v=-1,b=t.length;++v<b;){var R=t[v],O=i?i(n[R],e[R],R,n,e):s;O===s&&(O=e[R]),h?dn(n,R,O):ci(n,R,O)}return n}function y6(e,t){return on(e,q2(e),t)}function N6(e,t){return on(e,Rs(e),t)}function c1(e,t){return function(n,i){var h=he(n)?Iu:J4,v=t?t():{};return h(n,e,le(i,2),v)}}function yr(e){return me(function(t,n){var i=-1,h=n.length,v=h>1?n[h-1]:s,b=h>2?n[2]:s;for(v=e.length>3&&typeof v=="function"?(h--,v):s,b&&ut(n[0],n[1],b)&&(v=h<3?s:v,h=1),t=Se(t);++i<h;){var R=n[i];R&&e(t,R,i,v)}return t})}function ds(e,t){return function(n,i){if(n==null)return n;if(!gt(n))return e(n,i);for(var h=n.length,v=t?h:-1,b=Se(n);(t?v--:++v<h)&&i(b[v],v,b)!==!1;);return n}}function gs(e){return function(t,n,i){for(var h=-1,v=Se(t),b=i(t),R=b.length;R--;){var O=b[e?R:++h];if(n(v[O],O,v)===!1)break}return t}}function L6(e,t,n){var i=t&Y,h=pi(e);function v(){var b=this&&this!==Ve&&this instanceof v?h:e;return b.apply(i?n:this,arguments)}return v}function ps(e){return function(t){t=xe(t);var n=br(t)?Vt(t):s,i=n?n[0]:t.charAt(0),h=n?Wn(n,1).join(""):t.slice(1);return i[e]()+h}}function Nr(e){return function(t){return i2(dl(hl(t).replace(vu,"")),e,"")}}function pi(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=xr(e.prototype),i=e.apply(n,t);return ke(i)?i:n}}function O6(e,t,n){var i=pi(e);function h(){for(var v=arguments.length,b=W(v),R=v,O=Lr(h);R--;)b[R]=arguments[R];var q=v<3&&b[0]!==O&&b[v-1]!==O?[]:Nn(b,O);if(v-=q.length,v<n)return _s(e,t,f1,h.placeholder,s,b,q,s,s,n-v);var H=this&&this!==Ve&&this instanceof h?i:e;return Rt(H,this,b)}return h}function ms(e){return function(t,n,i){var h=Se(t);if(!gt(t)){var v=le(n,3);t=Ke(t),n=function(R){return v(h[R],R,h)}}var b=e(t,n,i);return b>-1?h[v?t[b]:b]:s}}function ws(e){return pn(function(t){var n=t.length,i=n,h=Ut.prototype.thru;for(e&&t.reverse();i--;){var v=t[i];if(typeof v!="function")throw new kt(d);if(h&&!b&&p1(v)=="wrapper")var b=new Ut([],!0)}for(i=b?i:n;++i<n;){v=t[i];var R=p1(v),O=R=="wrapper"?k2(v):s;O&&B2(O[0])&&O[1]==(te|D|U|J)&&!O[4].length&&O[9]==1?b=b[p1(O[0])].apply(b,O[3]):b=v.length==1&&B2(v)?b[R]():b.thru(v)}return function(){var q=arguments,H=q[0];if(b&&q.length==1&&he(H))return b.plant(H).value();for(var Z=0,X=n?t[Z].apply(this,q):H;++Z<n;)X=t[Z].call(this,X);return X}})}function f1(e,t,n,i,h,v,b,R,O,q){var H=t&te,Z=t&Y,X=t&se,ne=t&(D|$),ue=t&Q,pe=X?s:pi(e);function ae(){for(var ve=arguments.length,Ee=W(ve),Lt=ve;Lt--;)Ee[Lt]=arguments[Lt];if(ne)var at=Lr(ae),Ot=Bu(Ee,at);if(i&&(Ee=fs(Ee,i,h,ne)),v&&(Ee=hs(Ee,v,b,ne)),ve-=Ot,ne&&ve<q){var He=Nn(Ee,at);return _s(e,t,f1,ae.placeholder,n,Ee,He,R,O,q-ve)}var Jt=Z?n:this,vn=X?Jt[e]:e;return ve=Ee.length,R?Ee=J6(Ee,R):ue&&ve>1&&Ee.reverse(),H&&O<ve&&(Ee.length=O),this&&this!==Ve&&this instanceof ae&&(vn=pe||pi(vn)),vn.apply(Jt,Ee)}return ae}function Cs(e,t){return function(n,i){return i6(n,e,t(i),{})}}function h1(e,t){return function(n,i){var h;if(n===s&&i===s)return t;if(n!==s&&(h=n),i!==s){if(h===s)return i;typeof n=="string"||typeof i=="string"?(n=yt(n),i=yt(i)):(n=rs(n),i=rs(i)),h=e(n,i)}return h}}function W2(e){return pn(function(t){return t=Fe(t,xt(le())),me(function(n){var i=this;return e(t,function(h){return Rt(h,i,n)})})})}function d1(e,t){t=t===s?" ":yt(t);var n=t.length;if(n<2)return n?y2(t,e):t;var i=y2(t,ji(e/Er(t)));return br(t)?Wn(Vt(i),0,e).join(""):i.slice(0,e)}function S6(e,t,n,i){var h=t&Y,v=pi(e);function b(){for(var R=-1,O=arguments.length,q=-1,H=i.length,Z=W(H+O),X=this&&this!==Ve&&this instanceof b?v:e;++q<H;)Z[q]=i[q];for(;O--;)Z[q++]=arguments[++R];return Rt(X,h?n:this,Z)}return b}function vs(e){return function(t,n,i){return i&&typeof i!="number"&&ut(t,n,i)&&(n=i=s),t=Cn(t),n===s?(n=t,t=0):n=Cn(n),i=i===s?t<n?1:-1:Cn(i),m6(t,n,i,e)}}function g1(e){return function(t,n){return typeof t=="string"&&typeof n=="string"||(t=zt(t),n=zt(n)),e(t,n)}}function _s(e,t,n,i,h,v,b,R,O,q){var H=t&D,Z=H?b:s,X=H?s:b,ne=H?v:s,ue=H?s:v;t|=H?U:K,t&=~(H?K:U),t&ge||(t&=~(Y|se));var pe=[e,t,h,ne,Z,ue,X,R,O,q],ae=n.apply(s,pe);return B2(e)&&Ps(ae,pe),ae.placeholder=i,Is(ae,e,t)}function M2(e){var t=ze[e];return function(n,i){if(n=zt(n),i=i==null?0:tt(de(i),292),i&&I0(n)){var h=(xe(n)+"e").split("e"),v=t(h[0]+"e"+(+h[1]+i));return h=(xe(v)+"e").split("e"),+(h[0]+"e"+(+h[1]-i))}return t(n)}}var P6=Tr&&1/zi(new Tr([,-0]))[1]==vt?function(e){return new Tr(e)}:r3;function bs(e){return function(t){var n=nt(t);return n==st?f2(t):n==ht?Yu(t):Hu(t,e(t))}}function gn(e,t,n,i,h,v,b,R){var O=t&se;if(!O&&typeof e!="function")throw new kt(d);var q=i?i.length:0;if(q||(t&=~(U|K),i=h=s),b=b===s?b:$e(de(b),0),R=R===s?R:de(R),q-=h?h.length:0,t&K){var H=i,Z=h;i=h=s}var X=O?s:k2(e),ne=[e,t,n,i,h,H,Z,v,b,R];if(X&&V6(ne,X),e=ne[0],t=ne[1],n=ne[2],i=ne[3],h=ne[4],R=ne[9]=ne[9]===s?O?0:e.length:$e(ne[9]-q,0),!R&&t&(D|$)&&(t&=~(D|$)),!t||t==Y)var ue=L6(e,t,n);else t==D||t==$?ue=O6(e,t,R):(t==U||t==(Y|U))&&!h.length?ue=S6(e,t,n,i):ue=f1.apply(s,ne);var pe=X?ts:Ps;return Is(pe(ue,ne),e,t)}function Es(e,t,n,i){return e===s||Yt(e,Ar[n])&&!Ne.call(i,n)?t:e}function As(e,t,n,i,h,v){return ke(e)&&ke(t)&&(v.set(t,e),l1(e,t,s,As,v),v.delete(t)),e}function I6(e){return Ci(e)?s:e}function Ts(e,t,n,i,h,v){var b=n&V,R=e.length,O=t.length;if(R!=O&&!(b&&O>R))return!1;var q=v.get(e),H=v.get(t);if(q&&H)return q==t&&H==e;var Z=-1,X=!0,ne=n&M?new rr:s;for(v.set(e,t),v.set(t,e);++Z<R;){var ue=e[Z],pe=t[Z];if(i)var ae=b?i(pe,ue,Z,t,e,v):i(ue,pe,Z,e,t,v);if(ae!==s){if(ae)continue;X=!1;break}if(ne){if(!o2(t,function(ve,Ee){if(!ii(ne,Ee)&&(ue===ve||h(ue,ve,n,i,v)))return ne.push(Ee)})){X=!1;break}}else if(!(ue===pe||h(ue,pe,n,i,v))){X=!1;break}}return v.delete(e),v.delete(t),X}function W6(e,t,n,i,h,v,b){switch(n){case Rn:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Kn:return!(e.byteLength!=t.byteLength||!v(new Yi(e),new Yi(t)));case En:case An:case $n:return Yt(+e,+t);case Tn:return e.name==t.name&&e.message==t.message;case cn:case Gn:return e==t+"";case st:var R=f2;case ht:var O=i&V;if(R||(R=zi),e.size!=t.size&&!O)return!1;var q=b.get(e);if(q)return q==t;i|=M,b.set(e,t);var H=Ts(R(e),R(t),i,h,v,b);return b.delete(e),H;case mr:if(ai)return ai.call(e)==ai.call(t)}return!1}function M6(e,t,n,i,h,v){var b=n&V,R=D2(e),O=R.length,q=D2(t),H=q.length;if(O!=H&&!b)return!1;for(var Z=O;Z--;){var X=R[Z];if(!(b?X in t:Ne.call(t,X)))return!1}var ne=v.get(e),ue=v.get(t);if(ne&&ue)return ne==t&&ue==e;var pe=!0;v.set(e,t),v.set(t,e);for(var ae=b;++Z<O;){X=R[Z];var ve=e[X],Ee=t[X];if(i)var Lt=b?i(Ee,ve,X,t,e,v):i(ve,Ee,X,e,t,v);if(!(Lt===s?ve===Ee||h(ve,Ee,n,i,v):Lt)){pe=!1;break}ae||(ae=X=="constructor")}if(pe&&!ae){var at=e.constructor,Ot=t.constructor;at!=Ot&&"constructor"in e&&"constructor"in t&&!(typeof at=="function"&&at instanceof at&&typeof Ot=="function"&&Ot instanceof Ot)&&(pe=!1)}return v.delete(e),v.delete(t),pe}function pn(e){return $2(Os(e,s,Us),e+"")}function D2(e){return $0(e,Ke,q2)}function F2(e){return $0(e,pt,Rs)}var k2=t1?function(e){return t1.get(e)}:r3;function p1(e){for(var t=e.name+"",n=Rr[t],i=Ne.call(Rr,t)?n.length:0;i--;){var h=n[i],v=h.func;if(v==null||v==e)return h.name}return t}function Lr(e){var t=Ne.call(w,"placeholder")?w:e;return t.placeholder}function le(){var e=w.iteratee||t3;return e=e===t3?V0:e,arguments.length?e(arguments[0],arguments[1]):e}function m1(e,t){var n=e.__data__;return z6(t)?n[typeof t=="string"?"string":"hash"]:n.map}function U2(e){for(var t=Ke(e),n=t.length;n--;){var i=t[n],h=e[i];t[n]=[i,h,Ns(h)]}return t}function sr(e,t){var n=Gu(e,t);return G0(n)?n:s}function D6(e){var t=Ne.call(e,tr),n=e[tr];try{e[tr]=s;var i=!0}catch(v){}var h=Vi.call(e);return i&&(t?e[tr]=n:delete e[tr]),h}var q2=d2?function(e){return e==null?[]:(e=Se(e),xn(d2(e),function(t){return S0.call(e,t)}))}:i3,Rs=d2?function(e){for(var t=[];e;)yn(t,q2(e)),e=Ji(e);return t}:i3,nt=lt;(g2&&nt(new g2(new ArrayBuffer(1)))!=Rn||si&&nt(new si)!=st||p2&&nt(p2.resolve())!=Zn||Tr&&nt(new Tr)!=ht||li&&nt(new li)!=Vn)&&(nt=function(e){var t=lt(e),n=t==et?e.constructor:s,i=n?lr(n):"";if(i)switch(i){case C4:return Rn;case v4:return st;case _4:return Zn;case b4:return ht;case E4:return Vn}return t});function F6(e,t,n){for(var i=-1,h=n.length;++i<h;){var v=n[i],b=v.size;switch(v.type){case"drop":e+=b;break;case"dropRight":t-=b;break;case"take":t=tt(t,e+b);break;case"takeRight":e=$e(e,t-b);break}}return{start:e,end:t}}function k6(e){var t=e.match(Go);return t?t[1].split(Vo):[]}function xs(e,t,n){t=In(t,e);for(var i=-1,h=t.length,v=!1;++i<h;){var b=sn(t[i]);if(!(v=e!=null&&n(e,b)))break;e=e[b]}return v||++i!=h?v:(h=e==null?0:e.length,!!h&&A1(h)&&mn(b,h)&&(he(e)||ur(e)))}function U6(e){var t=e.length,n=new e.constructor(t);return t&&typeof e[0]=="string"&&Ne.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function ys(e){return typeof e.constructor=="function"&&!mi(e)?xr(Ji(e)):{}}function q6(e,t,n){var i=e.constructor;switch(t){case Kn:return I2(e);case En:case An:return new i(+e);case Rn:return A6(e,n);case Gr:case Vr:case Kr:case Yr:case Jr:case Xr:case Qr:case jr:case wr:return as(e,n);case st:return new i;case $n:case Gn:return new i(e);case cn:return T6(e);case ht:return new i;case mr:return R6(e)}}function H6(e,t){var n=t.length;if(!n)return e;var i=n-1;return t[i]=(n>1?"& ":"")+t[i],t=t.join(n>2?", ":" "),e.replace(Zo,`{
/* [wrapped with `+t+`] */
`)}function B6(e){return he(e)||ur(e)||!!(P0&&e&&e[P0])}function mn(e,t){var n=typeof e;return t=t==null?_t:t,!!t&&(n=="number"||n!="symbol"&&g.test(e))&&e>-1&&e%1==0&&e<t}function ut(e,t,n){if(!ke(n))return!1;var i=typeof t;return(i=="number"?gt(n)&&mn(t,n.length):i=="string"&&t in n)?Yt(n[t],e):!1}function H2(e,t){if(he(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||Nt(e)?!0:Ho.test(e)||!qo.test(e)||t!=null&&e in Se(t)}function z6(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function B2(e){var t=p1(e),n=w[t];if(typeof n!="function"||!(t in be.prototype))return!1;if(e===n)return!0;var i=k2(n);return!!i&&e===i[0]}function $6(e){return!!N0&&N0 in e}var Z6=Zi?wn:o3;function mi(e){var t=e&&e.constructor,n=typeof t=="function"&&t.prototype||Ar;return e===n}function Ns(e){return e===e&&!ke(e)}function Ls(e,t){return function(n){return n==null?!1:n[e]===t&&(t!==s||e in Se(n))}}function G6(e){var t=b1(e,function(i){return n.size===m&&n.clear(),i}),n=t.cache;return t}function V6(e,t){var n=e[1],i=t[1],h=n|i,v=h<(Y|se|te),b=i==te&&n==D||i==te&&n==J&&e[7].length<=t[8]||i==(te|J)&&t[7].length<=t[8]&&n==D;if(!(v||b))return e;i&Y&&(e[2]=t[2],h|=n&Y?0:ge);var R=t[3];if(R){var O=e[3];e[3]=O?fs(O,R,t[4]):R,e[4]=O?Nn(e[3],A):t[4]}return R=t[5],R&&(O=e[5],e[5]=O?hs(O,R,t[6]):R,e[6]=O?Nn(e[5],A):t[6]),R=t[7],R&&(e[7]=R),i&te&&(e[8]=e[8]==null?t[8]:tt(e[8],t[8])),e[9]==null&&(e[9]=t[9]),e[0]=t[0],e[1]=h,e}function K6(e){var t=[];if(e!=null)for(var n in Se(e))t.push(n);return t}function Y6(e){return Vi.call(e)}function Os(e,t,n){return t=$e(t===s?e.length-1:t,0),function(){for(var i=arguments,h=-1,v=$e(i.length-t,0),b=W(v);++h<v;)b[h]=i[t+h];h=-1;for(var R=W(t+1);++h<t;)R[h]=i[h];return R[t]=n(b),Rt(e,this,R)}}function Ss(e,t){return t.length<2?e:or(e,Ht(t,0,-1))}function J6(e,t){for(var n=e.length,i=tt(t.length,n),h=dt(e);i--;){var v=t[i];e[i]=mn(v,n)?h[v]:s}return e}function z2(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var Ps=Ws(ts),wi=f4||function(e,t){return Ve.setTimeout(e,t)},$2=Ws(v6);function Is(e,t,n){var i=t+"";return $2(e,H6(i,X6(k6(i),n)))}function Ws(e){var t=0,n=0;return function(){var i=p4(),h=ot-(i-n);if(n=i,h>0){if(++t>=Pe)return arguments[0]}else t=0;return e.apply(s,arguments)}}function w1(e,t){var n=-1,i=e.length,h=i-1;for(t=t===s?i:t;++n<t;){var v=x2(n,h),b=e[v];e[v]=e[n],e[n]=b}return e.length=t,e}var Ms=G6(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(Bo,function(n,i,h,v){t.push(h?v.replace(Jo,"$1"):i||n)}),t});function sn(e){if(typeof e=="string"||Nt(e))return e;var t=e+"";return t=="0"&&1/e==-vt?"-0":t}function lr(e){if(e!=null){try{return Gi.call(e)}catch(t){}try{return e+""}catch(t){}}return""}function X6(e,t){return Ft(pr,function(n){var i="_."+n[0];t&n[1]&&!Hi(e,i)&&e.push(i)}),e.sort()}function Ds(e){if(e instanceof be)return e.clone();var t=new Ut(e.__wrapped__,e.__chain__);return t.__actions__=dt(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function Q6(e,t,n){(n?ut(e,t,n):t===s)?t=1:t=$e(de(t),0);var i=e==null?0:e.length;if(!i||t<1)return[];for(var h=0,v=0,b=W(ji(i/t));h<i;)b[v++]=Ht(e,h,h+=t);return b}function j6(e){for(var t=-1,n=e==null?0:e.length,i=0,h=[];++t<n;){var v=e[t];v&&(h[i++]=v)}return h}function ea(){var e=arguments.length;if(!e)return[];for(var t=W(e-1),n=arguments[0],i=e;i--;)t[i-1]=arguments[i];return yn(he(n)?dt(n):[n],Je(t,1))}var ta=me(function(e,t){return qe(e)?fi(e,Je(t,1,qe,!0)):[]}),na=me(function(e,t){var n=Bt(t);return qe(n)&&(n=s),qe(e)?fi(e,Je(t,1,qe,!0),le(n,2)):[]}),ra=me(function(e,t){var n=Bt(t);return qe(n)&&(n=s),qe(e)?fi(e,Je(t,1,qe,!0),s,n):[]});function ia(e,t,n){var i=e==null?0:e.length;return i?(t=n||t===s?1:de(t),Ht(e,t<0?0:t,i)):[]}function oa(e,t,n){var i=e==null?0:e.length;return i?(t=n||t===s?1:de(t),t=i-t,Ht(e,0,t<0?0:t)):[]}function sa(e,t){return e&&e.length?a1(e,le(t,3),!0,!0):[]}function la(e,t){return e&&e.length?a1(e,le(t,3),!0):[]}function ua(e,t,n,i){var h=e==null?0:e.length;return h?(n&&typeof n!="number"&&ut(e,t,n)&&(n=0,i=h),e6(e,t,n,i)):[]}function Fs(e,t,n){var i=e==null?0:e.length;if(!i)return-1;var h=n==null?0:de(n);return h<0&&(h=$e(i+h,0)),Bi(e,le(t,3),h)}function ks(e,t,n){var i=e==null?0:e.length;if(!i)return-1;var h=i-1;return n!==s&&(h=de(n),h=n<0?$e(i+h,0):tt(h,i-1)),Bi(e,le(t,3),h,!0)}function Us(e){var t=e==null?0:e.length;return t?Je(e,1):[]}function aa(e){var t=e==null?0:e.length;return t?Je(e,vt):[]}function ca(e,t){var n=e==null?0:e.length;return n?(t=t===s?1:de(t),Je(e,t)):[]}function fa(e){for(var t=-1,n=e==null?0:e.length,i={};++t<n;){var h=e[t];i[h[0]]=h[1]}return i}function qs(e){return e&&e.length?e[0]:s}function ha(e,t,n){var i=e==null?0:e.length;if(!i)return-1;var h=n==null?0:de(n);return h<0&&(h=$e(i+h,0)),_r(e,t,h)}function da(e){var t=e==null?0:e.length;return t?Ht(e,0,-1):[]}var ga=me(function(e){var t=Fe(e,S2);return t.length&&t[0]===e[0]?b2(t):[]}),pa=me(function(e){var t=Bt(e),n=Fe(e,S2);return t===Bt(n)?t=s:n.pop(),n.length&&n[0]===e[0]?b2(n,le(t,2)):[]}),ma=me(function(e){var t=Bt(e),n=Fe(e,S2);return t=typeof t=="function"?t:s,t&&n.pop(),n.length&&n[0]===e[0]?b2(n,s,t):[]});function wa(e,t){return e==null?"":d4.call(e,t)}function Bt(e){var t=e==null?0:e.length;return t?e[t-1]:s}function Ca(e,t,n){var i=e==null?0:e.length;if(!i)return-1;var h=i;return n!==s&&(h=de(n),h=h<0?$e(i+h,0):tt(h,i-1)),t===t?Xu(e,t,h):Bi(e,_0,h,!0)}function va(e,t){return e&&e.length?X0(e,de(t)):s}var _a=me(Hs);function Hs(e,t){return e&&e.length&&t&&t.length?R2(e,t):e}function ba(e,t,n){return e&&e.length&&t&&t.length?R2(e,t,le(n,2)):e}function Ea(e,t,n){return e&&e.length&&t&&t.length?R2(e,t,s,n):e}var Aa=pn(function(e,t){var n=e==null?0:e.length,i=w2(e,t);return es(e,Fe(t,function(h){return mn(h,n)?+h:h}).sort(cs)),i});function Ta(e,t){var n=[];if(!(e&&e.length))return n;var i=-1,h=[],v=e.length;for(t=le(t,3);++i<v;){var b=e[i];t(b,i,e)&&(n.push(b),h.push(i))}return es(e,h),n}function Z2(e){return e==null?e:w4.call(e)}function Ra(e,t,n){var i=e==null?0:e.length;return i?(n&&typeof n!="number"&&ut(e,t,n)?(t=0,n=i):(t=t==null?0:de(t),n=n===s?i:de(n)),Ht(e,t,n)):[]}function xa(e,t){return u1(e,t)}function ya(e,t,n){return N2(e,t,le(n,2))}function Na(e,t){var n=e==null?0:e.length;if(n){var i=u1(e,t);if(i<n&&Yt(e[i],t))return i}return-1}function La(e,t){return u1(e,t,!0)}function Oa(e,t,n){return N2(e,t,le(n,2),!0)}function Sa(e,t){var n=e==null?0:e.length;if(n){var i=u1(e,t,!0)-1;if(Yt(e[i],t))return i}return-1}function Pa(e){return e&&e.length?ns(e):[]}function Ia(e,t){return e&&e.length?ns(e,le(t,2)):[]}function Wa(e){var t=e==null?0:e.length;return t?Ht(e,1,t):[]}function Ma(e,t,n){return e&&e.length?(t=n||t===s?1:de(t),Ht(e,0,t<0?0:t)):[]}function Da(e,t,n){var i=e==null?0:e.length;return i?(t=n||t===s?1:de(t),t=i-t,Ht(e,t<0?0:t,i)):[]}function Fa(e,t){return e&&e.length?a1(e,le(t,3),!1,!0):[]}function ka(e,t){return e&&e.length?a1(e,le(t,3)):[]}var Ua=me(function(e){return Pn(Je(e,1,qe,!0))}),qa=me(function(e){var t=Bt(e);return qe(t)&&(t=s),Pn(Je(e,1,qe,!0),le(t,2))}),Ha=me(function(e){var t=Bt(e);return t=typeof t=="function"?t:s,Pn(Je(e,1,qe,!0),s,t)});function Ba(e){return e&&e.length?Pn(e):[]}function za(e,t){return e&&e.length?Pn(e,le(t,2)):[]}function $a(e,t){return t=typeof t=="function"?t:s,e&&e.length?Pn(e,s,t):[]}function G2(e){if(!(e&&e.length))return[];var t=0;return e=xn(e,function(n){if(qe(n))return t=$e(n.length,t),!0}),a2(t,function(n){return Fe(e,s2(n))})}function Bs(e,t){if(!(e&&e.length))return[];var n=G2(e);return t==null?n:Fe(n,function(i){return Rt(t,s,i)})}var Za=me(function(e,t){return qe(e)?fi(e,t):[]}),Ga=me(function(e){return O2(xn(e,qe))}),Va=me(function(e){var t=Bt(e);return qe(t)&&(t=s),O2(xn(e,qe),le(t,2))}),Ka=me(function(e){var t=Bt(e);return t=typeof t=="function"?t:s,O2(xn(e,qe),s,t)}),Ya=me(G2);function Ja(e,t){return ss(e||[],t||[],ci)}function Xa(e,t){return ss(e||[],t||[],gi)}var Qa=me(function(e){var t=e.length,n=t>1?e[t-1]:s;return n=typeof n=="function"?(e.pop(),n):s,Bs(e,n)});function zs(e){var t=w(e);return t.__chain__=!0,t}function ja(e,t){return t(e),e}function C1(e,t){return t(e)}var e8=pn(function(e){var t=e.length,n=t?e[0]:0,i=this.__wrapped__,h=function(v){return w2(v,e)};return t>1||this.__actions__.length||!(i instanceof be)||!mn(n)?this.thru(h):(i=i.slice(n,+n+(t?1:0)),i.__actions__.push({func:C1,args:[h],thisArg:s}),new Ut(i,this.__chain__).thru(function(v){return t&&!v.length&&v.push(s),v}))});function t8(){return zs(this)}function n8(){return new Ut(this.value(),this.__chain__)}function r8(){this.__values__===s&&(this.__values__=rl(this.value()));var e=this.__index__>=this.__values__.length,t=e?s:this.__values__[this.__index__++];return{done:e,value:t}}function i8(){return this}function o8(e){for(var t,n=this;n instanceof r1;){var i=Ds(n);i.__index__=0,i.__values__=s,t?h.__wrapped__=i:t=i;var h=i;n=n.__wrapped__}return h.__wrapped__=e,t}function s8(){var e=this.__wrapped__;if(e instanceof be){var t=e;return this.__actions__.length&&(t=new be(this)),t=t.reverse(),t.__actions__.push({func:C1,args:[Z2],thisArg:s}),new Ut(t,this.__chain__)}return this.thru(Z2)}function l8(){return os(this.__wrapped__,this.__actions__)}var u8=c1(function(e,t,n){Ne.call(e,n)?++e[n]:dn(e,n,1)});function a8(e,t,n){var i=he(e)?C0:j4;return n&&ut(e,t,n)&&(t=s),i(e,le(t,3))}function c8(e,t){var n=he(e)?xn:B0;return n(e,le(t,3))}var f8=ms(Fs),h8=ms(ks);function d8(e,t){return Je(v1(e,t),1)}function g8(e,t){return Je(v1(e,t),vt)}function p8(e,t,n){return n=n===s?1:de(n),Je(v1(e,t),n)}function $s(e,t){var n=he(e)?Ft:Sn;return n(e,le(t,3))}function Zs(e,t){var n=he(e)?Wu:H0;return n(e,le(t,3))}var m8=c1(function(e,t,n){Ne.call(e,n)?e[n].push(t):dn(e,n,[t])});function w8(e,t,n,i){e=gt(e)?e:Sr(e),n=n&&!i?de(n):0;var h=e.length;return n<0&&(n=$e(h+n,0)),T1(e)?n<=h&&e.indexOf(t,n)>-1:!!h&&_r(e,t,n)>-1}var C8=me(function(e,t,n){var i=-1,h=typeof t=="function",v=gt(e)?W(e.length):[];return Sn(e,function(b){v[++i]=h?Rt(t,b,n):hi(b,t,n)}),v}),v8=c1(function(e,t,n){dn(e,n,t)});function v1(e,t){var n=he(e)?Fe:K0;return n(e,le(t,3))}function _8(e,t,n,i){return e==null?[]:(he(t)||(t=t==null?[]:[t]),n=i?s:n,he(n)||(n=n==null?[]:[n]),Q0(e,t,n))}var b8=c1(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]});function E8(e,t,n){var i=he(e)?i2:E0,h=arguments.length<3;return i(e,le(t,4),n,h,Sn)}function A8(e,t,n){var i=he(e)?Mu:E0,h=arguments.length<3;return i(e,le(t,4),n,h,H0)}function T8(e,t){var n=he(e)?xn:B0;return n(e,E1(le(t,3)))}function R8(e){var t=he(e)?F0:w6;return t(e)}function x8(e,t,n){(n?ut(e,t,n):t===s)?t=1:t=de(t);var i=he(e)?K4:C6;return i(e,t)}function y8(e){var t=he(e)?Y4:_6;return t(e)}function N8(e){if(e==null)return 0;if(gt(e))return T1(e)?Er(e):e.length;var t=nt(e);return t==st||t==ht?e.size:A2(e).length}function L8(e,t,n){var i=he(e)?o2:b6;return n&&ut(e,t,n)&&(t=s),i(e,le(t,3))}var O8=me(function(e,t){if(e==null)return[];var n=t.length;return n>1&&ut(e,t[0],t[1])?t=[]:n>2&&ut(t[0],t[1],t[2])&&(t=[t[0]]),Q0(e,Je(t,1),[])}),_1=c4||function(){return Ve.Date.now()};function S8(e,t){if(typeof t!="function")throw new kt(d);return e=de(e),function(){if(--e<1)return t.apply(this,arguments)}}function Gs(e,t,n){return t=n?s:t,t=e&&t==null?e.length:t,gn(e,te,s,s,s,s,t)}function Vs(e,t){var n;if(typeof t!="function")throw new kt(d);return e=de(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=s),n}}var V2=me(function(e,t,n){var i=Y;if(n.length){var h=Nn(n,Lr(V2));i|=U}return gn(e,i,t,n,h)}),Ks=me(function(e,t,n){var i=Y|se;if(n.length){var h=Nn(n,Lr(Ks));i|=U}return gn(t,i,e,n,h)});function Ys(e,t,n){t=n?s:t;var i=gn(e,D,s,s,s,s,s,t);return i.placeholder=Ys.placeholder,i}function Js(e,t,n){t=n?s:t;var i=gn(e,$,s,s,s,s,s,t);return i.placeholder=Js.placeholder,i}function Xs(e,t,n){var i,h,v,b,R,O,q=0,H=!1,Z=!1,X=!0;if(typeof e!="function")throw new kt(d);t=zt(t)||0,ke(n)&&(H=!!n.leading,Z="maxWait"in n,v=Z?$e(zt(n.maxWait)||0,t):v,X="trailing"in n?!!n.trailing:X);function ne(He){var Jt=i,vn=h;return i=h=s,q=He,b=e.apply(vn,Jt),b}function ue(He){return q=He,R=wi(ve,t),H?ne(He):b}function pe(He){var Jt=He-O,vn=He-q,ml=t-Jt;return Z?tt(ml,v-vn):ml}function ae(He){var Jt=He-O,vn=He-q;return O===s||Jt>=t||Jt<0||Z&&vn>=v}function ve(){var He=_1();if(ae(He))return Ee(He);R=wi(ve,pe(He))}function Ee(He){return R=s,X&&i?ne(He):(i=h=s,b)}function Lt(){R!==s&&ls(R),q=0,i=O=h=R=s}function at(){return R===s?b:Ee(_1())}function Ot(){var He=_1(),Jt=ae(He);if(i=arguments,h=this,O=He,Jt){if(R===s)return ue(O);if(Z)return ls(R),R=wi(ve,t),ne(O)}return R===s&&(R=wi(ve,t)),b}return Ot.cancel=Lt,Ot.flush=at,Ot}var P8=me(function(e,t){return q0(e,1,t)}),I8=me(function(e,t,n){return q0(e,zt(t)||0,n)});function W8(e){return gn(e,Q)}function b1(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new kt(d);var n=function(){var i=arguments,h=t?t.apply(this,i):i[0],v=n.cache;if(v.has(h))return v.get(h);var b=e.apply(this,i);return n.cache=v.set(h,b)||v,b};return n.cache=new(b1.Cache||hn),n}b1.Cache=hn;function E1(e){if(typeof e!="function")throw new kt(d);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function M8(e){return Vs(2,e)}var D8=E6(function(e,t){t=t.length==1&&he(t[0])?Fe(t[0],xt(le())):Fe(Je(t,1),xt(le()));var n=t.length;return me(function(i){for(var h=-1,v=tt(i.length,n);++h<v;)i[h]=t[h].call(this,i[h]);return Rt(e,this,i)})}),K2=me(function(e,t){var n=Nn(t,Lr(K2));return gn(e,U,s,t,n)}),Qs=me(function(e,t){var n=Nn(t,Lr(Qs));return gn(e,K,s,t,n)}),F8=pn(function(e,t){return gn(e,J,s,s,s,t)});function k8(e,t){if(typeof e!="function")throw new kt(d);return t=t===s?t:de(t),me(e,t)}function U8(e,t){if(typeof e!="function")throw new kt(d);return t=t==null?0:$e(de(t),0),me(function(n){var i=n[t],h=Wn(n,0,t);return i&&yn(h,i),Rt(e,this,h)})}function q8(e,t,n){var i=!0,h=!0;if(typeof e!="function")throw new kt(d);return ke(n)&&(i="leading"in n?!!n.leading:i,h="trailing"in n?!!n.trailing:h),Xs(e,t,{leading:i,maxWait:t,trailing:h})}function H8(e){return Gs(e,1)}function B8(e,t){return K2(P2(t),e)}function z8(){if(!arguments.length)return[];var e=arguments[0];return he(e)?e:[e]}function $8(e){return qt(e,S)}function Z8(e,t){return t=typeof t=="function"?t:s,qt(e,S,t)}function G8(e){return qt(e,P|S)}function V8(e,t){return t=typeof t=="function"?t:s,qt(e,P|S,t)}function K8(e,t){return t==null||U0(e,t,Ke(t))}function Yt(e,t){return e===t||e!==e&&t!==t}var Y8=g1(_2),J8=g1(function(e,t){return e>=t}),ur=Z0(function(){return arguments}())?Z0:function(e){return Ue(e)&&Ne.call(e,"callee")&&!S0.call(e,"callee")},he=W.isArray,X8=h0?xt(h0):o6;function gt(e){return e!=null&&A1(e.length)&&!wn(e)}function qe(e){return Ue(e)&&gt(e)}function Q8(e){return e===!0||e===!1||Ue(e)&&lt(e)==En}var Mn=h4||o3,j8=d0?xt(d0):s6;function e5(e){return Ue(e)&&e.nodeType===1&&!Ci(e)}function t5(e){if(e==null)return!0;if(gt(e)&&(he(e)||typeof e=="string"||typeof e.splice=="function"||Mn(e)||Or(e)||ur(e)))return!e.length;var t=nt(e);if(t==st||t==ht)return!e.size;if(mi(e))return!A2(e).length;for(var n in e)if(Ne.call(e,n))return!1;return!0}function n5(e,t){return di(e,t)}function r5(e,t,n){n=typeof n=="function"?n:s;var i=n?n(e,t):s;return i===s?di(e,t,s,n):!!i}function Y2(e){if(!Ue(e))return!1;var t=lt(e);return t==Tn||t==Di||typeof e.message=="string"&&typeof e.name=="string"&&!Ci(e)}function i5(e){return typeof e=="number"&&I0(e)}function wn(e){if(!ke(e))return!1;var t=lt(e);return t==bt||t==Zr||t==tn||t==Fi}function js(e){return typeof e=="number"&&e==de(e)}function A1(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=_t}function ke(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}function Ue(e){return e!=null&&typeof e=="object"}var el=g0?xt(g0):u6;function o5(e,t){return e===t||E2(e,t,U2(t))}function s5(e,t,n){return n=typeof n=="function"?n:s,E2(e,t,U2(t),n)}function l5(e){return tl(e)&&e!=+e}function u5(e){if(Z6(e))throw new fe(c);return G0(e)}function a5(e){return e===null}function c5(e){return e==null}function tl(e){return typeof e=="number"||Ue(e)&&lt(e)==$n}function Ci(e){if(!Ue(e)||lt(e)!=et)return!1;var t=Ji(e);if(t===null)return!0;var n=Ne.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n instanceof n&&Gi.call(n)==s4}var J2=p0?xt(p0):a6;function f5(e){return js(e)&&e>=-_t&&e<=_t}var nl=m0?xt(m0):c6;function T1(e){return typeof e=="string"||!he(e)&&Ue(e)&&lt(e)==Gn}function Nt(e){return typeof e=="symbol"||Ue(e)&&lt(e)==mr}var Or=w0?xt(w0):f6;function h5(e){return e===s}function d5(e){return Ue(e)&&nt(e)==Vn}function g5(e){return Ue(e)&&lt(e)==Po}var p5=g1(T2),m5=g1(function(e,t){return e<=t});function rl(e){if(!e)return[];if(gt(e))return T1(e)?Vt(e):dt(e);if(oi&&e[oi])return Ku(e[oi]());var t=nt(e),n=t==st?f2:t==ht?zi:Sr;return n(e)}function Cn(e){if(!e)return e===0?e:0;if(e=zt(e),e===vt||e===-vt){var t=e<0?-1:1;return t*Lo}return e===e?e:0}function de(e){var t=Cn(e),n=t%1;return t===t?n?t-n:t:0}function il(e){return e?ir(de(e),0,j):0}function zt(e){if(typeof e=="number")return e;if(Nt(e))return Ae;if(ke(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=ke(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=A0(e);var n=u.test(e);return n||f.test(e)?Su(e.slice(2),n?2:8):o.test(e)?Ae:+e}function ol(e){return on(e,pt(e))}function w5(e){return e?ir(de(e),-_t,_t):e===0?e:0}function xe(e){return e==null?"":yt(e)}var C5=yr(function(e,t){if(mi(t)||gt(t)){on(t,Ke(t),e);return}for(var n in t)Ne.call(t,n)&&ci(e,n,t[n])}),sl=yr(function(e,t){on(t,pt(t),e)}),R1=yr(function(e,t,n,i){on(t,pt(t),e,i)}),v5=yr(function(e,t,n,i){on(t,Ke(t),e,i)}),_5=pn(w2);function b5(e,t){var n=xr(e);return t==null?n:k0(n,t)}var E5=me(function(e,t){e=Se(e);var n=-1,i=t.length,h=i>2?t[2]:s;for(h&&ut(t[0],t[1],h)&&(i=1);++n<i;)for(var v=t[n],b=pt(v),R=-1,O=b.length;++R<O;){var q=b[R],H=e[q];(H===s||Yt(H,Ar[q])&&!Ne.call(e,q))&&(e[q]=v[q])}return e}),A5=me(function(e){return e.push(s,As),Rt(ll,s,e)});function T5(e,t){return v0(e,le(t,3),rn)}function R5(e,t){return v0(e,le(t,3),v2)}function x5(e,t){return e==null?e:C2(e,le(t,3),pt)}function y5(e,t){return e==null?e:z0(e,le(t,3),pt)}function N5(e,t){return e&&rn(e,le(t,3))}function L5(e,t){return e&&v2(e,le(t,3))}function O5(e){return e==null?[]:s1(e,Ke(e))}function S5(e){return e==null?[]:s1(e,pt(e))}function X2(e,t,n){var i=e==null?s:or(e,t);return i===s?n:i}function P5(e,t){return e!=null&&xs(e,t,t6)}function Q2(e,t){return e!=null&&xs(e,t,n6)}var I5=Cs(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=Vi.call(t)),e[t]=n},e3(mt)),W5=Cs(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=Vi.call(t)),Ne.call(e,t)?e[t].push(n):e[t]=[n]},le),M5=me(hi);function Ke(e){return gt(e)?D0(e):A2(e)}function pt(e){return gt(e)?D0(e,!0):h6(e)}function D5(e,t){var n={};return t=le(t,3),rn(e,function(i,h,v){dn(n,t(i,h,v),i)}),n}function F5(e,t){var n={};return t=le(t,3),rn(e,function(i,h,v){dn(n,h,t(i,h,v))}),n}var k5=yr(function(e,t,n){l1(e,t,n)}),ll=yr(function(e,t,n,i){l1(e,t,n,i)}),U5=pn(function(e,t){var n={};if(e==null)return n;var i=!1;t=Fe(t,function(v){return v=In(v,e),i||(i=v.length>1),v}),on(e,F2(e),n),i&&(n=qt(n,P|y|S,I6));for(var h=t.length;h--;)L2(n,t[h]);return n});function q5(e,t){return ul(e,E1(le(t)))}var H5=pn(function(e,t){return e==null?{}:g6(e,t)});function ul(e,t){if(e==null)return{};var n=Fe(F2(e),function(i){return[i]});return t=le(t),j0(e,n,function(i,h){return t(i,h[0])})}function B5(e,t,n){t=In(t,e);var i=-1,h=t.length;for(h||(h=1,e=s);++i<h;){var v=e==null?s:e[sn(t[i])];v===s&&(i=h,v=n),e=wn(v)?v.call(e):v}return e}function z5(e,t,n){return e==null?e:gi(e,t,n)}function $5(e,t,n,i){return i=typeof i=="function"?i:s,e==null?e:gi(e,t,n,i)}var al=bs(Ke),cl=bs(pt);function Z5(e,t,n){var i=he(e),h=i||Mn(e)||Or(e);if(t=le(t,4),n==null){var v=e&&e.constructor;h?n=i?new v:[]:ke(e)?n=wn(v)?xr(Ji(e)):{}:n={}}return(h?Ft:rn)(e,function(b,R,O){return t(n,b,R,O)}),n}function G5(e,t){return e==null?!0:L2(e,t)}function V5(e,t,n){return e==null?e:is(e,t,P2(n))}function K5(e,t,n,i){return i=typeof i=="function"?i:s,e==null?e:is(e,t,P2(n),i)}function Sr(e){return e==null?[]:c2(e,Ke(e))}function Y5(e){return e==null?[]:c2(e,pt(e))}function J5(e,t,n){return n===s&&(n=t,t=s),n!==s&&(n=zt(n),n=n===n?n:0),t!==s&&(t=zt(t),t=t===t?t:0),ir(zt(e),t,n)}function X5(e,t,n){return t=Cn(t),n===s?(n=t,t=0):n=Cn(n),e=zt(e),r6(e,t,n)}function Q5(e,t,n){if(n&&typeof n!="boolean"&&ut(e,t,n)&&(t=n=s),n===s&&(typeof t=="boolean"?(n=t,t=s):typeof e=="boolean"&&(n=e,e=s)),e===s&&t===s?(e=0,t=1):(e=Cn(e),t===s?(t=e,e=0):t=Cn(t)),e>t){var i=e;e=t,t=i}if(n||e%1||t%1){var h=W0();return tt(e+h*(t-e+Ou("1e-"+((h+"").length-1))),t)}return x2(e,t)}var j5=Nr(function(e,t,n){return t=t.toLowerCase(),e+(n?fl(t):t)});function fl(e){return j2(xe(e).toLowerCase())}function hl(e){return e=xe(e),e&&e.replace(p,zu).replace(_u,"")}function ec(e,t,n){e=xe(e),t=yt(t);var i=e.length;n=n===s?i:ir(de(n),0,i);var h=n;return n-=t.length,n>=0&&e.slice(n,h)==t}function tc(e){return e=xe(e),e&&Fo.test(e)?e.replace(ei,$u):e}function nc(e){return e=xe(e),e&&zo.test(e)?e.replace(ti,"\\$&"):e}var rc=Nr(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),ic=Nr(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),oc=ps("toLowerCase");function sc(e,t,n){e=xe(e),t=de(t);var i=t?Er(e):0;if(!t||i>=t)return e;var h=(t-i)/2;return d1(e1(h),n)+e+d1(ji(h),n)}function lc(e,t,n){e=xe(e),t=de(t);var i=t?Er(e):0;return t&&i<t?e+d1(t-i,n):e}function uc(e,t,n){e=xe(e),t=de(t);var i=t?Er(e):0;return t&&i<t?d1(t-i,n)+e:e}function ac(e,t,n){return n||t==null?t=0:t&&(t=+t),m4(xe(e).replace(ni,""),t||0)}function cc(e,t,n){return(n?ut(e,t,n):t===s)?t=1:t=de(t),y2(xe(e),t)}function fc(){var e=arguments,t=xe(e[0]);return e.length<3?t:t.replace(e[1],e[2])}var hc=Nr(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()});function dc(e,t,n){return n&&typeof n!="number"&&ut(e,t,n)&&(t=n=s),n=n===s?j:n>>>0,n?(e=xe(e),e&&(typeof t=="string"||t!=null&&!J2(t))&&(t=yt(t),!t&&br(e))?Wn(Vt(e),0,n):e.split(t,n)):[]}var gc=Nr(function(e,t,n){return e+(n?" ":"")+j2(t)});function pc(e,t,n){return e=xe(e),n=n==null?0:ir(de(n),0,e.length),t=yt(t),e.slice(n,n+t.length)==t}function mc(e,t,n){var i=w.templateSettings;n&&ut(e,t,n)&&(t=s),e=xe(e),t=R1({},t,i,Es);var h=R1({},t.imports,i.imports,Es),v=Ke(h),b=c2(h,v),R,O,q=0,H=t.interpolate||E,Z="__p += '",X=h2((t.escape||E).source+"|"+H.source+"|"+(H===Ui?Xo:E).source+"|"+(t.evaluate||E).source+"|$","g"),ne="//# sourceURL="+(Ne.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Ru+"]")+`
`;e.replace(X,function(ae,ve,Ee,Lt,at,Ot){return Ee||(Ee=Lt),Z+=e.slice(q,Ot).replace(T,Zu),ve&&(R=!0,Z+=`' +
__e(`+ve+`) +
'`),at&&(O=!0,Z+=`';
`+at+`;
__p += '`),Ee&&(Z+=`' +
((__t = (`+Ee+`)) == null ? '' : __t) +
'`),q=Ot+ae.length,ae}),Z+=`';
`;var ue=Ne.call(t,"variable")&&t.variable;if(!ue)Z=`with (obj) {
`+Z+`
}
`;else if(Yo.test(ue))throw new fe(C);Z=(O?Z.replace(Io,""):Z).replace(Wo,"$1").replace(Mo,"$1;"),Z="function("+(ue||"obj")+`) {
`+(ue?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(R?", __e = _.escape":"")+(O?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+Z+`return __p
}`;var pe=gl(function(){return Te(v,ne+"return "+Z).apply(s,b)});if(pe.source=Z,Y2(pe))throw pe;return pe}function wc(e){return xe(e).toLowerCase()}function Cc(e){return xe(e).toUpperCase()}function vc(e,t,n){if(e=xe(e),e&&(n||t===s))return A0(e);if(!e||!(t=yt(t)))return e;var i=Vt(e),h=Vt(t),v=T0(i,h),b=R0(i,h)+1;return Wn(i,v,b).join("")}function _c(e,t,n){if(e=xe(e),e&&(n||t===s))return e.slice(0,y0(e)+1);if(!e||!(t=yt(t)))return e;var i=Vt(e),h=R0(i,Vt(t))+1;return Wn(i,0,h).join("")}function bc(e,t,n){if(e=xe(e),e&&(n||t===s))return e.replace(ni,"");if(!e||!(t=yt(t)))return e;var i=Vt(e),h=T0(i,Vt(t));return Wn(i,h).join("")}function Ec(e,t){var n=G,i=Ce;if(ke(t)){var h="separator"in t?t.separator:h;n="length"in t?de(t.length):n,i="omission"in t?yt(t.omission):i}e=xe(e);var v=e.length;if(br(e)){var b=Vt(e);v=b.length}if(n>=v)return e;var R=n-Er(i);if(R<1)return i;var O=b?Wn(b,0,R).join(""):e.slice(0,R);if(h===s)return O+i;if(b&&(R+=O.length-R),J2(h)){if(e.slice(R).search(h)){var q,H=O;for(h.global||(h=h2(h.source,xe(qi.exec(h))+"g")),h.lastIndex=0;q=h.exec(H);)var Z=q.index;O=O.slice(0,Z===s?R:Z)}}else if(e.indexOf(yt(h),R)!=R){var X=O.lastIndexOf(h);X>-1&&(O=O.slice(0,X))}return O+i}function Ac(e){return e=xe(e),e&&Do.test(e)?e.replace(ki,Qu):e}var Tc=Nr(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),j2=ps("toUpperCase");function dl(e,t,n){return e=xe(e),t=n?s:t,t===s?Vu(e)?t4(e):ku(e):e.match(t)||[]}var gl=me(function(e,t){try{return Rt(e,s,t)}catch(n){return Y2(n)?n:new fe(n)}}),Rc=pn(function(e,t){return Ft(t,function(n){n=sn(n),dn(e,n,V2(e[n],e))}),e});function xc(e){var t=e==null?0:e.length,n=le();return e=t?Fe(e,function(i){if(typeof i[1]!="function")throw new kt(d);return[n(i[0]),i[1]]}):[],me(function(i){for(var h=-1;++h<t;){var v=e[h];if(Rt(v[0],this,i))return Rt(v[1],this,i)}})}function yc(e){return Q4(qt(e,P))}function e3(e){return function(){return e}}function Nc(e,t){return e==null||e!==e?t:e}var Lc=ws(),Oc=ws(!0);function mt(e){return e}function t3(e){return V0(typeof e=="function"?e:qt(e,P))}function Sc(e){return Y0(qt(e,P))}function Pc(e,t){return J0(e,qt(t,P))}var Ic=me(function(e,t){return function(n){return hi(n,e,t)}}),Wc=me(function(e,t){return function(n){return hi(e,n,t)}});function n3(e,t,n){var i=Ke(t),h=s1(t,i);n==null&&!(ke(t)&&(h.length||!i.length))&&(n=t,t=e,e=this,h=s1(t,Ke(t)));var v=!(ke(n)&&"chain"in n)||!!n.chain,b=wn(e);return Ft(h,function(R){var O=t[R];e[R]=O,b&&(e.prototype[R]=function(){var q=this.__chain__;if(v||q){var H=e(this.__wrapped__),Z=H.__actions__=dt(this.__actions__);return Z.push({func:O,args:arguments,thisArg:e}),H.__chain__=q,H}return O.apply(e,yn([this.value()],arguments))})}),e}function Mc(){return Ve._===this&&(Ve._=l4),this}function r3(){}function Dc(e){return e=de(e),me(function(t){return X0(t,e)})}var Fc=W2(Fe),kc=W2(C0),Uc=W2(o2);function pl(e){return H2(e)?s2(sn(e)):p6(e)}function qc(e){return function(t){return e==null?s:or(e,t)}}var Hc=vs(),Bc=vs(!0);function i3(){return[]}function o3(){return!1}function zc(){return{}}function $c(){return""}function Zc(){return!0}function Gc(e,t){if(e=de(e),e<1||e>_t)return[];var n=j,i=tt(e,j);t=le(t),e-=j;for(var h=a2(i,t);++n<e;)t(n);return h}function Vc(e){return he(e)?Fe(e,sn):Nt(e)?[e]:dt(Ms(xe(e)))}function Kc(e){var t=++o4;return xe(e)+t}var Yc=h1(function(e,t){return e+t},0),Jc=M2("ceil"),Xc=h1(function(e,t){return e/t},1),Qc=M2("floor");function jc(e){return e&&e.length?o1(e,mt,_2):s}function e9(e,t){return e&&e.length?o1(e,le(t,2),_2):s}function t9(e){return b0(e,mt)}function n9(e,t){return b0(e,le(t,2))}function r9(e){return e&&e.length?o1(e,mt,T2):s}function i9(e,t){return e&&e.length?o1(e,le(t,2),T2):s}var o9=h1(function(e,t){return e*t},1),s9=M2("round"),l9=h1(function(e,t){return e-t},0);function u9(e){return e&&e.length?u2(e,mt):0}function a9(e,t){return e&&e.length?u2(e,le(t,2)):0}return w.after=S8,w.ary=Gs,w.assign=C5,w.assignIn=sl,w.assignInWith=R1,w.assignWith=v5,w.at=_5,w.before=Vs,w.bind=V2,w.bindAll=Rc,w.bindKey=Ks,w.castArray=z8,w.chain=zs,w.chunk=Q6,w.compact=j6,w.concat=ea,w.cond=xc,w.conforms=yc,w.constant=e3,w.countBy=u8,w.create=b5,w.curry=Ys,w.curryRight=Js,w.debounce=Xs,w.defaults=E5,w.defaultsDeep=A5,w.defer=P8,w.delay=I8,w.difference=ta,w.differenceBy=na,w.differenceWith=ra,w.drop=ia,w.dropRight=oa,w.dropRightWhile=sa,w.dropWhile=la,w.fill=ua,w.filter=c8,w.flatMap=d8,w.flatMapDeep=g8,w.flatMapDepth=p8,w.flatten=Us,w.flattenDeep=aa,w.flattenDepth=ca,w.flip=W8,w.flow=Lc,w.flowRight=Oc,w.fromPairs=fa,w.functions=O5,w.functionsIn=S5,w.groupBy=m8,w.initial=da,w.intersection=ga,w.intersectionBy=pa,w.intersectionWith=ma,w.invert=I5,w.invertBy=W5,w.invokeMap=C8,w.iteratee=t3,w.keyBy=v8,w.keys=Ke,w.keysIn=pt,w.map=v1,w.mapKeys=D5,w.mapValues=F5,w.matches=Sc,w.matchesProperty=Pc,w.memoize=b1,w.merge=k5,w.mergeWith=ll,w.method=Ic,w.methodOf=Wc,w.mixin=n3,w.negate=E1,w.nthArg=Dc,w.omit=U5,w.omitBy=q5,w.once=M8,w.orderBy=_8,w.over=Fc,w.overArgs=D8,w.overEvery=kc,w.overSome=Uc,w.partial=K2,w.partialRight=Qs,w.partition=b8,w.pick=H5,w.pickBy=ul,w.property=pl,w.propertyOf=qc,w.pull=_a,w.pullAll=Hs,w.pullAllBy=ba,w.pullAllWith=Ea,w.pullAt=Aa,w.range=Hc,w.rangeRight=Bc,w.rearg=F8,w.reject=T8,w.remove=Ta,w.rest=k8,w.reverse=Z2,w.sampleSize=x8,w.set=z5,w.setWith=$5,w.shuffle=y8,w.slice=Ra,w.sortBy=O8,w.sortedUniq=Pa,w.sortedUniqBy=Ia,w.split=dc,w.spread=U8,w.tail=Wa,w.take=Ma,w.takeRight=Da,w.takeRightWhile=Fa,w.takeWhile=ka,w.tap=ja,w.throttle=q8,w.thru=C1,w.toArray=rl,w.toPairs=al,w.toPairsIn=cl,w.toPath=Vc,w.toPlainObject=ol,w.transform=Z5,w.unary=H8,w.union=Ua,w.unionBy=qa,w.unionWith=Ha,w.uniq=Ba,w.uniqBy=za,w.uniqWith=$a,w.unset=G5,w.unzip=G2,w.unzipWith=Bs,w.update=V5,w.updateWith=K5,w.values=Sr,w.valuesIn=Y5,w.without=Za,w.words=dl,w.wrap=B8,w.xor=Ga,w.xorBy=Va,w.xorWith=Ka,w.zip=Ya,w.zipObject=Ja,w.zipObjectDeep=Xa,w.zipWith=Qa,w.entries=al,w.entriesIn=cl,w.extend=sl,w.extendWith=R1,n3(w,w),w.add=Yc,w.attempt=gl,w.camelCase=j5,w.capitalize=fl,w.ceil=Jc,w.clamp=J5,w.clone=$8,w.cloneDeep=G8,w.cloneDeepWith=V8,w.cloneWith=Z8,w.conformsTo=K8,w.deburr=hl,w.defaultTo=Nc,w.divide=Xc,w.endsWith=ec,w.eq=Yt,w.escape=tc,w.escapeRegExp=nc,w.every=a8,w.find=f8,w.findIndex=Fs,w.findKey=T5,w.findLast=h8,w.findLastIndex=ks,w.findLastKey=R5,w.floor=Qc,w.forEach=$s,w.forEachRight=Zs,w.forIn=x5,w.forInRight=y5,w.forOwn=N5,w.forOwnRight=L5,w.get=X2,w.gt=Y8,w.gte=J8,w.has=P5,w.hasIn=Q2,w.head=qs,w.identity=mt,w.includes=w8,w.indexOf=ha,w.inRange=X5,w.invoke=M5,w.isArguments=ur,w.isArray=he,w.isArrayBuffer=X8,w.isArrayLike=gt,w.isArrayLikeObject=qe,w.isBoolean=Q8,w.isBuffer=Mn,w.isDate=j8,w.isElement=e5,w.isEmpty=t5,w.isEqual=n5,w.isEqualWith=r5,w.isError=Y2,w.isFinite=i5,w.isFunction=wn,w.isInteger=js,w.isLength=A1,w.isMap=el,w.isMatch=o5,w.isMatchWith=s5,w.isNaN=l5,w.isNative=u5,w.isNil=c5,w.isNull=a5,w.isNumber=tl,w.isObject=ke,w.isObjectLike=Ue,w.isPlainObject=Ci,w.isRegExp=J2,w.isSafeInteger=f5,w.isSet=nl,w.isString=T1,w.isSymbol=Nt,w.isTypedArray=Or,w.isUndefined=h5,w.isWeakMap=d5,w.isWeakSet=g5,w.join=wa,w.kebabCase=rc,w.last=Bt,w.lastIndexOf=Ca,w.lowerCase=ic,w.lowerFirst=oc,w.lt=p5,w.lte=m5,w.max=jc,w.maxBy=e9,w.mean=t9,w.meanBy=n9,w.min=r9,w.minBy=i9,w.stubArray=i3,w.stubFalse=o3,w.stubObject=zc,w.stubString=$c,w.stubTrue=Zc,w.multiply=o9,w.nth=va,w.noConflict=Mc,w.noop=r3,w.now=_1,w.pad=sc,w.padEnd=lc,w.padStart=uc,w.parseInt=ac,w.random=Q5,w.reduce=E8,w.reduceRight=A8,w.repeat=cc,w.replace=fc,w.result=B5,w.round=s9,w.runInContext=N,w.sample=R8,w.size=N8,w.snakeCase=hc,w.some=L8,w.sortedIndex=xa,w.sortedIndexBy=ya,w.sortedIndexOf=Na,w.sortedLastIndex=La,w.sortedLastIndexBy=Oa,w.sortedLastIndexOf=Sa,w.startCase=gc,w.startsWith=pc,w.subtract=l9,w.sum=u9,w.sumBy=a9,w.template=mc,w.times=Gc,w.toFinite=Cn,w.toInteger=de,w.toLength=il,w.toLower=wc,w.toNumber=zt,w.toSafeInteger=w5,w.toString=xe,w.toUpper=Cc,w.trim=vc,w.trimEnd=_c,w.trimStart=bc,w.truncate=Ec,w.unescape=Ac,w.uniqueId=Kc,w.upperCase=Tc,w.upperFirst=j2,w.each=$s,w.eachRight=Zs,w.first=qs,n3(w,function(){var e={};return rn(w,function(t,n){Ne.call(w.prototype,n)||(e[n]=t)}),e}(),{chain:!1}),w.VERSION=r,Ft(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){w[e].placeholder=w}),Ft(["drop","take"],function(e,t){be.prototype[e]=function(n){n=n===s?1:$e(de(n),0);var i=this.__filtered__&&!t?new be(this):this.clone();return i.__filtered__?i.__takeCount__=tt(n,i.__takeCount__):i.__views__.push({size:tt(n,j),type:e+(i.__dir__<0?"Right":"")}),i},be.prototype[e+"Right"]=function(n){return this.reverse()[e](n).reverse()}}),Ft(["filter","map","takeWhile"],function(e,t){var n=t+1,i=n==B||n==je;be.prototype[e]=function(h){var v=this.clone();return v.__iteratees__.push({iteratee:le(h,3),type:n}),v.__filtered__=v.__filtered__||i,v}}),Ft(["head","last"],function(e,t){var n="take"+(t?"Right":"");be.prototype[e]=function(){return this[n](1).value()[0]}}),Ft(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");be.prototype[e]=function(){return this.__filtered__?new be(this):this[n](1)}}),be.prototype.compact=function(){return this.filter(mt)},be.prototype.find=function(e){return this.filter(e).head()},be.prototype.findLast=function(e){return this.reverse().find(e)},be.prototype.invokeMap=me(function(e,t){return typeof e=="function"?new be(this):this.map(function(n){return hi(n,e,t)})}),be.prototype.reject=function(e){return this.filter(E1(le(e)))},be.prototype.slice=function(e,t){e=de(e);var n=this;return n.__filtered__&&(e>0||t<0)?new be(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==s&&(t=de(t),n=t<0?n.dropRight(-t):n.take(t-e)),n)},be.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},be.prototype.toArray=function(){return this.take(j)},rn(be.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),i=/^(?:head|last)$/.test(t),h=w[i?"take"+(t=="last"?"Right":""):t],v=i||/^find/.test(t);h&&(w.prototype[t]=function(){var b=this.__wrapped__,R=i?[1]:arguments,O=b instanceof be,q=R[0],H=O||he(b),Z=function(ve){var Ee=h.apply(w,yn([ve],R));return i&&X?Ee[0]:Ee};H&&n&&typeof q=="function"&&q.length!=1&&(O=H=!1);var X=this.__chain__,ne=!!this.__actions__.length,ue=v&&!X,pe=O&&!ne;if(!v&&H){b=pe?b:new be(this);var ae=e.apply(b,R);return ae.__actions__.push({func:C1,args:[Z],thisArg:s}),new Ut(ae,X)}return ue&&pe?e.apply(this,R):(ae=this.thru(Z),ue?i?ae.value()[0]:ae.value():ae)})}),Ft(["pop","push","shift","sort","splice","unshift"],function(e){var t=$i[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",i=/^(?:pop|shift)$/.test(e);w.prototype[e]=function(){var h=arguments;if(i&&!this.__chain__){var v=this.value();return t.apply(he(v)?v:[],h)}return this[n](function(b){return t.apply(he(b)?b:[],h)})}}),rn(be.prototype,function(e,t){var n=w[t];if(n){var i=n.name+"";Ne.call(Rr,i)||(Rr[i]=[]),Rr[i].push({name:t,func:n})}}),Rr[f1(s,se).name]=[{name:"wrapper",func:s}],be.prototype.clone=A4,be.prototype.reverse=T4,be.prototype.value=R4,w.prototype.at=e8,w.prototype.chain=t8,w.prototype.commit=n8,w.prototype.next=r8,w.prototype.plant=o8,w.prototype.reverse=s8,w.prototype.toJSON=w.prototype.valueOf=w.prototype.value=l8,w.prototype.first=w.prototype.head,oi&&(w.prototype[oi]=i8),w},Ln=n4();typeof define=="function"&&typeof define.amd=="object"&&define.amd?(Ve._=Ln,define(function(){return Ln})):er?((er.exports=Ln)._=Ln,t2._=Ln):Ve._=Ln}).call(Ir)});var Fn=we(Qt=>{"use strict";var A9=Qt&&Qt.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Qt,"__esModule",{value:!0});Qt.Value=Qt.Arity=Qt.FloatOrMilliseconds=void 0;var Ei=A9(f3()),T9=Wr(),R9=new RegExp("[1-9][0-9]{3}-[01][0-9]-[0-3][0-9][T ][0-2][0-9]:[0-5][0-9]"),x9=new RegExp("^-?[0-9]+:[0-5][0-9]"),y9=s=>{let r=s.trim();if(r==="")return new Ei.default(0);if(R9.test(r))return new Ei.default(new Date(r).valueOf());if(x9.test(r)){let c=r.charAt(0)=="-",d=r.slice(c?1:0),C=parseInt(d.slice(0,-3))*60+parseInt(d.slice(-2));return new Ei.default((c?-1:1)*C*6e4)}let l=new Ei.default(r);return l.isNaN()?new Ei.default(0):l};Qt.FloatOrMilliseconds=y9;var H1=class{constructor(r,l){this.isRow=()=>this.rows>1&&this.cols===1,this.isColumn=()=>this.rows===1&&this.cols>1,this.isCell=()=>this.rows===1&&this.cols===1,this.rows=r,this.cols=l}};Qt.Arity=H1;var h3=class{constructor(r){this.get=(l,c)=>this.val[l][c],this.getAsNumber=(l,c)=>{let d=this.get(l,c);return(0,Qt.FloatOrMilliseconds)(d)},this.getArity=()=>{let l=this.val.reduce((c,d)=>Math.max(c,d.length),0);return new H1(this.val.length,l)},this.toString=()=>this.getArity().isCell()?this.get(0,0):`[${(0,T9.flatten)(this.val).map(l=>l.trim()).filter(l=>l!=="").join(", ")}]`,this.val=r}};Qt.Value=h3});var Rl=we($1=>{"use strict";Object.defineProperty($1,"__esModule",{value:!0});$1.AlgebraicOperation=void 0;var ar=Xt(),d3=$t(),Tl=Ai(),B1=Fn(),z1=Wr(),g3=class{constructor(r,l){this.getValue=(_,m)=>{switch(this.operator){case"+":return this.add(_,m);case"-":return this.subtract(_,m);case"*":return this.multiply(_,m);case"/":return this.divide(_,m);default:return(0,ar.err)(Error("Invalid algbraic operator: "+this.operator))}},this.withCellAndRange=(_,m,A,P,y)=>{let S=this.leftSource.getValue(_,m);if(S.isErr())return(0,ar.err)(S.error);let V=this.rightSource.getValue(_,m);if(V.isErr())return(0,ar.err)(V.error);let M=S.value.getArity(),Y=V.value.getArity();if(!Y.isCell()&&!M.isCell())return(0,ar.err)(Error(`At least one operand in algebraic "${A}" must be a single cell.`));if(!Y.isCell()&&!P)return(0,ar.err)(Error(`Right operand in algebraic "${A}" must be a single cell.`));if(Y.isCell()){let D=V.value.getAsNumber(0,0),$=(0,z1.map)(S.value.val,U=>(0,z1.map)(U,K=>{let te=(0,B1.FloatOrMilliseconds)(K);return y(te,D).toString()}));return(0,ar.ok)(new B1.Value($))}let se=S.value.getAsNumber(0,0),ge=(0,z1.map)(V.value.val,D=>(0,z1.map)(D,$=>{let U=(0,B1.FloatOrMilliseconds)($);return y(se,U).toString()}));return(0,ar.ok)(new B1.Value(ge))},this.add=(_,m)=>this.withCellAndRange(_,m,"add",!0,(A,P)=>A.plus(P)),this.subtract=(_,m)=>this.withCellAndRange(_,m,"subtract",!0,(A,P)=>A.minus(P)),this.multiply=(_,m)=>this.withCellAndRange(_,m,"multiply",!0,(A,P)=>A.times(P)),this.divide=(_,m)=>this.withCellAndRange(_,m,"divide",!1,(A,P)=>A.dividedBy(P));let c=(0,d3.checkType)(r,"algebraic_operation");if(c)throw c;let d=(0,d3.checkChildLength)(r,3);if(d)throw d;let C=(0,d3.checkType)(r.children[1],"algebraic_operator");if(C)throw C;this.operator=r.children[1].text;try{this.leftSource=new Tl.Source(r.children[0],l),this.rightSource=new Tl.Source(r.children[2],l)}catch(_){throw _}}};$1.AlgebraicOperation=g3});var xl=we(G1=>{"use strict";Object.defineProperty(G1,"__esModule",{value:!0});G1.ConditionalFunctionCall=void 0;var jt=Xt(),Ti=$t(),Z1=Ai(),p3=class{constructor(r,l){this.getValue=(C,_)=>this.predicate.eval(C,_).andThen(m=>m?this.leftSource.getValue(C,_):this.rightSource.getValue(C,_));let c=(0,Ti.checkType)(r,"conditional_function_call");if(c)throw c;let d=(0,Ti.checkChildLength)(r,3);if(d)throw d;try{this.predicate=new m3(r.children[0],l),this.leftSource=new Z1.Source(r.children[1],l),this.rightSource=new Z1.Source(r.children[2],l)}catch(C){throw C}}};G1.ConditionalFunctionCall=p3;var m3=class{constructor(r,l){this.eval=(_,m)=>{let A=this.leftSource.getValue(_,m);if(A.isErr())return(0,jt.err)(A.error);let P=this.rightSource.getValue(_,m);if(P.isErr())return(0,jt.err)(P.error);let y=A.value.getArity(),S=P.value.getArity();if(!y.isCell())return(0,jt.err)(Error("Can only use comparison operator on a single cell. Left side is not a cell."));if(!S.isCell())return(0,jt.err)(Error("Can only use comparison operator on a single cell. Right side is not a cell."));let V=A.value.getAsNumber(0,0),M=P.value.getAsNumber(0,0);switch(this.operator){case">":return(0,jt.ok)(V.greaterThan(M));case">=":return(0,jt.ok)(V.greaterThanOrEqualTo(M));case"<":return(0,jt.ok)(V.lessThan(M));case"<=":return(0,jt.ok)(V.lessThanOrEqualTo(M));case"==":return(0,jt.ok)(V.equals(M));case"!=":return(0,jt.ok)(!V.equals(M));default:return(0,jt.err)(Error("Invalid conditional operator: "+this.operator))}};let c=(0,Ti.checkType)(r,"predicate");if(c)throw c;let d=(0,Ti.checkChildLength)(r,3);if(d)throw d;let C=(0,Ti.checkType)(r.children[1],"conditional_operator");if(C)throw C;this.operator=r.children[1].text;try{this.leftSource=new Z1.Source(r.children[0],l),this.rightSource=new Z1.Source(r.children[2],l)}catch(_){throw _}}}});var yl=we(V1=>{"use strict";Object.defineProperty(V1,"__esModule",{value:!0});V1.Constant=void 0;var N9=Xt(),L9=$t(),O9=Fn(),w3=class{constructor(r,l){let c=(0,L9.checkType)(r,"real","float");if(c)throw c;let d=r.text[0]==="-"?-1:1;r.type==="real"?this.value=d*parseInt(r.children[0].text):this.value=d*parseFloat(r.children[0].text+"."+r.children[1].text)}getValue(r,l){return(0,N9.ok)(new O9.Value([[this.value.toString()]]))}};V1.Constant=w3});var v3=we(Un=>{"use strict";Object.defineProperty(Un,"__esModule",{value:!0});Un.AbsoluteColumn=Un.Column=Un.newColumn=void 0;var kn=Xt(),Ri=$t(),S9=Fn(),P9=(s,r)=>{try{switch(s.type){case"relative_column":return(0,kn.ok)(new C3(s,r));case"absolute_column":return(0,kn.ok)(new K1(s,r));default:return(0,kn.err)(new Error(`Formula element '${s.text}' is a ${s.type} but expected an relatve_column or absolute_column in this position.`))}}catch(l){return(0,kn.err)(l)}};Un.newColumn=P9;var xi=class{constructor(){this.getValue=(r,l)=>{var c;let d=((c=r.getCellAt(l.row,this.getIndex(l)))===null||c===void 0?void 0:c.toText())||"";return(0,kn.ok)(new S9.Value([[d]]))}}};Un.Column=xi;var C3=class extends xi{constructor(r,l){super(),this.getIndex=_=>_.column+this.offset,this.getAbsoluteIndex=()=>(0,kn.err)(Ri.errRelativeReferenceIndex);let c=(0,Ri.checkType)(r,"relative_column");if(c)throw c;let d=(0,Ri.checkChildLength)(r,1);if(d)throw d;let C=r.text[1]==="-"?-1:1;this.offset=C*parseInt(r.children[0].text)}},K1=class extends xi{constructor(r,l){super(),this.getIndex=C=>this.index,this.getAbsoluteIndex=()=>(0,kn.ok)(this.index);let c=-1,d="";switch(r.children.length){case 0:d=r.text[1];break;case 1:let C=(0,Ri.checkType)(r.children[0],"int");if(C)throw(0,kn.err)(C);c=parseInt(r.children[0].text);break;default:throw new Error(`Formula element '${r.text}' is a ${r.type} but expected a 'absolute_column' in this position.`)}switch(d){case"":break;case"<":c=1;break;case">":c=l.getWidth();break;default:throw new Error(`Invalid column symbol '${d}'`)}if(c===0)throw Ri.errIndex0;this.index=c-1}};Un.AbsoluteColumn=K1});var b3=we(Hn=>{"use strict";Object.defineProperty(Hn,"__esModule",{value:!0});Hn.AbsoluteRow=Hn.Row=Hn.newRow=void 0;var qn=Xt(),yi=$t(),I9=Fn(),W9=(s,r)=>{try{switch(s.type){case"relative_row":return(0,qn.ok)(new _3(s,r));case"absolute_row":return(0,qn.ok)(new Y1(s,r));default:return(0,qn.err)(new Error(`Formula element '${s.text}' is a ${s.type} but expected an relatve_row or absolute_row  in this position.`))}}catch(l){return(0,qn.err)(l)}};Hn.newRow=W9;var Ni=class{constructor(){this.getValue=(r,l)=>{var c;let d=((c=r.getCellAt(this.getIndex(l),l.column))===null||c===void 0?void 0:c.toText())||"";return(0,qn.ok)(new I9.Value([[d]]))}}};Hn.Row=Ni;var _3=class extends Ni{constructor(r,l){super(),this.getIndex=_=>_.row+this.offset,this.getAbsoluteIndex=()=>(0,qn.err)(yi.errRelativeReferenceIndex);let c=(0,yi.checkType)(r,"relative_row");if(c)throw c;let d=(0,yi.checkChildLength)(r,1);if(d)throw d;let C=r.text[1]==="-"?-1:1;this.offset=C*parseInt(r.children[0].text)}},Y1=class extends Ni{constructor(r,l){super(),this.getIndex=C=>this.index,this.getAbsoluteIndex=()=>(0,qn.ok)(this.index);let c=-1,d="";switch(r.children.length){case 0:d=r.text[1];break;case 1:let C=(0,yi.checkType)(r.children[0],"int");if(C)throw(0,qn.err)(C);c=parseInt(r.children[0].text);break;default:throw new Error(`Formula element '${r.text}' is a ${r.type} but expected a 'absolute_row' in this position.`)}switch(d){case"":break;case"<":c=1;break;case">":c=l.getHeight()-1;break;case"I":c=2;break;default:throw new Error(`Invalid row symbol '${d}'`)}if(c===0)throw yi.errIndex0;c===1?this.index=0:this.index=c}};Hn.AbsoluteRow=Y1});var T3=we(J1=>{"use strict";Object.defineProperty(J1,"__esModule",{value:!0});J1.Reference=void 0;var M9=Xt(),E3=$t(),D9=v3(),F9=Fn(),k9=b3(),A3=class{constructor(r,l){this.getValue=(d,C)=>{var _;let m={row:this.row?this.row.getIndex(C):C.row,column:this.column?this.column.getIndex(C):C.column},A=((_=d.getCellAt(m.row,m.column))===null||_===void 0?void 0:_.toText())||"";return(0,M9.ok)(new F9.Value([[A]]))};let c=(0,E3.checkType)(r,"source_reference","absolute_reference","relative_reference");if(c)throw c;for(let d=0;d<r.children.length;d++){let C=r.children[d];switch(C.type){case"relative_row":case"absolute_row":if(this.row!==void 0)throw Error("Reference may only have at most 1 row, more than 1 provided");let _=(0,k9.newRow)(C,l);if(_.isErr()){if(_.error===E3.errIndex0)break;throw _.error}this.row=_.value;break;case"relative_column":case"absolute_column":if(this.column!==void 0)throw Error("Reference may only have at most 1 column, more than 1 provided");let m=(0,D9.newColumn)(C,l);if(m.isErr()){if(m.error===E3.errIndex0)break;throw m.error}this.column=m.value;break}}}};J1.Reference=A3});var x3=we(Q1=>{"use strict";Object.defineProperty(Q1,"__esModule",{value:!0});Q1.Range=void 0;var X1=Xt(),Mr=$t(),Nl=T3(),U9=Fn(),cr=Wr(),R3=class{constructor(r,l){this.getValue=(P,y)=>{let S=this.startColumn?this.startColumn.getIndex(y):y.column,V=this.endColumn?this.endColumn.getIndex(y):S,M=this.startRow?this.startRow.getIndex(y):y.row,Y=this.endRow?this.endRow.getIndex(y):y.row;return(0,X1.ok)(new U9.Value((0,cr.map)((0,cr.range)(M,Y+1),se=>(0,cr.map)((0,cr.range)(S,V+1),ge=>{var D;return((D=P.getCellAt(se,ge))===null||D===void 0?void 0:D.toText())||""}))))},this.asCells=()=>{if(!this.startColumn||!this.startRow||!this.endRow)return(0,X1.err)(new Error("A range used as a desintation must define rows and cells"));let P=this.endColumn;P||(P=this.startColumn);let y=this.startRow.getAbsoluteIndex(),S=this.endRow.getAbsoluteIndex(),V=this.startColumn.getAbsoluteIndex(),M=P.getAbsoluteIndex();if(y.isErr()||S.isErr()||V.isErr()||M.isErr())return(0,X1.err)(new Error("A relative range can not be used in a formula destination"));let Y=Math.min(y.value,S.value),se=Math.max(y.value,S.value),ge=Math.min(V.value,M.value),D=Math.max(V.value,M.value);return(0,X1.ok)((0,cr.flatMap)((0,cr.range)(Y,se+1),$=>(0,cr.range)(ge,D+1).map(U=>({row:$,column:U}))))};let c=(0,Mr.checkType)(r,"range");if(c)throw c;let d=(0,Mr.checkChildLength)(r,2);if(d)throw d;let C=r.children[0],_=r.children[1];if(c=(0,Mr.checkType)(C,"source_reference"),c||(c=(0,Mr.checkType)(_,"source_reference"),c))throw c;if(d=(0,Mr.checkChildLength)(C,1),d||(d=(0,Mr.checkChildLength)(_,1),d))throw d;let m=new Nl.Reference(C.children[0],l),A=new Nl.Reference(_.children[0],l);if(m.row&&!A.row||A.row&&!m.row)throw new Error("Range must use references of the same kind");if(!m.row&&!m.column)throw console.log(m),new Error("Range must have a row or a column defined");m.row&&(this.startRow=m.row),m.column&&(this.startColumn=m.column),A.row&&(this.endRow=A.row),A.column?this.endColumn=A.column:this.endColumn=m.column}};Q1.Range=R3});var Pl=we(Zt=>{"use strict";Object.defineProperty(Zt,"__esModule",{value:!0});Zt.RangeDestination=Zt.CellDestination=Zt.ColumnDestination=Zt.RowDestination=Zt.newDestination=void 0;var Pt=Xt(),wt=$t(),Ll=v3(),q9=x3(),Ol=b3(),Sl=Wr(),H9=(s,r,l)=>{let c=(0,wt.checkType)(s,"destination");if(c)return(0,Pt.err)(c);let d=(0,wt.checkChildLength)(s,1);if(d)return(0,Pt.err)(d);let C=s.children[0];if(C.type==="range")return(0,Pt.ok)(new no(C,r,l));try{switch(C.children.length){case 2:return(0,Pt.ok)(new to(C,r,l));case 1:let _=C.children[0];if(_.type==="absolute_row")return(0,Pt.ok)(new j1(C,r,l));if(_.type==="absolute_column")return(0,Pt.ok)(new eo(C,r,l));default:return(0,Pt.err)(new Error("Unexpected destination type "+C.type))}}catch(_){return _===wt.errIndex0?(0,Pt.err)(new Error("Index 0 may not be used in a destination")):(0,Pt.err)(_)}};Zt.newDestination=H9;var j1=class{constructor(r,l,c){this.merge=(m,A)=>{let P=(0,Sl.range)(0,A.getWidth()).map(y=>({row:this.row.index,column:y}));return ro(m,A,P,this.formatter)},this.formatter=c;let d=(0,wt.checkType)(r,"absolute_reference");if(d)throw d;let C=(0,wt.checkChildLength)(r,1);if(C)throw C;let _=r.children[0];try{this.row=new Ol.AbsoluteRow(_,l)}catch(m){throw m}}};Zt.RowDestination=j1;var eo=class{constructor(r,l,c){this.merge=(m,A)=>{let P=(0,Sl.range)(2,A.getHeight()).map(y=>({row:y,column:this.column.index}));return ro(m,A,P,this.formatter)},this.formatter=c;let d=(0,wt.checkType)(r,"absolute_reference");if(d)throw d;let C=(0,wt.checkChildLength)(r,1);if(C)throw C;let _=r.children[0];try{this.column=new Ll.AbsoluteColumn(_,l)}catch(m){throw m}}};Zt.ColumnDestination=eo;var to=class{constructor(r,l,c){this.merge=(A,P)=>{let y={row:this.row.index,column:this.column.index};return ro(A,P,[y],this.formatter)},this.formatter=c;let d=(0,wt.checkType)(r,"absolute_reference");if(d)throw d;let C=(0,wt.checkChildLength)(r,2);if(C)throw C;let _=r.children[0],m=r.children[1];try{this.row=new Ol.AbsoluteRow(_,l),this.column=new Ll.AbsoluteColumn(m,l)}catch(A){throw A}}};Zt.CellDestination=to;var no=class{constructor(r,l,c){this.merge=(_,m)=>this.range.asCells().andThen(A=>ro(_,m,A,this.formatter)),this.formatter=c;let d=(0,wt.checkType)(r,"range");if(d)throw d;let C=(0,wt.checkChildLength)(r,2);if(C)throw C;r.children.forEach(_=>{let m=(0,wt.checkType)(_,"source_reference");if(m)throw m;let A=(0,wt.checkChildLength)(_,1);if(A)throw A;if(m=(0,wt.checkType)(_.children[0],"absolute_reference"),m)throw m}),this.range=new q9.Range(r,l)}};Zt.RangeDestination=no;var ro=(s,r,l,c)=>l.reduce((d,C)=>d.andThen(_=>s.getValue(_,C).andThen(m=>(0,Pt.ok)(m.toString())).andThen(m=>(0,Pt.ok)(m.trim()===""?"0":m)).andThen(m=>(0,Pt.ok)(_.setCellAt(C.row,C.column,c.format(m))))),(0,Pt.ok)(r))});var Il=we(Dr=>{"use strict";Object.defineProperty(Dr,"__esModule",{value:!0});Dr.DisplayDirective=Dr.DefaultFormatter=void 0;var fr=$t(),y3=class{constructor(){this.format=r=>typeof r=="string"?r:r.toString()}};Dr.DefaultFormatter=y3;var N3=class{constructor(r){this.format=m=>{let A=typeof m=="string"?parseFloat(m):m;if(this.displayAsDatetime){let P=new Date(A),y=ge=>`0${ge}`.slice(-2),S=P.getFullYear(),V=y(P.getMonth()+1),M=y(P.getDate()),Y=y(P.getHours()),se=y(P.getMinutes());return`${S}-${V}-${M} ${Y}:${se}`}if(this.displayAsHourMinute){let P=A<0?"-":"",y=Math.floor(Math.abs(A)/6e4),S=Y=>`0${Y}`.slice(-2),V=S(Math.floor(y/60)),M=S(y%60);return`${P}${V}:${M}`}return A.toFixed(this.decimalLength)};let l=(0,fr.checkType)(r,"display_directive");if(l)throw l;let c=(0,fr.checkChildLength)(r,1);if(c)throw c;let d=r.children[0];if(l=(0,fr.checkType)(d,"display_directive_option"),l)throw l;if(c=(0,fr.checkChildLength)(d,1),c)throw c;let C=d.children[0];if(l=(0,fr.checkType)(C,"formatting_directive","datetime_directive","hourminute_directive"),l)throw l;if(this.displayAsDatetime=C.type==="datetime_directive",this.displayAsHourMinute=C.type==="hourminute_directive",this.displayAsDatetime||this.displayAsHourMinute){this.decimalLength=-1;return}if(c=(0,fr.checkChildLength)(C,1),c)throw c;let _=C.children[0];if(l=(0,fr.checkType)(_,"int"),l)throw l;this.decimalLength=parseInt(_.text)}};Dr.DisplayDirective=N3});var Wl=we(Fr=>{"use strict";var B9=Fr&&Fr.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Fr,"__esModule",{value:!0});Fr.SingleParamFunctionCall=void 0;var z9=Xt(),L3=$t(),$9=Ai(),O3=Fn(),Z9=B9(f3()),S3=class{constructor(r,l){this.getValue=(m,A)=>this.param.getValue(m,A).andThen(P=>(0,z9.ok)(this.op(P)));let c=(0,L3.checkType)(r,"single_param_function_call");if(c)throw c;let d=(0,L3.checkChildLength)(r,2);if(d)throw d;let C=(0,L3.checkType)(r.children[0],"single_param_function");if(C)throw C;let _=r.children[0].text;switch(_){case"sum":this.op=G9;break;case"mean":this.op=V9;break;default:throw Error("Unknown single param function call: "+_)}this.param=new $9.Source(r.children[1],l)}};Fr.SingleParamFunctionCall=S3;var G9=s=>{let r=s.val.reduce((l,c)=>c.reduce((d,C)=>(0,O3.FloatOrMilliseconds)(C).add(d),l),new Z9.default(0));return new O3.Value([[r.toString()]])},V9=s=>{let{total:r,count:l}=s.val.reduce(({total:c,count:d},C)=>C.reduce(({total:_,count:m},A)=>({total:_+ +A,count:m+1}),{total:c,count:d}),{total:0,count:0});return new O3.Value([[(r/l).toString()]])}});var oo=we(io=>{"use strict";Object.defineProperty(io,"__esModule",{value:!0});io.TokenError=void 0;var P3=class extends Error{constructor(r,l){if(super(r),this.message=r,this.token=l,l&&l.errors)l.errors.push(this);else throw this}inspect(){return"SyntaxError: "+this.message}};io.TokenError=P3});var Li=we(It=>{"use strict";Object.defineProperty(It,"__esModule",{value:!0});It.Parser=It.findRuleByName=It.parseRuleName=It.escapeRegExp=It.readToken=void 0;var Ml=/^[A-Z0-9_]+$/,Dl=/(\?|\+|\*)$/,Fl=/^(@|&|!)/,so="WS",lo=oo();function I3(s,r){let l=r.exec(s);return l&&l.index==0?l[0].length==0&&r.source.length>0?null:{type:null,text:l[0],rest:s.substr(l[0].length),start:0,end:l[0].length-1,fullText:l[0],errors:[],children:[],parent:null}:null}It.readToken=I3;function kl(s){return s.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}It.escapeRegExp=kl;function Ul(s){s.rest="",s.children&&s.children.forEach(r=>Ul(r))}function ql(s,r){s.start+=r,s.end+=r,s.children&&s.children.forEach(l=>ql(l,s.start))}function Hl(s,r){r.errors&&r.errors.length&&r.errors.forEach(l=>s.push(l)),r.children&&r.children.forEach(l=>Hl(s,l))}function hr(s){let r=Dl.exec(s),l=Fl.exec(s),c=r&&r[0]||"",d=l&&l[0]||"",C={raw:s,name:s.replace(Dl,"").replace(Fl,""),isOptional:c=="?"||c=="*",allowRepetition:c=="+"||c=="*",atLeastOne:c=="+",lookupPositive:d=="&",lookupNegative:d=="!",pinned:d=="@",lookup:!1,isLiteral:!1};return C.isLiteral=C.name[0]=="'"||C.name[0]=='"',C.lookup=C.lookupNegative||C.lookupPositive,C}It.parseRuleName=hr;function Bl(s,r){let l=hr(s);return r.cachedRules[l.name]||null}It.findRuleByName=Bl;function W3(s,r){if(s.children){let l=s.children.filter(c=>c.type&&r.test(c.type));for(let c=0;c<l.length;c++){let d=s.children.indexOf(l[c]);d!=-1&&s.children.splice(d,1)}s.children.forEach(c=>W3(c,r))}}var K9=["EOF"],uo=class{constructor(r,l){this.grammarRules=r,this.options=l,this.cachedRules={},this.debug=l?l.debug===!0:!1;let c=[],d=[];if(r.forEach(C=>{let _=hr(C.name);if(_.name in this.cachedRules){c.push("Duplicated rule "+_.name);return}else this.cachedRules[_.name]=C;if(!C.bnf||!C.bnf.length){let m="Missing rule content, rule: "+C.name;c.indexOf(m)==-1&&c.push(m)}else C.bnf.forEach(m=>{if(typeof m[0]=="string"&&hr(m[0]).name==C.name){let P="Left recursion is not allowed, rule: "+C.name;c.indexOf(P)==-1&&c.push(P)}m.forEach(A=>{if(typeof A=="string"){let P=hr(A);!P.isLiteral&&d.indexOf(P.name)==-1&&K9.indexOf(P.name)==-1&&d.push(P.name)}})});so==C.name&&(C.implicitWs=!1),C.implicitWs&&d.indexOf(so)==-1&&d.push(so),C.recover&&d.indexOf(C.recover)==-1&&d.push(C.recover)}),d.forEach(C=>{C in this.cachedRules||c.push("Missing rule "+C)}),c.length)throw new Error(c.join(`
`))}getAST(r,l){l||(l=this.grammarRules.filter(d=>!d.fragment&&d.name.indexOf("%")!=0)[0].name);let c=this.parse(r,l);if(c){Hl(c.errors,c),ql(c,0),W3(c,/^%/),(!this.options||!this.options.keepUpperRules)&&W3(c,Ml);let d=c.rest;d&&new lo.TokenError(`Unexpected end of input: 
`+d,c),Ul(c),c.rest=d}return c}emitSource(){return"CANNOT EMIT SOURCE FROM BASE Parser"}parse(r,l,c=0){let d=null,C=hr(l),_,m=this.debug&&!Ml.test(C.name);m&&console.log(new Array(c).join("\u2502  ")+"Trying to get "+l+" from "+JSON.stringify(r.split(`
`)[0]));let A=C.name,P=Bl(C.name,this);if(C.name=="EOF"){if(r.length)return null;if(r.length==0)return{type:"EOF",text:"",rest:"",start:0,end:0,fullText:"",errors:[],children:[],parent:null}}try{if(!P&&C.isLiteral){let y=C.name.trim();if(y.startsWith('"')?y=JSON.parse(y):y.startsWith("'")&&(y=y.replace(/^'(.+)'$/,"$1").replace(/\\'/g,"'")),y==="")return{type:"%%EMPTY%%",text:"",rest:r,start:0,end:0,fullText:"",errors:[],children:[],parent:null};_=new RegExp(kl(y)),A=null}}catch(y){return y instanceof ReferenceError&&console.error(y),null}if(_){let y=I3(r,_);if(y)return y.type=A,y}else{let y=P.bnf;y instanceof Array&&y.forEach(S=>{if(d)return;let V=null,M={type:C.name,text:"",children:[],end:0,errors:[],fullText:"",parent:null,start:0,rest:r};P.fragment&&(M.fragment=!0);let Y=r,se=0,ge=S.length>0,D=!1;for(let $=0;$<S.length;$++)if(typeof S[$]=="string"){let U=hr(S[$]);ge=ge&&U.isOptional;let K,te=!1;do{if(K=null,P.implicitWs&&(K=this.parse(Y,U.name,c+1),!K)){let J;do if(J=this.parse(Y,so,c+1),J)M.text=M.text+J.text,M.end=M.text.length,J.parent=M,M.children.push(J),Y=Y.substr(J.text.length),se+=J.text.length;else break;while(J&&J.text.length)}if(K=K||this.parse(Y,U.name,c+1),U.lookupNegative){if(K)return;break}if(U.lookupPositive&&!K)return;if(!K&&(U.isOptional||U.atLeastOne&&te))break;if(K&&P.pinned==$+1&&(V=K,m&&console.log(new Array(c+1).join("\u2502  ")+"\u2514\u2500 "+K.type+" PINNED")),K||(K=this.parseRecovery(P,Y,c+1)),!K)if(V)d=M,K={type:"SyntaxError",text:Y,children:[],end:Y.length,errors:[],fullText:"",parent:null,start:0,rest:""},Y.length?new lo.TokenError(`Unexpected end of input. Expecting ${U.name} Got: ${Y}`,K):new lo.TokenError(`Unexpected end of input. Missing ${U.name}`,K),m&&console.log(new Array(c+1).join("\u2502  ")+"\u2514\u2500 "+K.type+" "+JSON.stringify(K.text));else return;if(te=!0,D=!0,K.type=="%%EMPTY%%")break;K.start+=se,K.end+=se,!U.lookupPositive&&K.type&&(K.fragment?K.children&&K.children.forEach(J=>{J.start+=se,J.end+=se,J.parent=M,M.children.push(J)}):(K.parent=M,M.children.push(K))),U.lookup&&(K.lookup=!0),m&&console.log(new Array(c+1).join("\u2502  ")+"\u2514\u2500 "+K.type+" "+JSON.stringify(K.text)),!U.lookup&&!K.lookup&&(M.text=M.text+K.text,M.end=M.text.length,Y=Y.substr(K.text.length),se+=K.text.length),M.rest=Y}while(K&&U.allowRepetition&&Y.length&&!K.lookup)}else{let U=I3(Y,S[$]);if(!U)return;m&&console.log(new Array(c+1).join("\u2502  ")+"\u2514> "+JSON.stringify(U.text)+S[$].source),D=!0,U.start+=se,U.end+=se,M.text=M.text+U.text,M.end=M.text.length,Y=Y.substr(U.text.length),se+=U.text.length,M.rest=Y}D&&(d=M,m&&console.log(new Array(c).join("\u2502  ")+"\u251C<\u2500\u2534< PUSHING "+d.type+" "+JSON.stringify(d.text)))}),d&&P.simplifyWhenOneChildren&&d.children.length==1&&(d=d.children[0])}return d||m&&console.log(l+" NOT RESOLVED FROM "+r),d}parseRecovery(r,l,c){if(r.recover&&l.length){let d=this.debug;d&&console.log(new Array(c+1).join("\u2502  ")+"Trying to recover until token "+r.recover+" from "+JSON.stringify(l.split(`
`)[0]+l.split(`
`)[1]));let C={type:"SyntaxError",text:"",children:[],end:0,errors:[],fullText:"",parent:null,start:0,rest:""},_;do if(_=this.parse(l,r.recover,c+1),_){new lo.TokenError('Unexpected input: "'+C.text+`" Expecting: ${r.name}`,C);break}else C.text=C.text+l[0],C.end=C.text.length,l=l.substr(1);while(!_&&l.length>0);if(C.text.length>0&&_)return d&&console.log(new Array(c+1).join("\u2502  ")+"Recovered text: "+JSON.stringify(C.text)),C}return null}};It.Parser=uo;It.default=uo});var zl=we(ao=>{"use strict";Object.defineProperty(ao,"__esModule",{value:!0});ao.findChildrenByType=void 0;function Y9(s,r){return s.children?s.children.filter(l=>l.type==r):[]}ao.findChildrenByType=Y9});var $l=we(F3=>{"use strict";Object.defineProperty(F3,"__esModule",{value:!0});var Bn=zl(),M3=Li(),D3;(function(s){s.RULES=[{name:"syntax",bnf:[["RULE_EOL*","rule+"]]},{name:"rule",bnf:[['" "*','"<"',"rule-name",'">"','" "*','"::="',"firstExpression","otherExpression*",'" "*',"RULE_EOL+",'" "*']]},{name:"firstExpression",bnf:[['" "*',"list"]]},{name:"otherExpression",bnf:[['" "*','"|"','" "*',"list"]]},{name:"RULE_EOL",bnf:[['"\\r"'],['"\\n"']]},{name:"list",bnf:[["term",'" "*',"list"],["term"]]},{name:"term",bnf:[["literal"],['"<"',"rule-name",'">"']]},{name:"literal",bnf:[[`'"'`,"RULE_CHARACTER1*",`'"'`],[`"'"`,"RULE_CHARACTER2*",`"'"`]]},{name:"RULE_CHARACTER",bnf:[['" "'],["RULE_LETTER"],["RULE_DIGIT"],["RULE_SYMBOL"]]},{name:"RULE_LETTER",bnf:[['"A"'],['"B"'],['"C"'],['"D"'],['"E"'],['"F"'],['"G"'],['"H"'],['"I"'],['"J"'],['"K"'],['"L"'],['"M"'],['"N"'],['"O"'],['"P"'],['"Q"'],['"R"'],['"S"'],['"T"'],['"U"'],['"V"'],['"W"'],['"X"'],['"Y"'],['"Z"'],['"a"'],['"b"'],['"c"'],['"d"'],['"e"'],['"f"'],['"g"'],['"h"'],['"i"'],['"j"'],['"k"'],['"l"'],['"m"'],['"n"'],['"o"'],['"p"'],['"q"'],['"r"'],['"s"'],['"t"'],['"u"'],['"v"'],['"w"'],['"x"'],['"y"'],['"z"']]},{name:"RULE_DIGIT",bnf:[['"0"'],['"1"'],['"2"'],['"3"'],['"4"'],['"5"'],['"6"'],['"7"'],['"8"'],['"9"']]},{name:"RULE_SYMBOL",bnf:[['"-"'],['"_"'],['"!"'],['"#"'],['"$"'],['"%"'],['"&"'],['"("'],['")"'],['"*"'],['"+"'],['","'],['"-"'],['"."'],['"/"'],['":"'],['";"'],['"<"'],['"="'],['">"'],['"?"'],['"@"'],['"["'],['"\\"'],['"]"'],['"^"'],['"_"'],['"`"'],['"{"'],['"|"'],['"}"'],['"~"']]},{name:"RULE_CHARACTER1",bnf:[["RULE_CHARACTER"],[`"'"`]]},{name:"RULE_CHARACTER2",bnf:[["RULE_CHARACTER"],[`'"'`]]},{name:"rule-name",bnf:[["RULE_LETTER","RULE_CHAR*"]]},{name:"RULE_CHAR",bnf:[["RULE_LETTER"],["RULE_DIGIT"],['"_"'],['"-"']]}],s.defaultParser=new M3.Parser(s.RULES,{debug:!1});function r(C){let _=Bn.findChildrenByType(C,"term").map(m=>Bn.findChildrenByType(m,"literal").concat(Bn.findChildrenByType(m,"rule-name"))[0].text);return Bn.findChildrenByType(C,"list").forEach(m=>{_=_.concat(r(m))}),_}function l(C,_=s.defaultParser){let m=_.getAST(C);if(!m)throw new Error("Could not parse "+C);if(m.errors&&m.errors.length)throw m.errors[0];let P=Bn.findChildrenByType(m,"rule").map(y=>{let S=Bn.findChildrenByType(y,"rule-name")[0].text,V=Bn.findChildrenByType(y,"firstExpression").concat(Bn.findChildrenByType(y,"otherExpression")),M=[];return V.forEach(Y=>{M.push(r(Y))}),{name:S,bnf:M}});return P.some(y=>y.name=="EOL")||P.push({name:"EOL",bnf:[['"\\r\\n"','"\\r"','"\\n"']]}),P}s.getRules=l;function c(C,_=s.defaultParser){return l(C.join(""),_)}s.Transform=c;class d extends M3.Parser{constructor(_,m){let A=m&&m.debugRulesParser===!0?new M3.Parser(s.RULES,{debug:!0}):s.defaultParser;super(l(_,A),m),this.source=_}emitSource(){return this.source}}s.Parser=d})(D3||(D3={}));F3.default=D3});var Zl=we(U3=>{"use strict";Object.defineProperty(U3,"__esModule",{value:!0});var Oi=Li(),k3;(function(s){s.RULES=[{name:"Grammar",bnf:[["RULE_S*","%Atomic*","EOF"]]},{name:"%Atomic",bnf:[["Production","RULE_S*"]],fragment:!0},{name:"Production",bnf:[["NCName","RULE_S*",'"::="',"RULE_WHITESPACE*","Choice","RULE_WHITESPACE*","RULE_EOL+","RULE_S*"]]},{name:"NCName",bnf:[[/[a-zA-Z][a-zA-Z_0-9]*/]]},{name:"Choice",bnf:[["SequenceOrDifference","%_Choice_1*"]],fragment:!0},{name:"%_Choice_1",bnf:[["RULE_WHITESPACE*",'"|"',"RULE_WHITESPACE*","SequenceOrDifference"]],fragment:!0},{name:"SequenceOrDifference",bnf:[["Item","RULE_WHITESPACE*","%_Item_1?"]]},{name:"%_Item_1",bnf:[["Minus","Item"],["Item*"]],fragment:!0},{name:"Minus",bnf:[['"-"']]},{name:"Item",bnf:[["RULE_WHITESPACE*","%Primary","PrimaryDecoration?"]],fragment:!0},{name:"PrimaryDecoration",bnf:[['"?"'],['"*"'],['"+"']]},{name:"DecorationName",bnf:[['"ebnf://"',/[^\x5D#]+/]]},{name:"%Primary",bnf:[["NCName"],["StringLiteral"],["CharCode"],["CharClass"],["SubItem"]],fragment:!0},{name:"SubItem",bnf:[['"("',"RULE_WHITESPACE*","Choice","RULE_WHITESPACE*",'")"']]},{name:"StringLiteral",bnf:[[`'"'`,/[^"]*/,`'"'`],[`"'"`,/[^']*/,`"'"`]],pinned:1},{name:"CharCode",bnf:[['"#x"',/[0-9a-zA-Z]+/]]},{name:"CharClass",bnf:[["'['","'^'?","%RULE_CharClass_1+",'"]"']]},{name:"%RULE_CharClass_1",bnf:[["CharCodeRange"],["CharRange"],["CharCode"],["RULE_Char"]],fragment:!0},{name:"RULE_Char",bnf:[[/\x09/],[/\x0A/],[/\x0D/],[/[\x20-\x5c]/],[/[\x5e-\uD7FF]/],[/[\uE000-\uFFFD]/]]},{name:"CharRange",bnf:[["RULE_Char",'"-"',"RULE_Char"]]},{name:"CharCodeRange",bnf:[["CharCode",'"-"',"CharCode"]]},{name:"RULE_WHITESPACE",bnf:[["%RULE_WHITESPACE_CHAR*"],["Comment","RULE_WHITESPACE*"]]},{name:"RULE_S",bnf:[["RULE_WHITESPACE","RULE_S*"],["RULE_EOL","RULE_S*"]]},{name:"%RULE_WHITESPACE_CHAR",bnf:[[/\x09/],[/\x20/]],fragment:!0},{name:"Comment",bnf:[['"/*"',"%RULE_Comment_Body*",'"*/"']]},{name:"%RULE_Comment_Body",bnf:[['!"*/"',/[^*]/]],fragment:!0},{name:"RULE_EOL",bnf:[[/\x0D/,/\x0A/],[/\x0A/],[/\x0D/]]},{name:"Link",bnf:[["'['","Url","']'"]]},{name:"Url",bnf:[[/[^\x5D:/?#]/,'"://"',/[^\x5D#]+/,"%Url1?"]]},{name:"%Url1",bnf:[['"#"',"NCName"]],fragment:!0}],s.defaultParser=new Oi.Parser(s.RULES,{debug:!1});let r=/^(!|&)/,l=/(\?|\+|\*)$/,c=/^%/;function d(D,$){if(typeof D=="string"){if(r.test(D))return"";if(c.test(D)){let K=l.exec(D),te=K?K[0]+" ":"";return C(D,$)?m(D,$)+te:"("+m(D,$)+")"+te}return D}else return D.source.replace(/\\(?:x|u)([a-zA-Z0-9]+)/g,"#x$1").replace(/\[\\(?:x|u)([a-zA-Z0-9]+)-\\(?:x|u)([a-zA-Z0-9]+)\]/g,"[#x$1-#x$2]")}function C(D,$){let U=Oi.findRuleByName(D,$);return U&&U.bnf.length==1&&U.bnf[0].length==1&&(U.bnf[0][0]instanceof RegExp||U.bnf[0][0][0]=='"'||U.bnf[0][0][0]=="'")}function _(D,$){return D.map(U=>d(U,$)).join(" ")}function m(D,$){let U=Oi.findRuleByName(D,$);return U?U.bnf.map(K=>_(K,$)).join(" | "):"RULE_NOT_FOUND {"+D+"}"}function A(D){let $=[];return D.grammarRules.forEach(U=>{if(!/^%/.test(U.name)){let K=U.recover?" /* { recoverUntil="+U.recover+" } */":"";$.push(U.name+" ::= "+m(U.name,D)+K)}}),$.join(`
`)}s.emit=A;let P=0;function y(D,$){throw console.log("reberia restar "+$+" a "+D),new Error("Difference not supported yet")}function S(D){return new RegExp(D.replace(/#x([a-zA-Z0-9]{4})/g,"\\u$1").replace(/#x([a-zA-Z0-9]{3})/g,"\\u0$1").replace(/#x([a-zA-Z0-9]{2})/g,"\\x$1").replace(/#x([a-zA-Z0-9]{1})/g,"\\x0$1"))}function V(D,$,U){let K=null,te=[];return $.children.forEach((J,Q)=>{J.type=="Minus"&&y(K,J);let G=$.children[Q+1];G=G&&G.type=="PrimaryDecoration"&&G.text||"";let Ce="";switch(J.type){case"SubItem":let Pe="%"+(U+P++);M(D,J,Pe),te.push(Ce+Pe+G);break;case"NCName":case"StringLiteral":te.push(Ce+J.text+G);break;case"CharCode":case"CharClass":if(G||Ce){let ot={name:"%"+(U+P++),bnf:[[S(J.text)]]};D.push(ot),te.push(Ce+ot.name+G)}else te.push(S(J.text));break;case"PrimaryDecoration":break;default:throw new Error(" HOW SHOULD I PARSE THIS? "+J.type+" -> "+JSON.stringify(J.text))}K=J}),te}function M(D,$,U){let K=$.children.filter(Q=>Q.type=="SequenceOrDifference").map(Q=>V(D,Q,U)),te={name:U,bnf:K},J=null;K.forEach(Q=>{J=J||Q.recover,delete Q.recover}),U.indexOf("%")==0&&(te.fragment=!0),J&&(te.recover=J),D.push(te)}function Y(D,$=s.defaultParser){let U=$.getAST(D);if(!U)throw new Error("Could not parse "+D);if(U.errors&&U.errors.length)throw U.errors[0];let K=[];return U.children.filter(te=>te.type=="Production").map(te=>{let J=te.children.filter(Q=>Q.type=="NCName")[0].text;M(K,te,J)}),K}s.getRules=Y;function se(D,$=s.defaultParser){return Y(D.join(""),$)}s.Transform=se;class ge extends Oi.Parser{constructor($,U){let K=U&&U.debugRulesParser===!0?new Oi.Parser(s.RULES,{debug:!0}):s.defaultParser;super(Y($,K),U)}emitSource(){return A(this)}}s.Parser=ge})(k3||(k3={}));U3.default=k3});var Gl=we(H3=>{"use strict";Object.defineProperty(H3,"__esModule",{value:!0});var co=oo(),kr=Li(),q3;(function(s){s.RULES=[{name:"Grammar",bnf:[["RULE_S*","Attributes?","RULE_S*","%Atomic*","EOF"]]},{name:"%Atomic",bnf:[["Production","RULE_S*"]],fragment:!0},{name:"Production",bnf:[["NCName","RULE_S*",'"::="',"RULE_WHITESPACE*","%Choice","RULE_WHITESPACE*","Attributes?","RULE_EOL+","RULE_S*"]]},{name:"NCName",bnf:[[/[a-zA-Z][a-zA-Z_0-9]*/]]},{name:"Attributes",bnf:[['"{"',"Attribute","%Attributes*","RULE_S*",'"}"']]},{name:"%Attributes",bnf:[["RULE_S*",'","',"Attribute"]],fragment:!0},{name:"Attribute",bnf:[["RULE_S*","NCName","RULE_WHITESPACE*",'"="',"RULE_WHITESPACE*","AttributeValue"]]},{name:"AttributeValue",bnf:[["NCName"],[/[1-9][0-9]*/]]},{name:"%Choice",bnf:[["SequenceOrDifference","%_Choice_1*"]],fragment:!0},{name:"%_Choice_1",bnf:[["RULE_S*",'"|"',"RULE_S*","SequenceOrDifference"]],fragment:!0},{name:"SequenceOrDifference",bnf:[["%Item","RULE_WHITESPACE*","%_Item_1?"]]},{name:"%_Item_1",bnf:[["Minus","%Item"],["%Item*"]],fragment:!0},{name:"Minus",bnf:[['"-"']]},{name:"%Item",bnf:[["RULE_WHITESPACE*","PrimaryPreDecoration?","%Primary","PrimaryDecoration?"]],fragment:!0},{name:"PrimaryDecoration",bnf:[['"?"'],['"*"'],['"+"']]},{name:"PrimaryPreDecoration",bnf:[['"&"'],['"!"'],['"~"']]},{name:"%Primary",bnf:[["NCName"],["StringLiteral"],["CharCode"],["CharClass"],["SubItem"]],fragment:!0},{name:"SubItem",bnf:[['"("',"RULE_S*","%Choice","RULE_S*",'")"']]},{name:"StringLiteral",bnf:[[`'"'`,/[^"]*/,`'"'`],[`"'"`,/[^']*/,`"'"`]]},{name:"CharCode",bnf:[['"#x"',/[0-9a-zA-Z]+/]]},{name:"CharClass",bnf:[["'['","'^'?","%RULE_CharClass_1+",'"]"']]},{name:"%RULE_CharClass_1",bnf:[["CharCodeRange"],["CharRange"],["CharCode"],["RULE_Char"]],fragment:!0},{name:"RULE_Char",bnf:[[/\x09/],[/\x0A/],[/\x0D/],[/[\x20-\x5c]/],[/[\x5e-\uD7FF]/],[/[\uE000-\uFFFD]/]]},{name:"CharRange",bnf:[["RULE_Char",'"-"',"RULE_Char"]]},{name:"CharCodeRange",bnf:[["CharCode",'"-"',"CharCode"]]},{name:"RULE_WHITESPACE",bnf:[["%RULE_WHITESPACE_CHAR*"],["Comment","RULE_WHITESPACE*"]]},{name:"RULE_S",bnf:[["RULE_WHITESPACE","RULE_S*"],["RULE_EOL","RULE_S*"]]},{name:"%RULE_WHITESPACE_CHAR",bnf:[[/\x09/],[/\x20/]],fragment:!0},{name:"Comment",bnf:[['"/*"',"%RULE_Comment_Body*",'"*/"']]},{name:"%RULE_Comment_Body",bnf:[[/[^*]/],['"*"+',/[^/]*/]],fragment:!0},{name:"RULE_EOL",bnf:[[/\x0D/,/\x0A/],[/\x0A/],[/\x0D/]]},{name:"Link",bnf:[["'['","Url","']'"]]},{name:"Url",bnf:[[/[^\x5D:/?#]/,'"://"',/[^\x5D#]+/,"%Url1?"]]},{name:"%Url1",bnf:[['"#"',"NCName"]],fragment:!0}],s.defaultParser=new kr.Parser(s.RULES,{debug:!1});let r=/^(!|&)/,l=/(\?|\+|\*)$/,c=/^%/;function d(D,$){if(typeof D=="string"){let U=l.exec(D),K=r.exec(D),te=K?K[0]:"",J=U?U[0]+" ":"";return c.test(D)?C(D,$)?te+m(D,$)+J:te+"("+m(D,$)+")"+J:D.replace(r,te)}else return D.source.replace(/\\(?:x|u)([a-zA-Z0-9]+)/g,"#x$1").replace(/\[\\(?:x|u)([a-zA-Z0-9]+)-\\(?:x|u)([a-zA-Z0-9]+)\]/g,"[#x$1-#x$2]")}function C(D,$){let U=kr.findRuleByName(D,$);return U&&U.bnf.length==1&&U.bnf[0].length==1&&(U.bnf[0][0]instanceof RegExp||U.bnf[0][0][0]=='"'||U.bnf[0][0][0]=="'")}function _(D,$){return D.map(U=>d(U,$)).join(" ")}function m(D,$){let U=kr.findRuleByName(D,$);return U?U.bnf.map(K=>_(K,$)).join(" | "):"RULE_NOT_FOUND {"+D+"}"}function A(D){let $=[];return D.grammarRules.forEach(U=>{if(!/^%/.test(U.name)){let K=U.recover?" { recoverUntil="+U.recover+" }":"";$.push(U.name+" ::= "+m(U.name,D)+K)}}),$.join(`
`)}s.emit=A;let P=0;function y(D,$){throw console.log("reberia restar "+$+" a "+D),new Error("Difference not supported yet")}function S(D){return new RegExp(D.replace(/#x([a-zA-Z0-9]{4})/g,"\\u$1").replace(/#x([a-zA-Z0-9]{3})/g,"\\u0$1").replace(/#x([a-zA-Z0-9]{2})/g,"\\x$1").replace(/#x([a-zA-Z0-9]{1})/g,"\\x0$1"))}function V(D,$,U,K){let te=null,J=[];return $.children.forEach((Q,G)=>{Q.type=="Minus"&&y(te,Q);let Ce=$.children[G+1];Ce=Ce&&Ce.type=="PrimaryDecoration"&&Ce.text||"";let Pe="";te&&te.type=="PrimaryPreDecoration"&&(Pe=te.text);let ot=Pe=="~"?1:void 0;switch(ot&&(Pe=""),Q.type){case"SubItem":let B="%"+(U+P++);M(D,Q,B,K),J.push(Pe+B+Ce);break;case"NCName":J.push(Pe+Q.text+Ce);break;case"StringLiteral":if(Ce||Pe||!/^['"/()a-zA-Z0-9&_.:=,+*\-\^\\]+$/.test(Q.text))J.push(Pe+Q.text+Ce);else for(let Oe of Q.text.slice(1,-1))K&&K.ignoreCase=="true"&&/[a-zA-Z]/.test(Oe)?J.push(new RegExp("["+Oe.toUpperCase()+Oe.toLowerCase()+"]")):J.push(new RegExp(kr.escapeRegExp(Oe)));break;case"CharCode":case"CharClass":if(Ce||Pe){let Oe={name:"%"+(U+P++),bnf:[[S(Q.text)]],pinned:ot};D.push(Oe),J.push(Pe+Oe.name+Ce)}else J.push(S(Q.text));break;case"PrimaryPreDecoration":case"PrimaryDecoration":break;default:throw new Error(" HOW SHOULD I PARSE THIS? "+Q.type+" -> "+JSON.stringify(Q.text))}te=Q}),J}function M(D,$,U,K=void 0){let te=$.children.filter(Ce=>Ce.type=="Attributes")[0],J={};te&&te.children.forEach(Ce=>{let Pe=Ce.children.filter(ot=>ot.type=="NCName")[0].text;if(Pe in J)throw new co.TokenError("Duplicated attribute "+Pe,Ce);J[Pe]=Ce.children.filter(ot=>ot.type=="AttributeValue")[0].text});let Q=$.children.filter(Ce=>Ce.type=="SequenceOrDifference").map(Ce=>V(D,Ce,U,K||J)),G={name:U,bnf:Q};if(U.indexOf("%")==0&&(G.fragment=!0),J.recoverUntil&&(G.recover=J.recoverUntil,G.bnf.length>1))throw new co.TokenError("only one-option productions are suitable for error recovering",$);if("pin"in J){let Ce=parseInt(J.pin);if(isNaN(Ce)||(G.pinned=Ce),G.bnf.length>1)throw new co.TokenError("only one-option productions are suitable for pinning",$)}"ws"in J?G.implicitWs=J.ws!="explicit":G.implicitWs=null,G.fragment=G.fragment||J.fragment=="true",G.simplifyWhenOneChildren=J.simplifyWhenOneChildren=="true",D.push(G)}function Y(D,$=s.defaultParser){let U=$.getAST(D);if(!U)throw new Error("Could not parse "+D);if(U.errors&&U.errors.length)throw U.errors[0];let K=null,te=U.children.filter(G=>G.type=="Attributes")[0],J={};te&&te.children.forEach(G=>{let Ce=G.children.filter(Pe=>Pe.type=="NCName")[0].text;if(Ce in J)throw new co.TokenError("Duplicated attribute "+Ce,G);J[Ce]=G.children.filter(Pe=>Pe.type=="AttributeValue")[0].text}),K=J.ws=="implicit";let Q=[];return U.children.filter(G=>G.type=="Production").map(G=>{let Ce=G.children.filter(Pe=>Pe.type=="NCName")[0].text;M(Q,G,Ce)}),Q.forEach(G=>{G.implicitWs===null&&(G.implicitWs=K)}),Q}s.getRules=Y;function se(D,$=s.defaultParser){return Y(D.join(""),$)}s.Transform=se;class ge extends kr.Parser{constructor($,U){let K=U&&U.debugRulesParser===!0?new kr.Parser(s.RULES,{debug:!0}):s.defaultParser;super(Y($,K),U)}emitSource(){return A(this)}}s.Parser=ge})(q3||(q3={}));H3.default=q3});var Vl=we(Si=>{"use strict";Object.defineProperty(Si,"__esModule",{value:!0});var J9=$l();Object.defineProperty(Si,"BNF",{enumerable:!0,get:function(){return J9.default}});var X9=Zl();Object.defineProperty(Si,"W3C",{enumerable:!0,get:function(){return X9.default}});var Q9=Gl();Object.defineProperty(Si,"Custom",{enumerable:!0,get:function(){return Q9.default}})});var Kl=we(Pi=>{"use strict";Object.defineProperty(Pi,"__esModule",{value:!0});var j9=Li();Object.defineProperty(Pi,"Parser",{enumerable:!0,get:function(){return j9.Parser}});var e7=oo();Object.defineProperty(Pi,"TokenError",{enumerable:!0,get:function(){return e7.TokenError}});Pi.Grammars=Vl()});var Ai=we(en=>{"use strict";Object.defineProperty(en,"__esModule",{value:!0});en.parseFormula=en.parseAndApply=en.Source=en.Formula=void 0;var Xe=Xt(),t7=Rl(),B3=$t(),n7=xl(),Yl=yl(),r7=Pl(),Jl=Il(),i7=x3(),o7=T3(),s7=Wl(),l7=Kl(),u7=Wr(),a7=`
tblfm_line   ::= "<!-- TBLFM: " formula_list " -->"
formula_list ::= formula ( "::" formula_list )?
formula      ::= destination "=" source display_directive?

source           ::= range | source_reference | single_param_function_call | conditional_function_call | algebraic_operation | float | real
range            ::= source_reference ".." source_reference
source_reference ::= absolute_reference | relative_reference
destination      ::= range | absolute_reference

relative_reference ::= (relative_row | absolute_row) (relative_column | absolute_column) | relative_row | relative_column
relative_row ::= "@" ( "-" | "+" ) int
relative_column ::= "$" ( "-" | "+" ) int

absolute_reference ::= absolute_row absolute_column | absolute_row | absolute_column
absolute_row ::= "@" ( "I" | "<" | ">" | int )
absolute_column ::= "$" ( "<" | ">" | int )

single_param_function_call ::= single_param_function "(" source ")" 
single_param_function      ::= "mean" | "sum"

conditional_function_call ::= "if(" predicate "," " "? source "," " "? source ")"
predicate                 ::= source_without_range conditional_operator source_without_range
source_without_range      ::= source_reference | single_param_function_call | conditional_function_call | algebraic_operation | float | real
conditional_operator      ::= ">" | "<" | ">=" | "<=" | "==" | "!="

algebraic_operation ::= "(" source " "? algebraic_operator " "? source ")"
algebraic_operator  ::= "+" | "-" | "*" | "/"

display_directive        ::= ";" display_directive_option
display_directive_option ::= formatting_directive | datetime_directive | hourminute_directive
formatting_directive     ::= "%." int "f"
datetime_directive       ::= "dt"
hourminute_directive     ::= "hm"

float ::= "-"? int "." int
real ::= "-"? int
int  ::= [0-9]+
`,fo=class{constructor(r,l){this.merge=C=>this.destination.merge(this.source,C);let c=new Jl.DefaultFormatter;r.children.length===3&&(c=new Jl.DisplayDirective(r.children[2]));let d=(0,r7.newDestination)(r.children[0],l,c);if(d.isErr())throw d.error;this.destination=d.value,this.source=new ho(r.children[1],l)}};en.Formula=fo;var ho=class{constructor(r,l){if(this.getValue=(C,_)=>this.locationDescriptor.getValue(C,_),r.type!=="source"&&r.type!=="source_without_range")throw Error("Invalid AST token type of "+r.type);if(r.children.length!==1)throw Error("Unexpected children length in Source");let c=r.children[0],d=c7(c,l);if(d.isErr())throw d.error;this.locationDescriptor=d.value}};en.Source=ho;var c7=(s,r)=>{try{switch(s.type){case"range":return(0,Xe.ok)(new i7.Range(s,r));case"source_reference":let l=(0,B3.checkChildLength)(s,1);return l?(0,Xe.err)(l):(0,Xe.ok)(new o7.Reference(s.children[0],r));case"single_param_function_call":return(0,Xe.ok)(new s7.SingleParamFunctionCall(s,r));case"conditional_function_call":return(0,Xe.ok)(new n7.ConditionalFunctionCall(s,r));case"algebraic_operation":return(0,Xe.ok)(new t7.AlgebraicOperation(s,r));case"real":return(0,Xe.ok)(new Yl.Constant(s,r));case"float":return(0,Xe.ok)(new Yl.Constant(s,r));default:throw Error("Unrecognized valueProvider type "+s.type)}}catch(l){return(0,Xe.err)(l)}},f7=(s,r)=>s.reduce((c,d)=>c.andThen(C=>{let _=(0,en.parseFormula)(d,r);return _.isErr()?_:(0,Xe.ok)((0,u7.concat)(_.value,C))}),(0,Xe.ok)([])).andThen(c=>c.reduceRight((d,C)=>d.andThen(_=>C.merge(_)),(0,Xe.ok)(r)));en.parseAndApply=f7;var h7=(s,r)=>{let c=new l7.Grammars.W3C.Parser(a7).getAST(s);if(!c)return(0,Xe.err)(new Error(`Formula '${s}' could not be parsed`));let d=(0,B3.checkType)(c,"tblfm_line");if(d)return(0,Xe.err)(d);let C=(0,B3.checkChildLength)(c,1);if(C)return(0,Xe.err)(C);let _=c.children[0].children,m=[];try{do m.push(new fo(_[0],r)),_.length>1&&_[1].type==="formula_list"?_=_[1].children:_=[];while(_.length>0);return(0,Xe.ok)(m)}catch(A){return(0,Xe.err)(A)}};en.parseFormula=h7});var Ii=we(po=>{"use strict";Object.defineProperty(po,"__esModule",{value:!0});po.Table=void 0;var d7=Ai(),Xl=W1(),go=O1(),g7=P1(),z3=class s{constructor(r){this._rows=r.slice()}getHeight(){return this._rows.length}getWidth(){return this._rows.map(r=>r.getWidth()).reduce((r,l)=>Math.max(r,l),0)}getHeaderWidth(){return this._rows[0].getWidth()}getRows(){return this._rows.slice()}getDelimiterRow(){let r=this._rows[1];if(r!==void 0&&r.isDelimiter())return r}getCellAt(r,l){let c=this._rows[r];if(c!==void 0)return c.getCellAt(l)}getFocusedCell(r){return this.getCellAt(r.row,r.column)}toLines(){return this._rows.map(r=>r.toText())}setCellAt(r,l,c){let d=this.getRows();return d[r]=d[r].setCellAt(l,c),new s(d)}focusOfPosition(r,l){let c=r.row-l,d=this._rows[c];if(d===void 0)return;if(r.column<d.marginLeft.length+1)return new Xl.Focus(c,-1,r.column);let C=d.getCells().map(P=>P.rawContent.length),_=d.marginLeft.length+1,m=0;for(;m<C.length&&!(_+C[m]+1>r.column);m++)_+=C[m]+1;let A=r.column-_;return new Xl.Focus(c,m,A)}positionOfFocus(r,l){let c=this._rows[r.row];if(c===void 0)return;let d=r.row+l;if(r.column<0)return new go.Point(d,r.offset);let C=c.getCells().map(A=>A.rawContent.length),_=Math.min(r.column,C.length),m=c.marginLeft.length+1;for(let A=0;A<_;A++)m+=C[A]+1;return new go.Point(d,m+r.offset)}selectionRangeOfFocus(r,l){let c=this._rows[r.row];if(c===void 0)return;let d=c.getCellAt(r.column);if(d===void 0||d.content==="")return;let C=r.row+l,_=c.getCells().map(A=>A.rawContent.length),m=c.marginLeft.length+1;for(let A=0;A<r.column;A++)m+=_[A]+1;return m+=d.paddingLeft,new g7.Range(new go.Point(C,m),new go.Point(C,m+d.content.length))}applyFormulas(r){return(0,d7.parseAndApply)(r,this)}};po.Table=z3});var $3=we(rt=>{"use strict";Object.defineProperty(rt,"__esModule",{value:!0});rt.readTable=rt._marginRegex=rt.marginRegexSrc=rt._readRow=rt._splitCells=void 0;var p7=Ii(),m7=Pr(),w7=_i(),C7=s=>{let r=[],l="",c=s;for(;c!=="";)switch(c[0]){case"`":{let d=c.match(/^`*/);if(d===null)break;let C=d[0],_=C,m=c.substr(C.length),A=!1;for(;m!=="";)if(m[0]==="`"){let P=m.match(/^`*/);if(P===null)break;let y=P[0];if(_+=y,m=m.substr(y.length),y.length===C.length){A=!0;break}}else _+=m[0],m=m.substr(1);A?(l+=_,c=m):(l+="`",c=c.substr(1))}break;case"\\":c.length>=2?(l+=c.substr(0,2),c=c.substr(2)):(l+="\\",c=c.substr(1));break;case"[":if(l+="[",c=c.substr(1),/\[[^\\|\]]+\|[^|\]]+]]/.test(c)){let d=c.indexOf("|");l+=c.slice(0,d),l+="\\|",c=c.substr(d+1)}break;case"|":r.push(l),l="",c=c.substr(1);break;default:l+=c[0],c=c.substr(1)}return r.push(l),r};rt._splitCells=C7;var v7=(s,r=/^\s*$/)=>{let l=(0,rt._splitCells)(s),c;l.length>0&&r.test(l[0])?(c=l[0],l=l.slice(1)):c="";let d;return l.length>1&&/^\s*$/.test(l[l.length-1])?(d=l[l.length-1],l=l.slice(0,l.length-1)):d="",new w7.TableRow(l.map(C=>new m7.TableCell(C)),c,d)};rt._readRow=v7;var _7=s=>{let r="";return s.forEach(l=>{l!=="|"&&l!=="\\"&&l!=="`"&&(r+=`\\u{${l.codePointAt(0).toString(16)}}`)}),`[\\s${r}]*`};rt.marginRegexSrc=_7;var b7=s=>new RegExp(`^${(0,rt.marginRegexSrc)(s)}$`,"u");rt._marginRegex=b7;var E7=(s,r)=>{let l=(0,rt._marginRegex)(r.leftMarginChars);return new p7.Table(s.map(c=>(0,rt._readRow)(c,l)))};rt.readTable=E7});var jl=we(Wi=>{"use strict";Object.defineProperty(Wi,"__esModule",{value:!0});var Z3=[[0,31,"N"],[32,126,"Na"],[127,160,"N"],[161,161,"A"],[162,163,"Na"],[164,164,"A"],[165,166,"Na"],[167,168,"A"],[169,169,"N"],[170,170,"A"],[171,171,"N"],[172,172,"Na"],[173,174,"A"],[175,175,"Na"],[176,180,"A"],[181,181,"N"],[182,186,"A"],[187,187,"N"],[188,191,"A"],[192,197,"N"],[198,198,"A"],[199,207,"N"],[208,208,"A"],[209,214,"N"],[215,216,"A"],[217,221,"N"],[222,225,"A"],[226,229,"N"],[230,230,"A"],[231,231,"N"],[232,234,"A"],[235,235,"N"],[236,237,"A"],[238,239,"N"],[240,240,"A"],[241,241,"N"],[242,243,"A"],[244,246,"N"],[247,250,"A"],[251,251,"N"],[252,252,"A"],[253,253,"N"],[254,254,"A"],[255,256,"N"],[257,257,"A"],[258,272,"N"],[273,273,"A"],[274,274,"N"],[275,275,"A"],[276,282,"N"],[283,283,"A"],[284,293,"N"],[294,295,"A"],[296,298,"N"],[299,299,"A"],[300,304,"N"],[305,307,"A"],[308,311,"N"],[312,312,"A"],[313,318,"N"],[319,322,"A"],[323,323,"N"],[324,324,"A"],[325,327,"N"],[328,331,"A"],[332,332,"N"],[333,333,"A"],[334,337,"N"],[338,339,"A"],[340,357,"N"],[358,359,"A"],[360,362,"N"],[363,363,"A"],[364,461,"N"],[462,462,"A"],[463,463,"N"],[464,464,"A"],[465,465,"N"],[466,466,"A"],[467,467,"N"],[468,468,"A"],[469,469,"N"],[470,470,"A"],[471,471,"N"],[472,472,"A"],[473,473,"N"],[474,474,"A"],[475,475,"N"],[476,476,"A"],[477,592,"N"],[593,593,"A"],[594,608,"N"],[609,609,"A"],[610,707,"N"],[708,708,"A"],[709,710,"N"],[711,711,"A"],[712,712,"N"],[713,715,"A"],[716,716,"N"],[717,717,"A"],[718,719,"N"],[720,720,"A"],[721,727,"N"],[728,731,"A"],[732,732,"N"],[733,733,"A"],[734,734,"N"],[735,735,"A"],[736,767,"N"],[768,879,"A"],[880,912,"N"],[913,929,"A"],[930,930,"N"],[931,937,"A"],[938,944,"N"],[945,961,"A"],[962,962,"N"],[963,969,"A"],[970,1024,"N"],[1025,1025,"A"],[1026,1039,"N"],[1040,1103,"A"],[1104,1104,"N"],[1105,1105,"A"],[1106,4351,"N"],[4352,4447,"W"],[4448,8207,"N"],[8208,8208,"A"],[8209,8210,"N"],[8211,8214,"A"],[8215,8215,"N"],[8216,8217,"A"],[8218,8219,"N"],[8220,8221,"A"],[8222,8223,"N"],[8224,8226,"A"],[8227,8227,"N"],[8228,8231,"A"],[8232,8239,"N"],[8240,8240,"A"],[8241,8241,"N"],[8242,8243,"A"],[8244,8244,"N"],[8245,8245,"A"],[8246,8250,"N"],[8251,8251,"A"],[8252,8253,"N"],[8254,8254,"A"],[8255,8307,"N"],[8308,8308,"A"],[8309,8318,"N"],[8319,8319,"A"],[8320,8320,"N"],[8321,8324,"A"],[8325,8360,"N"],[8361,8361,"H"],[8362,8363,"N"],[8364,8364,"A"],[8365,8450,"N"],[8451,8451,"A"],[8452,8452,"N"],[8453,8453,"A"],[8454,8456,"N"],[8457,8457,"A"],[8458,8466,"N"],[8467,8467,"A"],[8468,8469,"N"],[8470,8470,"A"],[8471,8480,"N"],[8481,8482,"A"],[8483,8485,"N"],[8486,8486,"A"],[8487,8490,"N"],[8491,8491,"A"],[8492,8530,"N"],[8531,8532,"A"],[8533,8538,"N"],[8539,8542,"A"],[8543,8543,"N"],[8544,8555,"A"],[8556,8559,"N"],[8560,8569,"A"],[8570,8584,"N"],[8585,8585,"A"],[8586,8591,"N"],[8592,8601,"A"],[8602,8631,"N"],[8632,8633,"A"],[8634,8657,"N"],[8658,8658,"A"],[8659,8659,"N"],[8660,8660,"A"],[8661,8678,"N"],[8679,8679,"A"],[8680,8703,"N"],[8704,8704,"A"],[8705,8705,"N"],[8706,8707,"A"],[8708,8710,"N"],[8711,8712,"A"],[8713,8714,"N"],[8715,8715,"A"],[8716,8718,"N"],[8719,8719,"A"],[8720,8720,"N"],[8721,8721,"A"],[8722,8724,"N"],[8725,8725,"A"],[8726,8729,"N"],[8730,8730,"A"],[8731,8732,"N"],[8733,8736,"A"],[8737,8738,"N"],[8739,8739,"A"],[8740,8740,"N"],[8741,8741,"A"],[8742,8742,"N"],[8743,8748,"A"],[8749,8749,"N"],[8750,8750,"A"],[8751,8755,"N"],[8756,8759,"A"],[8760,8763,"N"],[8764,8765,"A"],[8766,8775,"N"],[8776,8776,"A"],[8777,8779,"N"],[8780,8780,"A"],[8781,8785,"N"],[8786,8786,"A"],[8787,8799,"N"],[8800,8801,"A"],[8802,8803,"N"],[8804,8807,"A"],[8808,8809,"N"],[8810,8811,"A"],[8812,8813,"N"],[8814,8815,"A"],[8816,8833,"N"],[8834,8835,"A"],[8836,8837,"N"],[8838,8839,"A"],[8840,8852,"N"],[8853,8853,"A"],[8854,8856,"N"],[8857,8857,"A"],[8858,8868,"N"],[8869,8869,"A"],[8870,8894,"N"],[8895,8895,"A"],[8896,8977,"N"],[8978,8978,"A"],[8979,8985,"N"],[8986,8987,"W"],[8988,9e3,"N"],[9001,9002,"W"],[9003,9192,"N"],[9193,9196,"W"],[9197,9199,"N"],[9200,9200,"W"],[9201,9202,"N"],[9203,9203,"W"],[9204,9311,"N"],[9312,9449,"A"],[9450,9450,"N"],[9451,9547,"A"],[9548,9551,"N"],[9552,9587,"A"],[9588,9599,"N"],[9600,9615,"A"],[9616,9617,"N"],[9618,9621,"A"],[9622,9631,"N"],[9632,9633,"A"],[9634,9634,"N"],[9635,9641,"A"],[9642,9649,"N"],[9650,9651,"A"],[9652,9653,"N"],[9654,9655,"A"],[9656,9659,"N"],[9660,9661,"A"],[9662,9663,"N"],[9664,9665,"A"],[9666,9669,"N"],[9670,9672,"A"],[9673,9674,"N"],[9675,9675,"A"],[9676,9677,"N"],[9678,9681,"A"],[9682,9697,"N"],[9698,9701,"A"],[9702,9710,"N"],[9711,9711,"A"],[9712,9724,"N"],[9725,9726,"W"],[9727,9732,"N"],[9733,9734,"A"],[9735,9736,"N"],[9737,9737,"A"],[9738,9741,"N"],[9742,9743,"A"],[9744,9747,"N"],[9748,9749,"W"],[9750,9755,"N"],[9756,9756,"A"],[9757,9757,"N"],[9758,9758,"A"],[9759,9791,"N"],[9792,9792,"A"],[9793,9793,"N"],[9794,9794,"A"],[9795,9799,"N"],[9800,9811,"W"],[9812,9823,"N"],[9824,9825,"A"],[9826,9826,"N"],[9827,9829,"A"],[9830,9830,"N"],[9831,9834,"A"],[9835,9835,"N"],[9836,9837,"A"],[9838,9838,"N"],[9839,9839,"A"],[9840,9854,"N"],[9855,9855,"W"],[9856,9874,"N"],[9875,9875,"W"],[9876,9885,"N"],[9886,9887,"A"],[9888,9888,"N"],[9889,9889,"W"],[9890,9897,"N"],[9898,9899,"W"],[9900,9916,"N"],[9917,9918,"W"],[9919,9919,"A"],[9920,9923,"N"],[9924,9925,"W"],[9926,9933,"A"],[9934,9934,"W"],[9935,9939,"A"],[9940,9940,"W"],[9941,9953,"A"],[9954,9954,"N"],[9955,9955,"A"],[9956,9959,"N"],[9960,9961,"A"],[9962,9962,"W"],[9963,9969,"A"],[9970,9971,"W"],[9972,9972,"A"],[9973,9973,"W"],[9974,9977,"A"],[9978,9978,"W"],[9979,9980,"A"],[9981,9981,"W"],[9982,9983,"A"],[9984,9988,"N"],[9989,9989,"W"],[9990,9993,"N"],[9994,9995,"W"],[9996,10023,"N"],[10024,10024,"W"],[10025,10044,"N"],[10045,10045,"A"],[10046,10059,"N"],[10060,10060,"W"],[10061,10061,"N"],[10062,10062,"W"],[10063,10066,"N"],[10067,10069,"W"],[10070,10070,"N"],[10071,10071,"W"],[10072,10101,"N"],[10102,10111,"A"],[10112,10132,"N"],[10133,10135,"W"],[10136,10159,"N"],[10160,10160,"W"],[10161,10174,"N"],[10175,10175,"W"],[10176,10213,"N"],[10214,10221,"Na"],[10222,10628,"N"],[10629,10630,"Na"],[10631,11034,"N"],[11035,11036,"W"],[11037,11087,"N"],[11088,11088,"W"],[11089,11092,"N"],[11093,11093,"W"],[11094,11097,"A"],[11098,11903,"N"],[11904,11929,"W"],[11930,11930,"N"],[11931,12019,"W"],[12020,12031,"N"],[12032,12245,"W"],[12246,12271,"N"],[12272,12283,"W"],[12284,12287,"N"],[12288,12288,"F"],[12289,12350,"W"],[12351,12352,"N"],[12353,12438,"W"],[12439,12440,"N"],[12441,12543,"W"],[12544,12548,"N"],[12549,12591,"W"],[12592,12592,"N"],[12593,12686,"W"],[12687,12687,"N"],[12688,12771,"W"],[12772,12783,"N"],[12784,12830,"W"],[12831,12831,"N"],[12832,12871,"W"],[12872,12879,"A"],[12880,19903,"W"],[19904,19967,"N"],[19968,42124,"W"],[42125,42127,"N"],[42128,42182,"W"],[42183,43359,"N"],[43360,43388,"W"],[43389,44031,"N"],[44032,55203,"W"],[55204,57343,"N"],[57344,63743,"A"],[63744,64255,"W"],[64256,65023,"N"],[65024,65039,"A"],[65040,65049,"W"],[65050,65071,"N"],[65072,65106,"W"],[65107,65107,"N"],[65108,65126,"W"],[65127,65127,"N"],[65128,65131,"W"],[65132,65280,"N"],[65281,65376,"F"],[65377,65470,"H"],[65471,65473,"N"],[65474,65479,"H"],[65480,65481,"N"],[65482,65487,"H"],[65488,65489,"N"],[65490,65495,"H"],[65496,65497,"N"],[65498,65500,"H"],[65501,65503,"N"],[65504,65510,"F"],[65511,65511,"N"],[65512,65518,"H"],[65519,65532,"N"],[65533,65533,"A"],[65534,94175,"N"],[94176,94180,"W"],[94181,94191,"N"],[94192,94193,"W"],[94194,94207,"N"],[94208,100343,"W"],[100344,100351,"N"],[100352,101589,"W"],[101590,101631,"N"],[101632,101640,"W"],[101641,110591,"N"],[110592,110878,"W"],[110879,110927,"N"],[110928,110930,"W"],[110931,110947,"N"],[110948,110951,"W"],[110952,110959,"N"],[110960,111355,"W"],[111356,126979,"N"],[126980,126980,"W"],[126981,127182,"N"],[127183,127183,"W"],[127184,127231,"N"],[127232,127242,"A"],[127243,127247,"N"],[127248,127277,"A"],[127278,127279,"N"],[127280,127337,"A"],[127338,127343,"N"],[127344,127373,"A"],[127374,127374,"W"],[127375,127376,"A"],[127377,127386,"W"],[127387,127404,"A"],[127405,127487,"N"],[127488,127490,"W"],[127491,127503,"N"],[127504,127547,"W"],[127548,127551,"N"],[127552,127560,"W"],[127561,127567,"N"],[127568,127569,"W"],[127570,127583,"N"],[127584,127589,"W"],[127590,127743,"N"],[127744,127776,"W"],[127777,127788,"N"],[127789,127797,"W"],[127798,127798,"N"],[127799,127868,"W"],[127869,127869,"N"],[127870,127891,"W"],[127892,127903,"N"],[127904,127946,"W"],[127947,127950,"N"],[127951,127955,"W"],[127956,127967,"N"],[127968,127984,"W"],[127985,127987,"N"],[127988,127988,"W"],[127989,127991,"N"],[127992,128062,"W"],[128063,128063,"N"],[128064,128064,"W"],[128065,128065,"N"],[128066,128252,"W"],[128253,128254,"N"],[128255,128317,"W"],[128318,128330,"N"],[128331,128334,"W"],[128335,128335,"N"],[128336,128359,"W"],[128360,128377,"N"],[128378,128378,"W"],[128379,128404,"N"],[128405,128406,"W"],[128407,128419,"N"],[128420,128420,"W"],[128421,128506,"N"],[128507,128591,"W"],[128592,128639,"N"],[128640,128709,"W"],[128710,128715,"N"],[128716,128716,"W"],[128717,128719,"N"],[128720,128722,"W"],[128723,128724,"N"],[128725,128727,"W"],[128728,128746,"N"],[128747,128748,"W"],[128749,128755,"N"],[128756,128764,"W"],[128765,128991,"N"],[128992,129003,"W"],[129004,129291,"N"],[129292,129338,"W"],[129339,129339,"N"],[129340,129349,"W"],[129350,129350,"N"],[129351,129400,"W"],[129401,129401,"N"],[129402,129483,"W"],[129484,129484,"N"],[129485,129535,"W"],[129536,129647,"N"],[129648,129652,"W"],[129653,129655,"N"],[129656,129658,"W"],[129659,129663,"N"],[129664,129670,"W"],[129671,129679,"N"],[129680,129704,"W"],[129705,129711,"N"],[129712,129718,"W"],[129719,129727,"N"],[129728,129730,"W"],[129731,129743,"N"],[129744,129750,"W"],[129751,131071,"N"],[131072,196605,"W"],[196606,196607,"N"],[196608,262141,"W"],[262142,917759,"N"],[917760,917999,"A"],[918e3,983039,"N"],[983040,1048573,"A"],[1048574,1048575,"N"],[1048576,1114109,"A"],[1114110,1114111,"N"]],A7="13.0.0";function T7(s){for(var r=0,l=Z3.length-1;r!==l;){var c=r+(l-r>>1),d=Z3[c],C=d[0],_=d[1],m=d[2];if(s<C)l=c-1;else if(s>_)r=c+1;else return m}return Z3[r][2]}function Ql(s,r){r===void 0&&(r=0);var l=s.codePointAt(r);if(l!==void 0)return T7(l)}var R7={N:1,Na:1,W:2,F:2,H:1,A:1};function x7(s,r){for(var l=0,c=0,d=s;c<d.length;c++){var C=d[c],_=Ql(C);l+=r&&r[_]||R7[_]}return l}Wi.computeWidth=x7;Wi.eawVersion=A7;Wi.getEAW=Ql});var wo=we(oe=>{"use strict";Object.defineProperty(oe,"__esModule",{value:!0});oe.moveColumn=oe.deleteColumn=oe.insertColumn=oe.moveRow=oe.deleteRow=oe.insertRow=oe.alterAlignment=oe.formatTable=oe.FormatType=oe._weakFormatTable=oe._formatTable=oe._padText=oe._alignText=oe._computeTextWidth=oe.completeTable=oe._extendArray=oe._delimiterText=void 0;var ft=vi(),Gt=Ii(),Ct=Pr(),Qe=_i(),y7=jl(),N7=(s,r)=>{let l="-".repeat(r);switch(s){case ft.Alignment.NONE:return` ${l} `;case ft.Alignment.LEFT:return`:${l} `;case ft.Alignment.RIGHT:return` ${l}:`;case ft.Alignment.CENTER:return`:${l}:`;default:throw new Error("Unknown alignment: "+s)}};oe._delimiterText=N7;var L7=(s,r,l)=>{let c=s.slice();for(let d=s.length;d<r;d++)c.push(l(d,s));return c};oe._extendArray=L7;var O7=(s,r)=>{let l=s.getHeight(),c=s.getWidth();if(l===0)throw new Error("Empty table");let d=s.getRows(),C=[],_=d[0],m=_.getCells();C.push(new Qe.TableRow((0,oe._extendArray)(m,c,P=>new Ct.TableCell(P===m.length?_.marginRight:"")),_.marginLeft,m.length<c?"":_.marginRight));let A=s.getDelimiterRow();if(A!==void 0){let P=A.getCells();C.push(new Qe.TableRow((0,oe._extendArray)(P,c,y=>new Ct.TableCell((0,oe._delimiterText)(ft.Alignment.NONE,y===P.length?Math.max(r.minDelimiterWidth,A.marginRight.length-2):r.minDelimiterWidth))),A.marginLeft,P.length<c?"":A.marginRight))}else C.push(new Qe.TableRow((0,oe._extendArray)([],c,()=>new Ct.TableCell((0,oe._delimiterText)(ft.Alignment.NONE,r.minDelimiterWidth))),"",""));for(let P=A!==void 0?2:1;P<l;P++){let y=d[P],S=y.getCells();C.push(new Qe.TableRow((0,oe._extendArray)(S,c,V=>new Ct.TableCell(V===S.length?y.marginRight:"")),y.marginLeft,S.length<c?"":y.marginRight))}return{table:new Gt.Table(C),delimiterInserted:A===void 0}};oe.completeTable=O7;var S7=(s,r)=>{let l=r.normalize?s.normalize("NFC"):s,c=0;for(let d of l){if(r.wideChars.has(d)){c+=2;continue}if(r.narrowChars.has(d)){c+=1;continue}switch((0,y7.getEAW)(d)){case"F":case"W":c+=2;break;case"A":c+=r.ambiguousAsWide?2:1;break;default:c+=1}}return c};oe._computeTextWidth=S7;var P7=(s,r,l,c)=>{let d=r-(0,oe._computeTextWidth)(s,c);if(d<0)return s;switch(l){case ft.Alignment.NONE:throw new Error("Unexpected default alignment");case ft.Alignment.LEFT:return s+" ".repeat(d);case ft.Alignment.RIGHT:return" ".repeat(d)+s;case ft.Alignment.CENTER:return" ".repeat(Math.floor(d/2))+s+" ".repeat(Math.ceil(d/2));default:throw new Error("Unknown alignment: "+l)}};oe._alignText=P7;var I7=s=>` ${s} `;oe._padText=I7;var W7=(s,r)=>{let l=s.getHeight(),c=s.getWidth();if(l===0)return{table:s,marginLeft:""};let d=s.getRows()[0].marginLeft;if(c===0){let y=new Array(l).fill(new Qe.TableRow([],d,""));return{table:new Gt.Table(y),marginLeft:d}}let C=s.getDelimiterRow(),_=new Array(c).fill(0);if(C!==void 0){let y=C.getWidth();for(let S=0;S<y;S++)_[S]=r.minDelimiterWidth}for(let y=0;y<l;y++){if(C!==void 0&&y===1)continue;let S=s.getRows()[y],V=S.getWidth();for(let M=0;M<V;M++)_[M]=Math.max(_[M],(0,oe._computeTextWidth)(S.getCellAt(M).content,r.textWidthOptions))}let m=C!==void 0?(0,oe._extendArray)(C.getCells().map(y=>y.getAlignment()),c,()=>r.defaultAlignment):new Array(c).fill(r.defaultAlignment),A=[],P=s.getRows()[0];A.push(new Qe.TableRow(P.getCells().map((y,S)=>new Ct.TableCell((0,oe._padText)((0,oe._alignText)(y.content,_[S],r.headerAlignment===ft.HeaderAlignment.FOLLOW?m[S]===ft.Alignment.NONE?r.defaultAlignment:m[S]:r.headerAlignment,r.textWidthOptions)))),d,"")),C!==void 0&&A.push(new Qe.TableRow(C.getCells().map((y,S)=>new Ct.TableCell((0,oe._delimiterText)(m[S],_[S]))),d,""));for(let y=C!==void 0?2:1;y<l;y++){let S=s.getRows()[y];A.push(new Qe.TableRow(S.getCells().map((V,M)=>new Ct.TableCell((0,oe._padText)((0,oe._alignText)(V.content,_[M],m[M]===ft.Alignment.NONE?r.defaultAlignment:m[M],r.textWidthOptions)))),d,""))}return{table:new Gt.Table(A),marginLeft:d}};oe._formatTable=W7;var M7=(s,r)=>{let l=s.getHeight(),c=s.getWidth();if(l===0)return{table:s,marginLeft:""};let d=s.getRows()[0].marginLeft;if(c===0){let A=new Array(l).fill(new Qe.TableRow([],d,""));return{table:new Gt.Table(A),marginLeft:d}}let C=s.getDelimiterRow(),_=[],m=s.getRows()[0];_.push(new Qe.TableRow(m.getCells().map(A=>new Ct.TableCell((0,oe._padText)(A.content))),d,"")),C!==void 0&&_.push(new Qe.TableRow(C.getCells().map(A=>new Ct.TableCell((0,oe._delimiterText)(A.getAlignment(),r.minDelimiterWidth))),d,""));for(let A=C!==void 0?2:1;A<l;A++){let P=s.getRows()[A];_.push(new Qe.TableRow(P.getCells().map(y=>new Ct.TableCell((0,oe._padText)(y.content))),d,""))}return{table:new Gt.Table(_),marginLeft:d}};oe._weakFormatTable=M7;var mo;(function(s){s.NORMAL="normal",s.WEAK="weak"})(mo||(oe.FormatType=mo={}));var D7=(s,r)=>{switch(r.formatType){case mo.NORMAL:return(0,oe._formatTable)(s,r);case mo.WEAK:return(0,oe._weakFormatTable)(s,r);default:throw new Error("Unknown format type: "+r.formatType)}};oe.formatTable=D7;var F7=(s,r,l,c)=>{if(s.getHeight()<1)return s;let d=s.getRows()[1];if(r<0||d.getWidth()-1<r)return s;let C=d.getCells();C[r]=new Ct.TableCell((0,oe._delimiterText)(l,c.minDelimiterWidth));let _=s.getRows();return _[1]=new Qe.TableRow(C,d.marginLeft,d.marginRight),new Gt.Table(_)};oe.alterAlignment=F7;var k7=(s,r,l)=>{let c=s.getRows();return c.splice(Math.max(r,2),0,l),new Gt.Table(c)};oe.insertRow=k7;var U7=(s,r)=>{if(r===1)return s;let l=s.getRows();if(r===0){let c=l[0];l[0]=new Qe.TableRow(new Array(c.getWidth()).fill(new Ct.TableCell("")),c.marginLeft,c.marginRight)}else l.splice(r,1);return new Gt.Table(l)};oe.deleteRow=U7;var q7=(s,r,l)=>{if(r<=1||l<=1||r===l)return s;let c=s.getRows(),d=c[r];return c.splice(r,1),c.splice(l,0,d),new Gt.Table(c)};oe.moveRow=q7;var H7=(s,r,l,c)=>{let d=s.getRows();for(let C=0;C<d.length;C++){let _=d[C],m=d[C].getCells(),A=C===1?new Ct.TableCell((0,oe._delimiterText)(ft.Alignment.NONE,c.minDelimiterWidth)):l[C>1?C-1:C];m.splice(r,0,A),d[C]=new Qe.TableRow(m,_.marginLeft,_.marginRight)}return new Gt.Table(d)};oe.insertColumn=H7;var B7=(s,r,l)=>{let c=s.getRows();for(let d=0;d<c.length;d++){let C=c[d],_=C.getCells();_.length<=1?_=[new Ct.TableCell(d===1?(0,oe._delimiterText)(ft.Alignment.NONE,l.minDelimiterWidth):"")]:_.splice(r,1),c[d]=new Qe.TableRow(_,C.marginLeft,C.marginRight)}return new Gt.Table(c)};oe.deleteColumn=B7;var z7=(s,r,l)=>{if(r===l)return s;let c=s.getRows();for(let d=0;d<c.length;d++){let C=c[d],_=C.getCells(),m=_[r];_.splice(r,1),_.splice(l,0,m),c[d]=new Qe.TableRow(_,C.marginLeft,C.marginRight)}return new Gt.Table(c)};oe.moveColumn=z7});var K3=we(Wt=>{"use strict";Object.defineProperty(Wt,"__esModule",{value:!0});Wt.shortestEditScript=Wt.applyEditScript=Wt._applyCommand=Wt.Delete=Wt.Insert=void 0;var Ur=class{constructor(r,l){this.row=r,this.line=l}};Wt.Insert=Ur;var qr=class{constructor(r){this.row=r}};Wt.Delete=qr;var $7=(s,r,l)=>{if(r instanceof Ur)s.insertLine(l+r.row,r.line);else if(r instanceof qr)s.deleteLine(l+r.row);else throw new Error("Unknown command")};Wt._applyCommand=$7;var Z7=(s,r,l)=>{for(let c of r)(0,Wt._applyCommand)(s,c,l)};Wt.applyEditScript=Z7;var Co=class{get car(){throw new Error("Not implemented")}get cdr(){throw new Error("Not implemented")}isEmpty(){throw new Error("Not implemented")}unshift(r){return new V3(r,this)}toArray(){let r=[],l=this;for(;!l.isEmpty();)r.push(l.car),l=l.cdr;return r}},G3=class extends Co{constructor(){super()}get car(){throw new Error("Empty list")}get cdr(){throw new Error("Empty list")}isEmpty(){return!0}},V3=class extends Co{constructor(r,l){super(),this._car=r,this._cdr=l}get car(){return this._car}get cdr(){return this._cdr}isEmpty(){return!1}},G7=(s,r,l=-1)=>{let c=s.length,d=r.length,C=l>=0?Math.min(l,c+d):c+d,_=new Array(Math.min(C,c)+Math.min(C,d)+1),m=Math.min(C,c);for(let A=0;A<=C;A++){let P=A<=c?-A:A-2*c,y=A<=d?A:-A+2*d;for(let S=P;S<=y;S+=2){let V,M;if(A===0)V=0,M=new G3;else if(S===-A)V=_[m+S+1].i+1,M=_[m+S+1].script.unshift(new qr(V+S));else if(S===A)V=_[m+S-1].i,M=_[m+S-1].script.unshift(new Ur(V+S-1,r[V+S-1]));else{let Y=_[m+S+1].i+1,se=_[m+S-1].i;Y>se?(V=Y,M=_[m+S+1].script.unshift(new qr(V+S))):(V=se,M=_[m+S-1].script.unshift(new Ur(V+S-1,r[V+S-1])))}for(;V<c&&V+S<d&&s[V]===r[V+S];)V+=1;if(S===d-c&&V===c)return M.toArray().reverse();_[m+S]={i:V,script:M}}}};Wt.shortestEditScript=G7});var eu=we(vo=>{"use strict";Object.defineProperty(vo,"__esModule",{value:!0});vo.ITextEditor=void 0;var Y3=class{getCursorPosition(){throw new Error("Not implemented: getCursorPosition")}setCursorPosition(r){throw new Error("Not implemented: setCursorPosition")}setSelectionRange(r){throw new Error("Not implemented: setSelectionRange")}getLastRow(){throw new Error("Not implemented: getLastRow")}acceptsTableEdit(r){throw new Error("Not implemented: acceptsTableEdit")}getLine(r){throw new Error("Not implemented: getLine")}insertLine(r,l){throw new Error("Not implemented: insertLine")}deleteLine(r){throw new Error("Not implemented: deleteLine")}replaceLines(r,l,c){throw new Error("Not implemented: replaceLines")}transact(r){throw new Error("Not implemented: transact")}};vo.ITextEditor=Y3});var ru=we(dr=>{"use strict";Object.defineProperty(dr,"__esModule",{value:!0});dr.defaultOptions=dr.optionsWithDefaults=void 0;var tu=vi(),V7=wo(),nu={normalize:!0,wideChars:new Set,narrowChars:new Set,ambiguousAsWide:!1},K7={leftMarginChars:new Set,formatType:V7.FormatType.NORMAL,minDelimiterWidth:3,defaultAlignment:tu.DefaultAlignment.LEFT,headerAlignment:tu.HeaderAlignment.FOLLOW,smartCursor:!1},Y7=s=>Object.assign(Object.assign(Object.assign({},K7),s),{textWidthOptions:s.textWidthOptions?Object.assign(Object.assign({},nu),s.textWidthOptions):nu});dr.optionsWithDefaults=Y7;dr.defaultOptions=(0,dr.optionsWithDefaults)({})});var su=we(We=>{"use strict";Object.defineProperty(We,"__esModule",{value:!0});We.TableEditor=We._computeNewOffset=We._createIsTableFormulaRegex=We._createIsTableRowRegex=We.SortOrder=void 0;var iu=K3(),_o=W1(),Le=wo(),Br=$3(),ln=O1(),J3=P1(),ou=Ii(),un=Pr(),Hr=_i(),X3;(function(s){s.Ascending="ascending",s.Descending="descending"})(X3||(We.SortOrder=X3={}));var J7=s=>new RegExp(`^${(0,Br.marginRegexSrc)(s)}\\|`,"u");We._createIsTableRowRegex=J7;var X7=s=>new RegExp(`^${(0,Br.marginRegexSrc)(s)}<!-- ?.+-->$`,"u");We._createIsTableFormulaRegex=X7;var Q7=(s,r,l,c)=>{if(c){let _=l.table.getFocusedCell(s);return _!==void 0?_.computeRawOffset(0):s.column<0?l.marginLeft.length:0}let d=r.getFocusedCell(s),C=l.table.getFocusedCell(s);if(d!==void 0&&C!==void 0){let _=Math.min(d.computeContentOffset(s.offset),C.content.length);return C.computeRawOffset(_)}return s.column<0?l.marginLeft.length:0};We._computeNewOffset=Q7;var Q3=class{constructor(r){this._textEditor=r,this._scActive=!1}resetSmartCursor(){this._scActive=!1}cursorIsInTable(r){let l=(0,We._createIsTableRowRegex)(r.leftMarginChars),c=this._textEditor.getCursorPosition();return this._textEditor.acceptsTableEdit(c.row)&&l.test(this._textEditor.getLine(c.row))}cursorIsInTableFormula(r){let l=(0,We._createIsTableFormulaRegex)(r.leftMarginChars),c=this._textEditor.getCursorPosition();return this._textEditor.acceptsTableEdit(c.row)&&l.test(this._textEditor.getLine(c.row))}_findTable(r){let l=(0,We._createIsTableRowRegex)(r.leftMarginChars),c=(0,We._createIsTableFormulaRegex)(r.leftMarginChars),d=this._textEditor.getCursorPosition(),C=this._textEditor.getLastRow(),_=[],m=[],A=d.row,P=d.row;{let M=this._textEditor.getLine(d.row);for(;c.test(M)&&d.row>=0;)d=new ln.Point(d.row-1,d.column),P--,M=this._textEditor.getLine(d.row)}{let M=this._textEditor.getLine(d.row);if(!this._textEditor.acceptsTableEdit(d.row)||!l.test(M))return;_.push(M)}for(let M=d.row-1;M>=0;M--){let Y=this._textEditor.getLine(M);if(!this._textEditor.acceptsTableEdit(M)||!l.test(Y))break;_.unshift(Y),A=M}for(let M=d.row+1;M<=C;M++){let Y=this._textEditor.getLine(M);if(!this._textEditor.acceptsTableEdit(M)||!l.test(Y))break;_.push(Y),P=M}for(let M=P+1;M<=C;M++){let Y=this._textEditor.getLine(M);if(!this._textEditor.acceptsTableEdit(M)||!c.test(Y))break;m.push(Y)}let y=new J3.Range(new ln.Point(A,0),new ln.Point(P,_[_.length-1].length)),S=(0,Br.readTable)(_,r),V=S.focusOfPosition(d,A);if(V!==void 0)return{range:y,lines:_,formulaLines:m,table:S,focus:V}}_withTable(r,l){let c=this._findTable(r);if(c!==void 0)return l(c)}_updateLines(r,l,c,d=void 0){if(d!==void 0){let C=(0,iu.shortestEditScript)(d,c,3);if(C!==void 0){(0,iu.applyEditScript)(this._textEditor,C,r);return}}this._textEditor.replaceLines(r,l,c)}_moveToFocus(r,l,c){let d=l.positionOfFocus(c,r);d!==void 0&&this._textEditor.setCursorPosition(d)}_selectFocus(r,l,c){let d=l.selectionRangeOfFocus(c,r);d!==void 0?this._textEditor.setSelectionRange(d):this._moveToFocus(r,l,c)}format(r){this.withCompletedTable(r,({range:l,lines:c,table:d,focus:C})=>{let _=C;this._textEditor.transact(()=>{this._updateLines(l.start.row,l.end.row+1,d.toLines(),c),this._moveToFocus(l.start.row,d,_)})})}escape(r){this._withTable(r,({range:l,lines:c,table:d,focus:C})=>{let _=(0,Le.completeTable)(d,r),m=(0,Le.formatTable)(_.table,r),A=l.end.row+(_.delimiterInserted?2:1);this._textEditor.transact(()=>{this._updateLines(l.start.row,l.end.row+1,m.table.toLines(),c);let P;if(A>this._textEditor.getLastRow())this._textEditor.insertLine(A,""),P=new ln.Point(A,0);else{let y=new RegExp(`^${(0,Br.marginRegexSrc)(r.leftMarginChars)}`,"u"),S=this._textEditor.getLine(A),V=y.exec(S)[0];P=new ln.Point(A,V.length)}this._textEditor.setCursorPosition(P)}),this.resetSmartCursor()})}alignColumn(r,l){this.withCompletedTable(l,({range:c,lines:d,table:C,focus:_})=>{let m=_,A=C;0<=m.column&&m.column<=A.getHeaderWidth()-1&&(A=(0,Le.alterAlignment)(C,m.column,r,l));let P=(0,Le.formatTable)(A,l);m=m.setOffset((0,We._computeNewOffset)(m,C,P,!1)),this._textEditor.transact(()=>{this._updateLines(c.start.row,c.end.row+1,P.table.toLines(),d),this._moveToFocus(c.start.row,P.table,m)})})}selectCell(r){this.withCompletedTable(r,({range:l,lines:c,table:d,focus:C})=>{let _=C;this._textEditor.transact(()=>{this._updateLines(l.start.row,l.end.row+1,d.toLines(),c),this._selectFocus(l.start.row,d,_)})})}moveFocus(r,l,c){this.withCompletedTable(c,({range:d,lines:C,table:_,focus:m})=>{let A=m,P=A;if(r!==0){let V=_.getHeight(),M=A.row<1&&A.row+r>=1?1:A.row>1&&A.row+r<=1?-1:0;A=A.setRow(Math.min(Math.max(A.row+r+M,0),V<=2?0:V-1))}if(l!==0){let V=_.getHeaderWidth();!(A.column<0&&l<0)&&!(A.column>V-1&&l>0)&&(A=A.setColumn(Math.min(Math.max(A.column+l,0),V-1)))}let y=!A.posEquals(P),S=(0,Le.formatTable)(_,c);A=A.setOffset((0,We._computeNewOffset)(A,_,S,y)),this._textEditor.transact(()=>{this._updateLines(d.start.row,d.end.row+1,S.table.toLines(),C),y?this._selectFocus(d.start.row,S.table,A):this._moveToFocus(d.start.row,S.table,A)}),y&&this.resetSmartCursor()})}nextCell(r){this._withTable(r,({range:l,lines:c,table:d,focus:C})=>{let _=this._scTablePos!==void 0&&!l.start.equals(this._scTablePos)||this._scLastFocus!==void 0&&!C.posEquals(this._scLastFocus);this._scActive&&_&&this.resetSmartCursor();let m=C,A=(0,Le.completeTable)(d,r);A.delimiterInserted&&m.row>0&&(m=m.setRow(m.row+1));let P=m,y=A.table;if(m.row===1){if(m=m.setRow(2),r.smartCursor?(m.column<0||y.getHeaderWidth()-1<m.column)&&(m=m.setColumn(0)):m=m.setColumn(0),m.row>y.getHeight()-1){let M=new Array(y.getHeaderWidth()).fill(new un.TableCell(""));y=(0,Le.insertRow)(y,y.getHeight(),new Hr.TableRow(M,"",""))}}else{if(m.column>y.getHeaderWidth()-1){let M=new Array(y.getHeight()-1).fill(new un.TableCell(""));y=(0,Le.insertColumn)(y,y.getHeaderWidth(),M,r)}m=m.setColumn(m.column+1)}let S=(0,Le.formatTable)(y,r);m=m.setOffset((0,We._computeNewOffset)(m,y,S,!0));let V=S.table.toLines();m.column>S.table.getHeaderWidth()-1&&(V[m.row]+=" ",m=m.setOffset(1)),this._textEditor.transact(()=>{this._updateLines(l.start.row,l.end.row+1,V,c),this._selectFocus(l.start.row,S.table,m)}),r.smartCursor&&(this._scActive||(this._scActive=!0,this._scTablePos=l.start,P.column<0||S.table.getHeaderWidth()-1<P.column?this._scStartFocus=new _o.Focus(P.row,0,0):this._scStartFocus=P),this._scLastFocus=m)})}previousCell(r){this.withCompletedTable(r,({range:l,lines:c,table:d,focus:C})=>{let _=C,m=_;_.row===0?_.column>0&&(_=_.setColumn(_.column-1)):_.row===1?_=new _o.Focus(0,d.getHeaderWidth()-1,_.offset):_.column>0?_=_.setColumn(_.column-1):_=new _o.Focus(_.row===2?0:_.row-1,d.getHeaderWidth()-1,_.offset);let A=!_.posEquals(m),P=(0,Le.formatTable)(d,r);_=_.setOffset((0,We._computeNewOffset)(_,d,P,A)),this._textEditor.transact(()=>{this._updateLines(l.start.row,l.end.row+1,P.table.toLines(),c),A?this._selectFocus(l.start.row,P.table,_):this._moveToFocus(l.start.row,P.table,_)}),A&&this.resetSmartCursor()})}nextRow(r){this._withTable(r,({range:l,lines:c,table:d,focus:C})=>{let _=this._scTablePos!==void 0&&!l.start.equals(this._scTablePos)||this._scLastFocus!==void 0&&!C.posEquals(this._scLastFocus);this._scActive&&_&&this.resetSmartCursor();let m=C,A=(0,Le.completeTable)(d,r);A.delimiterInserted&&m.row>0&&(m=m.setRow(m.row+1));let P=m,y=A.table;if(m.row===0?m=m.setRow(2):m=m.setRow(m.row+1),r.smartCursor?this._scActive&&this._scStartFocus!==void 0?m=m.setColumn(this._scStartFocus.column):(m.column<0||y.getHeaderWidth()-1<m.column)&&(m=m.setColumn(0)):m=m.setColumn(0),m.row>y.getHeight()-1){let V=new Array(y.getHeaderWidth()).fill(new un.TableCell(""));y=(0,Le.insertRow)(y,y.getHeight(),new Hr.TableRow(V,"",""))}let S=(0,Le.formatTable)(y,r);m=m.setOffset((0,We._computeNewOffset)(m,y,S,!0)),this._textEditor.transact(()=>{this._updateLines(l.start.row,l.end.row+1,S.table.toLines(),c),this._selectFocus(l.start.row,S.table,m)}),r.smartCursor&&(this._scActive||(this._scActive=!0,this._scTablePos=l.start,P.column<0||S.table.getHeaderWidth()-1<P.column?this._scStartFocus=new _o.Focus(P.row,0,0):this._scStartFocus=P),this._scLastFocus=m)})}insertRow(r){this.withCompletedTable(r,({range:l,lines:c,formulaLines:d,table:C,focus:_})=>{let m=_;m.row<=1&&(m=m.setRow(2)),m=m.setColumn(0);let A=new Array(C.getHeaderWidth()).fill(new un.TableCell("")),P=(0,Le.insertRow)(C,m.row,new Hr.TableRow(A,"",""));this.formatAndApply(r,l,c,d,P,m)})}deleteRow(r){this.withCompletedTable(r,({range:l,lines:c,formulaLines:d,table:C,focus:_})=>{let m=_,A=C,P=!1;m.row!==1&&(A=(0,Le.deleteRow)(A,m.row),P=!0,m.row>A.getHeight()-1&&(m=m.setRow(m.row===2?0:m.row-1))),this.formatAndApply(r,l,c,d,A,m,P)})}moveRow(r,l){this.withCompletedTable(l,({range:c,lines:d,formulaLines:C,table:_,focus:m})=>{let A=m,P=_;if(A.row>1){let y=Math.min(Math.max(A.row+r,2),P.getHeight()-1);P=(0,Le.moveRow)(P,A.row,y),A=A.setRow(y)}this.formatAndApply(l,c,d,C,P,A)})}evaluateFormulas(r){return this.withCompletedTable(r,({range:l,lines:c,formulaLines:d,table:C,focus:_})=>{let m=C.applyFormulas(d);if(m.isErr())return m.error;let{table:A,focus:P}=this.formatAndApply(r,l,c,d,m.value,_,!1)})}transpose(r){this.withCompletedTable(r,({range:l,lines:c,formulaLines:d,table:C,focus:_})=>{var m,A,P,y,S,V,M,Y;let se=C.getWidth(),ge=C.getHeight(),D=new Array(se+1);for(let te=0;te<se+1;++te)if(te===0){let J=new Array(ge-1);for(let Q=0;Q<ge;++Q)if(Q===0){let G=(A=(m=C.getCellAt(Q,te))===null||m===void 0?void 0:m.content)!==null&&A!==void 0?A:"";J[Q]=new un.TableCell(G)}else{if(Q===1)continue;if(Q>1){let G=(y=(P=C.getCellAt(Q,te))===null||P===void 0?void 0:P.content)!==null&&y!==void 0?y:"";J[Q-1]=new un.TableCell(G)}}D[te]=new Hr.TableRow(J,"","")}else if(te===1){let J=new Array(ge-1);for(let Q=0;Q<ge-1;++Q)J[Q]=new un.TableCell(" --- ");D[te]=new Hr.TableRow(J,"","");continue}else if(te>1){let J=new Array(ge-1);for(let Q=0;Q<ge;++Q)if(Q===0){let G=(V=(S=C.getCellAt(Q,te-1))===null||S===void 0?void 0:S.content)!==null&&V!==void 0?V:"";J[Q]=new un.TableCell(G)}else{if(Q===1)continue;if(Q>1){let G=(Y=(M=C.getCellAt(Q,te-1))===null||M===void 0?void 0:M.content)!==null&&Y!==void 0?Y:"";J[Q-1]=new un.TableCell(G)}}D[te]=new Hr.TableRow(J,"","")}let $=new ou.Table(D),{table:U,focus:K}=this.formatAndApply(r,l,c,d,$,_,!0);this._moveToFocus(l.start.row,U,K)})}sortRows(r,l){this.withCompletedTable(l,({range:c,lines:d,formulaLines:C,table:_,focus:m})=>{let A=_.getRows().slice(2),P=se=>/^\s*[-+]?((\d+(\.\d+)?)|(\d+\.)|(\.\d+))([eE][-+]?\d+)?\s*$/.test(se),y=A.map(se=>{var ge;return(ge=se.getCellAt(m.column))===null||ge===void 0?void 0:ge.content}).some(se=>se!==void 0&&se!==""&&!P(se));A.sort((se,ge)=>{let D=se.getCellAt(m.column),$=ge.getCellAt(m.column);if(D===void 0||D.content==="")return $===void 0||$.content===""?0:-1;if($===void 0||$.content==="")return 1;let U=y?D.content.replace(/[*~_$]/g,""):parseFloat(D.content),K=y?$.content.replace(/[*~_$]/g,""):parseFloat($.content);return U===K?0:U===void 0?-1:K===void 0?1:U<K?-1:1}),r===X3.Descending&&A.reverse();let S=_.getRows().slice(0,2).concat(A),V=new ou.Table(S),{table:M,focus:Y}=this.formatAndApply(l,c,d,C,V,m,!0);this._moveToFocus(c.start.row,M,Y)})}insertColumn(r){this.withCompletedTable(r,({range:l,lines:c,formulaLines:d,table:C,focus:_})=>{let m=_;m.row===1&&(m=m.setRow(0)),m.column<0&&(m=m.setColumn(0));let A=new Array(C.getHeight()-1).fill(new un.TableCell("")),P=(0,Le.insertColumn)(C,m.column,A,r);this.formatAndApply(r,l,c,d,P,m)})}deleteColumn(r){this.withCompletedTable(r,({range:l,lines:c,formulaLines:d,table:C,focus:_})=>{let m=_;m.row===1&&(m=m.setRow(0));let A=C,P=!1;0<=m.column&&m.column<=A.getHeaderWidth()-1&&(A=(0,Le.deleteColumn)(C,m.column,r),P=!0,m.column>A.getHeaderWidth()-1&&(m=m.setColumn(A.getHeaderWidth()-1))),this.formatAndApply(r,l,c,d,A,m,P)})}moveColumn(r,l){this.withCompletedTable(l,({range:c,lines:d,formulaLines:C,table:_,focus:m})=>{let A=m,P=_;if(0<=A.column&&A.column<=P.getHeaderWidth()-1){let y=Math.min(Math.max(A.column+r,0),P.getHeaderWidth()-1);P=(0,Le.moveColumn)(P,A.column,y),A=A.setColumn(y)}this.formatAndApply(l,c,d,C,P,A)})}formatAll(r){this._textEditor.transact(()=>{let l=(0,We._createIsTableRowRegex)(r.leftMarginChars),c=this._textEditor.getCursorPosition(),d=[],C,_=this._textEditor.getLastRow();for(let m=0;m<=_;m++){let A=this._textEditor.getLine(m);if(this._textEditor.acceptsTableEdit(m)&&l.test(A))d.push(A),C===void 0&&(C=m);else if(C!==void 0){let P=m-1,y=new J3.Range(new ln.Point(C,0),new ln.Point(P,d[d.length-1].length)),S=(0,Br.readTable)(d,r),V=S.focusOfPosition(c,C),M;if(V!==void 0){let Y=V,se=(0,Le.completeTable)(S,r);se.delimiterInserted&&Y.row>0&&(Y=Y.setRow(Y.row+1));let ge=(0,Le.formatTable)(se.table,r);Y=Y.setOffset((0,We._computeNewOffset)(Y,se.table,ge,!1));let D=ge.table.toLines();this._updateLines(y.start.row,y.end.row+1,D,d),M=D.length-d.length,c=ge.table.positionOfFocus(Y,C)}else{let Y=(0,Le.completeTable)(S,r),ge=(0,Le.formatTable)(Y.table,r).table.toLines();this._updateLines(y.start.row,y.end.row+1,ge,d),M=ge.length-d.length,c.row>P&&(c=new ln.Point(c.row+M,c.column))}d=[],C=void 0,_+=M,m+=M}}if(C!==void 0){let m=_,A=new J3.Range(new ln.Point(C,0),new ln.Point(m,d[d.length-1].length)),P=(0,Br.readTable)(d,r),S=P.focusOfPosition(c,C),V=(0,Le.completeTable)(P,r);V.delimiterInserted&&S.row>0&&(S=S.setRow(S.row+1));let M=(0,Le.formatTable)(V.table,r);S=S.setOffset((0,We._computeNewOffset)(S,V.table,M,!1));let Y=M.table.toLines();this._updateLines(A.start.row,A.end.row+1,Y,d),c=M.table.positionOfFocus(S,C)}this._textEditor.setCursorPosition(c)})}exportTable(r,l){return this.withCompletedTable(l,({range:c,lines:d,formulaLines:C,table:_,focus:m})=>{let A=_.getRows();return A.length>0&&!r&&A.splice(0,2),A.map(P=>P.getCells().map(y=>y.content))})}exportCSV(r,l){let c=this.exportTable(r,l);return c?c.map(d=>d.join("	")).join(`
`):void 0}withCompletedTable(r,l){return this._withTable(r,c=>{let d=c.focus,C=(0,Le.completeTable)(c.table,r);C.delimiterInserted&&d.row>0&&(d=d.setRow(d.row+1));let _=(0,Le.formatTable)(C.table,r);return d=d.setOffset((0,We._computeNewOffset)(d,C.table,_,!1)),c.table=_.table,c.focus=d,l(c)})}formatAndApply(r,l,c,d,C,_,m=!1){let A=(0,Le.formatTable)(C,r);return _=_.setOffset((0,We._computeNewOffset)(_,C,A,m)),this._textEditor.transact(()=>{this._updateLines(l.start.row,l.end.row+1,A.table.toLines(),c),m?this._selectFocus(l.start.row,A.table,_):this._moveToFocus(l.start.row,A.table,_)}),this.resetSmartCursor(),{range:l,lines:c,formulaLines:d,table:A.table,focus:_}}};We.TableEditor=Q3});var Mi=we(re=>{"use strict";Object.defineProperty(re,"__esModule",{value:!0});re.SortOrder=re.TableEditor=re.optionsWithDefaults=re.defaultOptions=re.ITextEditor=re.shortestEditScript=re.applyEditScript=re.Delete=re.Insert=re.moveColumn=re.deleteColumn=re.insertColumn=re.moveRow=re.deleteRow=re.insertRow=re.alterAlignment=re.formatTable=re.completeTable=re.FormatType=re.readTable=re.Table=re.TableRow=re.TableCell=re.HeaderAlignment=re.DefaultAlignment=re.Alignment=re.Focus=re.Range=re.Point=void 0;var j7=O1();Object.defineProperty(re,"Point",{enumerable:!0,get:function(){return j7.Point}});var ef=P1();Object.defineProperty(re,"Range",{enumerable:!0,get:function(){return ef.Range}});var tf=W1();Object.defineProperty(re,"Focus",{enumerable:!0,get:function(){return tf.Focus}});var j3=vi();Object.defineProperty(re,"Alignment",{enumerable:!0,get:function(){return j3.Alignment}});Object.defineProperty(re,"DefaultAlignment",{enumerable:!0,get:function(){return j3.DefaultAlignment}});Object.defineProperty(re,"HeaderAlignment",{enumerable:!0,get:function(){return j3.HeaderAlignment}});var nf=Pr();Object.defineProperty(re,"TableCell",{enumerable:!0,get:function(){return nf.TableCell}});var rf=_i();Object.defineProperty(re,"TableRow",{enumerable:!0,get:function(){return rf.TableRow}});var of=Ii();Object.defineProperty(re,"Table",{enumerable:!0,get:function(){return of.Table}});var sf=$3();Object.defineProperty(re,"readTable",{enumerable:!0,get:function(){return sf.readTable}});var an=wo();Object.defineProperty(re,"FormatType",{enumerable:!0,get:function(){return an.FormatType}});Object.defineProperty(re,"completeTable",{enumerable:!0,get:function(){return an.completeTable}});Object.defineProperty(re,"formatTable",{enumerable:!0,get:function(){return an.formatTable}});Object.defineProperty(re,"alterAlignment",{enumerable:!0,get:function(){return an.alterAlignment}});Object.defineProperty(re,"insertRow",{enumerable:!0,get:function(){return an.insertRow}});Object.defineProperty(re,"deleteRow",{enumerable:!0,get:function(){return an.deleteRow}});Object.defineProperty(re,"moveRow",{enumerable:!0,get:function(){return an.moveRow}});Object.defineProperty(re,"insertColumn",{enumerable:!0,get:function(){return an.insertColumn}});Object.defineProperty(re,"deleteColumn",{enumerable:!0,get:function(){return an.deleteColumn}});Object.defineProperty(re,"moveColumn",{enumerable:!0,get:function(){return an.moveColumn}});var bo=K3();Object.defineProperty(re,"Insert",{enumerable:!0,get:function(){return bo.Insert}});Object.defineProperty(re,"Delete",{enumerable:!0,get:function(){return bo.Delete}});Object.defineProperty(re,"applyEditScript",{enumerable:!0,get:function(){return bo.applyEditScript}});Object.defineProperty(re,"shortestEditScript",{enumerable:!0,get:function(){return bo.shortestEditScript}});var lf=eu();Object.defineProperty(re,"ITextEditor",{enumerable:!0,get:function(){return lf.ITextEditor}});var lu=ru();Object.defineProperty(re,"defaultOptions",{enumerable:!0,get:function(){return lu.defaultOptions}});Object.defineProperty(re,"optionsWithDefaults",{enumerable:!0,get:function(){return lu.optionsWithDefaults}});var uu=su();Object.defineProperty(re,"TableEditor",{enumerable:!0,get:function(){return uu.TableEditor}});Object.defineProperty(re,"SortOrder",{enumerable:!0,get:function(){return uu.SortOrder}})});var ff={};p9(ff,{default:()=>No});module.exports=m9(ff);var Cl=require("obsidian"),N1={spreadsheet:`
<svg version="1.1" viewBox="0 0 482.81 482.81" xmlns="http://www.w3.org/2000/svg">
  <path fill="currentColor" d="m457.58 25.464-432.83 0.42151c-13.658 0.013314-24.758 11.115-24.757 24.757l0.031024 347.45c7.4833e-4 8.3808 4.211 15.772 10.608 20.259 3.4533 2.4499 5.0716 3.2901 8.879 3.9022 1.7033 0.37333 3.4561 0.59471 5.2692 0.59294l432.84-0.42151c1.809-1e-3 3.5618-0.21823 5.2568-0.59294h1.2174v-0.37196c10.505-2.8727 18.279-12.397 18.278-23.788l-0.031-347.43c1e-3 -13.649-11.107-24.763-24.768-24.763zm3.5453 24.763v71.344h-163.31v-74.886h159.76c1.9641 0.0014 3.5467 1.5922 3.5467 3.5425zm-1.6737 350.37h-161.6v-67.207h163.31v64.268c1e-3 1.2572-0.70549 2.321-1.7033 2.9386zm-438.21-2.5171v-64.268h76.646v67.207h-74.942c-0.99784-0.61765-1.7033-1.6814-1.7033-2.9386zm255.28-155.18v69.688h-157.42v-69.688zm0 90.913v67.207h-157.42v-67.207zm-0.031-211.83h-157.42v-74.886h157.42zm0 21.226v77.826h-157.42v-77.826zm-178.64 77.826h-76.646v-77.826h76.646zm0.03102 21.862v69.688h-76.646v-69.688zm199.95 69.268v-69.697h163.31v69.697zm-0.031-91.552v-77.826h163.31v77.826z" stroke-width="1.3725"/>
</svg>`,alignLeft:`
<svg class="widget-icon" enable-background="new 0 0 512 512" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(-1 0 0 1 512 0)">
    <path d="m501.33 170.67h-320c-5.896 0-10.667 4.771-10.667 10.667v21.333c0 5.896 4.771 10.667 10.667 10.667h320c5.896 0 10.667-4.771 10.667-10.667v-21.333c0-5.896-4.771-10.667-10.667-10.667z"/>
    <path d="m501.33 298.67h-490.67c-5.896 0-10.667 4.771-10.667 10.666v21.333c0 5.896 4.771 10.667 10.667 10.667h490.67c5.896 0 10.667-4.771 10.667-10.667v-21.333c-1e-3 -5.895-4.772-10.666-10.668-10.666z"/>
    <path d="m501.33 426.67h-320c-5.896 0-10.667 4.771-10.667 10.667v21.333c0 5.896 4.771 10.667 10.667 10.667h320c5.896 0 10.667-4.771 10.667-10.667v-21.333c0-5.896-4.771-10.667-10.667-10.667z"/>
    <path d="m501.33 42.667h-490.67c-5.896 0-10.667 4.771-10.667 10.666v21.333c0 5.896 4.771 10.667 10.667 10.667h490.67c5.896 0 10.667-4.771 10.667-10.667v-21.333c-1e-3 -5.895-4.772-10.666-10.668-10.666z"/>
  </g>
</svg>`,alignCenter:`
<svg class="widget-icon" enable-background="new 0 0 512 512" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(-1 0 0 1 512 0)">
    <path d="m416 170.67h-320c-5.896 0-10.667 4.771-10.667 10.667v21.333c0 5.896 4.771 10.667 10.667 10.667h320c5.896 0 10.667-4.771 10.667-10.667v-21.333c0-5.896-4.771-10.667-10.667-10.667z"/>
    <path d="m501.33 298.67h-490.67c-5.896 0-10.667 4.771-10.667 10.666v21.333c0 5.896 4.771 10.667 10.667 10.667h490.67c5.896 0 10.667-4.771 10.667-10.667v-21.333c-1e-3 -5.895-4.772-10.666-10.668-10.666z"/>
    <path d="m416 426.67h-320c-5.896 0-10.667 4.771-10.667 10.667v21.333c0 5.896 4.771 10.667 10.667 10.667h320c5.896 0 10.667-4.771 10.667-10.667v-21.333c0-5.896-4.771-10.667-10.667-10.667z"/>
    <path d="m501.33 42.667h-490.67c-5.896 0-10.667 4.771-10.667 10.666v21.333c0 5.896 4.771 10.667 10.667 10.667h490.67c5.896 0 10.667-4.771 10.667-10.667v-21.333c-1e-3 -5.895-4.772-10.666-10.668-10.666z"/>
  </g>
</svg>`,alignRight:`
<svg class="widget-icon" enable-background="new 0 0 512 512" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m501.33 170.67h-320c-5.896 0-10.667 4.771-10.667 10.667v21.333c0 5.896 4.771 10.667 10.667 10.667h320c5.896 0 10.667-4.771 10.667-10.667v-21.333c0-5.896-4.771-10.667-10.667-10.667z"/>
  <path d="m501.33 298.67h-490.67c-5.896 0-10.667 4.771-10.667 10.666v21.333c0 5.896 4.771 10.667 10.667 10.667h490.67c5.896 0 10.667-4.771 10.667-10.667v-21.333c-1e-3 -5.895-4.772-10.666-10.668-10.666z"/>
  <path d="m501.33 426.67h-320c-5.896 0-10.667 4.771-10.667 10.667v21.333c0 5.896 4.771 10.667 10.667 10.667h320c5.896 0 10.667-4.771 10.667-10.667v-21.333c0-5.896-4.771-10.667-10.667-10.667z"/>
  <path d="m501.33 42.667h-490.67c-5.896 0-10.667 4.771-10.667 10.666v21.333c0 5.896 4.771 10.667 10.667 10.667h490.67c5.896 0 10.667-4.771 10.667-10.667v-21.333c-1e-3 -5.895-4.772-10.666-10.668-10.666z"/>
</svg>`,deleteColumn:`
<svg class="widget-icon" enable-background="new 0 0 26 26" version="1.1" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg">
  <path d="m13.594 20.85v3.15h-10v-22h10v3.15c0.633-0.323 1.304-0.565 2-0.727v-3.423c0-0.551-0.448-1-1-1h-12c-0.55 0-1 0.449-1 1v24c0 0.551 0.449 1 1 1h12c0.552 0 1-0.449 1-1v-3.424c-0.696-0.161-1.367-0.403-2-0.726z"/>
  <path d="m17.594 6.188c-3.762 0-6.813 3.051-6.812 6.813-1e-3 3.761 3.05 6.812 6.812 6.812s6.813-3.051 6.813-6.813-3.052-6.812-6.813-6.812zm3.632 7.802-7.267 1e-3v-1.982h7.268l-1e-3 1.981z"/>
</svg>`,deleteRow:`
<svg class="widget-icon" enable-background="new 0 0 15.381 15.381" version="1.1" viewBox="0 0 15.381 15.381" xmlns="http://www.w3.org/2000/svg">
  <path d="M0,1.732v7.732h6.053c0-0.035-0.004-0.07-0.004-0.104c0-0.434,0.061-0.854,0.165-1.255H1.36V3.092    h12.662v2.192c0.546,0.396,1.01,0.897,1.359,1.477V1.732H0z"/>
  <path d="m11.196 5.28c-2.307 0-4.183 1.877-4.183 4.184 0 2.308 1.876 4.185 4.183 4.185 2.309 0 4.185-1.877 4.185-4.185 0-2.307-1.876-4.184-4.185-4.184zm0 7.233c-1.679 0-3.047-1.367-3.047-3.049 0-1.68 1.368-3.049 3.047-3.049 1.684 0 3.05 1.369 3.05 3.049 0 1.682-1.366 3.049-3.05 3.049z"/>
  <rect x="9.312" y="8.759" width="3.844" height="1.104"/>
</svg>`,insertColumn:`
<svg class="widget-icon" version="1.1" viewBox="-21 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m288 106.67c-3.9258 0-7.8516-1.4297-10.922-4.3125l-80-74.664c-4.8008-4.4805-6.3789-11.457-3.9688-17.559 2.4102-6.1016 8.3203-10.133 14.891-10.133h160c6.5703 0 12.48 4.0117 14.891 10.133 2.4102 6.125 0.83203 13.078-3.9688 17.559l-80 74.664c-3.0703 2.8828-6.9961 4.3125-10.922 4.3125zm-39.402-74.668 39.402 36.777 39.402-36.777z"/>
  <path d="m432 512h-53.332c-20.59 0-37.336-16.746-37.336-37.332v-330.67c0-20.586 16.746-37.332 37.336-37.332h53.332c20.586 0 37.332 16.746 37.332 37.332v330.67c0 20.586-16.746 37.332-37.332 37.332zm-53.332-373.33c-2.9453 0-5.3359 2.3867-5.3359 5.332v330.67c0 2.9414 2.3906 5.332 5.3359 5.332h53.332c2.9453 0 5.332-2.3906 5.332-5.332v-330.67c0-2.9453-2.3867-5.332-5.332-5.332z"/>
  <path d="m197.33 512h-160c-20.586 0-37.332-16.746-37.332-37.332v-330.67c0-20.586 16.746-37.332 37.332-37.332h160c20.59 0 37.336 16.746 37.336 37.332v330.67c0 20.586-16.746 37.332-37.336 37.332zm-160-373.33c-2.9414 0-5.332 2.3867-5.332 5.332v330.67c0 2.9414 2.3906 5.332 5.332 5.332h160c2.9453 0 5.3359-2.3906 5.3359-5.332v-330.67c0-2.9453-2.3906-5.332-5.3359-5.332z"/>
  <path d="m453.33 325.33h-96c-8.832 0-16-7.168-16-16s7.168-16 16-16h96c8.832 0 16 7.168 16 16s-7.168 16-16 16z"/>
  <path d="m218.67 325.33h-202.67c-8.832 0-16-7.168-16-16s7.168-16 16-16h202.67c8.832 0 16 7.168 16 16s-7.168 16-16 16z"/>
  <path d="m117.33 512c-8.832 0-16-7.168-16-16v-373.33c0-8.832 7.168-16 16-16s16 7.168 16 16v373.33c0 8.832-7.168 16-16 16z"/>
</svg>`,insertRow:`
<svg class="widget-icon" version="1.1" viewBox="0 -21 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m16 277.33c-1.9844 0-3.9688-0.36328-5.8672-1.1094-6.1211-2.4102-10.133-8.3203-10.133-14.891v-160c0-6.5703 4.0117-12.48 10.133-14.891 6.1445-2.4102 13.078-0.85156 17.559 3.9688l74.664 80c5.7617 6.1445 5.7617 15.68 0 21.824l-74.664 80c-3.0938 3.3281-7.3398 5.0977-11.691 5.0977zm16-135.4v78.805l36.777-39.402z"/>
  <path d="m474.67 128h-330.67c-20.586 0-37.332-16.746-37.332-37.332v-53.336c0-20.586 16.746-37.332 37.332-37.332h330.67c20.586 0 37.332 16.746 37.332 37.332v53.336c0 20.586-16.746 37.332-37.332 37.332zm-330.67-96c-2.9453 0-5.332 2.3906-5.332 5.332v53.336c0 2.9414 2.3867 5.332 5.332 5.332h330.67c2.9414 0 5.332-2.3906 5.332-5.332v-53.336c0-2.9414-2.3906-5.332-5.332-5.332z"/>
  <path d="m474.67 469.33h-330.67c-20.586 0-37.332-16.746-37.332-37.332v-160c0-20.586 16.746-37.332 37.332-37.332h330.67c20.586 0 37.332 16.746 37.332 37.332v160c0 20.586-16.746 37.332-37.332 37.332zm-330.67-202.66c-2.9453 0-5.332 2.3867-5.332 5.332v160c0 2.9453 2.3867 5.332 5.332 5.332h330.67c2.9414 0 5.332-2.3867 5.332-5.332v-160c0-2.9453-2.3906-5.332-5.332-5.332z"/>
  <path d="m309.33 128c-8.832 0-16-7.168-16-16v-96c0-8.832 7.168-16 16-16s16 7.168 16 16v96c0 8.832-7.168 16-16 16z"/>
  <path d="m309.33 469.33c-8.832 0-16-7.168-16-16v-202.66c0-8.832 7.168-16 16-16s16 7.168 16 16v202.66c0 8.832-7.168 16-16 16z"/>
  <path d="m496 368h-373.33c-8.832 0-16-7.168-16-16s7.168-16 16-16h373.33c8.832 0 16 7.168 16 16s-7.168 16-16 16z"/>
</svg>`,moveColumnLeft:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512.02 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m357.35 512.01h96c32.363 0 58.668-26.305 58.668-58.668v-394.66c0-32.363-26.305-58.668-58.668-58.668h-96c-32.363 0-58.664 26.305-58.664 58.668v394.66c0 32.363 26.301 58.668 58.664 58.668zm96-480c14.699 0 26.668 11.969 26.668 26.668v394.66c0 14.699-11.969 26.668-26.668 26.668h-96c-14.699 0-26.664-11.969-26.664-26.668v-394.66c0-14.699 11.965-26.668 26.664-26.668z"/>
  <path d="m16.016 272.01h224c8.832 0 16-7.168 16-16s-7.168-16-16-16h-224c-8.832 0-16 7.168-16 16s7.168 16 16 16z"/>
  <path d="m101.35 357.34c4.0976 0 8.1914-1.5547 11.309-4.6914 6.25-6.25 6.25-16.383 0-22.637l-74.027-74.023 74.027-74.027c6.25-6.25 6.25-16.387 0-22.637s-16.383-6.25-22.637 0l-85.332 85.336c-6.25 6.25-6.25 16.383 0 22.633l85.332 85.332c3.1367 3.1602 7.2344 4.7148 11.328 4.7148z"/>
</svg>`,moveColumnRight:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512.02 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m154.67 512.01h-96c-32.363 0-58.668-26.305-58.668-58.668v-394.66c0-32.363 26.305-58.668 58.668-58.668h96c32.363 0 58.664 26.305 58.664 58.668v394.66c0 32.363-26.301 58.668-58.664 58.668zm-96-480c-14.699 0-26.668 11.969-26.668 26.668v394.66c0 14.699 11.969 26.668 26.668 26.668h96c14.699 0 26.664-11.969 26.664-26.668v-394.66c0-14.699-11.965-26.668-26.664-26.668z"/>
  <path d="m496 272.01h-224c-8.832 0-16-7.168-16-16 0-8.832 7.168-16 16-16h224c8.832 0 16 7.168 16 16 0 8.832-7.168 16-16 16z"/>
  <path d="m410.67 357.34c-4.0977 0-8.1914-1.5547-11.309-4.6914-6.25-6.25-6.25-16.383 0-22.637l74.027-74.023-74.027-74.027c-6.25-6.25-6.25-16.387 0-22.637s16.383-6.25 22.637 0l85.332 85.336c6.25 6.25 6.25 16.383 0 22.633l-85.332 85.332c-3.1367 3.1602-7.2344 4.7148-11.328 4.7148z"/>
</svg>`,moveRowDown:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m453.33 213.33h-394.66c-32.363 0-58.668-26.301-58.668-58.664v-96c0-32.363 26.305-58.668 58.668-58.668h394.66c32.363 0 58.668 26.305 58.668 58.668v96c0 32.363-26.305 58.664-58.668 58.664zm-394.66-181.33c-14.699 0-26.668 11.969-26.668 26.668v96c0 14.699 11.969 26.664 26.668 26.664h394.66c14.699 0 26.668-11.965 26.668-26.664v-96c0-14.699-11.969-26.668-26.668-26.668z"/>
  <path d="m256 512c-8.832 0-16-7.168-16-16v-224c0-8.832 7.168-16 16-16s16 7.168 16 16v224c0 8.832-7.168 16-16 16z"/>
  <path d="m256 512c-4.0977 0-8.1914-1.5586-11.309-4.6914l-85.332-85.336c-6.25-6.25-6.25-16.383 0-22.633s16.383-6.25 22.637 0l74.023 74.027 74.027-74.027c6.25-6.25 16.387-6.25 22.637 0s6.25 16.383 0 22.633l-85.336 85.336c-3.1562 3.1328-7.25 4.6914-11.348 4.6914z"/>
</svg>`,moveRowUp:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m453.33 298.67h-394.66c-32.363 0-58.668 26.301-58.668 58.664v96c0 32.363 26.305 58.668 58.668 58.668h394.66c32.363 0 58.668-26.305 58.668-58.668v-96c0-32.363-26.305-58.664-58.668-58.664zm-394.66 181.33c-14.699 0-26.668-11.969-26.668-26.668v-96c0-14.699 11.969-26.664 26.668-26.664h394.66c14.699 0 26.668 11.965 26.668 26.664v96c0 14.699-11.969 26.668-26.668 26.668z"/>
  <path d="m256 0c-8.832 0-16 7.168-16 16v224c0 8.832 7.168 16 16 16s16-7.168 16-16v-224c0-8.832-7.168-16-16-16z"/>
  <path d="m256 0c-4.0977 0-8.1914 1.5586-11.309 4.6914l-85.332 85.336c-6.25 6.25-6.25 16.383 0 22.633s16.383 6.25 22.637 0l74.023-74.027 74.027 74.027c6.25 6.25 16.387 6.25 22.637 0s6.25-16.383 0-22.633l-85.336-85.336c-3.1562-3.1328-7.25-4.6914-11.348-4.6914z"/>
</svg>`,transpose:`
<svg class="widget-icon" version="1.1" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
 <path d="m19 26h-5v-2h5a5.0055 5.0055 0 0 0 5-5v-5h2v5a7.0078 7.0078 0 0 1-7 7z"/>
 <path d="M8,30H4a2.0023,2.0023,0,0,1-2-2V14a2.0023,2.0023,0,0,1,2-2H8a2.0023,2.0023,0,0,1,2,2V28A2.0023,2.0023,0,0,1,8,30ZM4,14V28H8V14Z"/>
 <path d="M28,10H14a2.0023,2.0023,0,0,1-2-2V4a2.0023,2.0023,0,0,1,2-2H28a2.0023,2.0023,0,0,1,2,2V8A2.0023,2.0023,0,0,1,28,10ZM14,4V8H28V4Z"/>
</svg>`,sortAsc:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(1 0 0 -1 0 501.15)" stroke-width="1.3333">
    <path d="m353.6 74.486c-11.776 0-21.333 9.5573-21.333 21.333v298.67c0 11.776 9.5573 21.333 21.333 21.333s21.333-9.5573 21.333-21.333v-298.67c0-11.776-9.5573-21.333-21.333-21.333z"/>
    <path d="m353.6 74.486c-5.4636 0-10.922 2.0781-15.079 6.2552l-113.78 113.78c-8.3333 8.3333-8.3333 21.844 0 30.177 8.3333 8.3333 21.844 8.3333 30.183 0l98.697-98.703 98.703 98.703c8.3333 8.3333 21.849 8.3333 30.183 0 8.3333-8.3333 8.3333-21.844 0-30.177l-113.78-113.78c-4.2083-4.1771-9.6667-6.2552-15.131-6.2552z"/>
  </g>
  <path d="m166.04 210.11q-5.0971-13.492-9.5945-26.385-4.4974-13.192-9.2947-26.685h-94.146l-18.889 53.07h-30.283q11.993-32.981 22.487-60.865 10.494-28.184 20.388-53.369 10.194-25.186 20.089-47.973 9.8943-23.087 20.688-45.574h26.685q10.794 22.487 20.688 45.574 9.8943 22.787 19.789 47.973 10.194 25.186 20.688 53.369 10.494 27.884 22.487 60.865zm-27.284-77.056q-9.5945-26.085-19.189-50.371-9.2947-24.586-19.489-47.073-10.494 22.487-20.089 47.073-9.2947 24.286-18.589 50.371z"/>
  <path d="m173.24 325.25q-6.896 7.7955-16.191 18.889-8.9948 10.794-19.189 24.286-10.194 13.192-20.988 28.184-10.794 14.692-21.288 29.983-10.194 14.991-19.489 29.983-9.2947 14.991-16.79 28.484h116.93v24.886h-150.81v-19.489q6.2964-11.993 14.692-26.385 8.695-14.392 18.29-29.383 9.8943-14.991 20.388-30.283t20.688-29.383q10.494-14.092 20.088-26.385 9.8943-12.293 17.99-21.588h-106.74v-24.886h142.42z"/>
</svg>`,sortDesc:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(1 0 0 -1 0 501.15)" stroke-width="1.3333">
    <path d="m353.6 74.486c-11.776 0-21.333 9.5573-21.333 21.333v298.67c0 11.776 9.5573 21.333 21.333 21.333s21.333-9.5573 21.333-21.333v-298.67c0-11.776-9.5573-21.333-21.333-21.333z"/>
    <path d="m353.6 74.486c-5.4636 0-10.922 2.0781-15.079 6.2552l-113.78 113.78c-8.3333 8.3333-8.3333 21.844 0 30.177 8.3333 8.3333 21.844 8.3333 30.183 0l98.697-98.703 98.703 98.703c8.3333 8.3333 21.849 8.3333 30.183 0 8.3333-8.3333 8.3333-21.844 0-30.177l-113.78-113.78c-4.2083-4.1771-9.6667-6.2552-15.131-6.2552z"/>
  </g>
  <path d="m169.11 507.72q-5.0971-13.492-9.5945-26.385-4.4974-13.192-9.2947-26.685h-94.146l-18.889 53.07h-30.283q11.993-32.981 22.487-60.865 10.494-28.184 20.388-53.369 10.194-25.186 20.088-47.973 9.8943-23.087 20.688-45.574h26.685q10.794 22.487 20.688 45.574 9.8943 22.787 19.789 47.973 10.194 25.186 20.688 53.369 10.494 27.884 22.487 60.865zm-27.284-77.056q-9.5945-26.085-19.189-50.371-9.2947-24.586-19.489-47.073-10.494 22.487-20.089 47.073-9.2947 24.286-18.589 50.371z"/>
  <path d="m176.31 27.639q-6.896 7.7955-16.191 18.889-8.9948 10.794-19.189 24.286-10.194 13.192-20.988 28.184-10.794 14.692-21.288 29.983-10.194 14.991-19.489 29.983-9.2947 14.991-16.79 28.484h116.93v24.886h-150.81v-19.489q6.2964-11.993 14.692-26.385 8.695-14.392 18.29-29.383 9.8943-14.991 20.388-30.283 10.494-15.291 20.688-29.383 10.494-14.092 20.088-26.385 9.8943-12.293 17.99-21.588h-106.74v-24.886h142.42z"/>
</svg>`,formula:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m263.51 62.967c1.672-11.134 9.326-22.967 20.222-22.967 11.028 0 20 8.972 20 20h40c0-33.084-26.916-60-60-60-33.629 0-55.527 28.691-59.784 57.073l-12.862 86.927h-61.354v40h55.436l-39.22 265.07-0.116 0.937c-1.063 10.62-9.393 21.99-20.1 21.99-11.028 0-20-8.972-20-20h-40c0 33.084 26.916 60 60 60 33.661 0 56.771-29.141 59.848-57.496l40.023-270.5h60.129v-40h-54.211l11.989-81.033z"/>
  <polygon points="426.27 248 378.24 248 352.25 287.08 334.92 248 291.17 248 326 326.57 270.52 410 318.56 410 345.21 369.92 362.98 410 406.73 410 371.46 330.43"/>
</svg>`,help:`
<svg class="widget-icon" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m248.16 343.22c-14.639 0-26.491 12.2-26.491 26.84 0 14.291 11.503 26.84 26.491 26.84s26.84-12.548 26.84-26.84c0-14.64-12.199-26.84-26.84-26.84z"/>
  <path d="m252.69 140c-47.057 0-68.668 27.885-68.668 46.708 0 13.595 11.502 19.869 20.914 19.869 18.822 0 11.154-26.84 46.708-26.84 17.429 0 31.372 7.669 31.372 23.703 0 18.824-19.52 29.629-31.023 39.389-10.108 8.714-23.354 23.006-23.354 52.983 0 18.125 4.879 23.354 19.171 23.354 17.08 0 20.565-7.668 20.565-14.291 0-18.126 0.35-28.583 19.521-43.571 9.411-7.32 39.04-31.023 39.04-63.789s-29.629-57.515-74.246-57.515z"/>
  <path d="m256 0c-141.48 0-256 114.5-256 256v236c0 11.046 8.954 20 20 20h236c141.48 0 256-114.5 256-256 0-141.48-114.5-256-256-256zm0 472h-216v-216c0-119.38 96.607-216 216-216 119.38 0 216 96.607 216 216 0 119.38-96.607 216-216 216z"/>
</svg>`,csv:`
<svg class="widget-icon" version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
 <path d="m4.9979 9v-8h14.502l3.5 3.5 2e-7 18.5h-19m14-22v5h5m-16 7h-2c-1 0-2 0.5-2 1.5v1.5s1e-8 0.5 0 1.5 1 1.5 2 1.5h2m6.25-6h-2.5c-1.5 0-2 0.5-2 1.5s0.5 1.5 2 1.5 2 0.5 2 1.5-0.5 1.5-2 1.5h-2.5m12.25-7v0.5c0 0.5-2.5 6.5-2.5 6.5h-0.5s-2.5-6-2.5-6.5v-0.5" fill="none" stroke="var(--text-muted)" stroke-width="1.5"/>
</svg>`,arrowenter:`
<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
  <path fill="currentColor" d="m4.64119 12.5 2.87283 2.7038c.30163.2839.31602.7586.03213 1.0602-.28389.3017-.75854.316-1.06017.0321l-4.25-4c-.15059-.1417-.23598-.3393-.23598-.5461s.08539-.4044.23598-.5462l4.25-3.99995c.30163-.28389.77628-.2695 1.06017.03213s.2695.77628-.03213 1.06017l-2.87284 2.70385h10.10882c.9665 0 1.75-.7835 1.75-1.75v-4.5c0-.41421.3358-.75.75-.75s.75.33579.75.75v4.5c0 1.7949-1.4551 3.25-3.25 3.25z"/>
</svg>`,arrowtab:`
<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <path fill="currentColor" d="m18.2071068 11.2928932-6.5-6.49999998c-.3905243-.39052429-1.0236893-.39052429-1.4142136 0-.36048394.36048396-.38821348.92771502-.0831886 1.32000622l.0831886.09420734 4.7931068 4.79289322h-11.086c-.51283584 0-.93550716.3860402-.99327227.8833789l-.00672773.1166211c0 .5128358.38604019.9355072.88337887.9932723l.11662113.0067277h11.086l-4.7931068 4.7928932c-.36048394.360484-.38821348.927715-.0831886 1.3200062l.0831886.0942074c.360484.3604839.927715.3882135 1.3200062.0831886l.0942074-.0831886 6.5-6.5c.3604839-.360484.3882135-.927715.0831886-1.3200062l-.0831886-.0942074-6.5-6.49999998zm2.7928932 7.2071068v-13c0-.55228475-.4477153-1-1-1s-1 .44771525-1 1v13c0 .5522847.4477153 1 1 1s1-.4477153 1-1z" fill="#212121"/>
</svg>`},vl=()=>{Object.keys(N1).forEach(s=>{s!=="help"&&(0,Cl.addIcon)(s,N1[s])})};var Ao=y1(Mi()),e0={formatType:Ao.FormatType.NORMAL,showRibbonIcon:!0,bindEnter:!0,bindTab:!0},Eo=class{constructor(r){let l={...e0,...r};this.formatType=l.formatType,this.showRibbonIcon=l.showRibbonIcon,this.bindEnter=l.bindEnter,this.bindTab=l.bindTab}asOptions(){return(0,Ao.optionsWithDefaults)({formatType:this.formatType})}};var au=y1(Mi()),To=class{constructor(r,l,c){this.getCursorPosition=()=>{let r=this.editor.getCursor();return new au.Point(r.line,r.ch)};this.setCursorPosition=r=>{this.editor.setCursor({line:r.row,ch:r.column})};this.setSelectionRange=r=>{this.editor.setSelection({line:r.start.row,ch:r.start.column},{line:r.end.row,ch:r.end.column})};this.getLastRow=()=>this.editor.lastLine();this.acceptsTableEdit=r=>{let l=this.app.metadataCache.getFileCache(this.file);if(!l.sections)return!0;let c=l.sections.find(C=>C.position.start.line<=r&&C.position.end.line>=r&&C.type!=="code"&&C.type!=="math");if(c===void 0)return!1;let d=c.position.start.line;return!(d>=0&&this.getLine(d)==="-tx-")};this.getLine=r=>this.editor.getLine(r);this.insertLine=(r,l)=>{r>this.getLastRow()?this.editor.replaceRange(`
`+l,{line:r,ch:0}):this.editor.replaceRange(l+`
`,{line:r,ch:0})};this.deleteLine=r=>{if(r===this.getLastRow()){let l=this.getLine(r);this.editor.replaceRange("",{line:r,ch:0},{line:r,ch:l.length})}else this.editor.replaceRange("",{line:r,ch:0},{line:r+1,ch:0})};this.replaceLines=(r,l,c)=>{let d=l-1,_=this.editor.getLine(d).length;this.editor.replaceRange(c.join(`
`),{line:r,ch:0},{line:d,ch:_})};this.transact=r=>{r()};this.app=r,this.file=l,this.editor=c}};var _n=y1(Mi()),Ro=require("obsidian"),bn=class{constructor(r,l,c,d){this.cursorIsInTableFormula=()=>this.mte.cursorIsInTableFormula(this.settings.asOptions());this.cursorIsInTable=()=>this.mte.cursorIsInTable(this.settings.asOptions());this.nextCell=()=>{this.mte.nextCell(this.settings.asOptions())};this.previousCell=()=>{this.mte.previousCell(this.settings.asOptions())};this.nextRow=()=>{this.mte.nextRow(this.settings.asOptions())};this.formatTable=()=>{this.mte.format(this.settings.asOptions())};this.formatAllTables=()=>{this.mte.formatAll(this.settings.asOptions())};this.insertColumn=()=>{this.mte.insertColumn(this.settings.asOptions())};this.insertRow=()=>{this.mte.insertRow(this.settings.asOptions())};this.leftAlignColumn=()=>{this.mte.alignColumn(_n.Alignment.LEFT,this.settings.asOptions())};this.centerAlignColumn=()=>{this.mte.alignColumn(_n.Alignment.CENTER,this.settings.asOptions())};this.rightAlignColumn=()=>{this.mte.alignColumn(_n.Alignment.RIGHT,this.settings.asOptions())};this.moveColumnLeft=()=>{this.mte.moveColumn(-1,this.settings.asOptions())};this.moveColumnRight=()=>{this.mte.moveColumn(1,this.settings.asOptions())};this.moveRowUp=()=>{this.mte.moveRow(-1,this.settings.asOptions())};this.moveRowDown=()=>{this.mte.moveRow(1,this.settings.asOptions())};this.deleteColumn=()=>{this.mte.deleteColumn(this.settings.asOptions())};this.deleteRow=()=>{this.mte.deleteRow(this.settings.asOptions())};this.sortRowsAsc=()=>{this.mte.sortRows(_n.SortOrder.Ascending,this.settings.asOptions())};this.sortRowsDesc=()=>{this.mte.sortRows(_n.SortOrder.Descending,this.settings.asOptions())};this.transpose=()=>{this.mte.transpose(this.settings.asOptions())};this.escape=()=>{this.mte.escape(this.settings.asOptions())};this.evaluateFormulas=()=>{let r=this.mte.evaluateFormulas(this.settings.asOptions());r&&new Ro.Notice(r.message)};this.exportCSVModal=()=>{new t0(this.app,this.mte,this.settings).open()};this.app=r,this.settings=d;let C=new To(r,l,c);this.mte=new _n.TableEditor(C)}},t0=class extends Ro.Modal{constructor(r,l,c){super(r),this.mte=l,this.settings=c}onOpen(){let{contentEl:r}=this,l=r.createDiv({cls:"advanced-tables-csv-export"}),c=l.createEl("textarea",{attr:{readonly:!0}});c.value=this.mte.exportCSV(!0,this.settings.asOptions()),c.onClickEvent(()=>c.select());let d=l.createEl("label"),C=d.createEl("input",{type:"checkbox",attr:{checked:!0}});d.createSpan().setText("Include table headers"),C.onClickEvent(()=>{c.value=this.mte.exportCSV(C.checked,this.settings.asOptions())})}onClose(){let{contentEl:r}=this;r.empty()}};var zr=require("obsidian"),$r="advanced-tables-toolbar",xo=class extends zr.ItemView{constructor(l,c){super(l);this.draw=()=>{let l=this.containerEl.children[1],c=document.createElement("div");c.addClass("advanced-tables-buttons"),c.createDiv().createSpan({cls:"title"}).setText("Advanced Tables");let d=c.createDiv({cls:"nav-header"}),C=d.createDiv({cls:"nav-buttons-container"});C.createSpan({cls:"advanced-tables-row-label"}).setText("Align:"),this.drawBtn(C,"alignLeft","left align column",y=>y.leftAlignColumn()),this.drawBtn(C,"alignCenter","center align column",y=>y.centerAlignColumn()),this.drawBtn(C,"alignRight","right align column",y=>y.rightAlignColumn());let _=d.createDiv({cls:"nav-buttons-container"});_.createSpan({cls:"advanced-tables-row-label"}).setText("Move:"),this.drawBtn(_,"moveRowDown","move row down",y=>y.moveRowDown()),this.drawBtn(_,"moveRowUp","move row up",y=>y.moveRowUp()),this.drawBtn(_,"moveColumnRight","move column right",y=>y.moveColumnRight()),this.drawBtn(_,"moveColumnLeft","move column left",y=>y.moveColumnLeft()),this.drawBtn(_,"transpose","transpose",y=>y.transpose());let m=d.createDiv({cls:"nav-buttons-container"});m.createSpan({cls:"advanced-tables-row-label"}).setText("Edit:"),this.drawBtn(m,"insertRow","insert row above",y=>y.insertRow()),this.drawBtn(m,"insertColumn","insert column left",y=>y.insertColumn()),this.drawBtn(m,"deleteRow","delete row",y=>y.deleteRow()),this.drawBtn(m,"deleteColumn","delete column",y=>y.deleteColumn());let A=d.createDiv({cls:"nav-buttons-container"});A.createSpan({cls:"advanced-tables-row-label"}).setText("Sort/F:"),this.drawBtn(A,"sortAsc","sort by column ascending",y=>y.sortRowsAsc()),this.drawBtn(A,"sortDesc","sort by column descending",y=>y.sortRowsDesc()),this.drawBtn(A,"formula","evaluate formulas",y=>y.evaluateFormulas());let P=d.createDiv({cls:"nav-buttons-container"});P.createSpan({cls:"advanced-tables-row-label"}).setText("Misc:"),this.drawBtn(P,"csv","export as csv",y=>y.exportCSVModal()),this.drawBtn(P,"help","help",()=>window.open("https://github.com/tgrosinger/advanced-tables-obsidian/blob/main/docs/help.md")),l.empty(),l.appendChild(c)};this.drawBtn=(l,c,d,C)=>{let _=A=>d==="evaluate formulas"?A.cursorIsInTable()||A.cursorIsInTableFormula():A.cursorIsInTable(),m=l.createDiv({cls:"advanced-tables-button nav-action-button",title:d});m.onClickEvent(()=>this.withTE(C,_)),m.appendChild(uf(N1[c]))};this.withTE=(l,c,d=!0)=>{let C,_=this.app.workspace.getMostRecentLeaf();if(_.view instanceof zr.MarkdownView)C=_.view.editor;else{console.warn("Advanced Tables: Unable to determine current editor.");return}let m=new bn(this.app,_.view.file,C,this.settings);if(!c(m)){d&&new zr.Notice("Advanced Tables: Cursor must be in a table.");return}l(m)};this.settings=c}getViewType(){return $r}getDisplayText(){return"Advanced Tables"}getIcon(){return"spreadsheet"}load(){super.load(),this.draw()}},uf=s=>new DOMParser().parseFromString(s,"text/xml").documentElement;var fu=require("@codemirror/state"),hu=require("@codemirror/view"),yo=y1(Mi()),it=require("obsidian"),No=class extends it.Plugin{constructor(){super(...arguments);this.makeEditorExtension=()=>{let l=[];return this.settings.bindEnter&&l.push({key:"Enter",run:()=>this.newPerformTableActionCM6(c=>c.nextRow())(),preventDefault:!0}),this.settings.bindTab&&l.push({key:"Tab",run:()=>this.newPerformTableActionCM6(c=>c.nextCell())(),shift:()=>this.newPerformTableActionCM6(c=>c.previousCell())(),preventDefault:!0}),fu.Prec.highest(hu.keymap.of(l))};this.newPerformTableActionCM6=l=>()=>{let c=this.app.workspace.getActiveViewOfType(it.MarkdownView);if(c){let d=c.currentMode;if("sourceMode"in d&&!d.sourceMode)return!1;let C=new bn(this.app,c.file,c.editor,this.settings);if(C.cursorIsInTable())return l(C),!0}return!1};this.newPerformTableAction=(l,c=!0)=>(d,C,_)=>{let m=new bn(this.app,_.file,C,this.settings);if(d)return m.cursorIsInTable();l(m)};this.handleKeyDown=(l,c)=>{if(["Tab","Enter"].contains(c.key)){let d=this.app.workspace.getActiveViewOfType(it.MarkdownView),C=d?d.editor:null,_=this.newPerformTableAction(m=>{switch(c.key){case"Tab":if(!this.settings.bindTab)return;c.shiftKey?m.previousCell():m.nextCell();break;case"Enter":if(!this.settings.bindEnter)return;if(c.shiftKey)m.escape();else{if(c.ctrlKey||c.metaKey||c.altKey)return;m.nextRow()}break}c.preventDefault()},!1);_(!0,C,d)&&_(!1,C,d)}};this.toggleTableControlsView=async()=>{let l=this.app.workspace.getLeavesOfType($r);if(l.length){this.app.workspace.revealLeaf(l[0]);return}await this.app.workspace.getRightLeaf(!1).setViewState({type:$r,active:!0}),this.app.workspace.revealLeaf(this.app.workspace.getLeavesOfType($r)[0])};this.isMobile=()=>this.app.isMobile}async onload(){console.log("loading markdown-table-editor plugin"),await this.loadSettings(),this.registerView($r,l=>new xo(l,this.settings)),vl(),this.settings.showRibbonIcon&&this.addRibbonIcon("spreadsheet","Advanced Tables Toolbar",()=>{this.toggleTableControlsView()}),this.registerEditorExtension(this.makeEditorExtension()),this.addCommand({id:"next-row",name:"Go to next row",icon:"arrowenter",editorCheckCallback:this.newPerformTableAction(l=>{this.settings.bindEnter&&!this.isMobile&&new it.Notice("Advanced Tables: Next row also bound to enter. Possibly producing double actions. See Advanced Tables settings."),l.nextRow()})}),this.addCommand({id:"next-cell",name:"Go to next cell",icon:"arrowtab",editorCheckCallback:this.newPerformTableAction(l=>{this.settings.bindTab&&!this.isMobile&&new it.Notice("Advanced Tables: Next cell also bound to tab. Possibly producing double actions. See Advanced Tables settings."),l.nextCell()})}),this.addCommand({id:"previous-cell",name:"Go to previous cell",editorCheckCallback:this.newPerformTableAction(l=>{this.settings.bindTab&&!this.isMobile&&new it.Notice("Advanced Tables: Previous cell also bound to shift+tab. Possibly producing double actions. See Advanced Tables settings."),l.previousCell()})}),this.addCommand({id:"format-table",name:"Format table at the cursor",editorCheckCallback:this.newPerformTableAction(l=>{l.formatTable()})}),this.addCommand({id:"format-all-tables",name:"Format all tables in this file",editorCallback:(l,c)=>{new bn(this.app,c.file,l,this.settings).formatAllTables()}}),this.addCommand({id:"insert-column",name:"Insert column before current",icon:"insertColumn",editorCheckCallback:this.newPerformTableAction(l=>{l.insertColumn()})}),this.addCommand({id:"insert-row",name:"Insert row before current",icon:"insertRow",editorCheckCallback:this.newPerformTableAction(l=>{l.insertRow()})}),this.addCommand({id:"escape-table",name:"Move cursor out of table",editorCheckCallback:this.newPerformTableAction(l=>{l.escape()})}),this.addCommand({id:"left-align-column",name:"Left align column",icon:"alignLeft",editorCheckCallback:this.newPerformTableAction(l=>{l.leftAlignColumn()})}),this.addCommand({id:"center-align-column",name:"Center align column",icon:"alignCenter",editorCheckCallback:this.newPerformTableAction(l=>{l.centerAlignColumn()})}),this.addCommand({id:"right-align-column",name:"Right align column",icon:"alignRight",editorCheckCallback:this.newPerformTableAction(l=>{l.rightAlignColumn()})}),this.addCommand({id:"move-column-left",name:"Move column left",icon:"moveColumnLeft",editorCheckCallback:this.newPerformTableAction(l=>{l.moveColumnLeft()})}),this.addCommand({id:"move-column-right",name:"Move column right",icon:"moveColumnRight",editorCheckCallback:this.newPerformTableAction(l=>{l.moveColumnRight()})}),this.addCommand({id:"move-row-up",name:"Move row up",icon:"moveRowUp",editorCheckCallback:this.newPerformTableAction(l=>{l.moveRowUp()})}),this.addCommand({id:"move-row-down",name:"Move row down",icon:"moveRowDown",editorCheckCallback:this.newPerformTableAction(l=>{l.moveRowDown()})}),this.addCommand({id:"delete-column",name:"Delete column",icon:"deleteColumn",editorCheckCallback:this.newPerformTableAction(l=>{l.deleteColumn()})}),this.addCommand({id:"delete-row",name:"Delete row",icon:"deleteRow",editorCheckCallback:this.newPerformTableAction(l=>{l.deleteRow()})}),this.addCommand({id:"sort-rows-ascending",name:"Sort rows ascending",icon:"sortAsc",editorCheckCallback:this.newPerformTableAction(l=>{l.sortRowsAsc()})}),this.addCommand({id:"sort-rows-descending",name:"Sort rows descending",icon:"sortDesc",editorCheckCallback:this.newPerformTableAction(l=>{l.sortRowsDesc()})}),this.addCommand({id:"transpose",name:"Transpose",icon:"transpose",editorCheckCallback:this.newPerformTableAction(l=>{l.transpose()})}),this.addCommand({id:"evaluate-formulas",name:"Evaluate table formulas",icon:"formula",editorCheckCallback:(l,c,d)=>{let C=new bn(this.app,d.file,c,this.settings);if(l)return C.cursorIsInTable()||C.cursorIsInTableFormula();C.evaluateFormulas()}}),this.addCommand({id:"table-control-bar",name:"Open table controls toolbar",hotkeys:[{modifiers:["Mod","Shift"],key:"d"}],callback:()=>{this.toggleTableControlsView()}}),this.addSettingTab(new n0(this.app,this))}async loadSettings(){let l=Object.assign(e0,await this.loadData());this.settings=new Eo(l),this.saveData(this.settings)}},n0=class extends it.PluginSettingTab{constructor(r,l){super(r,l),this.plugin=l}display(){let{containerEl:r}=this;r.empty(),new it.Setting(r).setName("Bind enter to table navigation").setDesc('Requires restart of Obsidian. If enabled, when the cursor is in a table, enter advances to the next row. Disabling this can help avoid conflicting with tag or CJK autocompletion. If disabling, bind "Go to ..." in the Obsidian Hotkeys settings.').addToggle(C=>C.setValue(this.plugin.settings.bindEnter).onChange(_=>{this.plugin.settings.bindEnter=_,this.plugin.saveData(this.plugin.settings),this.display()})),new it.Setting(r).setName("Bind tab to table navigation").setDesc('Requires restart of Obsidian. If enabled, when the cursor is in a table, tab/shift+tab navigate between cells. Disabling this can help avoid conflicting with tag or CJK autocompletion. If disabling, bind "Go to ..." in the Obsidian Hotkeys settings.').addToggle(C=>C.setValue(this.plugin.settings.bindTab).onChange(_=>{this.plugin.settings.bindTab=_,this.plugin.saveData(this.plugin.settings),this.display()})),new it.Setting(r).setName("Pad cell width using spaces").setDesc("If enabled, table cells will have spaces added to match the width of the longest cell in the column.").addToggle(C=>C.setValue(this.plugin.settings.formatType===yo.FormatType.NORMAL).onChange(_=>{this.plugin.settings.formatType=_?yo.FormatType.NORMAL:yo.FormatType.WEAK,this.plugin.saveData(this.plugin.settings),this.display()})),new it.Setting(r).setName("Show icon in sidebar").setDesc("If enabled, a button which opens the table controls toolbar will be added to the Obsidian sidebar. The toolbar can also be opened with a Hotkey. Changes only take effect on reload.").addToggle(C=>C.setValue(this.plugin.settings.showRibbonIcon).onChange(_=>{this.plugin.settings.showRibbonIcon=_,this.plugin.saveData(this.plugin.settings),this.display()}));let l=r.createEl("div",{cls:"advanced-tables-donation"}),c=document.createElement("p");c.appendText("If this plugin adds value for you and you would like to help support continued development, please use the buttons below:"),l.appendChild(c);let d=new DOMParser;l.appendChild(cu("https://paypal.me/tgrosinger",d.parseFromString(cf,"text/xml").documentElement)),l.appendChild(cu("https://www.buymeacoffee.com/tgrosinger",d.parseFromString(af,"text/xml").documentElement))}},cu=(s,r)=>{let l=document.createElement("a");return l.setAttribute("href",s),l.addClass("advanced-tables-donate-button"),l.appendChild(r),l},af=`
<svg width="150" height="42" viewBox="0 0 260 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 11.68C0 5.22932 5.22931 0 11.68 0H248.2C254.651 0 259.88 5.22931 259.88 11.68V61.32C259.88 67.7707 254.651 73 248.2 73H11.68C5.22931 73 0 67.7707 0 61.32V11.68Z" fill="#FFDD00"/>
<path d="M52.2566 24.0078L52.2246 23.9889L52.1504 23.9663C52.1802 23.9915 52.2176 24.0061 52.2566 24.0078Z" fill="#0D0C22"/>
<path d="M52.7248 27.3457L52.6895 27.3556L52.7248 27.3457Z" fill="#0D0C22"/>
<path d="M52.2701 24.0024C52.266 24.0019 52.2619 24.0009 52.258 23.9995C52.2578 24.0022 52.2578 24.0049 52.258 24.0076C52.2624 24.007 52.2666 24.0052 52.2701 24.0024Z" fill="#0D0C22"/>
<path d="M52.2578 24.0094H52.2643V24.0054L52.2578 24.0094Z" fill="#0D0C22"/>
<path d="M52.6973 27.3394L52.7513 27.3086L52.7714 27.2973L52.7897 27.2778C52.7554 27.2926 52.7241 27.3135 52.6973 27.3394Z" fill="#0D0C22"/>
<path d="M52.3484 24.0812L52.2956 24.031L52.2598 24.0115C52.279 24.0454 52.3108 24.0705 52.3484 24.0812Z" fill="#0D0C22"/>
<path d="M39.0684 56.469C39.0262 56.4872 38.9893 56.5158 38.9609 56.552L38.9943 56.5306C39.0169 56.5098 39.0489 56.4853 39.0684 56.469Z" fill="#0D0C22"/>
<path d="M46.7802 54.9518C46.7802 54.9041 46.7569 54.9129 46.7626 55.0826C46.7626 55.0687 46.7683 55.0549 46.7708 55.0417C46.7739 55.0115 46.7764 54.982 46.7802 54.9518Z" fill="#0D0C22"/>
<path d="M45.9844 56.469C45.9422 56.4872 45.9053 56.5158 45.877 56.552L45.9103 56.5306C45.9329 56.5098 45.9649 56.4853 45.9844 56.469Z" fill="#0D0C22"/>
<path d="M33.6307 56.8301C33.5987 56.8023 33.5595 56.784 33.5176 56.7773C33.5515 56.7937 33.5855 56.81 33.6081 56.8226L33.6307 56.8301Z" fill="#0D0C22"/>
<path d="M32.4118 55.6598C32.4068 55.6103 32.3916 55.5624 32.3672 55.519C32.3845 55.5642 32.399 55.6104 32.4106 55.6573L32.4118 55.6598Z" fill="#0D0C22"/>
<path d="M40.623 34.7221C38.9449 35.4405 37.0404 36.2551 34.5722 36.2551C33.5397 36.2531 32.5122 36.1114 31.5176 35.834L33.2247 53.3605C33.2851 54.093 33.6188 54.7761 34.1595 55.2739C34.7003 55.7718 35.4085 56.0482 36.1435 56.048C36.1435 56.048 38.564 56.1737 39.3716 56.1737C40.2409 56.1737 42.8474 56.048 42.8474 56.048C43.5823 56.048 44.2904 55.7716 44.831 55.2737C45.3716 54.7759 45.7052 54.0929 45.7656 53.3605L47.594 33.993C46.7769 33.714 45.9523 33.5286 45.0227 33.5286C43.415 33.5279 42.1196 34.0817 40.623 34.7221Z" fill="white"/>
<path d="M26.2344 27.2449L26.2633 27.2719L26.2821 27.2832C26.2676 27.2688 26.2516 27.2559 26.2344 27.2449Z" fill="#0D0C22"/>
<path d="M55.4906 25.6274L55.2336 24.3307C55.0029 23.1673 54.4793 22.068 53.2851 21.6475C52.9024 21.513 52.468 21.4552 52.1745 21.1768C51.881 20.8983 51.7943 20.4659 51.7264 20.0649C51.6007 19.3289 51.4825 18.5923 51.3537 17.8575C51.2424 17.2259 51.1544 16.5163 50.8647 15.9368C50.4876 15.1586 49.705 14.7036 48.9269 14.4025C48.5282 14.2537 48.1213 14.1278 47.7082 14.0254C45.7642 13.5125 43.7202 13.324 41.7202 13.2165C39.3197 13.084 36.9128 13.1239 34.518 13.3359C32.7355 13.4981 30.8581 13.6942 29.1642 14.3108C28.5451 14.5364 27.9071 14.8073 27.4364 15.2856C26.8587 15.8733 26.6702 16.7821 27.0919 17.515C27.3917 18.0354 27.8996 18.4031 28.4382 18.6463C29.1398 18.9597 29.8726 19.1982 30.6242 19.3578C32.7172 19.8204 34.885 20.0021 37.0233 20.0794C39.3932 20.175 41.767 20.0975 44.1256 19.8474C44.7089 19.7833 45.2911 19.7064 45.8723 19.6168C46.5568 19.5118 46.9961 18.6168 46.7943 17.9933C46.553 17.2479 45.9044 16.9587 45.1709 17.0712C45.0628 17.0882 44.9553 17.1039 44.8472 17.1196L44.7692 17.131C44.5208 17.1624 44.2723 17.1917 44.0238 17.219C43.5105 17.2743 42.9959 17.3195 42.4801 17.3547C41.3249 17.4352 40.1665 17.4722 39.0088 17.4741C37.8712 17.4741 36.7329 17.4421 35.5978 17.3673C35.0799 17.3333 34.5632 17.2902 34.0478 17.2378C33.8134 17.2133 33.5796 17.1875 33.3458 17.1586L33.1233 17.1303L33.0749 17.1234L32.8442 17.0901C32.3728 17.0191 31.9014 16.9374 31.435 16.8387C31.388 16.8283 31.3459 16.8021 31.3157 16.7645C31.2856 16.7269 31.2691 16.6801 31.2691 16.6319C31.2691 16.5837 31.2856 16.5369 31.3157 16.4993C31.3459 16.4617 31.388 16.4356 31.435 16.4251H31.4438C31.848 16.339 32.2553 16.2655 32.6638 16.2014C32.8 16.18 32.9366 16.159 33.0736 16.1385H33.0774C33.3332 16.1215 33.5903 16.0757 33.8448 16.0455C36.0595 15.8151 38.2874 15.7366 40.5128 15.8104C41.5933 15.8419 42.6731 15.9053 43.7485 16.0147C43.9798 16.0386 44.2098 16.0637 44.4399 16.092C44.5279 16.1027 44.6165 16.1153 44.7051 16.1259L44.8836 16.1517C45.404 16.2292 45.9217 16.3233 46.4367 16.4339C47.1997 16.5999 48.1796 16.6539 48.519 17.4898C48.6271 17.7551 48.6761 18.0499 48.7359 18.3283L48.8119 18.6834C48.8139 18.6898 48.8154 18.6963 48.8163 18.7029C48.9961 19.5409 49.176 20.379 49.3562 21.217C49.3694 21.2789 49.3697 21.3429 49.3571 21.4049C49.3445 21.4669 49.3193 21.5257 49.2829 21.5776C49.2466 21.6294 49.2 21.6732 49.146 21.7062C49.092 21.7392 49.0317 21.7608 48.969 21.7695H48.964L48.854 21.7846L48.7453 21.799C48.4009 21.8439 48.056 21.8858 47.7107 21.9247C47.0307 22.0022 46.3496 22.0693 45.6674 22.1259C44.3119 22.2386 42.9536 22.3125 41.5927 22.3477C40.8992 22.3662 40.2059 22.3748 39.5129 22.3735C36.7543 22.3713 33.9981 22.211 31.2578 21.8933C30.9611 21.8581 30.6645 21.8204 30.3678 21.7821C30.5978 21.8116 30.2006 21.7594 30.1202 21.7481C29.9316 21.7217 29.7431 21.6943 29.5545 21.6658C28.9216 21.5709 28.2924 21.454 27.6607 21.3515C26.8971 21.2258 26.1667 21.2887 25.476 21.6658C24.909 21.976 24.4501 22.4518 24.1605 23.0297C23.8626 23.6456 23.7739 24.3163 23.6407 24.9781C23.5074 25.6399 23.3 26.3521 23.3786 27.0315C23.5477 28.4979 24.5728 29.6895 26.0473 29.956C27.4345 30.2074 28.8292 30.4111 30.2276 30.5846C35.7212 31.2574 41.2711 31.3379 46.7818 30.8247C47.2305 30.7828 47.6787 30.7371 48.1262 30.6876C48.266 30.6723 48.4074 30.6884 48.5401 30.7348C48.6729 30.7812 48.7936 30.8566 48.8934 30.9557C48.9932 31.0548 49.0695 31.1749 49.1169 31.3073C49.1642 31.4397 49.1814 31.5811 49.167 31.7209L49.0275 33.0773C48.7463 35.8181 48.4652 38.5587 48.184 41.299C47.8907 44.1769 47.5955 47.0545 47.2984 49.9319C47.2146 50.7422 47.1308 51.5524 47.047 52.3624C46.9666 53.16 46.9552 53.9827 46.8038 54.7709C46.5649 56.0103 45.7258 56.7715 44.5015 57.0499C43.3798 57.3052 42.2339 57.4392 41.0836 57.4497C39.8083 57.4566 38.5336 57.4 37.2583 57.4069C35.897 57.4145 34.2295 57.2887 33.1786 56.2756C32.2553 55.3856 32.1277 53.9921 32.002 52.7872C31.8344 51.192 31.6682 49.5971 31.5036 48.0023L30.5796 39.1344L29.9819 33.3966C29.9718 33.3017 29.9618 33.208 29.9524 33.1125C29.8807 32.428 29.3961 31.758 28.6324 31.7926C27.9788 31.8215 27.2359 32.3771 27.3125 33.1125L27.7557 37.3664L28.672 46.1657C28.9331 48.6652 29.1935 51.165 29.4533 53.6653C29.5036 54.1442 29.5507 54.6244 29.6035 55.1034C29.8908 57.7205 31.8895 59.131 34.3646 59.5282C35.8102 59.7607 37.291 59.8085 38.758 59.8324C40.6386 59.8626 42.538 59.9348 44.3877 59.5942C47.1287 59.0914 49.1853 57.2611 49.4788 54.422C49.5626 53.6024 49.6464 52.7826 49.7302 51.9626C50.0088 49.2507 50.2871 46.5386 50.5649 43.8263L51.4737 34.9641L51.8904 30.9026C51.9112 30.7012 51.9962 30.5118 52.133 30.3625C52.2697 30.2132 52.4509 30.1119 52.6497 30.0736C53.4335 29.9208 54.1827 29.66 54.7402 29.0635C55.6277 28.1138 55.8043 26.8756 55.4906 25.6274ZM26.0071 26.5035C26.019 26.4979 25.997 26.6003 25.9876 26.6481C25.9857 26.5758 25.9895 26.5117 26.0071 26.5035ZM26.0831 27.0918C26.0894 27.0874 26.1083 27.1126 26.1278 27.1428C26.0982 27.1151 26.0794 27.0944 26.0825 27.0918H26.0831ZM26.1579 27.1905C26.185 27.2364 26.1994 27.2653 26.1579 27.1905V27.1905ZM26.3082 27.3125H26.3119C26.3119 27.3169 26.3188 27.3213 26.3214 27.3257C26.3172 27.3208 26.3126 27.3164 26.3075 27.3125H26.3082ZM52.6132 27.1302C52.3317 27.3979 51.9074 27.5224 51.4882 27.5846C46.7868 28.2823 42.0169 28.6355 37.264 28.4796C33.8624 28.3633 30.4967 27.9856 27.129 27.5098C26.799 27.4633 26.4414 27.403 26.2145 27.1597C25.7871 26.7009 25.997 25.777 26.1083 25.2226C26.2101 24.7148 26.405 24.0378 27.009 23.9656C27.9518 23.8549 29.0466 24.2528 29.9794 24.3942C31.1023 24.5656 32.2295 24.7028 33.3609 24.8059C38.1892 25.2459 43.0986 25.1774 47.9056 24.5337C48.7817 24.416 49.6548 24.2792 50.5246 24.1233C51.2996 23.9844 52.1588 23.7236 52.6271 24.5262C52.9482 25.073 52.991 25.8046 52.9413 26.4225C52.926 26.6917 52.8084 26.9448 52.6126 27.1302H52.6132Z" fill="#0D0C22"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M81.1302 40.1929C80.8556 40.7169 80.4781 41.1732 79.9978 41.5604C79.5175 41.9479 78.9571 42.2633 78.3166 42.5062C77.6761 42.7497 77.0315 42.9131 76.3835 42.9964C75.7352 43.0799 75.106 43.0727 74.4963 42.9735C73.8863 42.8749 73.3674 42.6737 72.9408 42.3695L73.4214 37.3779C73.8633 37.2261 74.4197 37.0703 75.0909 36.9107C75.7619 36.7513 76.452 36.6371 77.1613 36.5689C77.8705 36.5003 78.5412 36.5084 79.1744 36.5917C79.8068 36.6753 80.3065 36.8765 80.6725 37.1958C80.8707 37.378 81.0387 37.5754 81.176 37.7883C81.313 38.0011 81.3969 38.2214 81.4276 38.4493C81.5037 39.0875 81.4047 39.6687 81.1302 40.1929ZM74.153 29.5602C74.4734 29.3627 74.8585 29.1877 75.3083 29.0356C75.7581 28.8841 76.2195 28.7774 76.6923 28.7167C77.1648 28.6562 77.6262 28.6481 78.0763 28.6938C78.5258 28.7395 78.9228 28.8647 79.2659 29.0697C79.6089 29.2751 79.8643 29.5714 80.032 29.9586C80.1997 30.3464 80.2456 30.8365 80.1693 31.429C80.1083 31.9001 79.9211 32.2991 79.6089 32.6256C79.2963 32.9526 78.9147 33.2259 78.4652 33.4462C78.0154 33.6668 77.5388 33.8415 77.0356 33.9702C76.5321 34.0997 76.0477 34.1949 75.5828 34.2553C75.1176 34.3163 74.7137 34.3545 74.3706 34.3692C74.0273 34.3845 73.8021 34.3921 73.6956 34.3921L74.153 29.5602ZM83.6007 36.9676C83.3566 36.4361 83.0287 35.9689 82.6172 35.5658C82.2054 35.1633 81.717 34.8709 81.1531 34.6885C81.3969 34.491 81.6371 34.1795 81.8737 33.7539C82.1099 33.3288 82.3119 32.865 82.4796 32.3636C82.6474 31.8619 82.762 31.357 82.8229 30.8478C82.8836 30.3389 82.8607 29.902 82.7544 29.537C82.4947 28.6256 82.087 27.9114 81.5303 27.3946C80.9734 26.8782 80.3257 26.5211 79.586 26.3233C78.8462 26.1264 78.0304 26.0842 77.1383 26.1981C76.2462 26.312 75.3347 26.5361 74.4049 26.8704C74.4049 26.7946 74.4124 26.7148 74.4278 26.6312C74.4426 26.548 74.4504 26.4604 74.4504 26.369C74.4504 26.1411 74.3361 25.9439 74.1074 25.7765C73.8787 25.6093 73.6155 25.5107 73.3183 25.4801C73.0209 25.45 72.731 25.5142 72.4489 25.6738C72.1665 25.8334 71.9721 26.1264 71.8656 26.5511C71.7434 27.9189 71.6215 29.3398 71.4996 30.8134C71.3774 32.2875 71.248 33.7767 71.1107 35.2812C70.9735 36.7855 70.8362 38.2784 70.6989 39.7598C70.5616 41.2414 70.4244 42.6659 70.2871 44.0333C70.333 44.4436 70.4473 44.7629 70.6304 44.9907C70.8133 45.2189 71.0268 45.3556 71.2709 45.401C71.5147 45.4467 71.7704 45.4045 72.0371 45.2755C72.3038 45.1469 72.5365 44.9222 72.735 44.6032C73.3447 44.9375 74.0311 45.1541 74.7938 45.253C75.5561 45.3516 76.3298 45.3516 77.1157 45.253C77.9007 45.1541 78.6747 44.9682 79.4374 44.6943C80.1997 44.4211 80.8936 44.079 81.519 43.669C82.1441 43.2586 82.6703 42.7911 83.0975 42.2671C83.5244 41.7426 83.8065 41.1767 83.9437 40.5691C84.081 39.946 84.119 39.3231 84.0581 38.7C83.9971 38.0771 83.8445 37.5 83.6007 36.9676Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M105.915 49.0017C105.832 49.5031 105.713 50.0311 105.561 50.586C105.408 51.1403 105.229 51.6458 105.023 52.1018C104.818 52.5575 104.589 52.9256 104.337 53.207C104.085 53.488 103.815 53.606 103.525 53.5606C103.296 53.5297 103.151 53.3854 103.091 53.1274C103.029 52.8686 103.029 52.5497 103.091 52.17C103.151 51.7901 103.269 51.3607 103.445 50.8821C103.62 50.4035 103.834 49.9284 104.085 49.4577C104.337 48.9864 104.623 48.5347 104.943 48.1015C105.264 47.6686 105.599 47.3075 105.95 47.0189C106.026 47.11 106.06 47.3378 106.053 47.7028C106.045 48.0674 105.999 48.5006 105.915 49.0017ZM113.67 39.1097C113.464 38.8819 113.213 38.7529 112.915 38.7223C112.618 38.6919 112.317 38.859 112.012 39.2237C111.813 39.5883 111.562 39.9379 111.257 40.2722C110.952 40.6067 110.635 40.9103 110.307 41.1839C109.98 41.4572 109.667 41.6931 109.37 41.8903C109.072 42.0881 108.84 42.2324 108.672 42.3235C108.611 41.8374 108.576 41.3132 108.569 40.7507C108.561 40.1886 108.573 39.619 108.603 39.0415C108.649 38.2209 108.744 37.393 108.889 36.557C109.034 35.7213 109.244 34.9007 109.518 34.0951C109.518 33.67 109.419 33.3242 109.221 33.0582C109.022 32.7924 108.782 32.625 108.5 32.5567C108.218 32.4885 107.929 32.5264 107.631 32.6707C107.334 32.8153 107.078 33.0775 106.865 33.4569C106.682 33.9586 106.472 34.5207 106.236 35.1436C105.999 35.7667 105.732 36.4012 105.435 37.0469C105.138 37.6931 104.806 38.3197 104.44 38.9273C104.074 39.5354 103.674 40.075 103.239 40.5457C102.804 41.0168 102.331 41.3854 101.821 41.6512C101.31 41.9172 100.757 42.0349 100.162 42.0045C99.8876 41.9285 99.6893 41.7235 99.5675 41.3889C99.4453 41.0549 99.373 40.6368 99.3504 40.1354C99.3275 39.634 99.3504 39.0831 99.4189 38.4828C99.4877 37.8828 99.5791 37.2863 99.6934 36.6938C99.8078 36.101 99.9337 35.5389 100.071 35.0071C100.208 34.4753 100.337 34.0268 100.46 33.6622C100.643 33.2218 100.643 32.8529 100.46 32.5567C100.277 32.2604 100.025 32.0631 99.705 31.964C99.3846 31.8654 99.0489 31.8694 98.6983 31.9755C98.3474 32.0819 98.0958 32.3173 97.9435 32.682C97.684 33.3054 97.4475 34.004 97.2342 34.779C97.0206 35.5539 96.8491 36.3558 96.7197 37.1836C96.5896 38.0121 96.5171 38.8327 96.502 39.6456C96.5011 39.6985 96.5037 39.7488 96.5034 39.8014C96.1709 40.6848 95.854 41.3525 95.553 41.7992C95.1641 42.377 94.7253 42.6277 94.2375 42.5513C94.0236 42.4603 93.8832 42.2477 93.8147 41.9132C93.7453 41.5792 93.7227 41.1689 93.7453 40.6822C93.7688 40.1964 93.826 39.6456 93.9171 39.0299C94.0091 38.4146 94.1229 37.7764 94.2601 37.1154C94.3977 36.4541 94.5425 35.7899 94.6949 35.121C94.8472 34.4525 94.9845 33.8218 95.107 33.2291C95.0916 32.6973 94.9352 32.291 94.6377 32.0097C94.3405 31.7289 93.9247 31.6187 93.3913 31.6791C93.0253 31.8312 92.7542 32.029 92.579 32.2719C92.4034 32.5148 92.2623 32.8265 92.1558 33.2062C92.0946 33.404 92.0032 33.799 91.8813 34.3918C91.7591 34.984 91.603 35.6644 91.4123 36.4315C91.2217 37.1992 90.9967 38.0005 90.7376 38.8362C90.4781 39.6719 90.1885 40.4283 89.8684 41.1041C89.548 41.7801 89.1972 42.3235 88.8161 42.7338C88.4348 43.1438 88.023 43.3113 87.5807 43.2352C87.3366 43.1895 87.1805 42.9388 87.112 42.4831C87.0432 42.0271 87.0319 41.4653 87.0775 40.7964C87.1233 40.1279 87.2148 39.3946 87.352 38.5971C87.4893 37.7993 87.63 37.0434 87.7752 36.3289C87.92 35.6149 88.0535 34.984 88.1756 34.4372C88.2975 33.8901 88.3814 33.5254 88.4272 33.3433C88.4272 32.9026 88.3277 32.5495 88.1298 32.2832C87.9313 32.0178 87.6913 31.8503 87.4092 31.7818C87.1268 31.7136 86.8372 31.7514 86.54 31.8957C86.2426 32.0403 85.9872 32.3026 85.7736 32.682C85.6973 33.0923 85.598 33.5674 85.4761 34.1067C85.3539 34.6459 85.2361 35.2006 85.1218 35.7705C85.0074 36.3404 84.9003 36.8988 84.8014 37.4459C84.7021 37.993 84.6299 38.4716 84.584 38.8819C84.5536 39.2008 84.519 39.5923 84.4813 40.0556C84.443 40.5194 84.4238 41.0092 84.4238 41.5257C84.4238 42.0427 84.4618 42.5554 84.5385 43.0643C84.6145 43.5735 84.7518 44.0408 84.95 44.4659C85.1482 44.8915 85.4265 45.2408 85.7852 45.5144C86.1433 45.7879 86.5972 45.9397 87.1463 45.9704C87.7101 46.0005 88.202 45.9591 88.6217 45.8449C89.041 45.731 89.4221 45.5523 89.7654 45.3091C90.1084 45.0665 90.421 44.7776 90.7033 44.443C90.9851 44.1091 91.2637 43.7444 91.5383 43.3491C91.7974 43.9269 92.1329 44.3748 92.5447 44.694C92.9565 45.013 93.3913 45.2032 93.8486 45.2637C94.306 45.3241 94.7715 45.2602 95.2442 45.0699C95.7167 44.8803 96.1436 44.5573 96.5252 44.1012C96.7762 43.8216 97.0131 43.5038 97.2354 43.1525C97.3297 43.317 97.4301 43.4758 97.543 43.6224C97.9168 44.1091 98.424 44.443 99.0645 44.6255C99.7506 44.808 100.421 44.8386 101.077 44.7169C101.733 44.5954 102.358 44.3748 102.953 44.0559C103.548 43.7366 104.101 43.3532 104.612 42.9047C105.122 42.4565 105.568 41.9895 105.95 41.5028C105.934 41.8524 105.927 42.1832 105.927 42.4944C105.927 42.8061 105.919 43.1438 105.904 43.5088C105.141 44.0408 104.421 44.679 103.742 45.4233C103.064 46.1676 102.469 46.9616 101.958 47.8051C101.447 48.6483 101.047 49.5031 100.757 50.3691C100.467 51.2357 100.326 52.0445 100.334 52.7969C100.341 53.549 100.521 54.206 100.871 54.7681C101.222 55.3306 101.794 55.7331 102.587 55.9763C103.411 56.2348 104.135 56.242 104.76 55.9991C105.386 55.7559 105.931 55.3531 106.396 54.791C106.861 54.2289 107.242 53.549 107.54 52.7512C107.837 51.9534 108.073 51.1215 108.249 50.2555C108.424 49.3894 108.535 48.5379 108.58 47.7028C108.626 46.8668 108.626 46.1219 108.58 45.4687C109.892 44.9219 110.967 44.2305 111.806 43.3945C112.645 42.5594 113.338 41.6778 113.887 40.7507C114.055 40.5229 114.112 40.2493 114.059 39.9304C114.006 39.6111 113.876 39.3376 113.67 39.1097Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M142.53 37.6515C142.575 37.3022 142.644 36.9335 142.735 36.546C142.827 36.1585 142.941 35.7823 143.079 35.4177C143.216 35.0531 143.376 34.7379 143.559 34.4718C143.742 34.2061 143.937 34.0161 144.142 33.9019C144.348 33.7883 144.558 33.7995 144.771 33.936C145 34.0731 145.141 34.3617 145.195 34.8021C145.248 35.2433 145.195 35.7141 145.034 36.2155C144.874 36.7172 144.588 37.1879 144.177 37.6286C143.765 38.0696 143.208 38.3579 142.507 38.4947C142.476 38.2824 142.484 38.0011 142.53 37.6515ZM150.456 38.5857C150.204 38.5103 149.964 38.5025 149.735 38.5632C149.506 38.6239 149.361 38.7835 149.301 39.042C149.178 39.5281 148.984 40.0258 148.717 40.5347C148.45 41.0439 148.122 41.5262 147.734 41.9822C147.345 42.438 146.906 42.8408 146.418 43.1901C145.93 43.5397 145.419 43.7904 144.886 43.9422C144.351 44.1096 143.91 44.1284 143.559 43.9991C143.208 43.8705 142.93 43.6498 142.724 43.3384C142.518 43.027 142.369 42.6508 142.278 42.2101C142.186 41.7694 142.133 41.3137 142.118 40.8424C142.987 40.9034 143.761 40.7478 144.44 40.3751C145.118 40.0032 145.694 39.509 146.167 38.8937C146.639 38.2784 146.998 37.587 147.242 36.8195C147.485 36.0524 147.623 35.2887 147.653 34.5288C147.669 33.8146 147.562 33.2108 147.333 32.7169C147.105 32.2233 146.796 31.839 146.407 31.5658C146.018 31.2922 145.572 31.1326 145.069 31.0872C144.566 31.0415 144.054 31.11 143.536 31.2922C142.91 31.505 142.381 31.8506 141.946 32.3294C141.512 32.808 141.149 33.3629 140.86 33.9933C140.57 34.6239 140.341 35.3038 140.173 36.033C140.005 36.7626 139.883 37.4806 139.807 38.1873C139.739 38.8214 139.702 39.4278 139.689 40.013C139.657 40.0874 139.625 40.1588 139.59 40.2383C139.354 40.7782 139.079 41.3062 138.766 41.8226C138.454 42.3394 138.107 42.7725 137.726 43.1218C137.344 43.4714 136.948 43.5929 136.536 43.4865C136.292 43.426 136.159 43.1444 136.136 42.6433C136.113 42.1416 136.139 41.5187 136.216 40.7741C136.292 40.0298 136.38 39.2239 136.479 38.3579C136.578 37.4918 136.628 36.664 136.628 35.8737C136.628 35.1898 136.498 34.5329 136.239 33.9019C135.979 33.2718 135.625 32.7473 135.175 32.3294C134.725 31.9113 134.203 31.634 133.608 31.4975C133.013 31.3605 132.373 31.4518 131.687 31.7708C131 32.09 130.455 32.5382 130.051 33.1157C129.647 33.6934 129.277 34.3009 128.942 34.9391C128.819 34.4528 128.641 34.0011 128.404 33.583C128.167 33.1651 127.878 32.8005 127.535 32.4888C127.191 32.1776 126.806 31.9344 126.38 31.7595C125.953 31.5851 125.502 31.4975 125.03 31.4975C124.572 31.4975 124.149 31.5851 123.76 31.7595C123.371 31.9344 123.017 32.1583 122.696 32.4318C122.376 32.7056 122.087 33.013 121.827 33.3551C121.568 33.6969 121.339 34.0352 121.141 34.3692C121.11 33.9742 121.076 33.6286 121.038 33.332C121 33.0359 120.931 32.7852 120.832 32.5801C120.733 32.3748 120.592 32.2193 120.409 32.1129C120.226 32.0067 119.967 31.9532 119.632 31.9532C119.464 31.9532 119.296 31.9874 119.128 32.0556C118.96 32.1241 118.811 32.2193 118.682 32.3407C118.552 32.4627 118.453 32.6105 118.385 32.7852C118.316 32.9598 118.297 33.1614 118.327 33.3892C118.342 33.5566 118.385 33.7576 118.453 33.9933C118.522 34.2289 118.587 34.5369 118.648 34.9163C118.708 35.2962 118.758 35.756 118.796 36.2953C118.834 36.8349 118.846 37.4959 118.831 38.2784C118.815 39.0611 118.758 39.9763 118.659 41.0248C118.56 42.0733 118.403 43.289 118.19 44.6714C118.16 44.9907 118.282 45.2492 118.556 45.4467C118.831 45.6439 119.143 45.7578 119.494 45.7885C119.845 45.8188 120.177 45.7578 120.489 45.6063C120.802 45.4539 120.981 45.1882 121.027 44.8085C121.072 44.0943 121.16 43.3347 121.29 42.529C121.419 41.724 121.579 40.9262 121.77 40.1359C121.961 39.346 122.178 38.5938 122.422 37.8793C122.666 37.1651 122.937 36.5347 123.234 35.9876C123.532 35.4405 123.84 35.0039 124.161 34.6771C124.481 34.3504 124.816 34.187 125.167 34.187C125.594 34.187 125.926 34.3805 126.162 34.7679C126.398 35.1557 126.566 35.6536 126.666 36.2609C126.765 36.869 126.81 37.5341 126.803 38.2555C126.795 38.9773 126.765 39.6724 126.711 40.341C126.658 41.0098 126.597 41.606 126.528 42.1303C126.46 42.6545 126.41 43.0157 126.38 43.2129C126.38 43.5625 126.513 43.8395 126.78 44.0448C127.046 44.2498 127.344 44.3716 127.672 44.4095C128 44.4476 128.309 44.3866 128.598 44.227C128.888 44.0674 129.056 43.7982 129.102 43.4179C129.254 42.324 129.464 41.2264 129.731 40.1247C129.997 39.023 130.303 38.0355 130.646 37.1616C130.989 36.2878 131.37 35.5735 131.79 35.0189C132.209 34.4646 132.655 34.187 133.128 34.187C133.371 34.187 133.559 34.3544 133.688 34.6884C133.818 35.0227 133.883 35.4784 133.883 36.0559C133.883 36.4815 133.848 36.9184 133.78 37.3666C133.711 37.8148 133.631 38.2784 133.54 38.7569C133.448 39.2358 133.368 39.7256 133.299 40.227C133.231 40.7287 133.196 41.2527 133.196 41.7998C133.196 42.1797 133.235 42.6204 133.311 43.1218C133.387 43.6229 133.532 44.0983 133.745 44.5462C133.959 44.9947 134.252 45.3744 134.626 45.6858C135 45.9973 135.476 46.1531 136.056 46.1531C136.925 46.1531 137.695 45.9669 138.366 45.5947C139.037 45.2226 139.613 44.7365 140.093 44.1362C140.118 44.1047 140.141 44.0711 140.165 44.0399C140.202 44.1287 140.235 44.2227 140.276 44.3071C140.604 44.9756 141.05 45.4921 141.615 45.857C142.178 46.2216 142.842 46.4229 143.605 46.4611C144.367 46.4987 145.198 46.3581 146.098 46.0392C146.769 45.796 147.352 45.4921 147.848 45.1275C148.343 44.7628 148.789 44.3184 149.186 43.7941C149.583 43.2699 149.945 42.6658 150.273 41.9822C150.601 41.2981 150.932 40.5159 151.268 39.6342C151.329 39.3916 151.272 39.1751 151.097 38.9848C150.921 38.7951 150.708 38.6621 150.456 38.5857Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M162.887 36.0434C162.81 36.4918 162.707 36.986 162.578 37.525C162.448 38.0646 162.284 38.623 162.086 39.2004C161.888 39.7779 161.644 40.2984 161.354 40.7616C161.064 41.2254 160.733 41.5935 160.359 41.8671C159.985 42.1406 159.555 42.2546 159.066 42.2089C158.822 42.1788 158.635 42.0117 158.506 41.7075C158.376 41.4038 158.308 41.0161 158.3 40.545C158.292 40.0743 158.334 39.5575 158.426 38.9951C158.517 38.4333 158.658 37.8821 158.849 37.3426C159.04 36.8036 159.272 36.3056 159.547 35.8496C159.821 35.3939 160.138 35.0405 160.496 34.7898C160.854 34.5391 161.247 34.4217 161.674 34.4365C162.101 34.4518 162.559 34.6643 163.047 35.0747C163.016 35.2725 162.963 35.5954 162.887 36.0434ZM171.019 37.787C170.782 37.6656 170.538 37.6392 170.287 37.7075C170.035 37.7757 169.856 38.0076 169.749 38.4026C169.688 38.8283 169.551 39.3294 169.338 39.9069C169.124 40.4843 168.861 41.0317 168.548 41.5478C168.236 42.0646 167.877 42.494 167.473 42.8358C167.069 43.1778 166.638 43.3337 166.181 43.3028C165.799 43.2727 165.532 43.079 165.38 42.7218C165.227 42.3647 165.147 41.9168 165.14 41.3769C165.132 40.838 165.186 40.2301 165.3 39.5538C165.414 38.8777 165.552 38.2054 165.712 37.5363C165.872 36.868 166.036 36.2258 166.204 35.6105C166.371 34.9951 166.508 34.4747 166.616 34.0493C166.738 33.6693 166.699 33.3466 166.501 33.0803C166.303 32.8149 166.055 32.6246 165.758 32.5107C165.46 32.3967 165.159 32.3664 164.854 32.4196C164.549 32.4728 164.351 32.6362 164.259 32.9094C163.359 32.1345 162.494 31.7166 161.663 31.6559C160.831 31.5952 160.065 31.7776 159.364 32.203C158.662 32.6284 158.041 33.2437 157.5 34.0493C156.958 34.8549 156.52 35.7322 156.184 36.6818C155.849 37.6314 155.639 38.6004 155.555 39.5879C155.471 40.5757 155.536 41.4761 155.75 42.289C155.963 43.1018 156.34 43.7669 156.882 44.283C157.423 44.7998 158.159 45.0583 159.089 45.0583C159.501 45.0583 159.898 44.9747 160.279 44.8076C160.66 44.6401 161.011 44.4426 161.331 44.2148C161.651 43.9869 161.933 43.7475 162.178 43.4968C162.421 43.2461 162.612 43.0373 162.749 42.8699C162.856 43.417 163.032 43.8808 163.276 44.2605C163.519 44.6401 163.798 44.9521 164.111 45.1948C164.423 45.4376 164.751 45.6164 165.094 45.7306C165.437 45.8445 165.769 45.9015 166.089 45.9015C166.806 45.9015 167.477 45.6583 168.102 45.1719C168.727 44.6861 169.288 44.0893 169.784 43.3829C170.279 42.6762 170.687 41.9319 171.007 41.1491C171.328 40.3666 171.541 39.6715 171.648 39.0634C171.755 38.8355 171.735 38.5964 171.591 38.3457C171.446 38.095 171.255 37.909 171.019 37.787Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M212.194 50.3701C212.064 50.8866 211.862 51.3238 211.587 51.6806C211.313 52.0377 210.97 52.2239 210.558 52.2393C210.299 52.2543 210.101 52.1175 209.963 51.8289C209.826 51.5401 209.731 51.1679 209.678 50.7122C209.624 50.2562 209.601 49.747 209.609 49.1849C209.616 48.6227 209.639 48.0681 209.678 47.521C209.715 46.9742 209.761 46.4647 209.815 45.9939C209.868 45.5226 209.91 45.1586 209.94 44.9C210.459 44.9608 210.89 45.1846 211.233 45.5723C211.576 45.9598 211.839 46.4193 212.022 46.9514C212.205 47.4831 212.312 48.0568 212.343 48.6722C212.373 49.2875 212.323 49.8534 212.194 50.3701ZM203.913 50.3701C203.783 50.8866 203.581 51.3238 203.307 51.6806C203.032 52.0377 202.689 52.2239 202.277 52.2393C202.018 52.2543 201.82 52.1175 201.683 51.8289C201.545 51.5401 201.45 51.1679 201.397 50.7122C201.343 50.2562 201.32 49.747 201.328 49.1849C201.336 48.6227 201.358 48.0681 201.397 47.521C201.434 46.9742 201.48 46.4647 201.534 45.9939C201.587 45.5226 201.629 45.1586 201.66 44.9C202.178 44.9608 202.609 45.1846 202.952 45.5723C203.295 45.9598 203.558 46.4193 203.741 46.9514C203.924 47.4831 204.031 48.0568 204.062 48.6722C204.092 49.2875 204.042 49.8534 203.913 50.3701ZM195.415 37.4241C195.399 37.7884 195.365 38.1114 195.312 38.3925C195.258 38.6741 195.186 38.8522 195.095 38.9283C194.927 38.8369 194.721 38.6018 194.477 38.2216C194.233 37.8419 194.042 37.4122 193.905 36.9336C193.768 36.4551 193.725 35.9843 193.779 35.5205C193.832 35.0573 194.073 34.6967 194.5 34.4379C194.667 34.3468 194.812 34.3809 194.934 34.5405C195.056 34.7001 195.155 34.9318 195.232 35.2357C195.308 35.5399 195.361 35.8892 195.392 36.2842C195.422 36.6795 195.43 37.0591 195.415 37.4241ZM193.39 41.9711C193.154 42.2215 192.89 42.4381 192.601 42.6206C192.311 42.803 192.014 42.9398 191.709 43.0309C191.404 43.1223 191.129 43.1448 190.885 43.0991C190.199 42.9627 189.673 42.666 189.307 42.2103C188.941 41.7545 188.708 41.219 188.609 40.6037C188.51 39.9881 188.521 39.3308 188.644 38.6319C188.765 37.933 188.971 37.2835 189.261 36.6832C189.551 36.0829 189.902 35.5662 190.313 35.1333C190.725 34.7001 191.175 34.4306 191.663 34.3239C191.48 35.0989 191.419 35.9007 191.48 36.7286C191.541 37.5568 191.739 38.3355 192.075 39.0648C192.288 39.506 192.544 39.9082 192.841 40.2729C193.139 40.6378 193.501 40.9492 193.928 41.2075C193.806 41.466 193.626 41.7204 193.39 41.9711ZM218.702 37.6519C218.747 37.3026 218.816 36.9336 218.908 36.5462C218.999 36.159 219.114 35.7828 219.251 35.4181C219.388 35.0532 219.548 34.738 219.731 34.4723C219.914 34.2065 220.108 34.0163 220.314 33.9024C220.52 33.7884 220.73 33.7997 220.943 33.9365C221.172 34.0735 221.313 34.3621 221.367 34.8025C221.42 35.2435 221.367 35.7142 221.207 36.2159C221.046 36.7173 220.761 37.1884 220.349 37.6288C219.937 38.07 219.38 38.3583 218.679 38.4951C218.648 38.2826 218.656 38.0015 218.702 37.6519ZM227.921 37.6519C227.966 37.3026 228.035 36.9336 228.126 36.5462C228.218 36.159 228.332 35.7828 228.47 35.4181C228.607 35.0532 228.767 34.738 228.95 34.4723C229.133 34.2065 229.328 34.0163 229.533 33.9024C229.739 33.7884 229.949 33.7997 230.162 33.9365C230.391 34.0735 230.532 34.3621 230.586 34.8025C230.639 35.2435 230.586 35.7142 230.425 36.2159C230.265 36.7173 229.979 37.1884 229.568 37.6288C229.156 38.07 228.599 38.3583 227.898 38.4951C227.867 38.2826 227.875 38.0015 227.921 37.6519ZM236.488 38.9852C236.312 38.7955 236.099 38.6625 235.847 38.5862C235.595 38.5104 235.355 38.5029 235.126 38.5636C234.897 38.6244 234.752 38.784 234.692 39.0422C234.57 39.5286 234.375 40.0262 234.108 40.5349C233.841 41.0444 233.514 41.5267 233.125 41.9824C232.736 42.4381 232.297 42.8412 231.81 43.1905C231.321 43.5401 230.81 43.7908 230.277 43.9423C229.743 44.1101 229.301 44.1289 228.95 43.9996C228.599 43.8706 228.321 43.6503 228.115 43.3389C227.909 43.0271 227.761 42.6512 227.669 42.2103C227.578 41.7699 227.524 41.3142 227.509 40.8428C228.378 40.9038 229.152 40.7483 229.831 40.3755C230.509 40.0034 231.085 39.5092 231.558 38.8939C232.031 38.2788 232.389 37.5874 232.633 36.82C232.877 36.0526 233.014 35.2892 233.045 34.5293C233.06 33.815 232.953 33.211 232.724 32.7171C232.496 32.2235 232.187 31.8395 231.798 31.5662C231.409 31.2924 230.963 31.133 230.46 31.0874C229.957 31.0417 229.445 31.1105 228.927 31.2924C228.302 31.5055 227.772 31.851 227.338 32.3296C226.903 32.8085 226.54 33.3634 226.251 33.9934C225.961 34.6244 225.732 35.3039 225.564 36.0335C225.396 36.7627 225.274 37.481 225.199 38.1874C225.124 38.873 225.084 39.5292 225.075 40.1572C225.017 40.2824 224.956 40.4082 224.889 40.5349C224.622 41.0444 224.295 41.5267 223.906 41.9824C223.517 42.4381 223.078 42.8412 222.591 43.1905C222.102 43.5401 221.592 43.7908 221.058 43.9423C220.524 44.1101 220.082 44.1289 219.731 43.9996C219.38 43.8706 219.102 43.6503 218.896 43.3389C218.691 43.0271 218.542 42.6512 218.45 42.2103C218.359 41.7699 218.305 41.3142 218.29 40.8428C219.159 40.9038 219.933 40.7483 220.612 40.3755C221.29 40.0034 221.866 39.5092 222.339 38.8939C222.811 38.2788 223.17 37.5874 223.414 36.82C223.658 36.0526 223.795 35.2892 223.826 34.5293C223.841 33.815 223.734 33.211 223.506 32.7171C223.277 32.2235 222.968 31.8395 222.579 31.5662C222.19 31.2924 221.744 31.133 221.241 31.0874C220.738 31.0417 220.227 31.1105 219.708 31.2924C219.083 31.5055 218.553 31.851 218.119 32.3296C217.684 32.8085 217.321 33.3634 217.032 33.9934C216.742 34.6244 216.513 35.3039 216.346 36.0335C216.178 36.7627 216.056 37.481 215.98 38.1874C215.936 38.5859 215.907 38.9722 215.886 39.3516C215.739 39.4765 215.595 39.6023 215.442 39.7258C214.916 40.1514 214.363 40.5349 213.784 40.8769C213.204 41.219 212.601 41.5001 211.977 41.7204C211.351 41.9408 210.71 42.0738 210.055 42.1192L211.473 26.9847C211.565 26.6655 211.519 26.3847 211.336 26.1415C211.153 25.8983 210.916 25.7312 210.627 25.6401C210.337 25.5488 210.028 25.5566 209.7 25.6627C209.372 25.7694 209.102 26.0126 208.888 26.3919C208.781 26.9697 208.671 27.7597 208.557 28.7625C208.442 29.7653 208.328 30.8595 208.213 32.0448C208.099 33.23 207.985 34.4532 207.87 35.7142C207.756 36.9759 207.657 38.1533 207.573 39.2472C207.569 39.2958 207.566 39.3398 207.562 39.3878C207.429 39.5005 207.299 39.6142 207.161 39.7258C206.635 40.1514 206.082 40.5349 205.503 40.8769C204.923 41.219 204.321 41.5001 203.696 41.7204C203.07 41.9408 202.429 42.0738 201.774 42.1192L203.192 26.9847C203.284 26.6655 203.238 26.3847 203.055 26.1415C202.872 25.8983 202.635 25.7312 202.346 25.6401C202.056 25.5488 201.747 25.5566 201.419 25.6627C201.091 25.7694 200.821 26.0126 200.607 26.3919C200.501 26.9697 200.39 27.7597 200.276 28.7625C200.161 29.7653 200.047 30.8595 199.933 32.0448C199.818 33.23 199.704 34.4532 199.589 35.7142C199.475 36.9759 199.376 38.1533 199.292 39.2472C199.29 39.2692 199.289 39.2891 199.287 39.3111C199.048 39.4219 198.786 39.519 198.503 39.6006C198.213 39.6844 197.885 39.7339 197.519 39.7489C197.58 39.4751 197.63 39.1712 197.668 38.8369C197.706 38.5029 197.737 38.1533 197.76 37.7884C197.782 37.4241 197.79 37.0591 197.782 36.6945C197.774 36.3296 197.755 35.9956 197.725 35.6914C197.649 35.0385 197.508 34.4191 197.302 33.8338C197.096 33.2491 196.818 32.7593 196.467 32.3637C196.116 31.9687 195.678 31.7027 195.151 31.5662C194.626 31.4294 194.012 31.4748 193.31 31.7027C192.273 31.5662 191.339 31.6613 190.508 31.9878C189.677 32.3149 188.956 32.7894 188.346 33.4122C187.736 34.0357 187.237 34.7684 186.848 35.6119C186.459 36.4551 186.2 37.3214 186.07 38.21C186.015 38.5868 185.988 38.9618 185.98 39.336C185.744 39.8177 185.486 40.2388 185.201 40.5921C184.797 41.0935 184.377 41.5038 183.943 41.8228C183.508 42.142 183.077 42.3852 182.65 42.5523C182.223 42.7198 181.842 42.8337 181.507 42.8941C181.11 42.9702 180.729 42.978 180.363 42.917C179.997 42.8565 179.661 42.6816 179.357 42.3927C179.112 42.1802 178.925 41.8381 178.796 41.3671C178.666 40.896 178.59 40.3608 178.567 39.7602C178.544 39.1599 178.567 38.533 178.636 37.8798C178.705 37.2266 178.822 36.6072 178.99 36.0222C179.158 35.4372 179.371 34.913 179.631 34.4492C179.89 33.9862 180.195 33.6554 180.546 33.4579C180.744 33.4886 180.866 33.606 180.912 33.811C180.958 34.0163 180.969 34.2595 180.946 34.5405C180.923 34.8219 180.889 35.1105 180.843 35.4066C180.797 35.703 180.775 35.9502 180.775 36.1474C180.851 36.5577 180.999 36.877 181.221 37.1048C181.441 37.3327 181.69 37.466 181.964 37.5036C182.239 37.5417 182.509 37.4773 182.776 37.3098C183.043 37.143 183.26 36.877 183.428 36.512C183.443 36.5274 183.466 36.5349 183.497 36.5349L183.817 33.6404C183.909 33.2451 183.847 32.8958 183.634 32.5919C183.42 32.288 183.138 32.113 182.788 32.0676C182.345 31.4294 181.747 31.0914 180.992 31.0532C180.237 31.0154 179.463 31.2623 178.67 31.7941C178.182 32.144 177.751 32.626 177.378 33.2413C177.004 33.857 176.699 34.5405 176.463 35.2926C176.226 36.0448 176.058 36.8391 175.959 37.6748C175.86 38.5104 175.841 39.3236 175.902 40.1133C175.963 40.9038 176.104 41.6484 176.325 42.347C176.546 43.0462 176.855 43.6312 177.252 44.102C177.587 44.5123 177.968 44.8127 178.395 45.0027C178.822 45.1927 179.268 45.3101 179.734 45.3558C180.199 45.4012 180.66 45.3821 181.118 45.2988C181.575 45.2155 182.01 45.0978 182.421 44.9454C182.955 44.7482 183.505 44.4972 184.069 44.1933C184.633 43.8897 185.174 43.5248 185.693 43.0991C185.966 42.8753 186.228 42.6313 186.482 42.3696C186.598 42.6553 186.727 42.9317 186.882 43.1905C187.294 43.8741 187.85 44.429 188.552 44.8544C189.253 45.2797 190.115 45.4844 191.137 45.4697C192.235 45.4544 193.249 45.1774 194.18 44.6378C195.11 44.0988 195.872 43.3042 196.467 42.256C197.358 42.256 198.234 42.1096 199.096 41.819C199.089 41.911 199.081 42.0079 199.075 42.0966C199.014 42.9019 198.983 43.4487 198.983 43.7376C198.968 44.239 198.934 44.8581 198.88 45.5949C198.827 46.332 198.793 47.1069 198.778 47.9198C198.763 48.7326 198.793 49.5532 198.869 50.3817C198.945 51.2096 199.105 51.962 199.349 52.6383C199.593 53.3141 199.94 53.8878 200.39 54.3591C200.84 54.8299 201.431 55.1112 202.163 55.2023C202.941 55.3084 203.612 55.1717 204.176 54.792C204.74 54.412 205.198 53.8918 205.549 53.2308C205.899 52.5695 206.147 51.8061 206.292 50.9401C206.437 50.074 206.479 49.2039 206.418 48.3301C206.357 47.4562 206.196 46.6321 205.937 45.8575C205.678 45.0822 205.319 44.444 204.862 43.9423C205.137 43.8669 205.465 43.7226 205.846 43.5095C206.227 43.2969 206.62 43.0575 207.024 42.7915C207.123 42.7261 207.221 42.6573 207.32 42.5902C207.283 43.1286 207.264 43.5126 207.264 43.7376C207.249 44.239 207.215 44.8581 207.161 45.5949C207.108 46.332 207.073 47.1069 207.058 47.9198C207.043 48.7326 207.073 49.5532 207.15 50.3817C207.226 51.2096 207.386 51.962 207.63 52.6383C207.874 53.3141 208.221 53.8878 208.671 54.3591C209.121 54.8299 209.712 55.1112 210.444 55.2023C211.221 55.3084 211.892 55.1717 212.457 54.792C213.021 54.412 213.478 53.8918 213.83 53.2308C214.18 52.5695 214.428 51.8061 214.573 50.9401C214.718 50.074 214.759 49.2039 214.699 48.3301C214.637 47.4562 214.477 46.6321 214.218 45.8575C213.959 45.0822 213.601 44.444 213.143 43.9423C213.418 43.8669 213.745 43.7226 214.127 43.5095C214.508 43.2969 214.9 43.0575 215.305 42.7915C215.515 42.6533 215.724 42.5107 215.932 42.3641C216.01 43.1072 216.179 43.759 216.448 44.3073C216.776 44.9761 217.222 45.4925 217.787 45.8575C218.351 46.2218 219.014 46.4234 219.777 46.4612C220.539 46.4988 221.37 46.3586 222.271 46.0393C222.941 45.7965 223.525 45.4925 224.02 45.1279C224.516 44.763 224.962 44.3185 225.358 43.7946C225.381 43.7642 225.403 43.7313 225.425 43.7006C225.496 43.9134 225.574 44.1179 225.667 44.3073C225.995 44.9761 226.441 45.4925 227.006 45.8575C227.569 46.2218 228.233 46.4234 228.996 46.4612C229.758 46.4988 230.589 46.3586 231.489 46.0393C232.16 45.7965 232.744 45.4925 233.239 45.1279C233.735 44.763 234.181 44.3185 234.577 43.7946C234.974 43.27 235.336 42.666 235.664 41.9824C235.992 41.2985 236.323 40.5164 236.659 39.6347C236.72 39.3918 236.663 39.1752 236.488 38.9852Z" fill="#0D0C23"/>
</svg>`,cf=`
<svg xmlns="http://www.w3.org/2000/svg" width="150" height="40">
<path fill="#253B80" d="M46.211 6.749h-6.839a.95.95 0 0 0-.939.802l-2.766 17.537a.57.57 0 0 0 .564.658h3.265a.95.95 0 0 0 .939-.803l.746-4.73a.95.95 0 0 1 .938-.803h2.165c4.505 0 7.105-2.18 7.784-6.5.306-1.89.013-3.375-.872-4.415-.972-1.142-2.696-1.746-4.985-1.746zM47 13.154c-.374 2.454-2.249 2.454-4.062 2.454h-1.032l.724-4.583a.57.57 0 0 1 .563-.481h.473c1.235 0 2.4 0 3.002.704.359.42.469 1.044.332 1.906zM66.654 13.075h-3.275a.57.57 0 0 0-.563.481l-.145.916-.229-.332c-.709-1.029-2.29-1.373-3.868-1.373-3.619 0-6.71 2.741-7.312 6.586-.313 1.918.132 3.752 1.22 5.031.998 1.176 2.426 1.666 4.125 1.666 2.916 0 4.533-1.875 4.533-1.875l-.146.91a.57.57 0 0 0 .562.66h2.95a.95.95 0 0 0 .939-.803l1.77-11.209a.568.568 0 0 0-.561-.658zm-4.565 6.374c-.316 1.871-1.801 3.127-3.695 3.127-.951 0-1.711-.305-2.199-.883-.484-.574-.668-1.391-.514-2.301.295-1.855 1.805-3.152 3.67-3.152.93 0 1.686.309 2.184.892.499.589.697 1.411.554 2.317zM84.096 13.075h-3.291a.954.954 0 0 0-.787.417l-4.539 6.686-1.924-6.425a.953.953 0 0 0-.912-.678h-3.234a.57.57 0 0 0-.541.754l3.625 10.638-3.408 4.811a.57.57 0 0 0 .465.9h3.287a.949.949 0 0 0 .781-.408l10.946-15.8a.57.57 0 0 0-.468-.895z"/>
<path fill="#179BD7" d="M94.992 6.749h-6.84a.95.95 0 0 0-.938.802l-2.766 17.537a.569.569 0 0 0 .562.658h3.51a.665.665 0 0 0 .656-.562l.785-4.971a.95.95 0 0 1 .938-.803h2.164c4.506 0 7.105-2.18 7.785-6.5.307-1.89.012-3.375-.873-4.415-.971-1.142-2.694-1.746-4.983-1.746zm.789 6.405c-.373 2.454-2.248 2.454-4.062 2.454h-1.031l.725-4.583a.568.568 0 0 1 .562-.481h.473c1.234 0 2.4 0 3.002.704.359.42.468 1.044.331 1.906zM115.434 13.075h-3.273a.567.567 0 0 0-.562.481l-.145.916-.23-.332c-.709-1.029-2.289-1.373-3.867-1.373-3.619 0-6.709 2.741-7.311 6.586-.312 1.918.131 3.752 1.219 5.031 1 1.176 2.426 1.666 4.125 1.666 2.916 0 4.533-1.875 4.533-1.875l-.146.91a.57.57 0 0 0 .564.66h2.949a.95.95 0 0 0 .938-.803l1.771-11.209a.571.571 0 0 0-.565-.658zm-4.565 6.374c-.314 1.871-1.801 3.127-3.695 3.127-.949 0-1.711-.305-2.199-.883-.484-.574-.666-1.391-.514-2.301.297-1.855 1.805-3.152 3.67-3.152.93 0 1.686.309 2.184.892.501.589.699 1.411.554 2.317zM119.295 7.23l-2.807 17.858a.569.569 0 0 0 .562.658h2.822c.469 0 .867-.34.939-.803l2.768-17.536a.57.57 0 0 0-.562-.659h-3.16a.571.571 0 0 0-.562.482z"/>
<path fill="#253B80" d="M7.266 29.154l.523-3.322-1.165-.027H1.061L4.927 1.292a.316.316 0 0 1 .314-.268h9.38c3.114 0 5.263.648 6.385 1.927.526.6.861 1.227 1.023 1.917.17.724.173 1.589.007 2.644l-.012.077v.676l.526.298a3.69 3.69 0 0 1 1.065.812c.45.513.741 1.165.864 1.938.127.795.085 1.741-.123 2.812-.24 1.232-.628 2.305-1.152 3.183a6.547 6.547 0 0 1-1.825 2c-.696.494-1.523.869-2.458 1.109-.906.236-1.939.355-3.072.355h-.73c-.522 0-1.029.188-1.427.525a2.21 2.21 0 0 0-.744 1.328l-.055.299-.924 5.855-.042.215c-.011.068-.03.102-.058.125a.155.155 0 0 1-.096.035H7.266z"/>
<path fill="#179BD7" d="M23.048 7.667c-.028.179-.06.362-.096.55-1.237 6.351-5.469 8.545-10.874 8.545H9.326c-.661 0-1.218.48-1.321 1.132L6.596 26.83l-.399 2.533a.704.704 0 0 0 .695.814h4.881c.578 0 1.069-.42 1.16-.99l.048-.248.919-5.832.059-.32c.09-.572.582-.992 1.16-.992h.73c4.729 0 8.431-1.92 9.513-7.476.452-2.321.218-4.259-.978-5.622a4.667 4.667 0 0 0-1.336-1.03z"/>
<path fill="#222D65" d="M21.754 7.151a9.757 9.757 0 0 0-1.203-.267 15.284 15.284 0 0 0-2.426-.177h-7.352a1.172 1.172 0 0 0-1.159.992L8.05 17.605l-.045.289a1.336 1.336 0 0 1 1.321-1.132h2.752c5.405 0 9.637-2.195 10.874-8.545.037-.188.068-.371.096-.55a6.594 6.594 0 0 0-1.017-.429 9.045 9.045 0 0 0-.277-.087z"/>
<path fill="#253B80" d="M9.614 7.699a1.169 1.169 0 0 1 1.159-.991h7.352c.871 0 1.684.057 2.426.177a9.757 9.757 0 0 1 1.481.353c.365.121.704.264 1.017.429.368-2.347-.003-3.945-1.272-5.392C20.378.682 17.853 0 14.622 0h-9.38c-.66 0-1.223.48-1.325 1.133L.01 25.898a.806.806 0 0 0 .795.932h5.791l1.454-9.225 1.564-9.906z"/>
</svg>`;
/*! Bundled license information:

decimal.js/decimal.js:
  (*!
   *  decimal.js v10.4.3
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)

lodash/lodash.js:
  (**
   * @license
   * Lodash <https://lodash.com/>
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/

/* nosourcemap */