/* deprecated positioning, use bottom bar instead */
.view-content > .sc-brand {
  position: fixed;
  bottom: 0;
  right: 0;
  background-color: var(--titlebar-background);
}

.sc-brand {
  > svg,
  > p {
    display: inline;
    margin: 0 0.1rem 0 0.3rem;
    color: var(--text-muted);
    font-size: var(--font-smallest);
    line-height: 1;
    height: 0.88rem;
    width: auto;
  }

  > p > a {
    color: var(--text-muted);
  }
}

.sc-list {
  .tree-item-self {
    cursor: pointer;

    small {
      color: var(--color-gray-40);
    }
  }

  > .sc-collapsed ul {
    display: none;
  }

  > .sc-collapsed span svg {
    transform: rotate(-90deg);
  }

  > :not(.sc-collapsed) span svg {
    transform: rotate(0deg);
  }

  > div {
    span svg {
      height: auto;
      margin: auto 0.5em auto 0;
      flex: none;
    }

    > span {
      display: inline-flex;
      width: 100%;
      padding-left: 0;
    }

    ul {
      margin: 0;
      padding-left: 1.3rem;
    }

    > a {
      display: block;
    }

    > ul > li {
      display: block;
    }
  }
  .sc-result {
    > ul {
      list-style: none;
      padding-left: 0;
    }
  }

  .sc-result.sc-result-plaintext {
    font-size: var(--font-ui-smaller);
    line-height: var(--line-height-tight);
    background-color: var(--search-result-background);
    border-radius: var(--radius-s);
    overflow: hidden;
    margin: var(--size-4-1) 0 var(--size-4-2);
    color: var(--text-muted);
    box-shadow: 0 0 0 1px var(--background-modifier-border);

    & > * li {
      cursor: var(--cursor);
      position: relative;
      padding: var(--size-4-2) var(--size-4-5) var(--size-4-2) var(--size-4-3);
      white-space: pre-wrap;
      width: 100%;
      border-bottom: 1px solid var(--background-modifier-border);
    }
  }

  .sc-result:not(.sc-result-plaintext) {
    cursor: pointer;
    padding: var(--nav-item-padding);
    padding-left: 10px;
    margin-bottom: 1px;
    align-items: baseline;
    border-radius: var(--radius-s);
    font-weight: var(--nav-item-weight);

    &:hover {
      color: var(--nav-item-color-active);
      background-color: var(--nav-item-background-active);
      font-weight: var(--nav-item-weight-active);
    }

    span {
      color: var(--h5-color);
    }

    small {
      color: var(--h5-color);
      font-size: 0.8rem;
      font-weight: 500;
    }

    p {
      margin-top: 0.3rem;
      margin-bottom: 0.3rem;
    }

    ul > li {
      h1 {
        font-size: 1.3rem;
      }

      h2 {
        font-size: 1.25rem;
      }

      h3 {
        font-size: 1.2rem;
      }

      h4 {
        font-size: 1.15rem;
      }

      h5 {
        font-size: 1.1rem;
      }

      h6 {
        font-size: 1.05rem;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-block-start: calc(var(--p-spacing)/2);
        margin-block-end: calc(var(--p-spacing)/2);
      }
    }
  }
} /* end .sc-list */

/* Only on right sidebar */
.mod-right-split .sc-list .sc-result {
  font-size: var(--font-text-size);
  font-size: 0.88rem;
}

.sc-top-bar {
  display: flex;
  width: 100%;
  justify-content: end;

  .search-input-container {
    width: calc(100% - var(--size-4-8));
    margin: 4px auto;
  }

  .sc-context {
    color: var(--nav-item-color);
    font-size: var(--nav-item-size);
    margin: 0.5em 0.5em 1em;
    width: 100%;
  }
}

/* Chat */
.sc-chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 100%;

  .sc-top-bar-container {
    align-self: flex-end;
    display: flex;
    width: 100%;

    .sc-chat-name-input {
      flex-grow: 1;
      min-width: 20px;
    }
  }

  .sc-thread {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    height: 100%;
    width: 100%;
    overflow: hidden;
    user-select: text;
    overflow-y: auto;

    .sc-message-container {
      border: 1px solid var(--divider-color);
      border-radius: 10px;
      margin: 0.5rem 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
      height: 100%;
      overflow-y: auto;
      background-color: var(--background-primary-alt);

      .sc-message {
        max-width: 90ch;
        width: 90%;
        margin: 10px;
        padding: 10px;
        border-radius: 1.5rem;
        word-break: break-word;

        &.user {
          align-self: flex-end;
          color: var(--text-normal);
          background-color: var(--background-primary);
        }

        &.assistant,
        &.system {
          background-color: var(--background-primary-alt);
          color: var(--text-normal);
        }

        .sc-message-content {
          margin: 0;
          padding: 1rem;

          > * p {
            margin: 0;
          }
        }
      }
    }

    .sc-chat-form {
      display: flex;
      padding: 0 10px 1rem 0;
      width: 100%;
      max-height: 50%;

      .sc-chat-input {
        flex-grow: 1;
        padding: 0.88rem;
        border: none;
        border-radius: 1.5rem;
        resize: none;
        height: auto;
        min-height: 4.2lh;
        max-height: 100%;
        background-color: var(--background-primary);
        color: var(--text-normal);
        margin-right: -2.7rem;
        padding-right: 3rem;
      }

      .sc-btn-container {
        width: 2rem;
        height: 2rem;
        margin: auto;

        > button.send-button {
          border-radius: 99999px;
          cursor: pointer;
          outline: 2px solid transparent;
          padding: 0;
          outline-offset: 2px;
          background: none !important;

          &:focus-visible {
            outline-color: var(--text-faint);
            box-shadow: none;
          }

          > svg {
            > circle {
              fill: var(--text-faint);
            }
            > path {
              fill: var(--background-primary);
            }
          }
        }
      }
    }
  }

  #settings {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: 100%;
  }
}

.sc-system {
  align-self: center;
  font-style: italic;
  color: var(--text-faint);
}

.sc-msg-button {
  cursor: pointer;
  float: right;
  margin-left: 5px;
  opacity: 0.8;

  &.cycle-branch {
    float: left;
    display: flex;
  }

  &:hover {
    opacity: 1;
  }
}

#sc-abort-button {
  cursor: pointer;
  padding: 10px;
  border-radius: 5px;

  &:hover {
    background-color: var(--background-primary);
  }
}

.sc-change {
  .sc-change-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > * {
      display: inline-block;
      color: var(--text-muted);
      font-size: var(--font-smallest);
    }

    .sc-brand {
      position: unset;
      background-color: var(--background-primary);
    }
  }

  .sc-variation {
    --new-content-color: hsl(124, 75%, 55%, 0.13);
    --old-content-color: hsl(3, 43%, 47%, 0.13);
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin: 0 -300px;
    padding: 0 300px;

    &:has(.new-content) {
      background-color: var(--new-content-color);

      > button {
        background-color: var(--new-content-color) !important;
      }
    }

    &:has(.old-content) {
      background-color: var(--old-content-color);

      > button {
        background-color: var(--old-content-color) !important;
      }
    }

    > div {
      > *:last-child {
        margin-bottom: 0;
      }
    }

    .old-content {
      color: var(--text-muted);
      text-decoration: line-through;

      > *:first-child {
        margin-top: 0;
      }
    }
  }
}

.markdown-source-view.mod-cm6 .cm-embed-block:not(.cm-table-widget):hover:has(.sc-change) {
  overflow: unset;
  box-shadow: unset;
  cursor: unset;
}

.notice .sc-notice-actions {
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
}

.sc-chat-container {
  #settings {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: 100%;
  }

  .sc-config-error-notice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background-color: #ffcccc;
    border: 1px solid #ff0000;
    border-radius: 5px;
    margin: 10px 0;
    font-size: 14px;
    font-weight: bold;
    color: #ff0000;
    width: 100%;

    span {
      flex-grow: 1;
    }

    button {
      margin-left: 10px;
    }
  }
}

.sc-supporters {
  border: 1px solid var(--h1-color);
  border-radius: 10px;
  padding: 10px;
  margin-left: -10px;
  box-shadow: 0 1px 0 1px var(--shadow-300);
  overflow: auto;
  text-wrap: balance;

  > p {
    margin-top: 0;
    margin-bottom: 5px;
  }

  > ul {
    margin-top: 5px;
  }

  > * li {
    margin-top: 0.37em;
  }
}

/* settings */
.vertical-tab-content:has(> .sc-supporters) {
  .setting-item-control {
    > select {
      max-width: 500px;
    }
  }
}

.sc-bottom-bar {
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--titlebar-background);
  padding: 0 0.5rem;

  .sc-brand {
    flex-shrink: 0;
  }

  .sc-context {
    flex-grow: 1;
    font-size: var(--font-smallest);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.setting-component[data-setting*="/"][data-setting*="api_key"] {
  display: none;
}

.setting-component[data-setting*="gpu"]:not([data-setting*="/"]) {
  display: none;
}

/* SINCE COMPONENT PATTERN SETTINGS */
.setting-component[data-setting="is_obsidian_vault"] {
  display: none;
}

.setting-component[data-setting="smart_change.active"] {
  display: none;
}

.group-header {
  display: flex;
  text-wrap: nowrap;
  flex-wrap: wrap;
  align-items: baseline;

  > h2 {
    width: 100%;
    margin-bottom: 0;
  }

  > * {
    flex-grow: 1;
    margin-bottom: 10px;
  }
}

/* SMART CHAT v2 */
.sc-context-list {
  list-style: none;
  margin: 0;
  padding: 0 1rem 1rem;
  display: none;
  flex-direction: column;
  gap: 0.5rem;
}

.sc-context-header[aria-expanded="true"] + .sc-context-list {
  display: flex;
}

.sc-context-header[aria-expanded="false"] + .sc-context-list {
  display: none;
}

.sc-context-toggle-icon {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.sc-context-header[aria-expanded="true"] .sc-context-toggle-icon {
  transform: rotate(180deg);
}

.sc-context-container {
  border: 1px solid var(--divider-color);
  border-radius: 10px;
  margin: 0.5rem 0;
  background-color: var(--background-primary-alt);
  overflow: auto;
  max-width: 95%;
  margin: 10px;
  flex-shrink: 0;

  .sc-context-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.88rem 1rem;
    background-color: var(--background-primary);
    color: var(--text-muted);
    font-weight: var(--font-medium);
    cursor: pointer;
    user-select: none;
  
    &:hover {
      background-color: var(--background-primary-hover);
    }
  
    &:focus {
      outline: 2px solid var(--text-muted);
      outline-offset: 2px;
    }
  }
}


.sc-context-item {
  padding: 0.5rem;
  border-radius: var(--radius-s);
  background-color: var(--background-secondary);
  color: var(--text-normal);
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  font-size: var(--font-smallest);
  flex-wrap: wrap;

  &:hover {
    background-color: var(--background-secondary-hover);
  }
}

.sc-context-item-path {
  font-weight: var(--font-medium);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 70%;
}

.sc-context-item-score {
  font-size: var(--font-small);
  color: var(--color-gray-40);
}

/* Tool Calls Component Styles */
.sc-tool-calls-container {
  border: 1px solid var(--divider-color);
  border-radius: 10px;
  margin: 0.5rem 0;
  background-color: var(--background-primary-alt);
  overflow: auto;
  max-width: 95%;
  margin: 10px;
  flex-shrink: 0;
}

.sc-tool-call {
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.sc-tool-call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.88rem 1rem;
  background-color: var(--background-primary);
  color: var(--text-muted);
  font-weight: var(--font-medium);
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--background-primary-hover);
  }

  &:focus {
    outline: 2px solid var(--text-muted);
    outline-offset: 2px;
  }

  &[aria-expanded="true"] .sc-tool-call-toggle-icon {
    transform: rotate(180deg);
  }
}

.sc-tool-call-content {
  padding: 0.5rem 1rem;
  background-color: var(--background-secondary);
  font-size: var(--font-smallest);

  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
  }

  code {
    font-family: var(--font-monospace);
  }
}

/* Hide content when collapsed */
.sc-tool-call-header[aria-expanded="false"] + .sc-tool-call-content {
  display: none;
}

/* Show content when expanded */
.sc-tool-call-header[aria-expanded="true"] + .sc-tool-call-content {
  display: block;
}

/* System Message Styles */
.sc-system-message-container {
  margin: 1rem 0;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  background: var(--background-secondary);
  flex-shrink: 0;
}

.sc-system-message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--background-modifier-hover);
  }

  span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .sc-system-message-toggle-icon {
    transition: transform 0.2s ease;
  }

  &[aria-expanded="true"] {
    border-bottom-color: var(--background-modifier-border);
  }
}

.sc-system-message-content {
  padding: 1rem;
  position: relative;
  background: var(--background-primary);
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;

  .sc-system-message-text {
    font-family: var(--font-monospace);
    white-space: pre-wrap;
    word-break: break-word;
    margin-right: 2rem;
  }

  .sc-system-message-copy {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.4rem;
    background: transparent;
    border: none;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }

    &.sc-copied {
      color: var(--text-accent);
    }
  }
}

.sc-chat-container {
  .smart-chat-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-primary-alt);
    z-index: 100;
    overflow: auto;

    .smart-chat-overlay-header {
      display: flex;
      justify-content: flex-end;
    }
    .setting-item {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}

.sc-typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  display: none;

  &.visible {
    display: flex;
  }

  .sc-typing-dots {
    display: flex;
    gap: 4px;

    .sc-typing-dot {
      width: 8px;
      height: 8px;
      background: var(--text-muted);
      border-radius: 50%;
      animation: typing-bounce 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: 0s;
      }

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

}
/* keyframes must be at root level */
@keyframes typing-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}

/* Smart Directories */
.sg-top-bar {
  display: flex;
  justify-content: flex-end;
}
.sg-list {
  > .sg-collapsed .sg-directory-header svg {
    transform: rotate(-90deg);
    transition: transform 0.15s ease;
  }

  > :not(.sg-collapsed) .sg-directory-header svg {
    transform: rotate(0deg);
    transition: transform 0.15s ease;
  }

  .sg-directory-header {
    cursor: pointer;
    user-select: none;

    &:hover {
      background-color: var(--nav-item-background-hover);
    }
  }
}

/* Add this to handle content visibility */
.sg-directory-item.sg-collapsed .sg-directory-content {
  display: none;
}

/* Lookup */
#sc-lookup-view {
  .sc-container {
    .sc-textarea-container {
      display: flex;
      padding: 0 10px 1rem 0;
      width: 100%;
      max-height: 50%;

      > textarea {
        flex-grow: 1;
        padding: 0.88rem;
        border: none;
        border-radius: 1.5rem;
        resize: none;
        height: auto;
        min-height: 4.2lh;
        max-height: 100%;
        background-color: var(--background-primary);
        color: var(--text-normal);
        margin-right: -2.7rem;
        padding-right: 3rem;
      }
      .sc-textarea-btn-container {
        width: 2rem;
        height: 2rem;
        margin: auto;

        > button.send-button {
          border-radius: 99999px;
          cursor: pointer;
          outline: 2px solid transparent;
          padding: 0;
          outline-offset: 2px;
          background: none !important;

          &:focus-visible {
            outline-color: var(--text-faint);
            box-shadow: none;
          }

          > svg {
            > circle {
              fill: var(--text-faint);
            }
            > path {
              fill: var(--background-primary);
            }
          }
        }
      }
    }
  }
}

/* Side Panes */
div.workspace-leaf-content[data-type^="smart-"] {
  > .view-content {
    display: flex;
    flex-direction: column;

    .sc-overlay {
      background-color: var(--background-primary-alt);
      max-width: 100%;
      max-height: 42%;

      &:has(.setting-component) {
        overflow-y: auto;
        padding: var(--size-4-4);
        flex: none;

        .setting-item {
          flex-direction: column;
          align-items: flex-start;
        }
      }
    }
  }
}

.sc-pro {
  .sc-settings {
    padding: var(--size-4-4);

    textarea {
      min-height: 4rem;
      flex-grow: 1;
    }
  }
}

.sc-inline-confirm-row {
  margin-top: 10px;
  padding: 6px;
  border: 1px solid var(--interactive-normal);
}
.sc-inline-confirm-row-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;

  & .sc-inline-confirm-yes {
    font-weight: bold;
  }
  & .sc-inline-confirm-cancel {
    font-weight: normal;
  }
}