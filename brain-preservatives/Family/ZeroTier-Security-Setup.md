# ZeroTier Network Security with ModSecurity WAF

Created: 16:36
Last modified: 16:36
Tags: #network #security #zerotier #waf

## Overview

This document details the security architecture of our dual ZeroTier network setup with an EC2 bridge for securely exposing services via Nginx Proxy Manager and ModSecurity WAF.

## Network Architecture

```
Internet --> EC2 Bridge --> ZeroTier Network 1 --> Home Router --> ZeroTier Network 2 --> Private Services
                                                                     |
                                                                     v
                                                               ModSecurity WAF
                                                                     |
                                                                     v
                                                            Nginx Proxy Manager
                                                                     |
                                                                     v
                                                             Service Containers
```

## Security Components

1. **Dual ZeroTier Networks**: Isolation between internet-facing and internal private networks
2. **EC2 Bridge**: Acts as a secure gateway between public internet and private networks
3. **ModSecurity WAF**: Application-layer filtering using OWASP Core Rule Set
4. **Nginx Proxy Manager**: TLS termination and proxy management

## Configuration Details

### ModSecurity WAF Configuration

The WAF is configured via Docker Compose with the following key security settings:

- OWASP ModSecurity Core Rule Set (CRS) enabled
- Paranoia level: 2 (balanced security)
- Services accessible only through specific path prefixes:
  - Sonarr: `/son/`
  - Radarr: `/rad/`
  - qBittorrent: `/qbt/`
  - Prowlarr: `/prowl/`
- Default block for undefined paths
- Protected internal network via dedicated subnet (**********/16)

### Security Hardening Recommendations

#### 1. WAF Tuning

- **Increase Paranoia Level**: Consider raising from level 2 to 3 for stronger security
- **Custom Rules**: Implement service-specific rules for deeper protection
- **Block Lists**: Add IP block lists for known malicious sources

```bash
# Edit docker-compose.yml to increase paranoia level
- PARANOIA=3  # More strict security rules (increased from 2)
```

#### 2. Network Security

- **Limit ZeroTier Network Access**: Restrict member access to the minimum necessary
- **Firewall Rules**: Implement iptables/ufw rules on the EC2 bridge
- **Port Restrictions**: Only expose necessary ports (80/443)

#### 3. Authentication

- **Strong Passwords**: Ensure all services use strong unique passwords
- **2FA**: Enable two-factor authentication where available
- **JWT Validation**: Add JWT validation for services that support it

#### 4. TLS Security

- **TLS 1.3 Only**: Disable older TLS versions in Nginx Proxy Manager
- **Strong Ciphers**: Use only high-security cipher suites
- **HSTS**: Enable HTTP Strict Transport Security

#### 5. Logging and Monitoring

- **Centralized Logging**: Forward all logs to a central system
- **Alerting**: Set up alerts for suspicious activities
- **Regular Audits**: Schedule weekly log reviews

## Maintenance Procedures

### Weekly Security Tasks

1. Check WAF logs for attack patterns
2. Update all container images
3. Verify ZeroTier network member access
4. Scan for exposed ports

### Monthly Security Tasks

1. Rotate all access credentials
2. Check for CRS updates
3. Review and tune WAF rules
4. Perform vulnerability scanning

### Quarterly Security Tasks

1. Full security audit
2. Penetration testing
3. Architecture review

## Troubleshooting

### Common WAF Issues

1. **False Positives**: If legitimate traffic is blocked:
   ```bash
   # Add exclusion rule to WAF
   SecRuleRemoveById 942100  # Example: SQL Injection rule
   ```

2. **Read-only filesystem errors**:
   - Ensure volume mounts are correctly configured
   - Use templates directory for configuration files

3. **Configuration doesn't apply**:
   - Check that templates are in the correct location
   - Verify environment variables are being passed

## References

- [OWASP ModSecurity Core Rule Set](https://coreruleset.org/)
- [ZeroTier Network Security](https://docs.zerotier.com/security/)
- [Nginx Proxy Manager Documentation](https://nginxproxymanager.com/guide/)
- [Docker Security Best Practices](https://docs.docker.com/engine/security/) 