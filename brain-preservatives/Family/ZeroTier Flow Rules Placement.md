---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - ZeroTier Flow Rules Configuration
  - Network Rules Placement
tags:
  - networking
  - zerotier
  - bridge
  - flow-rules
  - security
area: Infrastructure
project: Bridge Network
status: active
priority: high
links: 
  - "[[ZeroTier Bridge Connectivity Troubleshooting]]"
  - "[[Bridge network to access zero tier services from internet]]"
related:
  - "[[EC2 Bridge Server Maintenance]]"
  - "[[Media Streaming Server]]"
---

# ZeroTier Flow Rules Placement

This document explains the optimal placement of flow rules in a multi-network ZeroTier bridge setup, addressing where rules should be applied for maximum security and functionality.

## Network Architecture Overview

In a typical bridge server setup connecting internet services to local resources, there are two common configurations:

### Configuration 1: Single ZeroTier Network
- Both the EC2 bridge server and local services exist on the same ZeroTier network
- All devices share the same IP range (e.g., **********/16)
- Simpler to configure but less secure

### Configuration 2: Dual ZeroTier Networks (Recommended)
- **Network A**: Bridge server network (e.g., ***********/24)
- **Network B**: Local services network (e.g., **********/16)
- The bridge server is a member of both networks
- More secure but requires more configuration

## Where to Apply Flow Rules

### Single Network Configuration

If using a single network, apply your flow rules to that network only. These rules should:

1. Allow only necessary traffic between the bridge server and local services
2. Block direct access between internet clients and local services
3. Allow the bridge server to access the internet

### Dual Network Configuration (Recommended)

In the dual network setup, rules should be applied to both networks with different purposes:

#### Network A (Bridge Server Network)
- **Purpose**: Control what traffic can pass from the internet to your bridge server
- **Key Rules**:
  - Allow only HTTP/HTTPS (ports 80/443) from the internet to the bridge
  - Allow the bridge to communicate with your local network
  - Block unauthorized access to the bridge itself

#### Network B (Local Services Network)
- **Purpose**: Control what the bridge can access within your local network
- **Key Rules**:
  - Allow the bridge to access only specific services (Nginx, Jellyfin, etc.)
  - Allow specific ports needed for those services
  - Block access to other sensitive local resources

## Flow Rules Recommendations

### Network A (Bridge Network) Rules

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow traffic from the bridge to the internet
accept
  ipsrc ************/32  # Bridge's IP on Network A
;

# Allow internet traffic to bridge on ports 80/443 only
accept
  ipprotocol tcp
  and ipdest ************/32  # Bridge's IP on Network A
  and dport 80
;

accept
  ipprotocol tcp
  and ipdest ************/32  # Bridge's IP on Network A
  and dport 443
;

# Allow ICMP for troubleshooting
accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

# Allow ARP for address resolution
accept
  ethertype arp
;

# Drop everything else
drop;
```

### Network B (Local Services Network) Rules

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow HTTP/HTTPS to Nginx from bridge server
accept
  ipprotocol tcp
  and dport 80
  and ipdest *************/32  # Nginx server
  and ipsrc **************/32  # Bridge's IP on Network B
;

accept
  ipprotocol tcp
  and dport 443
  and ipdest *************/32  # Nginx server
  and ipsrc **************/32  # Bridge's IP on Network B
;

# Allow return traffic from Nginx to bridge
accept
  ipprotocol tcp
  and sport 80
  and ipsrc *************/32  # Nginx server
  and ipdest **************/32  # Bridge's IP on Network B
;

accept
  ipprotocol tcp
  and sport 443
  and ipsrc *************/32  # Nginx server
  and ipdest **************/32  # Bridge's IP on Network B
;

# Allow other necessary services (example: Jellyfin)
accept
  ipprotocol tcp
  and dport 8096
  and ipdest **********/16
  and ipsrc **************/32  # Bridge's IP on Network B
;

# Allow DNS queries
accept
  ipprotocol udp
  and dport 53
  and ipsrc **************/32  # Bridge's IP on Network B
;

# Allow ICMP for troubleshooting
accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

# Allow ARP for address resolution
accept
  ethertype arp
;

# Drop everything else
drop;
```

## Pros and Cons of Different Placements

### Placing Rules Only on Bridge Network

**Pros:**
- Simpler configuration
- Fewer rules to manage
- Single point of security policy

**Cons:**
- Local network remains exposed if bridge is compromised
- Can't restrict bridge's access to specific local resources
- Less granular control

### Placing Rules Only on Local Services Network

**Pros:**
- Protects local resources from the bridge server
- Maintains security even if bridge is compromised
- Can restrict bridge to only necessary services

**Cons:**
- Doesn't protect the bridge itself from internet threats
- Requires bridge to have other security measures
- May still expose bridge to attacks

### Placing Rules on Both Networks (Recommended)

**Pros:**
- Defense in depth - multiple security layers
- Granular control over both bridge access and local resource access
- Maintains security if either network is compromised
- Follows principle of least privilege

**Cons:**
- More complex to configure and manage
- Requires maintaining two sets of rules
- May cause troubleshooting complexity

## Implementation Guidelines

1. **Start with the Local Services Network**:
   - Begin by implementing rules on your local services network
   - Focus on protecting your sensitive local resources
   - Test connectivity from the bridge to ensure services work

2. **Then Configure Bridge Network**:
   - Apply rules to the bridge network after local services are working
   - Focus on protecting the bridge from internet threats
   - Ensure bridge can still access necessary internet services

3. **Test Incrementally**:
   - Test each rule set individually before combining them
   - Start with more permissive rules and add restrictions
   - Document what works at each step

## Troubleshooting Rule Placement Issues

If you experience connectivity problems after applying rules:

1. **Identify Which Network is Blocking**:
   - Temporarily disable rules on one network at a time
   - Determine which rule set is causing the issue

2. **Check Traffic Direction**:
   - Remember that rules need to allow both the initial connection and return traffic
   - Use `tcpdump` on both ends to see where traffic is being dropped

3. **Review IP Addresses**:
   - Ensure you're using the correct IP addresses for each device on each network
   - The bridge server will have different IPs on each ZeroTier network

## Security Considerations

1. **Default Deny Approach**:
   - Always start with a default deny stance and add specific allow rules
   - End your rule set with an explicit drop rule

2. **Principle of Least Privilege**:
   - Only allow the minimum access needed for each service
   - Be specific with port numbers and IP addresses

3. **Regular Audits**:
   - Review your flow rules regularly
   - Remove rules for services that are no longer needed

## Summary

- **Single Network**: Apply rules to control all traffic on this one network
- **Dual Networks**: Apply different rule sets to each network:
  - Bridge Network: Focus on controlling internet-to-bridge traffic
  - Local Network: Focus on controlling bridge-to-local-services traffic
- **Best Practice**: Implement rules on both networks for defense in depth

This approach provides the most security by creating multiple layers of protection, ensuring that even if one network is compromised, the other maintains security controls.

## Related Documentation

- [[ZeroTier Bridge Connectivity Troubleshooting]] - Detailed troubleshooting guide
- [[EC2 Bridge Server Maintenance]] - Maintaining your bridge server
- [[Bridge network to access zero tier services from internet]] - Overview of the network architecture 