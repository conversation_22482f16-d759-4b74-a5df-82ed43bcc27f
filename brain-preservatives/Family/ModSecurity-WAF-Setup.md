# ModSecurity WAF Configuration Guide

Created: 16:37
Last modified: 16:37
Tags: #security #waf #modsecurity #nginx

## Overview

This guide details the configuration and troubleshooting of ModSecurity WAF for protecting network services through Nginx Proxy Manager.

## Quick Reference

- **Container Name**: `modsecurity-waf`
- **Base Image**: `owasp/modsecurity-crs:nginx`
- **Internal Port**: 8080
- **Protected Services**:
  - Sonarr (**********:8989) via path `/son/`
  - Radarr (**********:7878) via path `/rad/`
  - qBittorrent (**********:8040) via path `/qbt/`
  - Prowlarr (**********:9696) via path `/prowl/`

## Configuration Files

### 1. Docker Compose Configuration

```yaml
waf:
  image: 'owasp/modsecurity-crs:nginx'
  container_name: modsecurity-waf
  restart: unless-stopped
  environment:
    - PARANOIA=2
    - PORT=8080
    - PROXY_LOGS=/var/log/modsecurity/proxy.log
    - MODSEC_AUDIT_LOG=/var/log/modsecurity/audit.log
    - ERROR_LOG=/var/log/modsecurity/error.log
  volumes:
    - ./waf/custom.conf.template:/etc/nginx/templates/conf.d/custom.conf.template:ro
    - ./logs/waf:/var/log/modsecurity:rw
  networks:
    proxy-manager:
      ipv4_address: **********
```

### 2. Custom Configuration Template

The template must be mounted to `/etc/nginx/templates/conf.d/custom.conf.template`:

```nginx
# Upstream definitions
upstream sonarr_backend {
    server **********:8989;
}

upstream radarr_backend {
    server **********:7878;
}

upstream qbittorrent_backend {
    server **********:8040;
}

upstream prowlarr_backend {
    server **********:9696;
}

# Websocket support
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Main server block
server {
    listen 8080 default_server;
    server_name _;
    
    # ModSecurity settings
    modsecurity on;
    modsecurity_rules_file /etc/modsecurity.d/modsecurity-override.conf;
    
    # Logging
    access_log /var/log/modsecurity/proxy.log;
    
    # Proxy headers
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    
    # Websocket headers
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;
    
    # Service locations
    location /son/ {
        proxy_pass http://sonarr_backend/;
    }
    
    location /rad/ {
        proxy_pass http://radarr_backend/;
    }
    
    location /qbt/ {
        proxy_pass http://qbittorrent_backend/;
    }
    
    location /prowl/ {
        proxy_pass http://prowlarr_backend/;
    }
    
    # Default rejection
    location / {
        return 404;
    }
}
```

## Common Issues and Solutions

### Issue: Read-only Filesystem Error

**Problem**: Container startup fails with error:
```
20-envsubst-on-templates.sh: cannot create /etc/nginx/conf.d/default.conf: Read-only file system
```

**Solution**:
1. Don't mount configuration directly to `/etc/nginx/conf.d/default.conf`
2. Instead, use the template system with:
   ```yaml
   - ./waf/custom.conf.template:/etc/nginx/templates/conf.d/custom.conf.template:ro
   ```

### Issue: ModSecurity Rules Too Strict

**Problem**: Legitimate traffic blocked by WAF

**Solution**:
1. Check logs for rule IDs triggering false positives
2. Create custom rules file to disable problematic rules:
   ```yaml
   volumes:
     - ./waf/custom-rules.conf:/etc/modsecurity.d/custom-rules.conf:ro
   ```
3. Add rule exceptions in custom-rules.conf:
   ```
   # Disable specific rule that causes false positives
   SecRuleRemoveById 942100
   
   # Allow specific IP address to bypass specific rule
   SecRule REMOTE_ADDR "@ipMatch *************" "id:1000,phase:1,pass,nolog,ctl:ruleRemoveById=942100"
   ```

## Advanced Configuration

### Custom Rule Sets

Create a file `waf/custom-rules.conf` with your custom rules:

```
# Allow specific file uploads
SecRule REQUEST_URI "@beginsWith /qbt/api/v2/torrents/add" \
    "id:1001,phase:1,pass,nolog,ctl:ruleRemoveById=200003"

# Block specific user agents
SecRule REQUEST_HEADERS:User-Agent "@contains malicious" \
    "id:1002,phase:1,deny,status:403,log,msg:'Blocked malicious user agent'"
```

### Log Rotation

Implement log rotation to prevent log files from growing too large:

```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/modsecurity

# Add the following configuration
/home/<USER>/Programs/torrents/logs/waf/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 root root
    sharedscripts
    postrotate
        docker exec modsecurity-waf nginx -s reload
    endscript
}
```

## Security Recommendations

1. **Increase Paranoia Level**: Set `PARANOIA=3` for stronger security
2. **Implement Rate Limiting**:
   ```nginx
   # Add to server block
   limit_req_zone $binary_remote_addr zone=mylimit:10m rate=10r/s;
   
   # Add to each location block
   limit_req zone=mylimit burst=20 nodelay;
   ```
3. **Enable GeoIP Filtering**: Block traffic from high-risk countries
4. **Implement Custom Security Headers**:
   ```nginx
   # Add to server block
   add_header X-Content-Type-Options "nosniff" always;
   add_header X-XSS-Protection "1; mode=block" always;
   add_header X-Frame-Options "SAMEORIGIN" always;
   add_header Content-Security-Policy "default-src 'self'" always;
   ```

## Monitoring

Monitor ModSecurity logs for attacks:

```bash
# Check for blocked requests
grep -i "Access denied" /home/<USER>/Programs/torrents/logs/waf/audit.log

# Count attacks by IP
grep -i "Access denied" /home/<USER>/Programs/torrents/logs/waf/audit.log | awk '{print $1}' | sort | uniq -c | sort -nr

# View rules triggering most often
grep -i "Access denied" /home/<USER>/Programs/torrents/logs/waf/audit.log | grep -o "\[id \"[0-9]*\"\]" | sort | uniq -c | sort -nr
```

## References

- [ModSecurity Reference Manual](https://github.com/SpiderLabs/ModSecurity/wiki)
- [OWASP ModSecurity Core Rule Set Documentation](https://coreruleset.org/documentation/)
- [ModSecurity Docker Image Documentation](https://hub.docker.com/r/owasp/modsecurity-crs/) 