---
creation_date: 2025-04-07
modification_date: 2025-04-07
type: overview
aliases:
  - Docker Compose Setup
  - Docker Configs
tags:
  - docker
  - docker-compose
  - configuration
  - yaml
  - mediaserver
  - nginx
area: Infrastructure
project: Bridge Network
status: active
priority: medium
links:
  - "[[Brain/Family/Managing Docker Folder Permissions]]"
  - "[[Family/Network Architecture Overview]]"
related:
  - "[[Simplified ZeroTier Network Setup]]"
  - "[[Troubleshooting ZeroTier and Docker Networking]]"
---

# Docker Configuration Files

This note provides an overview and explanation of the Docker Compose configuration files used in this setup. The actual files are stored as attachments for reference.

## Core Concepts

*   **Docker Compose**: A tool for defining and running multi-container Docker applications. It uses YAML files (`docker-compose.yml`) to configure application services, networks, and volumes.
*   **`.env` File**: A file used to store environment variables that can be referenced within the `docker-compose.yml` file. This is useful for managing sensitive information or configuration parameters like User/Group IDs (`PUID`/`PGID`), timezones (`TZ`), and file paths.

## Configuration Files

*(Please ensure you have copied your relevant `.yml` and `.env` files into the `Attachments/` subfolder)*

1.  **`docker-compose-npm.yml` (Example Name)**
    *   **Link**: `[[Attachments/docker-compose-npm.yml]]` (Update link if filename differs)
    *   **Purpose**: Defines the services for Nginx Proxy Manager (NPM) and potentially related utilities like Flaresolverr.
    *   **Key Sections**:
        *   `services`: Defines individual containers (e.g., `nginx-proxy-manager`, `flaresolverr`).
            *   `image`: Specifies the Docker image to use.
            *   `container_name`: Assigns a readable name.
            *   `volumes`: Mounts host directories or named volumes into the container (e.g., for configuration persistence `/data`, `/etc/letsencrypt`).
            *   `ports`: Maps host ports to container ports (e.g., `80:80`, `443:443`, `81:81` for NPM).
            *   `networks`: Connects the container to specific Docker networks (e.g., `proxy-manager` custom bridge network).
            *   `environment`: Sets environment variables within the container (e.g., `PUID`, `PGID` if applicable, `DB_SQLITE_FILE`).
            *   `restart`: Defines the container restart policy (e.g., `unless-stopped`).
        *   `networks`: Defines custom Docker networks (e.g., `proxy-manager: bridge`).
        *   `volumes`: Defines named volumes managed by Docker (e.g., `npm_data`).

2.  **`docker-compose-media.yml` (Example Name)**
    *   **Link**: `[[Attachments/docker-compose-media.yml]]` (Update link if filename differs)
    *   **Purpose**: Defines the services for the media stack (Radarr, Sonarr, qBittorrent if dockerized, etc.).
    *   **Key Sections (similar to above, with emphasis on)**:
        *   `environment`: Crucial for setting `PUID`, `PGID`, and `TZ` consistently across media applications. See [[Brain/Family/Managing Docker Folder Permissions]].
        *   `volumes`: Mapping configuration directories (`:/config`) and the shared media library path (e.g., `/mnt/storage/media:/media` or specific subfolders like `/mnt/storage/media/downloads:/downloads`). Ensure container paths are consistent where needed (e.g., download client reporting path to Radarr/Sonarr).
        *   `networks`: Likely connects to the same `proxy-manager` network if you want NPM to proxy them, or potentially a separate network.

3.  **`.env` (Example Name)**
    *   **Link**: `[[Attachments/.env]]` (Update link if filename differs)
    *   **Purpose**: Stores common variables referenced in the `docker-compose.yml` files.
    *   **Example Content**:
        ```dotenv
        # .env - Common Environment Variables
        
        # User/Group IDs (See Managing Docker Folder Permissions)
        PUID=1000
        PGID=2000 # Assuming a dedicated 'media' group
        
        # Timezone
        TZ=America/Los_Angeles # Set to your timezone
        
        # Root Paths (Adjust to your system)
        MEDIA_ROOT=/mnt/storage/media
        DOWNLOADS_ROOT=${MEDIA_ROOT}/downloads
        MOVIES_ROOT=${MEDIA_ROOT}/movies
        TV_ROOT=${MEDIA_ROOT}/tv
        APPDATA_ROOT=/opt/docker/appdata # Or ./appdata relative to compose file
        
        # Docker Network Name (Ensure consistency)
        PROXY_NETWORK=proxy-manager 
        ```

## Usage

Typically, you navigate to the directory containing the specific `docker-compose.yml` file and run commands:

*   `docker-compose up -d`: Start services in detached mode.
*   `docker-compose down`: Stop and remove containers, networks.
*   `docker-compose pull`: Update images to the latest version defined.
*   `docker-compose logs -f [service_name]`: View logs for a service (e.g., `radarr`).

Refer to the specific `docker-compose.yml` file and the [[Brain/Family/Managing Docker Folder Permissions]] guide for details on volume paths and environment variables needed for each service. 