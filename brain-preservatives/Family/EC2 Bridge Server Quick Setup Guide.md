---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - Bridge Server Quick Setup
tags:
  - networking
  - zerotier
  - aws
  - ec2
  - bridge
  - quick-guide
area: Infrastructure
project: Bridge Network
status: active
priority: high
links: 
  - "[[EC2 Bridge Server Maintenance]]"
  - "[[Bridge network to access zero tier services from internet]]"
related:
  - "[[Media Streaming Server]]"
---

# EC2 Bridge Server Quick Setup Guide

This is a condensed guide for quickly setting up and managing your EC2 bridge server for accessing ZeroTier services from the internet. For complete documentation, see [[EC2 Bridge Server Maintenance]].

## Prerequisites

- An AWS EC2 instance (Ubuntu/Debian recommended)
- An Elastic IP attached to your EC2 instance
- Domain name pointing to your EC2 instance's Elastic IP
- Local server with Nginx Proxy Manager installed
- Both servers connected to ZeroTier

## 1. Initial Setup

### On the EC2 Bridge Instance

```bash
# Install ZeroTier
curl -s https://install.zerotier.com | sudo bash

# Join your ZeroTier network
sudo zerotier-cli join 8056c2e21ce5322d

# Verify connection
sudo zerotier-cli info
sudo zerotier-cli listnetworks
```

### On ZeroTier Central

1. Go to [my.zerotier.com](https://my.zerotier.com/)
2. Select your network
3. Authorize the EC2 bridge instance (check the box)
4. Assign a managed IP if needed

## 2. Install the Maintenance Script

```bash
# Download the script (from another system with the script)
scp ec2-bridge-maintenance.sh ubuntu@your-ec2-instance:/tmp/

# Or create it directly on the server
sudo nano /usr/local/bin/ec2-bridge-maintenance.sh

# Make it executable
sudo chmod +x /usr/local/bin/ec2-bridge-maintenance.sh

# Edit configuration
sudo nano /usr/local/bin/ec2-bridge-maintenance.sh
```

Edit these configuration variables in the script:
```bash
INTERNET_IFACE="enX0"  # Your internet-facing interface
ZEROTIER_IFACE="ztmjfcpubr"  # Your ZeroTier interface name
LOCAL_SERVER_IP="*************"  # ZeroTier IP of your Nginx server
ZEROTIER_NETWORK_ID="8056c2e21ce5322d"  # Your ZeroTier network ID
```

## 3. Run the Maintenance Script

```bash
# Run the script
sudo /usr/local/bin/ec2-bridge-maintenance.sh

# Set up automated maintenance (optional)
sudo /usr/local/bin/ec2-bridge-maintenance.sh --add-cron
```

## 4. Configure Nginx Proxy Manager

On your local server with Nginx Proxy Manager:

1. Access Nginx Proxy Manager admin UI (http://*************:81)
2. Add a new Proxy Host:
   - Domain: your-domain.com
   - Scheme: http
   - Forward Hostname/IP: IP of your service (e.g., *************)
   - Forward Port: Port of your service (e.g., 8096 for Jellyfin)
3. Enable SSL (Let's Encrypt)

## 5. Test the Configuration

1. Access your domain in a web browser: https://your-domain.com
2. If not working, check:
   - EC2 security groups (ports 80 and 443 open)
   - iptables rules on the bridge instance
   - ZeroTier connection status
   - Nginx Proxy Manager configuration

## Common Operations

### View Current iptables Rules

```bash
sudo iptables -L -v -n
sudo iptables -t nat -L -v -n
```

### Check ZeroTier Status

```bash
sudo zerotier-cli info
sudo zerotier-cli listnetworks
```

### Generate a Status Report

```bash
sudo /usr/local/bin/ec2-bridge-maintenance.sh
```

### Verify Services are Running

```bash
sudo systemctl status zerotier-one
sudo systemctl status iptables-restore.service
```

### Add Port Forwarding for a New Service

1. Edit the maintenance script:

```bash
sudo nano /usr/local/bin/ec2-bridge-maintenance.sh
```

2. Add new iptables rules in the `setup_iptables_rules()` function:

```bash
# Forward new service port (e.g., 8080)
iptables -t nat -A PREROUTING -i "$INTERNET_IFACE" -p tcp --dport 8080 -j DNAT --to-destination "${LOCAL_SERVER_IP}:8080"
iptables -A FORWARD -i "$INTERNET_IFACE" -o "$ZEROTIER_IFACE" -p tcp --dport 8080 -d "$LOCAL_SERVER_IP" -j ACCEPT
```

3. Run the script to apply changes:

```bash
sudo /usr/local/bin/ec2-bridge-maintenance.sh
```

4. Configure Nginx Proxy Manager for the new service.

## Troubleshooting

### Traffic Not Being Forwarded

1. Verify iptables rules:

```bash
sudo iptables -t nat -L -v -n
```

2. Check ZeroTier connectivity:

```bash
ping *************  # Ping your local Nginx server
```

3. Ensure IP forwarding is enabled:

```bash
cat /proc/sys/net/ipv4/ip_forward  # Should show 1
```

4. Run the maintenance script again:

```bash
sudo /usr/local/bin/ec2-bridge-maintenance.sh
```

### ZeroTier Connection Issues

1. Restart the ZeroTier service:

```bash
sudo systemctl restart zerotier-one
```

2. Verify network membership:

```bash
sudo zerotier-cli listnetworks
```

3. Check authorization in ZeroTier Central.

### Script Not Running on Schedule

1. Check cron job:

```bash
sudo cat /etc/cron.d/bridge-maintenance
```

2. Verify script permissions:

```bash
sudo chmod +x /usr/local/bin/ec2-bridge-maintenance.sh
```

3. Check logs:

```bash
sudo cat /var/log/bridge-maintenance/maintenance.log
```

## Quick Reference

- **Script Location**: `/usr/local/bin/ec2-bridge-maintenance.sh`
- **Log Location**: `/var/log/bridge-maintenance/`
- **Status Reports**: `/var/log/bridge-maintenance/status_report_*.txt`
- **iptables Rules**: `/etc/iptables/rules.v4`
- **Systemd Service**: `iptables-restore.service`

For complete documentation, refer to [[EC2 Bridge Server Maintenance]]. 