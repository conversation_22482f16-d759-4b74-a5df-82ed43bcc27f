<!doctype html>
<html>

<head>
    <title>Chores Roster</title>
    <meta charset='utf-8' />
    <style>
        .ͼ1.cm-focused {
            outline: 1px dotted #212121;
        }

        .ͼ1 {
            position: relative !important;
            box-sizing: border-box;
            display: flex !important;
            flex-direction: column;
        }

        .ͼ1 .cm-scroller {
            display: flex !important;
            align-items: flex-start !important;
            font-family: monospace;
            line-height: 1.4;
            height: 100%;
            overflow-x: auto;
            position: relative;
            z-index: 0;
            overflow-anchor: none;
        }

        .ͼ1 .cm-content[contenteditable=true] {
            -webkit-user-modify: read-write-plaintext-only;
        }

        .ͼ1 .cm-content {
            margin: 0;
            flex-grow: 2;
            flex-shrink: 0;
            display: block;
            white-space: pre;
            word-wrap: normal;
            box-sizing: border-box;
            min-height: 100%;
            padding: 4px 0;
            outline: none;
        }

        .ͼ1 .cm-lineWrapping {
            white-space: pre-wrap;
            white-space: break-spaces;
            word-break: break-word;
            overflow-wrap: anywhere;
            flex-shrink: 1;
        }

        .ͼ2 .cm-content {
            caret-color: black;
        }

        .ͼ3 .cm-content {
            caret-color: white;
        }

        .ͼ1 .cm-line {
            display: block;
            padding: 0 2px 0 6px;
        }

        .ͼ1 .cm-layer>* {
            position: absolute;
        }

        .ͼ1 .cm-layer {
            position: absolute;
            left: 0;
            top: 0;
            contain: size style;
        }

        .ͼ2 .cm-selectionBackground {
            background: #d9d9d9;
        }

        .ͼ3 .cm-selectionBackground {
            background: #222;
        }

        .ͼ2.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground {
            background: #d7d4f0;
        }

        .ͼ3.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground {
            background: #233;
        }

        .ͼ1 .cm-cursorLayer {
            pointer-events: none;
        }

        .ͼ1.cm-focused>.cm-scroller>.cm-cursorLayer {
            animation: steps(1) cm-blink 1.2s infinite;
        }

        @keyframes cm-blink {
            50% {
                opacity: 0;
            }
        }

        @keyframes cm-blink2 {
            50% {
                opacity: 0;
            }
        }

        .ͼ1 .cm-cursor,
        .ͼ1 .cm-dropCursor {
            border-left: 1.2px solid black;
            margin-left: -0.6px;
            pointer-events: none;
        }

        .ͼ1 .cm-cursor {
            display: none;
        }

        .ͼ3 .cm-cursor {
            border-left-color: #ddd;
        }

        .ͼ1 .cm-dropCursor {
            position: absolute;
        }

        .ͼ1.cm-focused>.cm-scroller>.cm-cursorLayer .cm-cursor {
            display: block;
        }

        .ͼ1 .cm-iso {
            unicode-bidi: isolate;
        }

        .ͼ1 .cm-announced {
            position: fixed;
            top: -10000px;
        }

        @media print {
            .ͼ1 .cm-announced {
                display: none;
            }
        }

        .ͼ2 .cm-activeLine {
            background-color: #cceeff44;
        }

        .ͼ3 .cm-activeLine {
            background-color: #99eeff33;
        }

        .ͼ2 .cm-specialChar {
            color: red;
        }

        .ͼ3 .cm-specialChar {
            color: #f78;
        }

        .ͼ1 .cm-gutters {
            flex-shrink: 0;
            display: flex;
            height: 100%;
            box-sizing: border-box;
            inset-inline-start: 0;
            z-index: 200;
        }

        .ͼ2 .cm-gutters {
            background-color: #f5f5f5;
            color: #6c6c6c;
            border-right: 1px solid #ddd;
        }

        .ͼ3 .cm-gutters {
            background-color: #333338;
            color: #ccc;
        }

        .ͼ1 .cm-gutter {
            display: flex !important;
            flex-direction: column;
            flex-shrink: 0;
            box-sizing: border-box;
            min-height: 100%;
            overflow: hidden;
        }

        .ͼ1 .cm-gutterElement {
            box-sizing: border-box;
        }

        .ͼ1 .cm-lineNumbers .cm-gutterElement {
            padding: 0 3px 0 5px;
            min-width: 20px;
            text-align: right;
            white-space: nowrap;
        }

        .ͼ2 .cm-activeLineGutter {
            background-color: #e2f2ff;
        }

        .ͼ3 .cm-activeLineGutter {
            background-color: #222227;
        }

        .ͼ1 .cm-panels {
            box-sizing: border-box;
            position: sticky;
            left: 0;
            right: 0;
            z-index: 300;
        }

        .ͼ2 .cm-panels {
            background-color: #f5f5f5;
            color: black;
        }

        .ͼ2 .cm-panels-top {
            border-bottom: 1px solid #ddd;
        }

        .ͼ2 .cm-panels-bottom {
            border-top: 1px solid #ddd;
        }

        .ͼ3 .cm-panels {
            background-color: #333338;
            color: white;
        }

        .ͼ1 .cm-tab {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
        }

        .ͼ1 .cm-widgetBuffer {
            vertical-align: text-top;
            height: 1em;
            width: 0;
            display: inline;
        }

        .ͼ1 .cm-placeholder {
            color: #888;
            display: inline-block;
            vertical-align: top;
        }

        .ͼ1 .cm-highlightSpace {
            background-image: radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%);
            background-position: center;
        }

        .ͼ1 .cm-highlightTab {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>');
            background-size: auto 100%;
            background-position: right 90%;
            background-repeat: no-repeat;
        }

        .ͼ1 .cm-trailingSpace {
            background-color: #ff332255;
        }

        .ͼ1 .cm-button {
            vertical-align: middle;
            color: inherit;
            font-size: 70%;
            padding: .2em 1em;
            border-radius: 1px;
        }

        .ͼ2 .cm-button:active {
            background-image: linear-gradient(#b4b4b4, #d0d3d6);
        }

        .ͼ2 .cm-button {
            background-image: linear-gradient(#eff1f5, #d9d9df);
            border: 1px solid #888;
        }

        .ͼ3 .cm-button:active {
            background-image: linear-gradient(#111, #333);
        }

        .ͼ3 .cm-button {
            background-image: linear-gradient(#393939, #111);
            border: 1px solid #888;
        }

        .ͼ1 .cm-textfield {
            vertical-align: middle;
            color: inherit;
            font-size: 70%;
            border: 1px solid silver;
            padding: .2em .5em;
        }

        .ͼ2 .cm-textfield {
            background-color: white;
        }

        .ͼ3 .cm-textfield {
            border: 1px solid #555;
            background-color: inherit;
        }

        .ͼ1 .cm-foldPlaceholder {
            background-color: #eee;
            border: 1px solid #ddd;
            color: #888;
            border-radius: .2em;
            margin: 0 1px;
            padding: 0 1px;
            cursor: pointer;
        }

        .ͼ1 .cm-foldGutter span {
            padding: 0 1px;
            cursor: pointer;
        }

        @keyframes loading {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .workspace-leaf-content[data-type="git-view"] .button-border {
            border: 2px solid var(--interactive-accent);
            border-radius: var(--radius-s);
        }

        .workspace-leaf-content[data-type="git-view"] .view-content {
            padding: 0;
        }

        .workspace-leaf-content[data-type="git-history-view"] .view-content {
            padding: 0;
        }

        .loading>svg {
            animation: 2s linear infinite loading;
            transform-origin: 50% 50%;
            display: inline-block;
        }

        .obsidian-git-center {
            margin: auto;
            text-align: center;
            width: 50%;
        }

        .obsidian-git-textarea {
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .obsidian-git-disabled {
            opacity: 0.5;
        }

        .obsidian-git-center-button {
            display: block;
            margin: 20px auto;
        }

        .tooltip.mod-left {
            overflow-wrap: break-word;
        }

        .tooltip.mod-right {
            overflow-wrap: break-word;
        }

        .git-tools {
            display: flex;
            margin-left: auto;
        }

        .git-tools .type {
            padding-left: var(--size-2-1);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 11px;
        }

        .git-tools .type[data-type="M"] {
            color: orange;
        }

        .git-tools .type[data-type="D"] {
            color: red;
        }

        .git-tools .buttons {
            display: flex;
        }

        .git-tools .buttons>* {
            padding: 0 0;
            height: auto;
        }

        .is-active .git-tools .buttons>* {
            color: var(--nav-item-color-active);
        }

        .git-author {
            color: var(--text-accent);
        }

        .git-date {
            color: var(--text-accent);
        }

        .git-ref {
            color: var(--text-accent);
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-d-none {
            display: none;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-wrapper {
            text-align: left;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-header {
            background-color: var(--background-primary);
            border-bottom: 1px solid var(--interactive-accent);
            font-family: var(--font-monospace);
            height: 35px;
            padding: 5px 10px;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-header,
        .workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
            font-size: 14px;
            margin-left: auto;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-lines-added {
            border: 1px solid #b4e2b4;
            border-radius: 5px 0 0 5px;
            color: #399839;
            padding: 2px;
            text-align: right;
            vertical-align: middle;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-lines-deleted {
            border: 1px solid #e9aeae;
            border-radius: 0 5px 5px 0;
            color: #c33;
            margin-left: 1px;
            padding: 2px;
            text-align: left;
            vertical-align: middle;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-name-wrapper {
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            font-size: 15px;
            width: 100%;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-name {
            overflow-x: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-wrapper {
            border: 1px solid var(--background-modifier-border);
            border-radius: 3px;
            margin-bottom: 1em;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse {
            -webkit-box-pack: end;
            -ms-flex-pack: end;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            border: 1px solid var(--background-modifier-border);
            border-radius: 3px;
            cursor: pointer;
            display: none;
            font-size: 12px;
            justify-content: flex-end;
            padding: 4px 8px;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse.d2h-selected {
            background-color: #c8e1ff;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse-input {
            margin: 0 4px 0 0;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-diff-table {
            border-collapse: collapse;
            font-family: Menlo, Consolas, monospace;
            font-size: 13px;
            width: 100%;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-files-diff {
            width: 100%;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-diff {
            overflow-y: hidden;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-side-diff {
            display: inline-block;
            margin-bottom: -8px;
            margin-right: -4px;
            overflow-x: scroll;
            overflow-y: hidden;
            width: 50%;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-line {
            padding: 0 8em;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-line,
        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
            display: inline-block;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            white-space: nowrap;
            width: 100%;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
            padding: 0 4.5em;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-line-ctn {
            word-wrap: normal;
            background: none;
            display: inline-block;
            padding: 0;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
            vertical-align: middle;
            white-space: pre;
            width: 100%;
        }

        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del {
            background-color: #ffb6ba;
        }

        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del {
            background-color: #8d232881;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
        .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del,
        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
            border-radius: 0.2em;
            display: inline-block;
            margin-top: -1px;
            text-decoration: none;
            vertical-align: middle;
        }

        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
            background-color: #97f295;
            text-align: left;
        }

        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
            background-color: #1d921996;
            text-align: left;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix {
            word-wrap: normal;
            background: none;
            display: inline;
            padding: 0;
            white-space: pre;
        }

        .workspace-leaf-content[data-type="diff-view"] .line-num1 {
            float: left;
        }

        .workspace-leaf-content[data-type="diff-view"] .line-num1,
        .workspace-leaf-content[data-type="diff-view"] .line-num2 {
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            overflow: hidden;
            padding: 0 0.5em;
            text-overflow: ellipsis;
            width: 3.5em;
        }

        .workspace-leaf-content[data-type="diff-view"] .line-num2 {
            float: right;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber {
            background-color: var(--background-primary);
            border: solid var(--background-modifier-border);
            border-width: 0 1px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            color: var(--text-muted);
            cursor: pointer;
            display: inline-block;
            position: absolute;
            text-align: right;
            width: 7.5em;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber:after {
            content: "\200b";
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
            background-color: var(--background-primary);
            border: solid var(--background-modifier-border);
            border-width: 0 1px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            color: var(--text-muted);
            cursor: pointer;
            display: inline-block;
            overflow: hidden;
            padding: 0 0.5em;
            position: absolute;
            text-align: right;
            text-overflow: ellipsis;
            width: 4em;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-diff-tbody tr {
            position: relative;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber:after {
            content: "\200b";
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-emptyplaceholder,
        .workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
            background-color: var(--background-primary);
            border-color: var(--background-modifier-border);
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix,
        .workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber,
        .workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
        .workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
            direction: rtl;
        }

        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-del {
            background-color: #fee8e9;
            border-color: #e9aeae;
        }

        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
            background-color: #dfd;
            border-color: #b4e2b4;
        }

        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-del {
            background-color: #521b1d83;
            border-color: #691d1d73;
        }

        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
            background-color: rgba(30, 71, 30, 0.5);
            border-color: #13501381;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-info {
            background-color: var(--background-primary);
            border-color: var(--background-modifier-border);
            color: var(--text-normal);
        }

        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-file-diff .d2h-del.d2h-change {
            background-color: #fdf2d0;
        }

        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-file-diff .d2h-del.d2h-change {
            background-color: #55492480;
        }

        .theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-file-diff .d2h-ins.d2h-change {
            background-color: #ded;
        }

        .theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-file-diff .d2h-ins.d2h-change {
            background-color: rgba(37, 78, 37, 0.418);
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper {
            margin-bottom: 10px;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper a {
            color: #3572b0;
            text-decoration: none;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper a:visited {
            color: #3572b0;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list-header {
            text-align: left;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list-title {
            font-weight: 700;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list-line {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            text-align: left;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list {
            display: block;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list>li {
            border-bottom: 1px solid var(--background-modifier-border);
            margin: 0;
            padding: 5px 10px;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-list>li:last-child {
            border-bottom: none;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-file-switch {
            cursor: pointer;
            display: none;
            font-size: 10px;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-icon {
            fill: currentColor;
            margin-right: 10px;
            vertical-align: middle;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-deleted {
            color: #c33;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-added {
            color: #399839;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-changed {
            color: #d0b44c;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-moved {
            color: #3572b0;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-tag {
            background-color: var(--background-primary);
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            font-size: 10px;
            margin-left: 5px;
            padding: 0 2px;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-deleted-tag {
            border: 2px solid #c33;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-added-tag {
            border: 1px solid #399839;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-changed-tag {
            border: 1px solid #d0b44c;
        }

        .workspace-leaf-content[data-type="diff-view"] .d2h-moved-tag {
            border: 1px solid #3572b0;
        }

        /* ====================== Line Authoring Information ====================== */

        .cm-gutterElement.obs-git-blame-gutter {
            /* Add background color to spacing inbetween and around the gutter for better aesthetics */
            border-width: 0px 2px 0.2px 2px;
            border-style: solid;
            border-color: var(--background-secondary);
            background-color: var(--background-secondary);
        }

        .cm-gutterElement.obs-git-blame-gutter>div,
        .line-author-settings-preview {
            /* delegate text color to settings */
            color: var(--obs-git-gutter-text);
            font-family: monospace;
            height: 100%;
            /* ensure, that age-based background color occupies entire parent */
            text-align: right;
            padding: 0px 6px 0px 6px;
            white-space: pre;
            /* Keep spaces and do not collapse them. */
        }

        @media (max-width: 800px) {

            /* hide git blame gutter not to superpose text */
            .cm-gutterElement.obs-git-blame-gutter {
                display: none;
            }
        }

        .git-unified-diff-view,
        .git-split-diff-view .cm-deletedLine .cm-changedText {
            background-color: #ee443330;
        }

        .git-unified-diff-view,
        .git-split-diff-view .cm-insertedLine .cm-changedText {
            background-color: #22bb2230;
        }

        .templater_search {
            width: calc(100% - 20px);
        }

        .templater_div {
            border-top: 1px solid var(--background-modifier-border);
        }

        .templater_div>.setting-item {
            border-top: none !important;
            align-self: center;
        }

        .templater_div>.setting-item>.setting-item-control {
            justify-content: space-around;
            padding: 0;
            width: 100%;
        }

        .templater_div>.setting-item>.setting-item-control>.setting-editor-extra-setting-button {
            align-self: center;
        }

        .templater_donating {
            margin: 10px;
        }

        .templater_title {
            margin: 0;
            padding: 0;
            margin-top: 5px;
            text-align: center;
        }

        .templater_template {
            align-self: center;
            margin-left: 5px;
            margin-right: 5px;
            width: 70%;
        }

        .templater_cmd {
            margin-left: 5px;
            margin-right: 5px;
            font-size: 14px;
            width: 100%;
        }

        .templater_div2>.setting-item {
            align-content: center;
            justify-content: center;
        }

        .templater-prompt-div {
            display: flex;
        }

        .templater-prompt-form {
            display: flex;
            flex-grow: 1;
        }

        .templater-prompt-input {
            flex-grow: 1;
        }

        .templater-button-div {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 1rem;
        }

        textarea.templater-prompt-input {
            height: 10rem;
        }

        textarea.templater-prompt-input:focus {
            border-color: var(--interactive-accent);
        }

        .cm-s-obsidian .templater-command-bg {
            left: 0px;
            right: 0px;
            background-color: var(--background-primary-alt);
        }

        .cm-s-obsidian .cm-templater-command {
            font-size: 0.85em;
            font-family: var(--font-monospace);
            line-height: 1.3;
        }

        .cm-s-obsidian .templater-inline .cm-templater-command {
            background-color: var(--background-primary-alt);
        }

        .cm-s-obsidian .cm-templater-command.cm-templater-opening-tag {
            font-weight: bold;
        }

        .cm-s-obsidian .cm-templater-command.cm-templater-closing-tag {
            font-weight: bold;
        }

        .cm-s-obsidian .cm-templater-command.cm-templater-interpolation-tag {
            color: var(--code-property, #008bff);
        }

        .cm-s-obsidian .cm-templater-command.cm-templater-execution-tag {
            color: var(--code-function, #c0d700);
        }

        .cm-s-obsidian .cm-templater-command.cm-keyword {
            color: var(--code-keyword, #00a7aa);
            font-weight: normal;
        }

        .cm-s-obsidian .cm-templater-command.cm-atom {
            color: var(--code-normal, #f39b35);
        }

        .cm-s-obsidian .cm-templater-command.cm-value,
        .cm-s-obsidian .cm-templater-command.cm-number,
        .cm-s-obsidian .cm-templater-command.cm-type {
            color: var(--code-value, #a06fca);
        }

        .cm-s-obsidian .cm-templater-command.cm-def,
        .cm-s-obsidian .cm-templater-command.cm-type.cm-def {
            color: var(--code-normal, var(--text-normal));
        }

        .cm-s-obsidian .cm-templater-command.cm-property,
        .cm-s-obsidian .cm-templater-command.cm-property.cm-def,
        .cm-s-obsidian .cm-templater-command.cm-attribute {
            color: var(--code-function, #98e342);
        }

        .cm-s-obsidian .cm-templater-command.cm-variable,
        .cm-s-obsidian .cm-templater-command.cm-variable-2,
        .cm-s-obsidian .cm-templater-command.cm-variable-3,
        .cm-s-obsidian .cm-templater-command.cm-meta {
            color: var(--code-property, #d4d4d4);
        }

        .cm-s-obsidian .cm-templater-command.cm-callee,
        .cm-s-obsidian .cm-templater-command.cm-operator,
        .cm-s-obsidian .cm-templater-command.cm-qualifier,
        .cm-s-obsidian .cm-templater-command.cm-builtin {
            color: var(--code-operator, #fc4384);
        }

        .cm-s-obsidian .cm-templater-command.cm-tag {
            color: var(--code-tag, #fc4384);
        }

        .cm-s-obsidian .cm-templater-command.cm-comment,
        .cm-s-obsidian .cm-templater-command.cm-comment.cm-tag,
        .cm-s-obsidian .cm-templater-command.cm-comment.cm-attribute {
            color: var(--code-comment, #696d70);
        }

        .cm-s-obsidian .cm-templater-command.cm-string,
        .cm-s-obsidian .cm-templater-command.cm-string-2 {
            color: var(--code-string, #e6db74);
        }

        .cm-s-obsidian .cm-templater-command.cm-header,
        .cm-s-obsidian .cm-templater-command.cm-hr {
            color: var(--code-keyword, #da7dae);
        }

        .cm-s-obsidian .cm-templater-command.cm-link {
            color: var(--code-normal, #696d70);
        }

        .cm-s-obsidian .cm-templater-command.cm-error {
            border-bottom: 1px solid #c42412;
        }

        .CodeMirror-hints {
            position: absolute;
            z-index: 10;
            overflow: hidden;
            list-style: none;

            margin: 0;
            padding: 2px;

            -webkit-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
            -moz-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
            box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
            border-radius: 3px;
            border: 1px solid silver;

            background: white;
            font-size: 90%;
            font-family: monospace;

            max-height: 20em;
            overflow-y: auto;
        }

        .CodeMirror-hint {
            margin: 0;
            padding: 0 4px;
            border-radius: 2px;
            white-space: pre;
            color: black;
            cursor: pointer;
        }

        li.CodeMirror-hint-active {
            background: #08f;
            color: white;
        }

        .text-snippets-cursor>*>textarea,
        .text-snippets-tabstops>*>textarea,
        .text-snippets-newline>*>textarea,
        .text-snippets-delimiter>*>textarea {
            height: 30px;
            padding-top: 2px;
            padding-bottom: 0;
        }

        .text-snippets-class>*>textarea {
            width: 40em;
            height: 100%;
        }

        .text-snippets-class>div>div {}

        .text-snippets-class>* {
            height: 100%;
            width: 60em;
        }

        .text-snippets-class {
            height: 70%;
            background-color:
        }

        .zt-format {
            border: 1px solid var(--background-modifier-border);
            padding: 1rem;
            background-color: var(--background-primary);
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .zt-format__form {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            margin-bottom: 1rem;
            max-width: 600px;
        }

        .zt-format__form:last-child {
            margin-bottom: 0;
        }

        .zt-format__label {
            font-size: 0.9em;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .is-deprecated .zt-format__label {
            color: var(--text-error);
        }

        .zt-format__input-wrapper {
            display: flex;
            align-items: center;
        }

        .zt-format__input-wrapper textarea {
            resize: vertical;
        }

        .zt-format__input-wrapper>*:not(.checkbox-container) {
            width: 100% !important;
        }

        .is-deprecated .zt-format__input-wrapper button {
            width: auto !important;
            flex-grow: 0;
            flex-shrink: 0;
            margin-left: 5px;
        }

        .zt-format__delete-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            padding: 7px 9px;
            margin-left: 10px;
            flex-shrink: 0;
            flex-grow: 0;
        }

        .zt-json-viewer {
            font-size: 13px;
        }

        .zt-json-viewer .react-json-view {
            padding: 1em;
            border-radius: 10px;
            margin-top: 1em;
            overflow: auto;
            font-family: var(--font-monospace) !important;
        }

        .zt-json-viewer__btns {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .zt-json-viewer__btns label {
            display: block;
            font-weight: bold;
            padding-top: 1em;
        }

        .zt-json-viewer__btns select {
            font-size: 1em;
        }

        .zt-json-viewer__btns button {
            font-size: 1em;
            margin-right: 5px;
        }

        .zt-json-viewer__preview,
        .zt-json-viewer__data {
            border: 1px solid var(--background-modifier-border);
            border-radius: 10px;
            padding: 1em;
            margin-top: 1em;
        }

        .zt-json-viewer__preview.error {
            background-color: #ff000011;
            font-family: var(--font-monospace);
        }

        .zt-json-viewer__preview pre {
            overflow: auto;
            white-space: pre-wrap;
            margin: 0;
        }

        .zt-json-viewer__preview pre,
        .zt-json-viewer__preview code {
            font-family: inherit;
        }

        .zt-json-viewer__preview:not(.error) pre {
            font-family: var(--font-text, --font-default, --default-font);
            max-height: 70vh;
            min-height: 400px;
        }

        .zt-multiselect {
            width: 300px;
            text-align: left;
        }

        .zt-multiselect input {
            outline: none !important;
            box-shadow: none !important;
        }

        .zt-format__input-note {
            font-style: italic;
            font-size: 0.9em;
            padding-top: 10px;
            margin-bottom: 10px;
        }

        .zt-setting-item pre,
        .zt-format__input-note pre {
            display: inline-block;
            margin: 0;
            padding: 0 6px;
            background-color: var(--background-secondary-alt);
            border-radius: 4px;
        }

        .zt-asset-success {
            text-align: left;
            display: flex;
        }

        .zt-asset-success__icon {
            color: var(--interactive-success);
            font-size: 24px;
            margin-right: 5px;
        }

        .zt-asset-success__icon svg {
            width: 1em !important;
            height: 1em !important;
        }

        .zt-asset-success__message {
            font-size: 0.9em;
        }

        .zt-suggest-title {
            font-size: var(--font-ui-small);
            color: var(--text-muted);
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-top: var(--size-4-1);
        }

        .zt-suggest-loading-wrapper {
            display: flex;
            position: relative;
            align-items: center;
            justify-content: center;
            padding: var(--size-4-2) 0;
        }

        .zt-suggest-loading,
        .zt-suggest-loading:before,
        .zt-suggest-loading:after {
            border-radius: 999px;
            width: 1em;
            height: 1em;
            animation-fill-mode: both;
            animation: bblFadInOut 1.6s infinite ease-in-out;
        }

        .zt-suggest-loading {
            display: block;
            color: var(--text-muted);
            font-size: 7px;
            position: relative;
            animation-delay: -0.16s;
            top: -1em;
        }

        .zt-suggest-loading:before,
        .zt-suggest-loading:after {
            content: '';
            position: absolute;
        }

        .zt-suggest-loading:before {
            left: -2em;
            animation-delay: -0.32s;
        }

        .zt-suggest-loading:after {
            left: 2em;
        }

        .zt-color-chip {
            display: inline-block;
            width: 1em;
            height: 1em;
            border: 1px solid var(--background-modifier-border);
            border-radius: var(--radius-s);
            margin-right: var(--size-4-1);
        }

        @keyframes bblFadInOut {

            0%,
            80%,
            100% {
                box-shadow: 0 1em 0 -1.3em;
            }

            40% {
                box-shadow: 0 1em 0 0;
            }
        }

        div.neovis_setting {
            width: content-box;
        }

        div.cxtmenu-item {
            opacity: 0.8;
        }

        /*.cytoscape-navigatorView,*/
        div.cy-navigator {
            width: 150px;
            height: 150px;
            position: fixed;
            z-index: 3;
            bottom: 10px;
            right: 27px;
            border: #828282 1px solid;
            border-radius: 2px;
            background-color: rgba(130, 130, 130, 0.1);
            cursor: default;
            overflow: hidden;
        }

        div.juggl-error {
            background-color: red;
        }


        .cy-navigator>img {
            position: relative;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.8;

        }

        .cytoscape-navigatorView {
            position: relative;
            top: 0;
            left: 0;
            cursor: move;
            background: #828282;
            -moz-opacity: 0.20;
            opacity: 0.20;
            width: 50%;
            height: 50%;
            z-index: 0;
        }

        .cytoscape-navigatorOverlay {
            position: relative;
            top: 0;
            left: 0;
            z-index: 103;
            width: 100%;
            height: 100%;
        }

        .juggl-hover.is-loaded.hover-popover.popover {
            opacity: 0.9;
            height: max-content;
        }

        .juggl-preview-edge {
            height: 140px !important;
        }

        .cy-content {
            padding: 0 !important;
        }

        .cy-toolbar {
            position: relative;
            left: 8px;
            top: 8px;
            width: fit-content;
            max-width: inherit;
            height: 0;
            margin: 2px;
            margin-block-start: 0;
            margin-block-end: 0;
            z-index: 1000;
            background-color: rgba(0, 0, 0, 0);
        }

        .cy-toolbar-section {
            width: fit-content;
            text-align: center;
            background-color: var(--background-primary);
            display: inline-block;
            margin: 1px;
            padding: 1px;
            border-color: var(--background-modifier-border);
            border-radius: 4px;
            border-width: 1px;
            border-style: solid;
            opacity: 1;
        }

        .cy-toolbar>input[type='text'] {
            font-size: var(--font-small);
            background-color: var(--background-secondary);
            height: 30px;
            padding: 5px 7px;
        }


        button.juggl-button {
            width: 27px;
            height: 27px;
            text-align: center;
            background-color: var(--background-secondary);
            padding: 0 !important;
            margin: 1px;
        }


        .cy-toolbar-section>button:disabled {
            background: var(--background-primary);
            cursor: not-allowed;
        }

        button.juggl-button>svg>path {
            fill: var(--text-muted);
            opacity: 0.6;
        }

        button.juggl-button:hover>svg>path {
            opacity: 1;
        }

        button.juggl-button:disabled>svg>path {
            opacity: 0.3;
        }

        button.juggl-button-pane {
            background: none;
            margin: 0;
            padding: 0;
            width: 15px;
            height: 15px;
        }


        div.juggl-list-text {
            font-size: var(--font-small);
            word-break: break-word;
        }

        div.juggl-style-group {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            padding-bottom: 6px;
            padding-top: 6px;
            border-bottom: 1px solid var(--background-modifier-border-focus);
        }

        div.juggl-style-group-hidden {
            width: auto;
            display: inline;
        }

        div.juggl-nodes-pane,
        div.juggl-style-pane {
            overflow-y: auto;
            padding: 0 10px;
            font-size: 14px;
        }

        .break {
            flex-basis: 100%;
            height: 3px;
        }

        /* Use a collapsed column to break to a new column */
        .break-column {
            flex-basis: 100%;
            width: 0;
        }

        .react-icon {
            display: inline-flex;
            width: 24px;
            box-sizing: content-box;
            height: 24px;
            stroke-width: 0;
        }

        .react-icon>svg {
            vertical-align: top;
            line-height: 0;
            font-size: 0;
            margin-bottom: 3px;
        }

        .juggl-icon-picker>.suggestion-item {
            height: auto;
            min-height: 24px;
        }

        button.juggl-icon-button {
            height: 30px;
        }

        .juggl-style-pane-left {
            display: inline-flex;
            width: 50px;
            justify-content: center;
        }

        .juggl-inline-group {
            max-width: 9rem;
            display: flex;
            align-items: center;
            /*padding: .5rem;*/
        }

        .juggl-inline-group .form-control {
            text-align: right;
        }

        .form-control[type="number"]::-webkit-inner-spin-button,
        .form-control[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        body {
            --todoist-berry-red: #b8256f;
            --todoist-red: #db4035;
            --todoist-orange: #ff9933;
            --todoist-yellow: #fad000;
            --todoist-olive-green: #afb83b;
            --todoist-lime-green: #7ecc49;
            --todoist-green: #299438;
            --todoist-mint-green: #6accbc;
            --todoist-teal: #158fad;
            --todoist-sky-blue: #14aaf5;
            --todoist-light-blue: #96c3eb;
            --todoist-blue: #4073ff;
            --todoist-grape: #884dff;
            --todoist-violet: #af38eb;
            --todoist-lavender: #eb96eb;
            --todoist-magenta: #e05194;
            --todoist-salmon: #ff8d85;
            --todoist-charcoal: #808080;
            --todoist-grey: #b8b8b8;
            --todoist-taupe: #ccac93
        }

        .theme-dark {
            --todoist-p1-border: #ff7066;
            --todoist-p1-border-hover: #ff706680;
            --todoist-p1-background: rgba(255, 112, 102, .1);
            --todoist-p2-border: #ff9a14;
            --todoist-p2-border-hover: #ff9a1480;
            --todoist-p2-background: rgba(255, 154, 20, .1);
            --todoist-p3-border: #5297ff;
            --todoist-p3-border-hover: #5297ff80;
            --todoist-p3-background: rgba(82, 151, 255, .1);
            --todoist-p4-border: var(--color-base-50);
            --todoist-p4-border-hover: var(--color-base-50);
            --todoist-p4-background: unset;
            --todoist-task-separator-color: var(--color-base-30)
        }

        .theme-light {
            --todoist-p1-border: #d1453b;
            --todoist-p1-border-hover: #d1453b80;
            --todoist-p1-background: rgba(209, 69, 59, .1);
            --todoist-p2-border: #eb8909;
            --todoist-p2-border-hover: #eb890980;
            --todoist-p2-background: rgba(235, 137, 9, .1);
            --todoist-p3-border: #246fe0;
            --todoist-p3-border-hover: #246fe080;
            --todoist-p3-background: rgba(36, 111, 224, .1);
            --todoist-p4-border: var(--color-base-50);
            --todoist-p4-border-hover: var(--color-base-50);
            --todoist-p4-background: unset;
            --todoist-task-separator-color: var(--color-base-25)
        }

        .obsidian-icon {
            display: flex;
            align-items: center
        }

        .obsidian-icon[data-icon-size=xs] {
            --icon-size: var(--icon-xs);
            --icon-stroke: var(--icon-xs-stroke-width)
        }

        .obsidian-icon[data-icon-size=s] {
            --icon-size: var(--icon-s);
            --icon-stroke: var(--icon-s-stroke-width)
        }

        .obsidian-icon[data-icon-size=m] {
            --icon-size: var(--icon-m);
            --icon-stroke: var(--icon-m-stroke-width)
        }

        .obsidian-icon[data-icon-size=l] {
            --icon-size: var(--icon-l);
            --icon-stroke: var(--icon-l-stroke-width)
        }

        .obsidian-icon[data-icon-size=xl] {
            --icon-size: var(--icon-xl);
            --icon-stroke: var(--icon-xl-stroke-width)
        }

        .todoist-callout {
            margin-top: 1em;
            padding: 16px;
            background-color: var(--todoist-callout-color);
            border-radius: 4px
        }

        .todoist-callout .callout-header {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-weight: 600
        }

        .todoist-callout .callout-header .obsidian-icon {
            margin-right: .5em
        }

        .todoist-callout ul {
            margin-block-start: 0em;
            margin-block-end: 0em
        }

        .todoist-query-header {
            display: flex;
            align-items: center;
            justify-content: space-between
        }

        .todoist-query-header .todoist-query-title {
            font-size: 1.25em
        }

        .todoist-query-header .todoist-query-controls {
            display: flex;
            align-items: center;
            justify-content: end
        }

        .todoist-query-header .todoist-query-controls *+* {
            margin-left: .5em
        }

        .todoist-query-header .todoist-query-controls .todoist-query-control-button {
            padding: var(--size-2-2) var(--size-2-3);
            color: var(--text-muted);
            border-radius: var(--radius-s);
            border: 1px solid var(--color-base-40);
            box-shadow: none;
            transition: opacity .33s;
            opacity: 0
        }

        .block-language-todoist:hover .todoist-query-header .todoist-query-controls .todoist-query-control-button {
            opacity: 1
        }

        .todoist-query-header .todoist-query-controls .todoist-query-control-button:hover {
            background-color: inherit;
            border: 1px solid var(--interactive-accent)
        }

        .markdown-reading-view .todoist-query-header .todoist-query-controls .todoist-query-control-button.edit-query {
            display: none
        }

        .todoist-query-header .todoist-query-controls .todoist-query-control-button.refresh-query.is-refreshing>.obsidian-icon {
            animation: spin 1s linear infinite reverse
        }

        @-webkit-keyframes spin {
            to {
                -webkit-transform: rotate(360deg)
            }
        }

        .todoist-query-warnings {
            --todoist-callout-color: rgba(var(--color-yellow-rgb), .2)
        }

        .todoist-tasks-list {
            margin-top: 1em
        }

        .todoist-tasks-list .todoist-tasks-list {
            margin-top: 0;
            margin-left: 2em
        }

        .todoist-tasks-list .todoist-tasks-list+.todoist-task-container {
            border-top: none
        }

        .todoist-tasks-list .todoist-tasks-list .todoist-task-container:first-child {
            border-top: none
        }

        .todoist-task-container {
            display: flex;
            padding: .5em 0;
            border-bottom: 1px solid var(--todoist-task-separator-color)
        }

        .todoist-task-container:first-child {
            border-top: 1px solid var(--todoist-task-separator-color)
        }

        .todoist-task-container[data-priority="1"] {
            --todoist-checkbox-border: var(--todoist-p4-border);
            --todoist-checkbox-border-hover: var(--todoist-p4-border-hover);
            --todoist-checkbox-background: var(--todoist-p4-background)
        }

        .todoist-task-container[data-priority="2"] {
            --todoist-checkbox-border: var(--todoist-p3-border);
            --todoist-checkbox-border-hover: var(--todoist-p3-border-hover);
            --todoist-checkbox-background: var(--todoist-p3-background)
        }

        .todoist-task-container[data-priority="3"] {
            --todoist-checkbox-border: var(--todoist-p2-border);
            --todoist-checkbox-border-hover: var(--todoist-p2-border-hover);
            --todoist-checkbox-background: var(--todoist-p2-background)
        }

        .todoist-task-container[data-priority="4"] {
            --todoist-checkbox-border: var(--todoist-p1-border);
            --todoist-checkbox-border-hover: var(--todoist-p1-border-hover);
            --todoist-checkbox-background: var(--todoist-p1-background)
        }

        .todoist-task-container .todoist-task-checkbox {
            margin-top: 3px
        }

        .todoist-task-container .todoist-task-checkbox div {
            height: 16px;
            width: 16px;
            border-radius: 50%;
            border: 1px solid var(--todoist-checkbox-border);
            background-color: var(--todoist-checkbox-background)
        }

        .todoist-task-container .todoist-task-checkbox div:hover {
            border: 1px solid var(--todoist-checkbox-border-hover)
        }

        .todoist-task-container .todoist-task {
            margin-left: .5em;
            width: 100%
        }

        .todoist-task-container .todoist-task .todoist-task-description {
            font-size: var(--font-small);
            color: var(--text-muted)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-smaller);
            margin-top: .25em;
            color: var(--text-muted)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata>div {
            display: flex
        }

        .todoist-task-container .todoist-task .todoist-task-metadata>div>*+* {
            margin-left: 1em
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item {
            display: flex;
            align-items: center
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item>*+* {
            margin-left: .25em
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-task-metadata-kind=project] .obsidian-icon {
            color: var(--todoist-project-color)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-task-metadata-kind=labels] {
            color: var(--todoist-label-color)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=berry-red] {
            --todoist-project-color: var(--todoist-berry-red)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=berry-red] {
            --todoist-label-color: var(--todoist-berry-red)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=red] {
            --todoist-project-color: var(--todoist-red)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=red] {
            --todoist-label-color: var(--todoist-red)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=orange] {
            --todoist-project-color: var(--todoist-orange)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=orange] {
            --todoist-label-color: var(--todoist-orange)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=yellow] {
            --todoist-project-color: var(--todoist-yellow)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=yellow] {
            --todoist-label-color: var(--todoist-yellow)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=olive-green] {
            --todoist-project-color: var(--todoist-olive-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=olive-green] {
            --todoist-label-color: var(--todoist-olive-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=lime-green] {
            --todoist-project-color: var(--todoist-lime-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=lime-green] {
            --todoist-label-color: var(--todoist-lime-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=green] {
            --todoist-project-color: var(--todoist-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=green] {
            --todoist-label-color: var(--todoist-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=mint-green] {
            --todoist-project-color: var(--todoist-mint-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=mint-green] {
            --todoist-label-color: var(--todoist-mint-green)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=teal] {
            --todoist-project-color: var(--todoist-teal)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=teal] {
            --todoist-label-color: var(--todoist-teal)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=sky-blue] {
            --todoist-project-color: var(--todoist-sky-blue)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=sky-blue] {
            --todoist-label-color: var(--todoist-sky-blue)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=light-blue] {
            --todoist-project-color: var(--todoist-light-blue)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=light-blue] {
            --todoist-label-color: var(--todoist-light-blue)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=blue] {
            --todoist-project-color: var(--todoist-blue)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=blue] {
            --todoist-label-color: var(--todoist-blue)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=grape] {
            --todoist-project-color: var(--todoist-grape)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=grape] {
            --todoist-label-color: var(--todoist-grape)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=violet] {
            --todoist-project-color: var(--todoist-violet)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=violet] {
            --todoist-label-color: var(--todoist-violet)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=lavender] {
            --todoist-project-color: var(--todoist-lavender)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=lavender] {
            --todoist-label-color: var(--todoist-lavender)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=magenta] {
            --todoist-project-color: var(--todoist-magenta)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=magenta] {
            --todoist-label-color: var(--todoist-magenta)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=salmon] {
            --todoist-project-color: var(--todoist-salmon)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=salmon] {
            --todoist-label-color: var(--todoist-salmon)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=charcoal] {
            --todoist-project-color: var(--todoist-charcoal)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=charcoal] {
            --todoist-label-color: var(--todoist-charcoal)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=grey] {
            --todoist-project-color: var(--todoist-grey)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=grey] {
            --todoist-label-color: var(--todoist-grey)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=taupe] {
            --todoist-project-color: var(--todoist-taupe)
        }

        .todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=taupe] {
            --todoist-label-color: var(--todoist-taupe)
        }

        .todoist-task-container[data-due-metadata=overdue] .task-metadata-item[data-task-metadata-kind=due] {
            color: var(--todoist-red)
        }

        .todoist-group+.todoist-group {
            margin-top: 2em
        }

        .todoist-group-title {
            margin: 1em 0;
            font-weight: 600
        }

        .todoist-no-tasks {
            --todoist-callout-color: rgba(var(--color-green-rgb), .2)
        }

        .todoist-query-error {
            --todoist-callout-color: rgba(var(--color-red-rgb), .2)
        }

        .modal-popover {
            overflow-y: auto
        }

        .task-creation-modal-root .task-content-input textarea {
            width: 100%;
            border: 0;
            background-color: unset;
            box-shadow: none;
            resize: none;
            padding: 0
        }

        .task-creation-modal-root .task-content-input textarea:focus,
        .task-creation-modal-root .task-content-input textarea:hover {
            border: 0;
            background-color: unset;
            box-shadow: none
        }

        .task-creation-modal-root .task-name textarea {
            font-size: var(--font-ui-large);
            font-weight: var(--font-semibold)
        }

        .task-creation-modal-root .task-description textarea {
            font-size: var(--font-ui-small)
        }

        .task-creation-modal-root .task-creation-selectors {
            margin-top: .5em;
            display: flex;
            align-items: center
        }

        .task-creation-modal-root .task-creation-selectors>*+* {
            margin-left: .5em
        }

        .task-creation-modal-root .task-creation-selectors button {
            box-shadow: none;
            background-color: unset;
            border: 1px solid var(--color-base-25);
            color: var(--text-muted)
        }

        .task-creation-modal-root .task-creation-selectors button .obsidian-icon {
            margin-right: .5em
        }

        .task-creation-modal-root .task-creation-selectors button:hover,
        .task-creation-modal-root .task-creation-selectors button:focus {
            border: 1px solid var(--interactive-accent);
            box-shadow: var(--box-shadow-hover)
        }

        .task-creation-modal-root .task-creation-notes ul {
            padding-inline-start: 16px;
            font-size: var(--font-smallest);
            color: var(--text-muted)
        }

        .task-creation-modal-root .task-creation-controls {
            display: flex;
            justify-content: space-between
        }

        .is-mobile .task-creation-modal-root .task-creation-controls {
            flex-direction: column
        }

        .is-mobile .task-creation-modal-root .task-creation-controls>*+* {
            margin-top: .5em
        }

        .task-creation-modal-root .task-creation-controls .task-creation-action {
            display: flex
        }

        .task-creation-modal-root .task-creation-controls .task-creation-action>*+* {
            margin-left: 1em
        }

        .is-mobile .task-creation-modal-root .task-creation-controls .task-creation-action {
            justify-content: end
        }

        .task-creation-modal-root hr {
            margin: 1em 0;
            border-color: var(--color-base-25)
        }

        .task-option-dialog {
            background-color: var(--modal-background);
            border: var(--modal-border-width) solid var(--modal-border-color);
            border-radius: 4px;
            min-width: 200px;
            box-shadow: var(--shadow-s);
            padding: .5em 0
        }

        .task-date-menu .react-aria-MenuItem[data-focused] {
            background-color: var(--background-modifier-cover)
        }

        .task-date-menu .date-suggestion-elem {
            padding: 8px 1em;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-smaller)
        }

        .task-date-menu .date-suggestion-elem .date-suggestion-label {
            display: flex;
            align-items: center;
            font-weight: var(--font-semibold)
        }

        .task-date-menu .date-suggestion-elem .date-suggestion-label .obsidian-icon {
            margin-right: 1em
        }

        .task-date-menu .date-suggestion-elem .date-suggestion-day {
            color: var(--text-faint)
        }

        .task-date-menu hr {
            width: 100%;
            border-color: var(--color-base-25);
            margin: 1em 0
        }

        .task-date-menu .date-picker {
            padding: 0 1em;
            font-size: var(--font-small)
        }

        .task-date-menu .date-picker header {
            display: flex;
            align-items: center;
            justify-content: space-between
        }

        .task-date-menu .date-picker h4 {
            margin-left: .5em;
            font-size: var(--font-small);
            font-weight: var(--font-semibold)
        }

        .task-date-menu .date-picker .date-picker-controls {
            display: flex;
            align-items: center;
            justify-content: right
        }

        .task-date-menu .date-picker .date-picker-controls button {
            box-shadow: none;
            background-color: unset;
            border: 1px solid rgba(0, 0, 0, 0);
            color: var(--text-muted)
        }

        .task-date-menu .date-picker .date-picker-controls button[data-disabled] {
            opacity: .5
        }

        .task-date-menu .date-picker .date-picker-controls button:hover:not([data-disabled]) {
            border: 1px solid var(--interactive-accent);
            box-shadow: var(--box-shadow-hover)
        }

        .task-date-menu .date-picker .react-aria-CalendarCell {
            text-align: center;
            padding: 6px;
            border-radius: 2px
        }

        .task-date-menu .date-picker .react-aria-CalendarCell:hover {
            background-color: var(--background-modifier-cover)
        }

        .task-date-menu .date-picker .react-aria-CalendarCell[data-outside-month] {
            display: none
        }

        .task-date-menu .date-picker .react-aria-CalendarCell[data-disabled] {
            color: var(--text-faint)
        }

        .task-date-menu .date-picker .react-aria-CalendarCell[data-selected] {
            background-color: var(--interactive-accent);
            color: var(--text-on-accent)
        }

        .task-date-menu .time-picker-container {
            display: flex;
            justify-content: center
        }

        .task-date-menu .time-picker-container .time-picker-button {
            flex-grow: 1;
            margin: 0 1em 1em;
            color: var(--text-muted);
            box-shadow: none;
            background-color: unset;
            border: 1px solid var(--color-base-25)
        }

        .task-date-menu .time-picker-container .time-picker-button:hover,
        .task-date-menu .time-picker-container .time-picker-button:focus {
            border: 1px solid var(--interactive-accent);
            box-shadow: var(--box-shadow-hover)
        }

        .task-date-menu .time-picker-container .time-picker-button .obsidian-icon {
            margin-right: .5em
        }

        .task-priority-menu .priority-option {
            padding: 8px 1em;
            font-size: var(--font-smaller)
        }

        .task-priority-menu .priority-option:hover {
            background-color: var(--background-modifier-cover)
        }

        .task-priority-menu .priority-option.is-selected {
            background-color: var(--interactive-accent);
            color: var(--text-on-accent)
        }

        .task-label-menu .label-option {
            padding: 8px 1em;
            font-size: var(--font-smaller);
            display: flex;
            align-items: center;
            justify-content: space-between
        }

        .task-label-menu .label-option:hover {
            background-color: var(--background-modifier-cover)
        }

        button.project-selector {
            box-shadow: none;
            background-color: unset;
            border: 1px solid var(--color-base-25);
            color: var(--text-muted);
            display: flex;
            align-items: center
        }

        button.project-selector>*+* {
            margin-left: .5em
        }

        button.project-selector:hover,
        button.project-selector:focus {
            border: 1px solid var(--interactive-accent);
            box-shadow: var(--box-shadow-hover)
        }

        .task-project-menu .search-filter-container {
            display: flex;
            align-items: center;
            justify-content: center
        }

        .task-project-menu .search-filter-container input {
            flex-grow: 1;
            margin: 0 4px
        }

        .task-project-menu .search-filter-container input:hover {
            box-shadow: none;
            border: 1px solid var(--interactive-accent)
        }

        .task-project-menu hr {
            margin: .5em 0;
            border-color: var(--color-base-10)
        }

        .task-project-menu .project-option {
            padding: 8px 1em;
            font-size: var(--font-smaller);
            display: flex;
            align-items: center;
            --project-padding: 8px;
            padding-left: var(--project-padding)
        }

        .task-project-menu .project-option[data-depth="1"] {
            --project-padding: 32px
        }

        .task-project-menu .project-option[data-depth="2"] {
            --project-padding: 56px
        }

        .task-project-menu .project-option[data-depth="3"] {
            --project-padding: 80px
        }

        .task-project-menu .project-option[data-depth="3"] {
            --project-padding: 104px
        }

        .task-project-menu .project-option>*+* {
            margin-left: .5em
        }

        .task-project-menu .project-option:hover {
            background-color: var(--background-modifier-cover)
        }

        .task-project-menu .project-option[data-filtered=true] {
            display: none
        }

        .task-time-menu {
            padding: 1em;
            display: flex;
            flex-direction: column
        }

        .task-time-menu>*+* {
            margin-top: 1em
        }

        .task-time-menu .task-time-picker {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: var(--font-ui-small)
        }

        .task-time-menu .task-time-picker .task-time-picker-label {
            font-weight: 600
        }

        .task-time-menu .task-time-picker .task-time-picker-input {
            display: flex;
            padding: 4px;
            border-radius: 6px;
            min-width: 100px;
            border: 1px solid var(--color-base-25)
        }

        .task-time-menu .task-time-picker .task-time-picker-input .task-time-picker-input-segment {
            padding: 0 2px
        }

        .task-time-menu .task-time-picker .task-time-picker-input .task-time-picker-input-segment:focus {
            background-color: var(--interactive-accent);
            border-radius: 4px;
            color: var(--text-on-accent)
        }

        .task-time-menu .task-time-controls {
            display: flex;
            justify-content: end
        }

        .task-time-menu .task-time-controls>*+* {
            margin-left: 1em
        }

        .obsidian-icon.token-validation-error {
            color: var(--text-error)
        }

        .obsidian-icon.token-validation-success {
            color: var(--text-success)
        }

        .obsidian-icon.token-validation-in-progress {
            color: var(--text-warning);
            animation-name: spin;
            animation-duration: .5s;
            animation-iteration-count: infinite;
            animation-timing-function: linear
        }

        @keyframes spin {
            0% {
                transform: rotate(0)
            }

            to {
                transform: rotate(360deg)
            }
        }

        .todoist-onboarding-token-form {
            margin: 2em 0
        }

        .todoist-onboarding-token-form .react-aria-TextField {
            display: flex;
            flex-direction: column
        }

        .todoist-onboarding-token-form .react-aria-TextField>*+* {
            margin-top: .5em
        }

        .todoist-onboarding-token-form .react-aria-TextField .react-aria-Group {
            display: flex;
            align-items: center
        }

        .todoist-onboarding-token-form .react-aria-TextField .react-aria-Group>*+* {
            margin-left: .5em
        }

        .todoist-onboarding-token-form .react-aria-TextField input {
            flex-grow: 1
        }

        .todoist-onboarding-token-form .react-aria-TextField input[data-invalid] {
            border: 1px solid var(--background-modifier-error)
        }

        .todoist-onboarding-token-form .react-aria-TextField .react-aria-Label {
            font-weight: 600
        }

        .todoist-onboarding-token-form .react-aria-Button {
            float: right;
            margin-top: 1em
        }

        .todoist-onboarding-token-form .react-aria-FieldError {
            color: var(--text-error);
            font-size: var(--font-small)
        }

        .mod-cta .setting-button-icon {
            margin-right: .5em
        }

        .setting-item-control .react-aria-TextField {
            display: flex;
            flex-direction: column
        }

        .setting-item-control .react-aria-TextField>*+* {
            margin-top: .5em
        }

        .setting-item-control .react-aria-TextField input[data-invalid] {
            border: 1px solid var(--background-modifier-error)
        }

        .setting-item-control .react-aria-TextField .react-aria-FieldError {
            color: var(--text-error);
            font-size: var(--font-small)
        }

        .setting-item-deprecation-notice {
            display: flex;
            background-color: rgba(var(--color-yellow-rgb), .2);
            padding: 4px 12px;
            border-radius: 8px;
            margin-top: .5em
        }

        .setting-item-deprecation-notice .setting-item-deprecation-notice-message {
            margin-left: .5em
        }

        .pandoc-plugin-error {
            color: red;
        }

        .modal.mod-importer {
            max-height: var(--modal-height);
            padding: var(--size-4-4) 0 0 0;
            position: relative;
            overflow: hidden;
        }

        .modal.mod-importer .modal-title {
            padding: 0 var(--size-4-4);
        }

        .modal.mod-importer .modal-content {
            overflow: auto;
            padding: var(--size-4-4);
            margin-bottom: calc(var(--input-height) + var(--size-4-8));
            border-top: var(--border-width) solid var(--background-modifier-border);
        }

        .modal.mod-importer .modal-button-container {
            margin: 0 0 0 calc(var(--size-4-4) * -1);
            padding: var(--size-4-4);
            gap: var(--size-4-2);
            position: absolute;
            bottom: 0;
            background-color: var(--background-primary);
            border-top: var(--border-width) solid var(--background-modifier-border);
            width: 100%;
        }

        .importer-progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--background-secondary);
            overflow: hidden;
            box-shadow: inset 0px 0px 0px 1px var(--background-modifier-border);
            border-radius: var(--radius-s);
        }

        .importer-progress-bar-inner {
            width: 0;
            height: 100%;
            background-color: var(--interactive-accent);
        }

        .importer-status {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: var(--size-4-2) 0;
        }

        .importer-stats-container {
            display: flex;
            justify-content: space-evenly;
            margin-top: var(--size-4-5);
            margin-bottom: var(--size-4-5);
        }

        .importer-stat {
            text-align: center;
        }

        .importer-stat-count {
            font-size: var(--font-ui-large);
        }

        .importer-log {
            overflow: auto;
            flex-grow: 1;
            font-family: var(--font-monospace);
            font-size: var(--font-ui-smaller);
            color: var(--text-muted);
            border: 1px solid var(--background-modifier-border);
            padding: var(--size-4-4);
            background-color: var(--background-secondary);
            border-radius: var(--radius-s);
            max-height: 300px;
            user-select: text;
        }

        .importer-log .list-item {
            display: inline-block;
            line-height: var(--line-height-normal);
            white-space: pre;
            margin: var(--size-2-1);
        }

        .importer-error {
            color: var(--text-error);
        }

        .omnisearch-modal {}

        .omnisearch-result {
            white-space: normal;
            display: flex;
            flex-direction: row;
            /* justify-content: space-between; */
            flex-wrap: nowrap;
        }

        .omnisearch-result__title-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            column-gap: 5px;
            flex-wrap: wrap;
        }

        .omnisearch-result__title {
            white-space: pre-wrap;
            align-items: center;
            display: flex;
            gap: 5px;
        }

        .omnisearch-result__title>span {}

        .omnisearch-result__folder-path {
            font-size: 0.75rem;
            align-items: center;
            display: flex;
            gap: 5px;
            color: var(--text-muted);
        }

        .omnisearch-result__extension {
            font-size: 0.7rem;
            color: var(--text-muted);
        }

        .omnisearch-result__counter {
            font-size: 0.7rem;
            color: var(--text-muted);
        }

        .omnisearch-result__body {
            white-space: normal;
            font-size: small;
            word-wrap: normal;

            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;

            color: var(--text-muted);
            margin-inline-start: 0.5em;
        }

        .omnisearch-result__embed {
            margin-left: 1em;
        }


        .omnisearch-result__image-container {
            flex-basis: 20%;
            text-align: end;
        }

        .omnisearch-highlight {}

        .omnisearch-default-highlight {
            text-decoration: underline;
            text-decoration-color: var(--text-highlight-bg);
            text-decoration-thickness: 3px;
            text-underline-offset: -1px;
            text-decoration-skip-ink: none;
        }

        .omnisearch-input-container {
            display: flex;
            align-items: center;
            flex-direction: row;
            gap: 5px;
        }

        .omnisearch-result__icon {
            display: inline-block;
            vertical-align: middle;
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }

        .omnisearch-result__icon svg {
            width: 100%;
            height: 100%;
        }

        .omnisearch-result__icon--emoji {
            font-size: 16px;
            vertical-align: middle;
            margin-right: 4px;
        }

        @media only screen and (max-width: 600px) {
            .omnisearch-input-container {
                flex-direction: column;
            }

            .omnisearch-input-container__buttons {
                display: flex;
                flex-direction: row;
                width: 100%;
                padding: 0 1em 0 1em;
                gap: 1em;
            }

            .omnisearch-input-container__buttons>button {
                flex-grow: 1;
            }
        }

        @media only screen and (min-width: 600px) {
            .omnisearch-input-container__buttons {
                margin-inline-end: 1em;
            }
        }

        .omnisearch-input-field {
            position: relative;
            flex-grow: 1;
        }

        undefined
    </style>
</head>

<body>
    <h2 data-heading="Team JPRIMO" dir="auto">Team JPRIMO</h2>
    <p dir="auto"><a data-tooltip-position="top" aria-label="Pacey&amp;CoRosterCanvas.canvas"
            data-href="Pacey&amp;CoRosterCanvas.canvas"
            href="/home/<USER>/Documents/Notes/Obsidian/Brain/Brain/Brain/PaceyCo/Pacey&amp;CoRosterCanvas.canvas"
            class="internal-link" target="_blank" rel="noopener nofollow">Pacey&amp;CoRosterCanvas</a></p>
    <h3 data-heading="Duty Preferences" dir="auto">Duty Preferences</h3>
    <table>
        <thead>
            <tr>
                <th>member</th>
                <th>duty preference</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Jordan</td>
                <td>Floors, Feed, Lawn, Bins, Hanging, Litter, Packing</td>
            </tr>
            <tr>
                <td>Mama</td>
                <td>Dishes, Floors, Feed, Pack, Table, Laundry, Dinner</td>
            </tr>
            <tr>
                <td>Oliver</td>
                <td>Floors, Feed, Lawn, Packing Lunchboxes, Laundry, Table</td>
            </tr>
            <tr>
                <td>Phoebe</td>
                <td>Floors, Lawn, Packing Lunchboxes, Laundry, Table</td>
            </tr>
            <tr>
                <td>Imogen</td>
                <td>Floors, Feed, Lawn, Packing Lunchboxes, Laundry, Table</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
        </tbody>
    </table>
    <table>
        <thead>
            <tr>
                <th>Member</th>
                <th>Monday</th>
                <th>Tuesday</th>
                <th>Wednesday</th>
                <th>Thursday</th>
                <th>Friday</th>
                <th>Saturday</th>
                <th>Sunday</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Jordan</td>
                <td>Laundry, Dishes</td>
                <td>Pack, Dinner</td>
                <td>Floors, Bins</td>
                <td>Dinner, Litter, Dishes</td>
                <td>Feed, Bins</td>
                <td>Floors, Dinner, Litter</td>
                <td>Laundry, Bins, Dishes</td>
            </tr>
            <tr>
                <td>Mama</td>
                <td>Feed, Pack, Dinner</td>
                <td>Dishes, Bins</td>
                <td>Laundry, Dinner, Litter, Dishes</td>
                <td>Table, Bins</td>
                <td>Floors, Laundry, Dishes</td>
                <td>Feed, Table, Bins, Dishes</td>
                <td>Dinner, Litter</td>
            </tr>
            <tr>
                <td>Oliver</td>
                <td>Floors, Table, Hanging</td>
                <td>Hanging, Lawn</td>
                <td>Feed, Lawn</td>
                <td>Table, Hanging</td>
                <td>Lawn, Table</td>
                <td>Lawn, Hanging</td>
                <td>Pack, Table</td>
            </tr>
            <tr>
                <td>Phoebe</td>
                <td>Hanging, Lawn</td>
                <td>Floors, Lawn</td>
                <td>Pack, Table</td>
                <td>Feed, Lawn</td>
                <td>Table, Hanging</td>
                <td>Hanging, Lawn</td>
                <td>Floors, Hanging</td>
            </tr>
            <tr>
                <td>Imogen</td>
                <td>Lawn, Table</td>
                <td>Feed, Table</td>
                <td>Hanging, Lawn</td>
                <td>Floors, Pack</td>
                <td>Hanging, Lawn</td>
                <td>Table, Lawn</td>
                <td>Feed, Lawn</td>
            </tr>
        </tbody>
    </table>
    <br />
    <br />
    <br />
    <h2 data-heading="House Rules" dir="auto">House Rules</h2>
    <ul>
        <li dir="auto">Everyone must help bring in the groceries</li>
        <li dir="auto">Put bags and clothes away before TV</li>
        <li dir="auto">Brush your teeth</li>
    </ul>
    <p dir="auto"><strong>Rules</strong><br>
        Everyone must help bring in the groceries<br>
        Put Bags and clothes away before TV<br>
        Brush your teeth</p>
    <p dir="auto"><strong>Duty Guide</strong><br>
        Dishes<br>
        Floors<br>
        Feed = Feeding Cat<br>
        Laundry = Laundry (hang and dry)<br>
        Hanging = Hanging Washing<br>
        Pack = Packing Lunchboxes<br>
        Bins<br>
        Tidy = Tidy the Lawn<br>
        Dinner<br>
        Table = Table Cleaning<br>
        Litter = Change Cat Litter</p>
</body>

</html>
