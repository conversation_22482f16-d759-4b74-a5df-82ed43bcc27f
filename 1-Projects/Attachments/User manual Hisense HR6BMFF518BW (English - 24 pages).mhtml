From: <Saved by Blink>
Snapshot-Content-Location: https://www.usermanuals.au/hisense/hr6bmff518bw/manual?p=24
Subject: User manual Hisense HR6BMFF518BW (English - 24 pages)
Date: Sun, 4 May 2025 22:42:44 +1000
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----"


------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/hisense/hr6bmff518bw/manual?p=24

<!DOCTYPE html><html lang=3D"en_AU"><head><meta http-equiv=3D"Content-Type"=
 content=3D"text/html; charset=3DUTF-8"><link rel=3D"stylesheet" type=3D"te=
xt/css" href=3D"cid:<EMAIL>" /=
><link rel=3D"stylesheet" type=3D"text/css" href=3D"cid:css-14e72f73-7bbe-4=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/=
css" href=3D"cid:<EMAIL>" /><l=
ink rel=3D"stylesheet" type=3D"text/css" href=3D"cid:css-9a59011b-df7a-442e=
-<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css=
" href=3D"cid:<EMAIL>" /><link=
 rel=3D"stylesheet" type=3D"text/css" href=3D"cid:css-9a450093-a309-440c-88=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" h=
ref=3D"cid:<EMAIL>" /><link re=
l=3D"stylesheet" type=3D"text/css" href=3D"cid:css-6a34c5de-5f0c-4304-9ebf-=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-3f84651e-ddb5-41e9-b4e9-a=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-312b3015-bc95-4a1c-8be1-6=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-6a7f3dc3-d44a-4d1a-81ae-a=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-add76ccb-7d6f-4e33-a024-4=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-5cbfe1a5-0af9-4c32-973f-b=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-c0ebbcef-99ca-4acd-8f79-f=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-a98e0a65-240d-4163-b34e-f=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-c1c8aa5c-10cb-4ceb-9c52-4=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-416be646-c427-4086-8760-7=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-2558c601-787b-4207-9fd4-0=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-92261461-7403-4399-9572-e=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" />
<meta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=3D1"=
>
<title>User manual Hisense HR6BMFF518BW (English - 24 pages)</title>
<link rel=3D"preconnect" href=3D"https://adservice.google.com/">
<link rel=3D"preconnect" href=3D"https://googleads.g.doubleclick.net/">
<link rel=3D"preconnect" href=3D"https://www.googletagservices.com/">
<link rel=3D"preconnect" href=3D"https://tpc.googlesyndication.com/">






























<link rel=3D"stylesheet" href=3D"https://www.usermanuals.au/_nuxt/entry.h33=
TVmZ5.css" crossorigin=3D"">
<link rel=3D"stylesheet" href=3D"https://www.usermanuals.au/_nuxt/useConfig=
.C6h2z7Th.css" crossorigin=3D"">
<link rel=3D"preload" href=3D"https://pagead2.googlesyndication.com/pagead/=
js/adsbygoogle.js" as=3D"script">
<link rel=3D"preload" href=3D"https://www.usermanuals.au/viewer/14/2827814/=
1/f1.woff" as=3D"font" crossorigin=3D"anonymous">
<link rel=3D"modulepreload" as=3D"script" crossorigin=3D"" href=3D"https://=
www.usermanuals.au/_nuxt/D3yFRtQ0.js">
<link rel=3D"modulepreload" as=3D"script" crossorigin=3D"" href=3D"https://=
www.usermanuals.au/_nuxt/BEqpLzx4.js">
<link rel=3D"modulepreload" as=3D"script" crossorigin=3D"" href=3D"https://=
www.usermanuals.au/_nuxt/Caa5TkCd.js">
<link rel=3D"modulepreload" as=3D"script" crossorigin=3D"" href=3D"https://=
www.usermanuals.au/_nuxt/BExxsbWU.js">
<link rel=3D"preload" as=3D"fetch" fetchpriority=3D"low" crossorigin=3D"ano=
nymous" href=3D"https://www.usermanuals.au/_nuxt/builds/meta/d3a38187-9792-=
4735-a0a0-d432e8c3473d.json">
<link rel=3D"prefetch" as=3D"style" crossorigin=3D"" href=3D"https://www.us=
ermanuals.au/_nuxt/manual.DMb89GAp.css">
<link rel=3D"prefetch" as=3D"style" crossorigin=3D"" href=3D"https://www.us=
ermanuals.au/_nuxt/ContainerSide.B9MGHb34.css">
<link rel=3D"prefetch" as=3D"script" crossorigin=3D"" href=3D"https://www.u=
sermanuals.au/_nuxt/Buc7DDEQ.js">
<link rel=3D"prefetch" as=3D"script" crossorigin=3D"" href=3D"https://www.u=
sermanuals.au/_nuxt/BhnYsF2V.js">
<link rel=3D"prefetch" as=3D"script" crossorigin=3D"" href=3D"https://www.u=
sermanuals.au/_nuxt/CIOICYiM.js">
<link rel=3D"icon" type=3D"image/x-icon" href=3D"https://www.usermanuals.au=
/favicon.ico">



<link rel=3D"canonical" href=3D"https://www.usermanuals.au/hisense/hr6bmff5=
18bw/manual">
<link rel=3D"alternate" hreflang=3D"nl" href=3D"https://www.handleidi.ng/hi=
sense/hr6bmff518bw/handleiding">
<link rel=3D"alternate" hreflang=3D"de" href=3D"https://www.bedienungsanlei=
tu.ng/hisense/hr6bmff518bw/anleitung">
<link rel=3D"alternate" hreflang=3D"en-us" href=3D"https://www.manua.ls/his=
ense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-es" href=3D"https://www.manualpdf.es=
/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"it" href=3D"https://www.manualeduso.it/=
hisense/hr6bmff518bw/manuale">
<link rel=3D"alternate" hreflang=3D"pt" href=3D"https://www.manualpdf.pt/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"ru" href=3D"https://www.manualspdf.ru/h=
isense/hr6bmff518bw/%D0%B8%D0%BD%D1%81%D1%82%D1%80%D1%83%D0%BA%D1%86%D0%B8%=
D1%8F">
<link rel=3D"alternate" hreflang=3D"fr" href=3D"https://www.modesdemploi.fr=
/hisense/hr6bmff518bw/mode-d-emploi">
<link rel=3D"alternate" hreflang=3D"sv" href=3D"https://www.bruksanvisni.ng=
/hisense/hr6bmff518bw/bruksanvisning">
<link rel=3D"alternate" hreflang=3D"pl" href=3D"https://www.instrukcjaobslu=
gipdf.pl/hisense/hr6bmff518bw/instrukcja">
<link rel=3D"alternate" hreflang=3D"ro" href=3D"https://www.manualdeinstruc=
tiuni.ro/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"no" href=3D"https://www.bruksanvisningp=
df.no/hisense/hr6bmff518bw/bruksanvisning">
<link rel=3D"alternate" hreflang=3D"fi" href=3D"https://www.kayttooh.je/his=
ense/hr6bmff518bw/k%C3%A4ytt%C3%B6ohje">
<link rel=3D"alternate" hreflang=3D"da" href=3D"https://www.pdfmanualer.dk/=
hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"id" href=3D"https://www.petunjuk.co.id/=
hisense/hr6bmff518bw/petunjuk">
<link rel=3D"alternate" hreflang=3D"el" href=3D"https://www.xn----2lbcmca4c=
dtsdb1c.gr/hisense/hr6bmff518bw/%CE%B5%CE%B3%CF%87%CE%B5%CE%B9%CF%81%CE%AF%=
CE%B4%CE%B9%CE%BF">
<link rel=3D"alternate" hreflang=3D"uk" href=3D"https://www.xn--80aaexjatkp=
dggghih8b1a2yhv.com.ua/hisense/hr6bmff518bw/%D1%96%D0%BD%D1%81%D1%82%D1%80%=
D1%83%D0%BA%D1%86%D1%96%D1%8F-%D0%BA%D0%BE%D1%80%D0%B8%D1%81%D1%82%D1%83%D0=
%B2%D0%B0%D1%87%D0%B0">
<link rel=3D"alternate" hreflang=3D"cs" href=3D"https://www.manualypdf.cz/h=
isense/hr6bmff518bw/manu%C3%A1l">
<link rel=3D"alternate" hreflang=3D"bg" href=3D"https://www.xn--80adah2aybm=
ok5f.bg/hisense/hr6bmff518bw/%D1%80%D1%8A%D0%BA%D0%BE%D0%B2%D0%BE%D0%B4%D1%=
81%D1%82%D0%B2%D0%BE-%D0%BD%D0%B0-%D0%BF%D0%BE%D1%82%D1%80%D0%B5%D0%B1%D0%B=
8%D1%82%D0%B5%D0%BB%D1%8F">
<link rel=3D"alternate" hreflang=3D"vi" href=3D"https://www.huang-dan.vn/hi=
sense/hr6bmff518bw/h%C6%B0%E1%BB%9Bng-d%E1%BA%ABn-s%E1%BB%AD-d%E1%BB%A5ng">
<link rel=3D"alternate" hreflang=3D"hr" href=3D"https://www.prirucnici.hr/h=
isense/hr6bmff518bw/priru%C4%8Dnik">
<link rel=3D"alternate" hreflang=3D"sk" href=3D"https://www.prirucky.sk/his=
ense/hr6bmff518bw/pr%C3%ADru%C4%8Dka">
<link rel=3D"alternate" hreflang=3D"hu" href=3D"https://www.hasznalati-utas=
itasok.hu/hisense/hr6bmff518bw/haszn%C3%A1lati-utasit%C3%A1s">
<link rel=3D"alternate" hreflang=3D"en-gb" href=3D"https://www.manuals.co.u=
k/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-ar" href=3D"https://www.manual.ar/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"pt-br" href=3D"https://www.manualpdf.co=
m.br/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"en-ca" href=3D"https://www.manuals.ca/h=
isense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-ve" href=3D"https://www.manual.com.v=
e/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-pe" href=3D"https://www.manual.pe/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-mx" href=3D"https://www.manuales.mx/=
hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"ro-md" href=3D"https://www.manual.md/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-co" href=3D"https://www.manuales.com=
.co/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-cl" href=3D"https://www.manualpdf.cl=
/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-ec" href=3D"https://www.manual.ec/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-gt" href=3D"https://www.manual.gt/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"en-nz" href=3D"https://www.manual.nz/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-bo" href=3D"https://www.manual.bo/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-hn" href=3D"https://www.manual.hn/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-do" href=3D"https://www.manual.do/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-cr" href=3D"https://www.manual.cr/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-pa" href=3D"https://www.manual.pa/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"es-sv" href=3D"https://www.manual.sv/hi=
sense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"lv" href=3D"https://www.rokasgramataspd=
f.lv/hisense/hr6bmff518bw/rokasgr%C4%81mata">
<link rel=3D"alternate" hreflang=3D"lt" href=3D"https://www.vadovaspdf.lt/h=
isense/hr6bmff518bw/vadovas">
<link rel=3D"alternate" hreflang=3D"sl" href=3D"https://www.prirocnikpdf.si=
/hisense/hr6bmff518bw/priro%C4%8Dnik">
<link rel=3D"alternate" hreflang=3D"sq" href=3D"https://www.udhezimet.al/hi=
sense/hr6bmff518bw/udh%C3%ABzues">
<link rel=3D"alternate" hreflang=3D"mk" href=3D"https://www.xn--80apbinjhb8=
d.xn--d1alf/hisense/hr6bmff518bw/%D0%BF%D1%80%D0%B8%D1%80%D0%B0%D1%87%D0%BD=
%D0%B8%D0%BA">
<link rel=3D"alternate" hreflang=3D"sr" href=3D"https://www.uputstvo.rs/his=
ense/hr6bmff518bw/%D1%83%D0%BF%D1%83%D1%82%D1%81%D1%82%D0%B2%D0%BE-%D0%B7%D=
0%B0-%D1%83%D0%BF%D0%BE%D1%82%D1%80%D0%B5%D0%B1%D1%83">
<link rel=3D"alternate" hreflang=3D"hi" href=3D"https://www.xn--l2bmcno7cen=
.xn--i1b6b1a6a2e/hisense/hr6bmff518bw/%E0%A4%B9%E0%A4%BE%E0%A4%A5-%E0%A4%B8=
%E0%A5%87-%E0%A4%95%E0%A4%BF%E0%A4%AF%E0%A4%BE-%E0%A4%B9%E0%A5%81%E0%A4%86"=
>
<link rel=3D"alternate" hreflang=3D"ko" href=3D"https://www.xn--vg1b14l6tk.=
xn--3e0b707e/hisense/hr6bmff518bw/%EB%A7%A4%EB%89%B4%EC%96%BC">
<link rel=3D"alternate" hreflang=3D"en-in" href=3D"https://www.manualpdf.in=
/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"uz" href=3D"https://www.qollanmalar.uz/=
hisense/hr6bmff518bw/qo%E2%80%98llanma">
<link rel=3D"alternate" hreflang=3D"et" href=3D"https://www.juhend.ee/hisen=
se/hr6bmff518bw/juhend">
<link rel=3D"alternate" hreflang=3D"kk" href=3D"https://www.xn--80aweql3c59=
aea5q.xn--80ao21a/hisense/hr6bmff518bw/%D0%BD%D2%B1%D1%81%D2%9B%D0%B0%D1%83=
%D0%BB%D1%8B%D2%9B">
<link rel=3D"alternate" hreflang=3D"he" href=3D"https://www.manualpdf.co.il=
/hisense/hr6bmff518bw/%D7%9E%D7%93%D7%A8%D7%99%D7%9A">
<link rel=3D"alternate" hreflang=3D"tr" href=3D"https://www.kullanimkilavuz=
u.com.tr/hisense/hr6bmff518bw/k%C4%B1lavuz">
<link rel=3D"alternate" hreflang=3D"ka" href=3D"https://www.manualpdf.ge/hi=
sense/hr6bmff518bw/%E1%83%A1%E1%83%90%E1%83%AE%E1%83%94%E1%83%9A%E1%83%9B%E=
1%83%AB%E1%83%A6%E1%83%95%E1%83%90%E1%83%9C%E1%83%94%E1%83%9A%E1%83%9D">
<link rel=3D"alternate" hreflang=3D"en-au" href=3D"https://www.usermanuals.=
au/hisense/hr6bmff518bw/manual">
<link rel=3D"alternate" hreflang=3D"ja" href=3D"https://www.xn--eckp3a8evgm=
f.jp/hisense/hr6bmff518bw/%E3%83%9E%E3%83%8B%E3%83%A5%E3%82%A2%E3%83%AB">
<meta name=3D"description" content=3D"Manual Hisense HR6BMFF518BW. View the=
 Hisense HR6BMFF518BW manual for free or ask your question to other Hisense=
 HR6BMFF518BW owners.">
<link rel=3D"stylesheet" crossorigin=3D"" href=3D"https://www.usermanuals.a=
u/_nuxt/ContainerSide.B9MGHb34.css"><link rel=3D"stylesheet" crossorigin=3D=
"" href=3D"https://www.usermanuals.au/_nuxt/manual.DMb89GAp.css"><link href=
=3D"https://www.googletagmanager.com/" rel=3D"preconnect"><link rel=3D"styl=
esheet" crossorigin=3D"" href=3D"https://www.usermanuals.au/_nuxt/default.B=
Lw1kusm.css"><link rel=3D"modulepreload" as=3D"script" crossorigin=3D"" hre=
f=3D"https://www.usermanuals.au/_nuxt/D2ZJrMpv.js"><link rel=3D"stylesheet"=
 crossorigin=3D"" href=3D"https://www.usermanuals.au/_nuxt/index.C3HriZ4f.c=
ss"></head><body><div id=3D"__nuxt"><div><div id=3D"app"><!----><div class=
=3D"wrapper"><!--[--><div class=3D"manual" data-v-8e81d043=3D""><nav style=
=3D"" class=3D"navigation d-block d-md-none" data-v-8e81d043=3D"" data-v-95=
f55862=3D""><div class=3D"container" data-v-95f55862=3D""><a href=3D"https:=
//www.usermanuals.au/" class=3D"h4 navigation__logo" data-v-95f55862=3D""><=
div data-v-95f55862=3D"">UserManuals<span>.</span>au</div></a><div class=3D=
"search-button navigation__search" data-v-95f55862=3D"" data-v-75dbeedd=3D"=
"><button class=3D"text-dark" data-v-75dbeedd=3D""><div class=3D"app-icon s=
ize-lg magnifyingglass" data-v-75dbeedd=3D""><svg width=3D"100" height=3D"1=
00" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=3D"http://www.w3.org/2000/s=
vg">
<path d=3D"M15.9912 44.2725C15.9912 59.8584 28.6377 72.5049 44.2236 72.5049=
C49.7607 72.5049 54.8535 70.8643 59.126 68.0957L73.9941 82.9639C75.1221 84.=
0918 76.626 84.6387 78.1982 84.6387C81.5479 84.6387 84.0088 82.0752 84.0088=
 78.7939C84.0088 77.2217 83.4277 75.752 82.334 74.6582L67.6025 59.8584C70.6=
787 55.4492 72.4561 50.083 72.4561 44.2725C72.4561 28.6523 59.8096 16.0059 =
44.2236 16.0059C28.6377 16.0059 15.9912 28.6523 15.9912 44.2725ZM24.3311 44=
.2725C24.3311 33.2666 33.2178 24.3457 44.2236 24.3457C55.2295 24.3457 64.15=
04 33.2666 64.1504 44.2725C64.1504 55.2441 55.2295 64.165 44.2236 64.165C33=
.2178 64.165 24.3311 55.2441 24.3311 44.2725Z"></path>
</svg>
</div></button><template><!----></template></div></div></nav><div class=3D"=
manual__navigation d-none d-md-block d-lg-block d-xl-block" style=3D"backgr=
ound:#00ad9d;" data-v-8e81d043=3D""><nav style=3D"" class=3D"navigation nav=
igation--inverted" data-v-8e81d043=3D"" data-v-95f55862=3D""><div class=3D"=
container" data-v-95f55862=3D""><a href=3D"https://www.usermanuals.au/" cla=
ss=3D"h4 navigation__logo" data-v-95f55862=3D""><div data-v-95f55862=3D"">U=
serManuals<span>.</span>au</div></a><div class=3D"search-button navigation_=
_search" data-v-95f55862=3D"" data-v-75dbeedd=3D""><button class=3D"text-da=
rk" data-v-75dbeedd=3D""><div class=3D"app-icon size-lg magnifyingglass" da=
ta-v-75dbeedd=3D""><svg width=3D"100" height=3D"100" viewBox=3D"0 0 100 100=
" fill=3D"none" xmlns=3D"http://www.w3.org/2000/svg">
<path d=3D"M15.9912 44.2725C15.9912 59.8584 28.6377 72.5049 44.2236 72.5049=
C49.7607 72.5049 54.8535 70.8643 59.126 68.0957L73.9941 82.9639C75.1221 84.=
0918 76.626 84.6387 78.1982 84.6387C81.5479 84.6387 84.0088 82.0752 84.0088=
 78.7939C84.0088 77.2217 83.4277 75.752 82.334 74.6582L67.6025 59.8584C70.6=
787 55.4492 72.4561 50.083 72.4561 44.2725C72.4561 28.6523 59.8096 16.0059 =
44.2236 16.0059C28.6377 16.0059 15.9912 28.6523 15.9912 44.2725ZM24.3311 44=
.2725C24.3311 33.2666 33.2178 24.3457 44.2236 24.3457C55.2295 24.3457 64.15=
04 33.2666 64.1504 44.2725C64.1504 55.2441 55.2295 64.165 44.2236 64.165C33=
.2178 64.165 24.3311 55.2441 24.3311 44.2725Z"></path>
</svg>
</div></button><template><!----></template></div></div></nav><div class=3D"=
manual__navigation-sub--invert manual__navigation-sub--background manual__n=
avigation-sub" data-v-8e81d043=3D""><a href=3D"https://www.usermanuals.au/r=
efrigerators/hisense" class=3D"" data-v-8e81d043=3D""><div class=3D"app-ico=
n size-lg chevron.left" data-v-8e81d043=3D""><svg width=3D"100" height=3D"1=
00" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=3D"http://www.w3.org/2000/s=
vg">
<path d=3D"M27.4756 50.3223C27.4756 51.7236 27.9883 52.8857 29.1162 53.9795=
L55.4346 79.7168C56.2891 80.6055 57.3828 81.0498 58.6475 81.0498C61.2109 81=
.0498 63.2617 78.999 63.2617 76.4355C63.2617 75.1367 62.749 74.0088 61.8262=
 73.0859L38.4473 50.2881L61.8262 27.5586C62.749 26.6357 63.2617 25.4736 63.=
2617 24.209C63.2617 21.6455 61.2109 19.6289 58.6475 19.6289C57.3486 19.6289=
 56.2891 20.0391 55.4346 20.9277L29.1162 46.665C27.9883 47.7588 27.5098 48.=
9209 27.4756 50.3223Z"></path>
</svg>
</div> Hisense refrigerators =C2=B7 <div class=3D"smart-image" data-v-8e81d=
043=3D"" data-v-1460e71d=3D""><picture data-v-1460e71d=3D""><source srcset=
=3D"/thumbs/brands/s/2376-hisense_logo.webp" type=3D"image/webp" data-v-146=
0e71d=3D""><img src=3D"https://www.usermanuals.au/thumbs/brands/s/2376-hise=
nse_logo.png" width=3D"false" height=3D"false" alt=3D"Hisense manuals" load=
ing=3D"lazy" data-v-1460e71d=3D""></picture></div></a></div></div><div data=
-v-8e81d043=3D"" data-v-a411a370=3D""><!----></div><div class=3D"manual__he=
ader container" data-v-8e81d043=3D""><div style=3D"display:none;" class=3D"=
modal" id=3D"image" data-v-8e81d043=3D"" data-v-740f5b8e=3D""><div class=3D=
"modal__backdrop" data-v-740f5b8e=3D""><div class=3D"modal__container" data=
-v-740f5b8e=3D""><button class=3D"modal__close" data-v-740f5b8e=3D""><div c=
lass=3D"app-icon size-xl xmark text-white close" data-v-740f5b8e=3D""><svg =
width=3D"100" height=3D"100" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=3D=
"http://www.w3.org/2000/svg">
<path d=3D"M23.4082 76.9482C25.1172 78.6572 27.9883 78.5889 29.6289 76.9824=
L50 56.6113L70.3369 76.9824C72.0117 78.6572 74.8828 78.6572 76.5576 76.9482=
C78.2324 75.2393 78.2666 72.4023 76.5918 70.7275L56.2549 50.3564L76.5918 30=
.0195C78.2666 28.3447 78.2666 25.5078 76.5576 23.7988C74.8486 22.124 72.011=
7 22.0898 70.3369 23.7646L50 44.1357L29.6289 23.7646C27.9883 22.1582 25.083=
 22.0898 23.4082 23.7988C21.7334 25.5078 21.7676 28.3789 23.374 29.9854L43.=
7451 50.3564L23.374 70.7617C21.7676 72.3682 21.6992 75.2734 23.4082 76.9482=
Z"></path>
</svg>
</div></button><div class=3D"modal__header" data-v-740f5b8e=3D""><!--[--><!=
--]--></div><div class=3D"modal__content" data-v-740f5b8e=3D""><!--[--><div=
 class=3D"smart-image" data-v-8e81d043=3D"" data-v-1460e71d=3D""><picture d=
ata-v-1460e71d=3D""><!----><img src=3D"https://www.usermanuals.au/thumbs/pr=
oducts/l/1532132-hisense-hr6bmff518bw.webp" width=3D"false" height=3D"false=
" alt=3D"Hisense HR6BMFF518BW" loading=3D"lazy" data-v-1460e71d=3D""></pict=
ure></div><!--]--></div></div></div></div><h1 class=3D"manual__title" data-=
v-8e81d043=3D"">Hisense HR6BMFF518BW manual</h1><a href=3D"https://www.user=
manuals.au/thumbs/products/l/1532132-hisense-hr6bmff518bw.webp" class=3D"ma=
nual__main-image" data-v-8e81d043=3D""><div class=3D"smart-image" data-v-8e=
81d043=3D"" data-v-1460e71d=3D""><picture data-v-1460e71d=3D""><!----><img =
src=3D"https://www.usermanuals.au/thumbs/products/s/1532132-hisense-hr6bmff=
518bw.webp" width=3D"75" height=3D"75" alt=3D"Hisense HR6BMFF518BW" loading=
=3D"lazy" data-v-1460e71d=3D""></picture></div></a><div class=3D"manual__in=
fo" data-v-8e81d043=3D""><div id=3D"btn-review" class=3D"rating manual__rat=
ing" data-v-8e81d043=3D"" data-v-ff31764a=3D""><div class=3D"rating__stars"=
 data-v-ff31764a=3D""><div style=3D"width:75%;" class=3D"rating__stars-hove=
r" data-v-ff31764a=3D""></div></div><div class=3D"rating__stats" data-v-ff3=
1764a=3D""><span data-v-ff31764a=3D"">7.5</span><span class=3D"text-muted" =
data-v-ff31764a=3D""> =C2=B7 1</span></div><button class=3D"link p-0" data-=
v-ff31764a=3D""><div class=3D"rating__cta" data-v-ff31764a=3D"">give review=
</div></button><!----></div><div class=3D"manual__subtitle" data-v-8e81d043=
=3D""><span data-v-8e81d043=3D""><div class=3D"app-icon size-null doc" data=
-v-8e81d043=3D""><svg fill=3D"none" height=3D"100" viewBox=3D"0 0 100 100" =
width=3D"100" xmlns=3D"http://www.w3.org/2000/svg"><path d=3D"m31.748 87.61=
23h36.504c7.4853 0 11.4843-4.0332 11.4843-11.5869v-31.001c0-4.9902-.6836-7.=
3828-3.8281-10.5615l-17.2949-17.5342c-3.042-3.1103-5.6055-3.8281-10.2197-3.=
8281h-16.6456c-7.4853 0-11.4843 3.999-11.4843 11.5527v51.3721c0 7.5537 3.96=
48 11.5869 11.4843 11.5869zm.752-7.6221c-3.1104 0-4.6143-1.6064-4.6143-4.61=
42v-50.0733c0-2.9736 1.5039-4.6142 4.6485-4.6142h14.0478v17.876c0 5.332 2.5=
977 7.8955 7.9297 7.8955h17.6026v28.916c0 3.0078-1.5039 4.6142-4.6485 4.614=
2zm22.6611-39.9902c-1.4355 0-2.0849-.6494-2.0849-2.085v-16.372l18.2177 18.4=
57z"></path></svg>
</div> PDF manual</span><span data-v-8e81d043=3D""> &nbsp;=C2=B7 24 pages</=
span><span data-v-8e81d043=3D""><div class=3D"app-icon size-null globe flag=
" data-v-8e81d043=3D""><svg fill=3D"none" height=3D"100" viewBox=3D"0 0 100=
 100" width=3D"100" xmlns=3D"http://www.w3.org/2000/svg"><path d=3D"m50 85.=
9717c19.5508 0 35.6494-16.0987 35.6494-35.6494 0-19.5166-16.1328-35.6494-35=
.6836-35.6494-19.5166 0-35.6152 16.1328-35.6152 35.6494 0 19.5507 16.0986 3=
5.6494 35.6494 35.6494zm-20.6445-56.6699c3.3496-3.2813 7.4853-5.8106 12.099=
6-7.2461-2.461 2.4267-4.5117 5.8789-6.084 10.083-2.3584-.752-4.375-1.709-6.=
0156-2.8369zm29.2236-7.212c4.5801 1.4356 8.7158 3.9307 12.0654 7.2461-1.640=
6 1.128-3.6572 2.0508-6.0156 2.8028-1.5381-4.1699-3.623-7.6221-6.0498-10.04=
89zm-6.084 1.4014c2.7686 1.3672 5.2979 4.9902 7.1436 9.8779-2.2217.376-4.61=
43.6153-7.1436.7178zm-12.1338 9.8779c1.8457-4.8877 4.375-8.5107 7.1436-9.87=
79v10.5957c-2.5293-.1025-4.9219-.3418-7.1436-.7178zm-19.79 14.458c.4785-5.4=
687 2.4609-10.5615 5.5713-14.7998 2.0166 1.5381 4.6826 2.837 7.7929 3.8282-=
.8203 3.3496-1.333 7.041-1.5039 10.9716zm46.9971 0c-.1709-3.9306-.6836-7.62=
2-1.5039-10.9716 3.1103-.9912 5.7763-2.2901 7.7929-3.794 3.1104 4.2383 5.09=
28 9.2969 5.5713 14.7656zm-30.044 0c.1709-3.4179.6494-6.6992 1.4014-9.707 2=
.666.5127 5.5713.8545 8.5791.9912v8.7158zm14.9707 0v-8.7158c3.0078-.1367 5.=
9131-.4785 8.5791-.9912.752 3.0078 1.2305 6.2891 1.4014 9.707zm-31.9238 4.9=
903h11.8603c.1368 3.999.6836 7.7246 1.5381 11.1426-3.1103.9912-5.7422 2.255=
8-7.7588 3.7597-3.1445-4.2724-5.1611-9.3994-5.6396-14.9023zm16.9531 0h9.980=
5v8.8867c-3.0078.1367-5.8789.4785-8.5791.9912-.752-3.0762-1.2305-6.4258-1.4=
014-9.8779zm14.9707 8.8867v-8.8867h9.9805c-.1709 3.4521-.6494 6.8017-1.4014=
 9.8779-2.7002-.5127-5.5713-.8545-8.5791-.9912zm13.5352 2.2559c.8545-3.418 =
1.4013-7.1436 1.5381-11.1426h11.8603c-.4785 5.5029-2.4951 10.6299-5.6396 14=
.9023-2.0166-1.5039-4.6485-2.7685-7.7588-3.7597zm-25.6006 3.4863c2.1875-.37=
6 4.5459-.6494 7.0752-.752v10.5274c-2.7344-1.4014-5.2637-4.9903-7.0752-9.77=
54zm12.0654-.752c2.5293.1026 4.8877.376 7.0752.752-1.8115 4.7851-4.3408 8.3=
74-7.0752 9.7754zm12.0996 1.9483c2.3242.7519 4.3408 1.6748 5.9815 2.7685-3.=
3496 3.2813-7.4512 5.7422-11.9629 7.1436 2.3926-2.3926 4.4433-5.7764 5.9814=
-9.9121zm-35.1367 2.7685c1.6065-1.0937 3.6231-2.0166 5.9473-2.7685 1.5381 4=
.1357 3.5888 7.5195 5.9814 9.9121-4.5117-1.4014-8.6133-3.8623-11.9287-7.143=
6z"></path></svg>
</div> English</span></div></div></div><div style=3D"position:absolute;marg=
in-top:100px;" data-v-8e81d043=3D""></div><!----><div class=3D"row m-0 clea=
rfix" hardcoded-superflex=3D"" data-v-8e81d043=3D"" data-v-0eadb2a6=3D""><d=
iv class=3D"col-xl-2 d-xs-none d-none d-xl-block p-0 sticky-side" data-v-0e=
adb2a6=3D""><div class=3D"ad-placeholder position-sticky sticky-top" data-v=
-0eadb2a6=3D"" data-v-5e860da6=3D""><!----><ins data-ad-unit=3D"hz_manual_l=
eft" data-ad-channel=3D"4152078287" data-ad-region=3D"page-0.80092704952891=
42" data-ad-format=3D"vertical" data-ad-slot=3D"6114267696" data-ad-client=
=3D"ca-pub-4193547076534677" data-ad-layout-key=3D"" data-full-width-respon=
sive=3D"true" style=3D"display:block;" class=3D"adsbygoogle" data-v-0eadb2a=
6=3D"" data-v-5e860da6=3D""></ins></div><div class=3D"position-sticky stick=
y-top" data-v-0eadb2a6=3D"" data-v-a411a370=3D""><!----></div></div><div cl=
ass=3D"col-xl-2 d-xs-none d-none d-xl-block p-0 sticky-side" data-v-0eadb2a=
6=3D""><div class=3D"ad-placeholder position-sticky sticky-top" data-v-0ead=
b2a6=3D"" data-v-5e860da6=3D""><!----><ins data-ad-unit=3D"hz_manual_right"=
 data-ad-channel=3D"4152078287" data-ad-region=3D"page-0.8189249163683474" =
data-ad-format=3D"vertical" data-ad-slot=3D"8665189668" data-ad-client=3D"c=
a-pub-4193547076534677" data-ad-layout-key=3D"" data-full-width-responsive=
=3D"true" style=3D"display:block;" class=3D"adsbygoogle" data-v-0eadb2a6=3D=
"" data-v-5e860da6=3D""></ins></div></div><div class=3D"col-xl-8" data-v-0e=
adb2a6=3D""><!--[--><div id=3D"viewer" style=3D"min-height:350px;" data-v-8=
e81d043=3D"" class=3D"js-only"><div class=3D"tabs"><button><div class=3D"ap=
p-icon size-null office-file-pdf-1"><svg height=3D"18" viewBox=3D"0 0 18 18=
" width=3D"18" xmlns=3D"http://www.w3.org/2000/svg"><path d=3D"m1.6835937-.=
18945312c-.4969936-.00000001-.97279174.19739616-1.3242187.54882812-.3514246=
1.35142795-.55078125.8291782-.55078125 1.3261719v14.6250001c0 .497004.19919=
713.974718.55078125 1.326172.35145261.351494.8272703.548828 1.3242187.54882=
8h2.2500001a.75.75 0 0 0 .7499999-.75.75.75 0 0 0 -.7499999-.75h-2.2500001c=
-.099791 0-.1931749-.03887-.2636718-.109375-.070372-.07035-.1113281-.165829=
-.1113282-.265625v-14.6250001c0-.099746.040797-.1950934.1113282-.265625.070=
523-.070524.1639261-.109375.2636718-.109375h7.9726563c.049277.0000106.09713=
9.010449.1425781.029297a.750075.750075 0 0 0 .00195 0c.045489.018856.08608.=
046989.1210937.082031l4.402344 4.4023437c.03471.034713.06314.075442.08203.1=
210938.01877.045362.0293.094903.0293.1445312v3.4707032a.75.75 0 0 0 .75.750=
0001.75.75 0 0 0 .75-.7500001v-3.4707031c0-.2458918-.04835-.4890994-.142578=
-.7167968-.0941078-.2274279-.2339118-.4350678-.4082018-.609375l-4.402344-4.=
40234377c-.173986-.17412947-.379712-.31381345-.607422-.40820313-.22758-.094=
39978-.4724073-.14252479-.71875-.14257812z"></path><path d=3D"m9.5585938-.1=
8945312a.75.75 0 0 0 -.75.74999999v4.50000003c0 .4969954.1993555.9747461.55=
07812 1.3261718.3513917.3513251.829163.5488282 1.326172.5488282h4.5a.75.75 =
0 0 0 .75-.75.75.75 0 0 0 -.75-.75h-4.5c-.09979 0-.194977-.038741-.265625-.=
109375-.07053-.070534-.111328-.165901-.111328-.265625v-4.50000003a.75.75 0 =
0 0 -.7500002-.74999999z"></path><path d=3D"m6.1835937 11.060547a.75.75 0 0=
 0 -.75.75v5.625a.75.75 0 0 0 .75.75.75.75 0 0 0 .75-.75v-5.625a.75.75 0 0 =
0 -.75-.75z"></path><path d=3D"m6.1835937 11.060547a.75.75 0 0 0 -.75.75.75=
.75 0 0 0 .75.75h.5664063c.2489127 0 .4861037.09936.6621094.275391.1760643.=
176081.2753906.413257.2753906.662109 0 .248996-.099326.487981-.2753906.6640=
62-.1760149.175982-.4131967.273438-.6621094.273438h-.5625a.75.75 0 0 0 -.75=
.75.75.75 0 0 0 .75.75h.5625c.6461864 0 1.2657517-.256073 1.7226563-.712891=
a.750075.750075 0 0 0 .00195 0c.4568781-.456918.7128937-1.078406.7128937-1.=
724609 0-.646148-.2560156-1.265738-.7128906-1.722656-.4569137-.456972-1.078=
423-.714844-1.7246094-.714844z"></path><path d=3D"m10.685547 11.060547a.750=
075.750075 0 0 0 -.7500001.75v5.625a.750075.750075 0 0 0 .7500001.75c.79529=
1 0 1.558636-.316449 2.121094-.878906.562348-.562349.878906-1.325652.878906=
-2.121094v-1.125c0-.795375-.316631-1.558685-.878906-2.121094-.562409-.56227=
5-1.325719-.878906-2.121094-.878906zm.75 1.810547c.09748.06038.228071.04645=
.310547.128906.281524.281591.439453.662523.439453 1.060547v1.125c0 .398157-=
.158003.779096-.439453 1.060547-.08246.08246-.213088.06853-.310547.128906z"=
></path><path d=3D"m16.310547 11.060547c-.497126 0-.974854.197677-1.326172.=
548828-.351499.351499-.548828.829247-.548828 1.326172v4.5a.75.75 0 0 0 .75.=
75.75.75 0 0 0 .75-.75v-4.5c0-.09905.03971-.19331.109375-.263672.07068-.070=
65.165952-.111328.265625-.111328h1.125a.75.75 0 0 0 .75-.75.75.75 0 0 0 -.7=
5-.75z"></path><path d=3D"m15.185547 14.435547a.75.75 0 0 0 -.75.75.75.75 0=
 0 0 .75.75h1.6875a.75.75 0 0 0 .75-.75.75.75 0 0 0 -.75-.75z"></path></svg=
>
</div>tutorial</button><button><div class=3D"app-icon size-null conversatio=
n-chat-1"><svg enable-background=3D"new 0 0 24 24" viewBox=3D"0 0 24 24" xm=
lns=3D"http://www.w3.org/2000/svg"><path d=3D"m20.25 24c-.2 0-.389-.078-.53=
-.22l-4.28-4.28h-4.19c-1.241 0-2.25-1.009-2.25-2.25v-7.5c0-1.241 1.009-2.25=
 2.25-2.25h10.5c1.241 0 2.25 1.009 2.25 2.25v7.5c0 1.241-1.009 2.25-2.25 2.=
25h-.75v3.75c0 .304-.182.576-.463.693-.091.038-.188.057-.287.057zm-9-15c-.4=
14 0-.75.336-.75.75v7.5c0 .414.336.75.75.75h4.5c.198 0 .391.08.53.22l3.22 3=
.22v-2.69c0-.414.336-.75.75-.75h1.5c.414 0 .75-.336.75-.75v-7.5c0-.414-.336=
-.75-.75-.75z"></path><path d=3D"m3.75 16.5c-.099 0-.196-.019-.287-.057-.28=
1-.117-.463-.389-.463-.693v-3.75h-.75c-1.241 0-2.25-1.009-2.25-2.25v-7.5c0-=
1.241 1.009-2.25 2.25-2.25h10.5c1.241 0 2.25 1.009 2.25 2.25v3c0 .414-.336.=
75-.75.75s-.75-.336-.75-.75v-3c0-.414-.336-.75-.75-.75h-10.5c-.414 0-.75.33=
6-.75.75v7.5c0 .414.336.75.75.75h1.5c.414 0 .75.336.75.75v2.689l1.72-1.72c.=
141-.141.33-.219.53-.219s.389.078.53.22c.292.292.292.768 0 1.061l-3 3c-.141=
.141-.33.219-.53.219z"></path></svg></div>questions</button><button><div cl=
ass=3D"app-icon size-null task-list-text"><svg height=3D"18" viewBox=3D"0 0=
 18 18" width=3D"18" xmlns=3D"http://www.w3.org/2000/svg"><path d=3D"m6 7.5=
a.5.5 0 0 0 -.5.5.5.5 0 0 0 .5.5h6.75a.5.5 0 0 0 .5-.5.5.5 0 0 0 -.5-.5z"><=
/path><path d=3D"m6 10.5a.5.5 0 0 0 -.5.5.5.5 0 0 0 .5.5h6.75a.5.5 0 0 0 .5=
-.5.5.5 0 0 0 -.5-.5z"></path><path d=3D"m6 13.5a.5.5 0 0 0 -.5.5.5.5 0 0 0=
 .5.5h6.75a.5.5 0 0 0 .5-.5.5.5 0 0 0 -.5-.5z"></path><path d=3D"m8.75.5c-.=
8783373 0-1.7207172.34963518-2.3417969.9707031-.4985663.4985758-.762942 1.1=
537566-.8710937 1.8417969h-2.4121094c-.430789 0-.843828.1719675-1.1484375.4=
765625-.304595.3046095-.4765625.7176485-.4765625 1.1484375v11.8125c0 .43080=
6.172015.843847.4765625 1.148437.304629.304658.717682.476563 1.1484375.4765=
63h11.25c.430772 0 .843827-.171952 1.148438-.476563.30461-.30461.476562-.71=
7665.476562-1.148437v-11.8125c0-.4307555-.171905-.8438085-.476562-1.1484375=
-.304591-.3045475-.717632-.4765625-1.148438-.4765625h-2.412109c-.10815-.688=
0416-.372466-1.3432158-.871094-1.8417969-.621043-.62108974-1.4634597-.97070=
31-2.341797-.9707031zm0 1c.6135013 0 1.2010093.2439452 1.634766.6777344.433=
853.4338125.677734 1.0212554.677734 1.6347656a.50005.50005 0 0 0 .5.5h2.812=
5c.165994 0 .323998.066202.441406.1835937.117342.1173306.183594.2754224.183=
594.4414063v11.8125c0 .166027-.06621.324017-.183594.441406-.117389.117389-.=
275379.183594-.441406.183594h-11.25c-.1659839 0-.3240757-.06625-.4414062-.1=
83594-.1173921-.117408-.1835938-.275412-.1835938-.441406v-11.8125c0-.165950=
4.0662492-.3240562.1835938-.4414063.11735-.1173445.2754558-.1835937.4414062=
-.1835937h2.8125a.50005.50005 0 0 0 .5-.5c0-.6135013.2439234-1.2009464.6777=
344-1.6347656.4338192-.433811 1.0212643-.6777344 1.6347656-.6777344z"></pat=
h><path d=3D"m9.28125 2.5c-.2070025 0-.4063401.0820936-.5527344.2285156-.14=
6422.1463943-.2285156.3457319-.2285156.5527344 0 .1544974.04508.3052024.130=
8594.4335938.085854.1285307.2089248.2299701.3515625.2890624.1427254.059129.=
2996829.073094.4511719.042969.1514212-.0301401.2911416-.103614.4003906-.212=
8908.1092768-.109249.1827506-.2489694.2128906-.4003906.030126-.151489.01616=
-.3084465-.042969-.4511719-.0590921-.1426377-.1605315-.2657086-.2890622-.35=
15625-.1284569-.0858509-.2791564-.1308594-.4335938-.1308594zm-.042969.56640=
63c.042369-.00843.087008-.00288.1269531.013672.039858.016512.073602.044067.=
097656.080078.0239799.0358914.0371099.0778562.0371099.1210937 0 .0578227-.0=
23531.1133825-.0644531.1542969-.0409144.0409221-.0964742.0644531-.1542969.0=
644531-.043178 0-.085137-.013079-.1210937-.037109-.036011-.024054-.063566-.=
057799-.080078-.097656-.016549-.039945-.022097-.084584-.013672-.1269531.008=
42-.042301.02995-.080739.060547-.1113282.030589-.030597.069027-.052127.1113=
282-.060547z"></path></svg>
</div>specs</button></div><div id=3D"viewer" class=3D"viewer mt-2"><div><!-=
-[--><div class=3D"ad-placeholder" style=3D"max-width:968px;min-height:128p=
x;height:128px;" data-v-8e81d043=3D"" data-v-5e860da6=3D""><!----><ins data=
-ad-unit=3D"hz_manual_top_in-feed" data-ad-channel=3D"4152078287" data-ad-r=
egion=3D"page-0.032763490831032804" data-ad-format=3D"fluid" data-ad-slot=
=3D"9838186407" data-ad-client=3D"ca-pub-4193547076534677" data-ad-layout-k=
ey=3D"-hr-7+2h-1m-4u" data-full-width-responsive=3D"true" style=3D"display:=
block;" class=3D"adsbygoogle" data-v-8e81d043=3D"" data-v-5e860da6=3D""></i=
ns></div><!--]--></div><div style=3D"position:relative;"><h5 class=3D"viewe=
r__title">Hisense HR6BMFF518BW</h5><div class=3D"viewer__toolbar-top"><butt=
on><div class=3D"app-icon size-null search.2 d-none d-md-flex"><svg height=
=3D"100" viewBox=3D"0 0 100 100" width=3D"100" xmlns=3D"http://www.w3.org/2=
000/svg"><path d=3D"m15.9912 44.2725c0 15.5859 12.6465 28.2324 28.2324 28.2=
324 5.5371 0 10.6299-1.6406 14.9024-4.4092l14.8681 14.8682c1.128 1.1279 2.6=
319 1.6748 4.2041 1.6748 3.3497 0 5.8106-2.5635 5.8106-5.8448 0-1.5722-.581=
1-3.0419-1.6748-4.1357l-14.7315-14.7998c3.0762-4.4092 4.8536-9.7754 4.8536-=
15.5859 0-15.6202-12.6465-28.2666-28.2325-28.2666-15.5859 0-28.2324 12.6464=
-28.2324 28.2666zm8.3399 0c0-11.0059 8.8867-19.9268 19.8925-19.9268 11.0059=
 0 19.9268 8.9209 19.9268 19.9268 0 10.9716-8.9209 19.8925-19.9268 19.8925-=
11.0058 0-19.8925-8.9209-19.8925-19.8925zm8.2373 0c0 1.538 1.2646 2.8027 2.=
8027 2.8027h6.0498v6.0156c0 1.5723 1.2305 2.8369 2.8027 2.8369 1.5723 0 2.8=
028-1.2646 2.8028-2.8369v-6.0156h6.0498c1.5381 0 2.8027-1.2647 2.8027-2.802=
7 0-1.5723-1.2646-2.837-2.8027-2.837h-6.0498v-6.0156c0-1.5722-1.2305-2.8027=
-2.8028-2.8027-1.5722 0-2.8027 1.2305-2.8027 2.8027v6.0156h-6.0498c-1.5381 =
0-2.8027 1.2647-2.8027 2.837z"></path></svg></div></button><button class=3D=
"d-flex"><div class=3D"app-icon size-null printer"><svg height=3D"100" view=
Box=3D"0 0 100 100" width=3D"100" xmlns=3D"http://www.w3.org/2000/svg"><pat=
h d=3D"m21.7334 77.7002h3.4521v2.9394c0 5.3663 2.5635 7.793 7.793 7.793h34.=
0088c5.2295 0 7.793-2.4267 7.793-7.793v-2.9394h3.4863c7.1777 0 11.1426-3.86=
23 11.1426-11.04v-31.0352c0-7.2119-3.9649-11.04-11.1426-11.04h-2.9736v-1.84=
57c0-7.1436-3.5547-10.459-10.459-10.459h-29.668c-6.665 0-10.4931 3.3154-10.=
4931 10.459v1.8457h-2.9395c-6.9727 0-11.1426 3.8281-11.1426 11.04v31.0352c0=
 7.1777 3.9307 11.04 11.1426 11.04zm9.9121-55.5078c0-2.3584 1.2305-3.5547 3=
.5547-3.5547h29.5654c2.3584 0 3.5547 1.1963 3.5547 3.5547v2.3926h-36.6748zm=
35.3418 24.4385h-34.0088c-5.0586 0-7.793 2.4267-7.793 7.7929v16.543h-3.4179=
c-2.666 0-4.0674-1.4014-4.0674-4.0674v-31.5137c0-2.6318 1.4014-4.0673 4.067=
4-4.0673h56.4648c2.6319 0 4.0332 1.4355 4.0332 4.0673v31.5137c0 2.666-1.401=
3 4.0674-4.0332 4.0674h-3.4521v-16.543c0-5.3662-2.5635-7.7929-7.793-7.7929z=
m-1.1963-7.6221c0 2.5635 2.0508 4.5801 4.5801 4.5459 2.4609-.0342 4.5459-2.=
0166 4.5459-4.5459 0-2.4268-2.1191-4.5459-4.5459-4.5459-2.4951 0-4.5801 2.1=
191-4.5801 4.5459zm-31.582 42.9639c-1.5039 0-2.2901-.752-2.2901-2.2901v-24.=
3017c0-1.5039.7862-2.2559 2.2901-2.2559h31.582c1.5039 0 2.2559.752 2.2559 2=
.2559v24.3017c0 1.5381-.752 2.2901-2.2559 2.2901zm4.9219-17.6368h21.8066c1.=
4014 0 2.4951-1.0937 2.4951-2.4951 0-1.3672-1.0937-2.4267-2.4951-2.4267h-21=
.8066c-1.4356 0-2.4952 1.0595-2.4952 2.4267 0 1.4014 1.0596 2.4951 2.4952 2=
.4951zm0 11.4161h21.8066c1.4014 0 2.4951-1.0938 2.4951-2.461s-1.0937-2.4951=
-2.4951-2.4951h-21.8066c-1.4356 0-2.4952 1.1279-2.4952 2.4951s1.0596 2.461 =
2.4952 2.461z"></path></svg></div></button></div></div><div style=3D"positi=
on: relative; height: 1337.5px;"><div style=3D"overflow:hidden;"><div style=
=3D"height: 1317.5px; transform: translateX(0px);" class=3D"viewer-page vie=
wer-container active" height=3D"1317.5" data-v-dd20de80=3D""><!----><div st=
yle=3D"transform: scale(2.21429);" class=3D"page-24 pf w0 h0" data-v-dd20de=
80=3D""><div id=3D"pf18" class=3D"pf w0 h0" data-page-no=3D"18"><div class=
=3D"pc pc18 w0 h0"><div class=3D"bi x0 y0 w1 h1" alt=3D"" style=3D"backgrou=
nd: url('/viewer/14/2827814/24/bg18.webp'); background-size: contain;"></di=
v></div><div class=3D"pi" data-data=3D"{&quot;ctm&quot;:[1.000000,0.000000,=
0.000000,1.000000,0.000000,0.472443]}"></div></div>
</div><div class=3D"zoom" data-v-dd20de80=3D"" data-v-ac8a74d0=3D""><!---->=
</div></div></div><div class=3D"glide__arrows" data-glide-el=3D"controls"><=
a aria-current=3D"page" href=3D"https://www.usermanuals.au/hisense/hr6bmff5=
18bw/manual?p=3D23" class=3D"router-link-active router-link-exact-active gl=
ide__arrow glide__arrow--left"><div class=3D"app-icon size-null chevron.lef=
t"><svg width=3D"100" height=3D"100" viewBox=3D"0 0 100 100" fill=3D"none" =
xmlns=3D"http://www.w3.org/2000/svg">
<path d=3D"M27.4756 50.3223C27.4756 51.7236 27.9883 52.8857 29.1162 53.9795=
L55.4346 79.7168C56.2891 80.6055 57.3828 81.0498 58.6475 81.0498C61.2109 81=
.0498 63.2617 78.999 63.2617 76.4355C63.2617 75.1367 62.749 74.0088 61.8262=
 73.0859L38.4473 50.2881L61.8262 27.5586C62.749 26.6357 63.2617 25.4736 63.=
2617 24.209C63.2617 21.6455 61.2109 19.6289 58.6475 19.6289C57.3486 19.6289=
 56.2891 20.0391 55.4346 20.9277L29.1162 46.665C27.9883 47.7588 27.5098 48.=
9209 27.4756 50.3223Z"></path>
</svg>
</div></a><a aria-current=3D"page" href=3D"https://www.usermanuals.au/hisen=
se/hr6bmff518bw/manual" class=3D"router-link-active router-link-exact-activ=
e glide__arrow glide__arrow--right"><div class=3D"app-icon size-null chevro=
n.right"><svg width=3D"100" height=3D"100" viewBox=3D"0 0 100 100" fill=3D"=
none" xmlns=3D"http://www.w3.org/2000/svg">
<path d=3D"M72.4902 50.3223C72.4902 48.9209 71.9775 47.7588 70.8838 46.665L=
44.5654 20.9277C43.6768 20.0391 42.6172 19.6289 41.3525 19.6289C38.7891 19.=
6289 36.7041 21.6455 36.7041 24.209C36.7041 25.4736 37.251 26.6357 38.1738 =
27.5586L61.5527 50.2881L38.1738 73.0859C37.251 74.0088 36.7041 75.1367 36.7=
041 76.4355C36.7041 78.999 38.7891 81.0498 41.3525 81.0498C42.6172 81.0498 =
43.6768 80.6055 44.5654 79.7168L70.8838 53.9795C72.0117 52.8857 72.4902 51.=
7236 72.4902 50.3223Z"></path>
</svg>
</div></a></div></div><nav class=3D"viewer-toolbar mb-4" data-v-7feed808=3D=
""><div class=3D"viewer-toolbar__main" data-v-7feed808=3D""><a aria-current=
=3D"page" href=3D"https://www.usermanuals.au/hisense/hr6bmff518bw/manual?p=
=3D23" class=3D"router-link-active router-link-exact-active viewer-toolbar_=
_arrow" data-v-7feed808=3D""><div class=3D"app-icon size-null chevron.left"=
 data-v-7feed808=3D""><svg width=3D"100" height=3D"100" viewBox=3D"0 0 100 =
100" fill=3D"none" xmlns=3D"http://www.w3.org/2000/svg">
<path d=3D"M27.4756 50.3223C27.4756 51.7236 27.9883 52.8857 29.1162 53.9795=
L55.4346 79.7168C56.2891 80.6055 57.3828 81.0498 58.6475 81.0498C61.2109 81=
.0498 63.2617 78.999 63.2617 76.4355C63.2617 75.1367 62.749 74.0088 61.8262=
 73.0859L38.4473 50.2881L61.8262 27.5586C62.749 26.6357 63.2617 25.4736 63.=
2617 24.209C63.2617 21.6455 61.2109 19.6289 58.6475 19.6289C57.3486 19.6289=
 56.2891 20.0391 55.4346 20.9277L29.1162 46.665C27.9883 47.7588 27.5098 48.=
9209 27.4756 50.3223Z"></path>
</svg>
</div></a><div class=3D"viewer-toolbar__dropdown" data-v-7feed808=3D""><div=
 class=3D"viewer-toolbar__dropdown-container" data-v-7feed808=3D""><button =
class=3D"btn" data-v-7feed808=3D"">24 <span data-v-7feed808=3D"">/ 24 <div =
class=3D"app-icon size-null chevron.down" data-v-7feed808=3D""><svg width=
=3D"100" height=3D"100" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=3D"http=
://www.w3.org/2000/svg">
<path d=3D"M50 69.4629C51.4014 69.4629 52.5977 68.916 53.6914 67.7881L79.39=
45 41.5039C80.2832 40.6152 80.7275 39.5557 80.7275 38.291C80.7275 35.7275 7=
8.6768 33.6426 76.1133 33.6426C74.8828 33.6426 73.6865 34.1895 72.7637 35.1=
123L50.0342 58.4912L27.2363 35.1123C26.3135 34.1895 25.1514 33.6426 23.8525=
 33.6426C21.3232 33.6426 19.2725 35.7275 19.2725 38.291C19.2725 39.5557 19.=
751 40.6152 20.6055 41.5039L46.3086 67.8223C47.4365 68.916 48.5986 69.4629 =
50 69.4629Z"></path>
</svg>
</div></span></button><!----></div></div><a aria-current=3D"page" href=3D"h=
ttps://www.usermanuals.au/hisense/hr6bmff518bw/manual" class=3D"router-link=
-active router-link-exact-active viewer-toolbar__arrow" data-v-7feed808=3D"=
"><div class=3D"app-icon size-null chevron.right" data-v-7feed808=3D""><svg=
 width=3D"100" height=3D"100" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=
=3D"http://www.w3.org/2000/svg">
<path d=3D"M72.4902 50.3223C72.4902 48.9209 71.9775 47.7588 70.8838 46.665L=
44.5654 20.9277C43.6768 20.0391 42.6172 19.6289 41.3525 19.6289C38.7891 19.=
6289 36.7041 21.6455 36.7041 24.209C36.7041 25.4736 37.251 26.6357 38.1738 =
27.5586L61.5527 50.2881L38.1738 73.0859C37.251 74.0088 36.7041 75.1367 36.7=
041 76.4355C36.7041 78.999 38.7891 81.0498 41.3525 81.0498C42.6172 81.0498 =
43.6768 80.6055 44.5654 79.7168L70.8838 53.9795C72.0117 52.8857 72.4902 51.=
7236 72.4902 50.3223Z"></path>
</svg>
</div></a></div><div class=3D"viewer-toolbar__slider" data-v-7feed808=3D"">=
<div class=3D"slider" data-v-7feed808=3D"" data-v-b5a7cda6=3D""><div class=
=3D"slider__wrapper" data-v-b5a7cda6=3D""><div style=3D"left: 528px;" class=
=3D"slider__label" data-v-b5a7cda6=3D"">Page: 24</div><div class=3D"slider_=
_track" data-v-b5a7cda6=3D""></div><input value=3D"1" max=3D"24" class=3D"s=
lider__input" type=3D"range" min=3D"1" step=3D"1" data-v-b5a7cda6=3D""></di=
v></div></div></nav></div><!----><iframe id=3D"print-frame" class=3D"d-none=
"></iframe></div><div class=3D"grid" data-v-8e81d043=3D""><div class=3D"ad-=
placeholder my-3" data-v-8e81d043=3D"" data-v-5e860da6=3D""><!----><ins dat=
a-ad-unit=3D"hz_manual_bottom" data-ad-channel=3D"4152078287" data-ad-regio=
n=3D"page-0.08738777767808426" data-ad-format=3D"auto" data-ad-slot=3D"7826=
788708" data-ad-client=3D"ca-pub-4193547076534677" data-ad-layout-key=3D"" =
data-full-width-responsive=3D"false" style=3D"display:block;height:120px !i=
mportant;" class=3D"adsbygoogle" data-v-8e81d043=3D"" data-v-5e860da6=3D"">=
</ins></div><div data-v-8e81d043=3D"" data-v-a411a370=3D""><!----></div><di=
v class=3D"manual__description text-center" data-v-8e81d043=3D""><h2 data-v=
-8e81d043=3D"">Manual</h2><p data-v-8e81d043=3D"">View the manual for the H=
isense HR6BMFF518BW here, for free. This manual comes under the category re=
frigerators and has been rated by 1 people with an average of a 7.5. This m=
anual is available in the following languages: English. Do you have a quest=
ion about the Hisense HR6BMFF518BW or do you need help?</p><a href=3D"https=
://www.usermanuals.au/hisense/hr6bmff518bw/manual?p=3D24#" data-v-8e81d043=
=3D"">Ask your question here</a></div><!----><div class=3D"comments" data-v=
-8e81d043=3D"" data-v-01df5956=3D""><div class=3D"comments__hero" data-v-01=
df5956=3D"" data-protonpass-form=3D""><img src=3D"https://www.usermanuals.a=
u/images/comment-section.svg" loading=3D"lazy" data-v-01df5956=3D""><h2 dat=
a-v-01df5956=3D"">Need help?</h2><p data-v-01df5956=3D"">Do you have a ques=
tion about the Hisense  and is the answer not in the manual?</p><input plac=
eholder=3D"Type your question here ..." class=3D"primary lg block" type=3D"=
text" data-v-01df5956=3D""><button class=3D"primary lg block mt-2 mb-3" dat=
a-v-01df5956=3D"">Ask a question</button><ul data-v-01df5956=3D""><!--[--><=
li data-v-01df5956=3D"">Receive updates on solutions </li><li data-v-01df59=
56=3D"">Get answers to your question</li><!--]--></ul></div><div class=3D"c=
ontainer" data-v-01df5956=3D""><h2 data-v-01df5956=3D"">Question and answer=
</h2><span data-v-01df5956=3D"">Number of questions: 1</span></div><!----><=
!----><div id=3D"comments" class=3D"comments__container" data-v-01df5956=3D=
""><!--[--><!--[--><div class=3D"mt-2" data-v-01df5956=3D""><div class=3D"a=
d-placeholder my-4" data-v-01df5956=3D"" data-v-5e860da6=3D""><!----><ins d=
ata-ad-unit=3D"hz_manual_comments_1" data-ad-channel=3D"4152078287" data-ad=
-region=3D"page-0.3111608683521938" data-ad-format=3D"horizontal" data-ad-s=
lot=3D"4322946949" data-ad-client=3D"ca-pub-4193547076534677" data-ad-layou=
t-key=3D"" data-full-width-responsive=3D"false" style=3D"display:block;" cl=
ass=3D"adsbygoogle" data-v-01df5956=3D"" data-v-5e860da6=3D""></ins></div><=
div data-v-01df5956=3D"" data-v-a411a370=3D""><!----></div></div><div class=
=3D"comment" data-v-01df5956=3D"" data-v-282eccf6=3D""><div class=3D"commen=
t__header" data-v-282eccf6=3D""><div class=3D"comment-avatar" style=3D"back=
ground:#FF7CBE;" data-v-282eccf6=3D"" data-v-d8374c2e=3D"">B</div><div clas=
s=3D"d-flex flex-column" data-v-282eccf6=3D""><strong data-v-282eccf6=3D"">=
Bradley Horn</strong><small class=3D"text-muted" data-v-282eccf6=3D"">28 Ma=
rch 2025</small></div></div><div class=3D"comment__body" data-v-282eccf6=3D=
""><div data-v-282eccf6=3D""><p data-v-282eccf6=3D"">My fridge is not cold =
enough</p><!----><!----><!----></div></div><div class=3D"comment__footer" d=
ata-v-282eccf6=3D""><button class=3D"outline-primary like sm" data-v-282ecc=
f6=3D""><div class=3D"app-icon size-null thumbs-up" data-v-282eccf6=3D""><s=
vg width=3D"16" height=3D"16" viewBox=3D"0 0 16 16" fill=3D"none" xmlns=3D"=
http://www.w3.org/2000/svg"><path d=3D"M8.759 1.104 C 8.344 1.205,7.888 1.5=
28,7.668 1.878 C 7.533 2.093,7.470 2.268,7.373 2.701 C 7.237 3.308,7.137 3.=
564,6.885 3.950 C 6.714 4.212,6.483 4.456,6.105 4.775 C 5.773 5.055,5.605 5=
.229,5.426 5.480 C 5.310 5.642,5.067 6.100,5.067 6.156 C 5.067 6.228,5.022 =
6.175,4.949 6.020 C 4.791 5.679,4.385 5.362,4.005 5.283 C 3.814 5.243,1.946=
 5.243,1.755 5.283 C 1.306 5.376,0.911 5.729,0.743 6.187 L 0.680 6.360 0.68=
0 9.573 L 0.680 12.787 0.740 12.945 C 0.904 13.376,1.273 13.716,1.694 13.82=
6 C 1.925 13.886,3.835 13.886,4.066 13.826 C 4.488 13.716,4.861 13.371,5.02=
0 12.943 C 5.073 12.801,5.080 12.721,5.093 12.067 L 5.107 11.347 5.194 11.5=
14 C 5.392 11.893,5.666 12.243,5.934 12.462 C 6.195 12.674,7.344 13.434,7.5=
94 13.560 C 7.859 13.693,8.192 13.796,8.492 13.838 C 8.618 13.856,9.272 13.=
866,10.195 13.866 C 11.857 13.866,11.906 13.862,12.248 13.696 C 12.481 13.5=
84,12.748 13.367,12.897 13.170 C 13.102 12.898,13.199 12.656,13.272 12.237 =
C 13.290 12.132,13.314 12.098,13.440 11.996 C 13.889 11.632,14.129 11.091,1=
4.101 10.509 L 14.089 10.251 14.221 10.160 C 14.495 9.971,14.753 9.605,14.8=
70 9.241 C 14.923 9.073,14.939 8.968,14.941 8.760 C 14.943 8.461,14.910 8.2=
89,14.794 8.007 L 14.715 7.813 14.809 7.682 C 15.113 7.259,15.225 6.706,15.=
108 6.202 C 14.972 5.614,14.489 5.104,13.880 4.903 C 13.695 4.842,13.681 4.=
842,12.084 4.833 C 10.587 4.824,10.476 4.820,10.489 4.778 C 10.497 4.754,10=
.559 4.575,10.626 4.381 C 10.881 3.655,11.008 2.882,10.935 2.501 C 10.894 2=
.286,10.704 1.894,10.551 1.710 C 10.200 1.287,9.712 1.064,9.149 1.069 C 9.0=
08 1.070,8.833 1.086,8.759 1.104 M9.509 2.207 C 9.627 2.268,9.758 2.402,9.8=
33 2.540 C 9.909 2.681,9.894 3.012,9.792 3.432 C 9.619 4.139,9.391 4.671,8.=
925 5.453 L 8.671 5.880 11.102 5.893 L 13.533 5.907 13.686 5.981 C 13.947 6=
.110,14.080 6.330,14.080 6.634 C 14.080 6.834,14.004 7.030,13.882 7.145 C 1=
3.847 7.178,13.630 7.305,13.400 7.426 L 12.982 7.648 13.313 7.914 C 13.804 =
8.310,13.871 8.408,13.871 8.720 C 13.871 8.934,13.819 9.070,13.685 9.209 C =
13.538 9.362,13.424 9.408,12.982 9.493 C 12.761 9.536,12.575 9.576,12.569 9=
.582 C 12.563 9.588,12.648 9.744,12.759 9.928 C 12.870 10.112,12.979 10.313=
,13.000 10.374 C 13.082 10.605,13.032 10.887,12.877 11.076 C 12.780 11.193,=
12.631 11.273,12.318 11.374 L 12.089 11.448 12.151 11.744 C 12.216 12.055,1=
2.214 12.197,12.142 12.373 C 12.094 12.492,11.937 12.655,11.800 12.729 C 11=
.694 12.786,11.682 12.787,10.147 12.787 C 8.412 12.787,8.452 12.790,8.040 1=
2.588 C 7.923 12.531,7.560 12.302,7.234 12.080 C 6.788 11.776,6.597 11.629,=
6.461 11.482 C 6.244 11.250,6.081 10.954,5.995 10.640 C 5.933 10.414,5.933 =
10.411,5.934 8.680 C 5.935 6.981,5.936 6.943,5.993 6.740 C 6.122 6.280,6.34=
2 5.961,6.785 5.590 C 7.729 4.799,8.177 4.059,8.428 2.878 C 8.460 2.725,8.5=
08 2.558,8.534 2.507 C 8.599 2.381,8.735 2.255,8.880 2.187 C 9.038 2.114,9.=
346 2.123,9.509 2.207 M3.868 6.360 C 4.036 6.448,4.027 6.262,4.027 9.565 C =
4.026 12.525,4.026 12.575,3.973 12.661 C 3.944 12.709,3.889 12.760,3.852 12=
.774 C 3.812 12.789,3.401 12.800,2.877 12.800 C 2.119 12.800,1.958 12.793,1=
.892 12.760 C 1.724 12.673,1.733 12.857,1.733 9.560 C 1.733 6.274,1.725 6.4=
50,1.887 6.362 C 1.997 6.302,3.753 6.301,3.868 6.360 "></path></svg>
</div> 0</button><button class=3D"outline-primary like sm me-2" data-v-282e=
ccf6=3D""><div class=3D"app-icon size-null thumbs-down" data-v-282eccf6=3D"=
"><svg width=3D"16" height=3D"17" viewBox=3D"0 0 16 17" fill=3D"none" xmlns=
=3D"http://www.w3.org/2000/svg"><path d=3D"M2.000 3.229 C 1.750 3.272,1.483=
 3.418,1.284 3.620 C 1.186 3.719,1.074 3.861,1.034 3.935 C 0.874 4.236,0.88=
0 4.093,0.880 7.536 C 0.880 10.605,0.881 10.692,0.933 10.859 C 1.077 11.327=
,1.487 11.699,1.969 11.801 C 2.191 11.848,3.928 11.856,4.175 11.811 C 4.577=
 11.738,4.975 11.430,5.164 11.047 C 5.202 10.970,5.237 10.907,5.242 10.907 =
C 5.247 10.907,5.299 11.009,5.357 11.133 C 5.575 11.598,5.830 11.912,6.336 =
12.336 C 6.687 12.630,6.888 12.842,7.064 13.102 C 7.326 13.491,7.444 13.789=
,7.587 14.427 C 7.739 15.103,7.936 15.423,8.382 15.715 C 8.735 15.947,8.981=
 16.025,9.362 16.026 C 10.051 16.028,10.662 15.653,10.968 15.040 C 11.105 1=
4.766,11.172 14.508,11.173 14.253 C 11.173 13.938,10.955 13.008,10.763 12.5=
12 L 10.674 12.280 12.264 12.265 C 13.802 12.250,13.860 12.247,14.049 12.19=
1 C 14.305 12.114,14.464 12.031,14.660 11.874 C 15.102 11.520,15.321 11.098=
,15.352 10.546 C 15.375 10.130,15.256 9.740,14.998 9.385 L 14.910 9.263 14.=
976 9.124 C 15.089 8.889,15.123 8.745,15.138 8.440 C 15.148 8.223,15.141 8.=
100,15.110 7.967 C 15.019 7.581,14.800 7.231,14.488 6.974 L 14.307 6.824 14=
.304 6.512 C 14.300 6.059,14.200 5.740,13.961 5.414 C 13.863 5.281,13.656 5=
.078,13.543 5.004 C 13.485 4.966,13.472 4.933,13.459 4.793 C 13.406 4.241,1=
3.024 3.682,12.520 3.422 C 12.120 3.215,12.159 3.219,10.573 3.207 C 9.046 3=
.195,8.732 3.211,8.330 3.318 C 7.947 3.421,7.688 3.559,6.960 4.046 C 6.262 =
4.512,5.958 4.756,5.754 5.013 C 5.605 5.201,5.427 5.486,5.367 5.632 C 5.320=
 5.747,5.320 5.746,5.305 5.040 C 5.291 4.399,5.284 4.317,5.229 4.160 C 5.15=
1 3.936,5.088 3.834,4.911 3.647 C 4.736 3.462,4.577 3.358,4.347 3.276 C 4.1=
79 3.217,4.143 3.215,3.160 3.209 C 2.603 3.207,2.081 3.216,2.000 3.229 M4.0=
24 4.302 C 4.074 4.319,4.140 4.367,4.171 4.409 C 4.227 4.484,4.227 4.487,4.=
234 7.465 C 4.242 10.690,4.246 10.594,4.098 10.704 C 4.026 10.758,3.988 10.=
760,3.138 10.768 C 2.187 10.777,2.095 10.768,2.001 10.649 C 1.947 10.580,1.=
947 10.566,1.947 7.536 C 1.947 4.558,1.948 4.491,1.999 4.407 C 2.030 4.356,=
2.082 4.315,2.126 4.305 C 2.167 4.296,2.212 4.285,2.227 4.280 C 2.241 4.275=
,2.631 4.271,3.093 4.270 C 3.677 4.270,3.961 4.279,4.024 4.302 M11.973 4.33=
7 C 12.223 4.454,12.399 4.719,12.400 4.977 C 12.400 5.052,12.376 5.231,12.3=
46 5.375 C 12.317 5.518,12.296 5.637,12.300 5.638 C 12.304 5.640,12.430 5.6=
81,12.580 5.729 C 12.901 5.832,13.050 5.934,13.150 6.118 C 13.315 6.420,13.=
278 6.629,12.974 7.135 L 12.751 7.507 13.185 7.591 C 13.663 7.683,13.728 7.=
708,13.872 7.858 C 14.006 7.996,14.079 8.168,14.079 8.344 C 14.081 8.657,13=
.980 8.802,13.491 9.191 C 13.331 9.318,13.200 9.429,13.200 9.438 C 13.200 9=
.446,13.363 9.536,13.562 9.637 C 14.018 9.870,14.102 9.931,14.196 10.104 C =
14.393 10.466,14.264 10.912,13.908 11.102 L 13.800 11.160 11.329 11.173 L 8=
.858 11.187 9.059 11.520 C 9.616 12.441,9.831 12.941,10.015 13.747 C 10.137=
 14.284,10.108 14.528,9.895 14.741 C 9.740 14.896,9.613 14.946,9.373 14.946=
 C 9.124 14.946,9.000 14.894,8.847 14.724 C 8.714 14.578,8.708 14.563,8.627=
 14.187 C 8.377 13.035,7.896 12.243,6.992 11.499 C 6.661 11.227,6.485 11.01=
8,6.347 10.733 C 6.140 10.305,6.147 10.389,6.147 8.400 C 6.147 6.393,6.138 =
6.488,6.364 6.029 C 6.544 5.661,6.776 5.439,7.440 4.996 C 8.102 4.554,8.255=
 4.466,8.502 4.383 C 8.799 4.283,9.005 4.272,10.493 4.276 C 11.820 4.280,11=
.856 4.281,11.973 4.337 "></path></svg>
</div></button><button class=3D"outline-primary sm" data-v-282eccf6=3D""><d=
iv class=3D"app-icon size-null hand" data-v-282eccf6=3D""><svg width=3D"15"=
 height=3D"16" viewBox=3D"0 0 15 16" fill=3D"none" xmlns=3D"http://www.w3.o=
rg/2000/svg"><path d=3D"M8.348 0.994 C 8.112 1.147,8.062 1.462,8.241 1.675 =
C 8.336 1.788,8.423 1.823,8.732 1.875 C 9.116 1.940,9.357 2.059,9.582 2.297=
 C 9.797 2.526,9.912 2.759,9.964 3.075 C 9.985 3.206,10.014 3.350,10.029 3.=
396 C 10.126 3.702,10.518 3.811,10.760 3.599 C 10.927 3.452,10.957 3.294,10=
.889 2.913 C 10.793 2.372,10.609 2.010,10.238 1.635 C 9.797 1.190,9.319 0.9=
73,8.700 0.935 C 8.468 0.921,8.460 0.922,8.348 0.994 M5.754 1.611 C 5.327 1=
.715,4.902 2.053,4.705 2.447 C 4.647 2.562,4.600 2.671,4.600 2.690 C 4.600 =
2.736,4.592 2.735,4.414 2.664 C 3.652 2.360,2.720 2.734,2.364 3.487 C 2.252=
 3.722,2.213 3.913,2.215 4.213 C 2.217 4.479,2.242 4.596,2.363 4.906 C 2.37=
5 4.936,2.368 4.950,2.343 4.950 C 2.260 4.950,1.902 5.156,1.757 5.287 C 1.4=
13 5.598,1.220 6.028,1.218 6.488 C 1.216 6.775,1.253 6.932,1.390 7.218 C 1.=
481 7.408,1.541 7.491,1.730 7.689 C 1.910 7.877,1.952 7.934,1.923 7.951 C 1=
.904 7.964,1.877 7.974,1.865 7.975 C 1.807 7.976,1.473 8.211,1.345 8.342 C =
1.165 8.526,1.041 8.732,0.957 8.988 C 0.866 9.267,0.861 9.653,0.946 9.925 C=
 1.044 10.240,1.139 10.388,1.461 10.725 C 2.108 11.404,5.150 14.443,5.351 1=
4.611 C 6.456 15.536,7.960 15.982,9.405 15.813 C 11.777 15.535,13.674 13.76=
0,14.112 11.406 C 14.192 10.975,14.200 10.676,14.200 7.941 C 14.199 4.911,1=
4.204 5.013,14.045 4.666 C 13.778 4.087,13.208 3.724,12.565 3.725 C 12.096 =
3.726,11.756 3.867,11.425 4.199 C 11.085 4.539,10.955 4.845,10.931 5.358 L =
10.918 5.654 9.015 3.755 L 7.113 1.856 6.900 1.753 C 6.783 1.697,6.620 1.63=
4,6.538 1.613 C 6.358 1.567,5.941 1.566,5.754 1.611 M6.389 2.541 C 6.502 2.=
578,6.710 2.778,8.813 4.876 C 10.078 6.137,11.149 7.188,11.193 7.210 C 11.2=
37 7.232,11.333 7.250,11.406 7.250 C 11.573 7.250,11.719 7.164,11.804 7.015=
 C 11.862 6.914,11.863 6.899,11.875 6.025 L 11.888 5.138 11.965 5.000 C 12.=
181 4.615,12.689 4.538,13.045 4.837 C 13.087 4.872,13.150 4.959,13.186 5.03=
0 L 13.250 5.160 13.250 8.014 C 13.250 10.794,13.249 10.877,13.198 11.176 C=
 13.039 12.114,12.619 12.929,11.950 13.601 C 11.192 14.363,10.231 14.806,9.=
121 14.903 C 8.014 15.000,6.809 14.614,5.925 13.878 C 5.644 13.644,1.980 9.=
929,1.906 9.804 C 1.809 9.638,1.798 9.376,1.880 9.196 C 1.958 9.026,2.076 8=
.908,2.246 8.830 C 2.361 8.778,2.407 8.771,2.559 8.780 C 2.657 8.787,2.777 =
8.812,2.825 8.836 C 2.873 8.860,3.458 9.423,4.125 10.087 C 4.792 10.751,5.3=
71 11.311,5.413 11.331 C 5.545 11.396,5.625 11.404,5.759 11.364 C 6.036 11.=
284,6.172 10.993,6.052 10.738 C 6.020 10.669,5.416 10.050,4.151 8.787 C 2.7=
59 7.399,2.281 6.907,2.233 6.814 C 2.089 6.535,2.138 6.234,2.363 6.009 C 2.=
583 5.789,2.889 5.741,3.165 5.884 C 3.256 5.930,3.758 6.418,5.125 7.787 C 6=
.136 8.800,7.002 9.648,7.050 9.673 C 7.185 9.742,7.264 9.753,7.391 9.721 C =
7.636 9.660,7.783 9.408,7.722 9.156 C 7.695 9.043,7.584 8.928,5.474 6.813 C=
 4.254 5.589,3.234 4.548,3.209 4.500 C 3.174 4.434,3.163 4.358,3.163 4.188 =
C 3.163 3.987,3.170 3.950,3.231 3.846 C 3.310 3.712,3.421 3.611,3.563 3.547=
 C 3.703 3.483,3.977 3.485,4.125 3.552 C 4.212 3.591,4.742 4.106,6.463 5.82=
3 C 8.578 7.934,8.693 8.045,8.806 8.072 C 9.154 8.156,9.466 7.845,9.375 7.5=
06 C 9.345 7.395,9.217 7.261,7.450 5.488 C 6.171 4.204,5.543 3.556,5.514 3.=
491 C 5.402 3.244,5.447 2.945,5.629 2.739 C 5.826 2.515,6.098 2.444,6.389 2=
.541 M0.520 11.673 C 0.399 11.712,0.295 11.806,0.244 11.921 C 0.202 12.016,=
0.196 12.065,0.210 12.228 C 0.281 13.080,0.779 13.830,1.513 14.188 C 1.860 =
14.357,2.374 14.475,2.613 14.439 C 2.848 14.403,3.032 14.143,2.988 13.907 C=
 2.943 13.662,2.811 13.566,2.425 13.498 C 2.093 13.439,1.989 13.401,1.769 1=
3.257 C 1.432 13.036,1.234 12.708,1.152 12.231 C 1.098 11.919,1.045 11.807,=
0.913 11.725 C 0.818 11.666,0.622 11.641,0.520 11.673 "></path></svg>
</div> Same question</button><div class=3D"comment__cta" data-v-282eccf6=3D=
""><div class=3D"input" data-v-282eccf6=3D"">Type your response here <butto=
n class=3D"btn primary" data-v-282eccf6=3D"">Add my comment</button></div><=
/div></div><!----></div><!--]--><!--]--></div><!----><button class=3D"mt-3 =
primary lg block" data-v-01df5956=3D"">Ask a question</button></div><div da=
ta-v-8e81d043=3D"" data-v-a411a370=3D""><!----></div><div class=3D"ad-place=
holder my-3" data-v-8e81d043=3D"" data-v-5e860da6=3D""><!----><ins data-ad-=
unit=3D"hz_manual_bottom_2" data-ad-channel=3D"4152078287" data-ad-region=
=3D"page-0.8333793633581565" data-ad-format=3D"auto" data-ad-slot=3D"387124=
7739" data-ad-client=3D"ca-pub-4193547076534677" data-ad-layout-key=3D"" da=
ta-full-width-responsive=3D"true" style=3D"display:block;" class=3D"adsbygo=
ogle" data-v-8e81d043=3D"" data-v-5e860da6=3D""></ins></div><div class=3D"g=
allery" data-v-8e81d043=3D"" data-v-412cb6ea=3D""><h3 class=3D"mb-4" data-v=
-412cb6ea=3D"">Product Images (2)</h3><div class=3D"carousel" data-v-412cb6=
ea=3D""><div class=3D"carousel__prev" data-v-412cb6ea=3D""><div class=3D"ap=
p-icon size-null chevron.left" data-v-412cb6ea=3D""><svg width=3D"100" heig=
ht=3D"100" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=3D"http://www.w3.org=
/2000/svg">
<path d=3D"M27.4756 50.3223C27.4756 51.7236 27.9883 52.8857 29.1162 53.9795=
L55.4346 79.7168C56.2891 80.6055 57.3828 81.0498 58.6475 81.0498C61.2109 81=
.0498 63.2617 78.999 63.2617 76.4355C63.2617 75.1367 62.749 74.0088 61.8262=
 73.0859L38.4473 50.2881L61.8262 27.5586C62.749 26.6357 63.2617 25.4736 63.=
2617 24.209C63.2617 21.6455 61.2109 19.6289 58.6475 19.6289C57.3486 19.6289=
 56.2891 20.0391 55.4346 20.9277L29.1162 46.665C27.9883 47.7588 27.5098 48.=
9209 27.4756 50.3223Z"></path>
</svg>
</div></div><div class=3D"carousel__next" data-v-412cb6ea=3D""><div class=
=3D"app-icon size-null chevron.right" data-v-412cb6ea=3D""><svg width=3D"10=
0" height=3D"100" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=3D"http://www=
.w3.org/2000/svg">
<path d=3D"M72.4902 50.3223C72.4902 48.9209 71.9775 47.7588 70.8838 46.665L=
44.5654 20.9277C43.6768 20.0391 42.6172 19.6289 41.3525 19.6289C38.7891 19.=
6289 36.7041 21.6455 36.7041 24.209C36.7041 25.4736 37.251 26.6357 38.1738 =
27.5586L61.5527 50.2881L38.1738 73.0859C37.251 74.0088 36.7041 75.1367 36.7=
041 76.4355C36.7041 78.999 38.7891 81.0498 41.3525 81.0498C42.6172 81.0498 =
43.6768 80.6055 44.5654 79.7168L70.8838 53.9795C72.0117 52.8857 72.4902 51.=
7236 72.4902 50.3223Z"></path>
</svg>
</div></div><!--[--><div style=3D"" class=3D"carousel__item" data-v-412cb6e=
a=3D""><img src=3D"https://www.usermanuals.au/thumbs/products/l/1532132-his=
ense-hr6bmff518bw.webp" width=3D"500" height=3D"500" loading=3D"lazy" data-=
v-412cb6ea=3D""></div><div style=3D"display:none;" class=3D"carousel__item"=
 data-v-412cb6ea=3D""><img src=3D"https://www.usermanuals.au/thumbs/brands/=
l/2376-hisense_logo.webp" width=3D"500" height=3D"500" loading=3D"lazy" dat=
a-v-412cb6ea=3D""></div><!--]--></div><div class=3D"clearfix" data-v-412cb6=
ea=3D""><div class=3D"gallery__tile-container" data-v-412cb6ea=3D""><!--[--=
><div class=3D"image gallery__tile" data-v-412cb6ea=3D""><div class=3D"imag=
e" data-v-412cb6ea=3D""><img loading=3D"lazy" src=3D"https://www.usermanual=
s.au/thumbs/products/l/1532132-hisense-hr6bmff518bw.webp" alt=3D"Hisense HR=
6BMFF518BW #1" data-v-412cb6ea=3D""></div><!----><!----></div><div class=3D=
"image gallery__tile" data-v-412cb6ea=3D""><div class=3D"image" data-v-412c=
b6ea=3D""><img loading=3D"lazy" src=3D"https://www.usermanuals.au/thumbs/br=
ands/l/2376-hisense_logo.webp" alt=3D"Hisense logo" data-v-412cb6ea=3D""></=
div><!----><!----></div><!--]--></div></div></div><div class=3D"d-flex flex=
-grow-1 flex-column" data-v-8e81d043=3D"" data-v-998de295=3D""><h2 class=3D=
"pb-2" data-v-998de295=3D"">Hisense HR6BMFF518BW specifications</h2><p data=
-v-998de295=3D"">Below you will find the product specifications and the man=
ual specifications of the Hisense HR6BMFF518BW.</p><p style=3D"white-space:=
pre-wrap;" data-v-998de295=3D"">The Hisense HR6BMFF518BW refrigerator is a =
large capacity, French door fridge designed for families or households with=
 high storage needs. It features a clean white design and a spacious interi=
or layout that includes adjustable shelves and door bins for flexible stora=
ge options. The refrigerator is equipped with a multi-airflow system to ens=
ure even cooling throughout the compartments, helping to keep food fresh fo=
r longer periods.=20

The freezer section of the Hisense HR6BMFF518BW is located at the bottom of=
 the unit, providing easy access to frozen items without needing to bend do=
wn. The fridge also includes a convenient ice and water dispenser, allowing=
 users to enjoy chilled water or ice cubes at the touch of a button.=20

In terms of energy efficiency, the Hisense HR6BMFF518BW is designed to be p=
ower-saving with a high energy star rating. This makes it an environmentall=
y friendly option for those looking to reduce their electricity consumption=
.=20

Overall, the Hisense HR6BMFF518BW refrigerator is a reliable and practical =
choice for those in need of a large capacity fridge with modern features.</=
p><div class=3D"feature-logos pb-4" data-v-998de295=3D""><!--[--><!--]--></=
div><div id=3D"specs" class=3D"" data-v-998de295=3D""><div data-v-998de295=
=3D""><div class=3D"specs__highlight" data-v-998de295=3D""><!--[--><!--]-->=
</div><div class=3D"card mb-4" data-v-998de295=3D""><h5 class=3D"d-flex" da=
ta-v-998de295=3D""><div class=3D"app-icon size-null task-list-approve me-2"=
 data-v-998de295=3D""><svg enable-background=3D"new 0 0 24 24" viewBox=3D"0=
 0 24 24" xmlns=3D"http://www.w3.org/2000/svg"><path d=3D"m11.25 15.011c-.4=
14 0-.75-.336-.75-.75s.336-.75.75-.75h5.25c.414 0 .75.336.75.75s-.336.75-.7=
5.75z"></path><path d=3D"m11.25 19.511c-.414 0-.75-.336-.75-.75s.336-.75.75=
-.75h5.25c.414 0 .75.336.75.75s-.336.75-.75.75z"></path><circle cx=3D"8.25"=
 cy=3D"14.261" r=3D"1.125"></circle><path d=3D"m11.25 10.511c-.414 0-.75-.3=
36-.75-.75s.336-.75.75-.75h5.25c.414 0 .75.336.75.75s-.336.75-.75.75z"></pa=
th><circle cx=3D"8.25" cy=3D"9.761" r=3D"1.125"></circle><circle cx=3D"8.25=
" cy=3D"18.761" r=3D"1.125"></circle><path d=3D"m4.5 24.011c-1.241 0-2.25-1=
.009-2.25-2.25v-15.75c0-1.241 1.009-2.25 2.25-2.25h3.063c.361-2.128 2.247-3=
.75 4.437-3.75s4.076 1.621 4.437 3.75h3.063c1.241 0 2.25 1.009 2.25 2.25v15=
.75c0 1.241-1.009 2.25-2.25 2.25zm0-18.75c-.414 0-.75.336-.75.75v15.75c0 .4=
14.336.75.75.75h15c.414 0 .75-.336.75-.75v-15.75c0-.414-.336-.75-.75-.75h-3=
.75c-.414 0-.75-.336-.75-.75 0-1.654-1.346-3-3-3s-3 1.346-3 3c0 .414-.336.7=
5-.75.75z"></path><circle cx=3D"12" cy=3D"4.136" r=3D"1.125"></circle></svg=
></div> General</h5><table class=3D"table table-sm" data-v-998de295=3D""><t=
body data-v-998de295=3D""><tr data-v-998de295=3D""><td class=3D"text-muted"=
 data-v-998de295=3D"">Brand</td><td itemprop=3D"brand" data-v-998de295=3D""=
><a href=3D"https://www.usermanuals.au/hisense" class=3D"" data-v-998de295=
=3D"">Hisense</a></td></tr><tr data-v-998de295=3D""><td class=3D"text-muted=
" data-v-998de295=3D"">Model</td><td itemprop=3D"name" data-v-998de295=3D""=
>HR6BMFF518BW</td></tr><tr data-v-998de295=3D""><td class=3D"text-muted" da=
ta-v-998de295=3D"">Product</td><td itemprop=3D"category" data-v-998de295=3D=
""><a href=3D"https://www.usermanuals.au/kitchen-appliances/refrigerators" =
class=3D"" data-v-998de295=3D"">refrigerator</a></td></tr><!----><tr data-v=
-998de295=3D""><td class=3D"text-muted" data-v-998de295=3D"">Language</td><=
td data-v-998de295=3D"">English</td></tr><tr data-v-998de295=3D""><td class=
=3D"text-muted" data-v-998de295=3D"">Filetype</td><td data-v-998de295=3D"">=
User manual (PDF)</td></tr></tbody></table></div><!--[--><!--]--></div></di=
v><!----></div><div data-v-8e81d043=3D"" data-v-a411a370=3D""><!----></div>=
<div class=3D"ad-placeholder my-3" data-v-8e81d043=3D"" data-v-5e860da6=3D"=
"><!----><ins data-ad-unit=3D"hz_manual_bottom_3" data-ad-channel=3D"415207=
8287" data-ad-region=3D"page-0.5920672578193604" data-ad-format=3D"auto" da=
ta-ad-slot=3D"3412862983" data-ad-client=3D"ca-pub-4193547076534677" data-a=
d-layout-key=3D"" data-full-width-responsive=3D"true" style=3D"display:bloc=
k;" class=3D"adsbygoogle" data-v-8e81d043=3D"" data-v-5e860da6=3D""></ins><=
/div><div itemscope=3D"" itemtype=3D"https://schema.org/FAQPage" class=3D"f=
aq-listing" data-v-8e81d043=3D"" data-v-bf996add=3D""><h3 class=3D"pb-2 h1"=
 data-v-bf996add=3D"">Frequently Asked Questions</h3><p data-v-bf996add=3D"=
">Can't find the answer to your question in the manual? You may find the an=
swer to your question in the FAQs about the Hisense HR6BMFF518BW below.</p>=
<div class=3D"ad-placeholder my-3" data-v-bf996add=3D"" data-v-5e860da6=3D"=
"><!----><ins data-ad-unit=3D"hz_manual_faqs_1" data-ad-channel=3D"41520782=
87" data-ad-region=3D"page-0.9788697214810123" data-ad-format=3D"horizontal=
" data-ad-slot=3D"7715874968" data-ad-client=3D"ca-pub-4193547076534677" da=
ta-ad-layout-key=3D"" data-full-width-responsive=3D"false" style=3D"display=
:block;" class=3D"adsbygoogle" data-v-bf996add=3D"" data-v-5e860da6=3D""></=
ins></div><!----><!--[--><!--[--><div class=3D"faq-item" itemscope=3D"" ite=
mprop=3D"mainEntity" itemtype=3D"https://schema.org/Question" data-v-bf996a=
dd=3D"" data-v-867a1798=3D""><h4 class=3D"h5" itemprop=3D"text name" data-v=
-867a1798=3D""><!--[-->Is the manual of the Hisense HR6BMFF518BW available =
in English?<!--]--></h4><div itemscope=3D"" itemprop=3D"acceptedAnswer" ite=
mtype=3D"https://schema.org/Answer" data-v-867a1798=3D""><div itemprop=3D"t=
ext" data-v-867a1798=3D""><!--[-->Yes, the manual of the Hisense HR6BMFF518=
BW is available in English .<!--]--></div><div class=3D"faq-item__footer" d=
ata-v-867a1798=3D""><button class=3D"outline-primary like sm" data-v-867a17=
98=3D""><div class=3D"app-icon size-null thumbs-up" data-v-867a1798=3D""><s=
vg width=3D"16" height=3D"16" viewBox=3D"0 0 16 16" fill=3D"none" xmlns=3D"=
http://www.w3.org/2000/svg"><path d=3D"M8.759 1.104 C 8.344 1.205,7.888 1.5=
28,7.668 1.878 C 7.533 2.093,7.470 2.268,7.373 2.701 C 7.237 3.308,7.137 3.=
564,6.885 3.950 C 6.714 4.212,6.483 4.456,6.105 4.775 C 5.773 5.055,5.605 5=
.229,5.426 5.480 C 5.310 5.642,5.067 6.100,5.067 6.156 C 5.067 6.228,5.022 =
6.175,4.949 6.020 C 4.791 5.679,4.385 5.362,4.005 5.283 C 3.814 5.243,1.946=
 5.243,1.755 5.283 C 1.306 5.376,0.911 5.729,0.743 6.187 L 0.680 6.360 0.68=
0 9.573 L 0.680 12.787 0.740 12.945 C 0.904 13.376,1.273 13.716,1.694 13.82=
6 C 1.925 13.886,3.835 13.886,4.066 13.826 C 4.488 13.716,4.861 13.371,5.02=
0 12.943 C 5.073 12.801,5.080 12.721,5.093 12.067 L 5.107 11.347 5.194 11.5=
14 C 5.392 11.893,5.666 12.243,5.934 12.462 C 6.195 12.674,7.344 13.434,7.5=
94 13.560 C 7.859 13.693,8.192 13.796,8.492 13.838 C 8.618 13.856,9.272 13.=
866,10.195 13.866 C 11.857 13.866,11.906 13.862,12.248 13.696 C 12.481 13.5=
84,12.748 13.367,12.897 13.170 C 13.102 12.898,13.199 12.656,13.272 12.237 =
C 13.290 12.132,13.314 12.098,13.440 11.996 C 13.889 11.632,14.129 11.091,1=
4.101 10.509 L 14.089 10.251 14.221 10.160 C 14.495 9.971,14.753 9.605,14.8=
70 9.241 C 14.923 9.073,14.939 8.968,14.941 8.760 C 14.943 8.461,14.910 8.2=
89,14.794 8.007 L 14.715 7.813 14.809 7.682 C 15.113 7.259,15.225 6.706,15.=
108 6.202 C 14.972 5.614,14.489 5.104,13.880 4.903 C 13.695 4.842,13.681 4.=
842,12.084 4.833 C 10.587 4.824,10.476 4.820,10.489 4.778 C 10.497 4.754,10=
.559 4.575,10.626 4.381 C 10.881 3.655,11.008 2.882,10.935 2.501 C 10.894 2=
.286,10.704 1.894,10.551 1.710 C 10.200 1.287,9.712 1.064,9.149 1.069 C 9.0=
08 1.070,8.833 1.086,8.759 1.104 M9.509 2.207 C 9.627 2.268,9.758 2.402,9.8=
33 2.540 C 9.909 2.681,9.894 3.012,9.792 3.432 C 9.619 4.139,9.391 4.671,8.=
925 5.453 L 8.671 5.880 11.102 5.893 L 13.533 5.907 13.686 5.981 C 13.947 6=
.110,14.080 6.330,14.080 6.634 C 14.080 6.834,14.004 7.030,13.882 7.145 C 1=
3.847 7.178,13.630 7.305,13.400 7.426 L 12.982 7.648 13.313 7.914 C 13.804 =
8.310,13.871 8.408,13.871 8.720 C 13.871 8.934,13.819 9.070,13.685 9.209 C =
13.538 9.362,13.424 9.408,12.982 9.493 C 12.761 9.536,12.575 9.576,12.569 9=
.582 C 12.563 9.588,12.648 9.744,12.759 9.928 C 12.870 10.112,12.979 10.313=
,13.000 10.374 C 13.082 10.605,13.032 10.887,12.877 11.076 C 12.780 11.193,=
12.631 11.273,12.318 11.374 L 12.089 11.448 12.151 11.744 C 12.216 12.055,1=
2.214 12.197,12.142 12.373 C 12.094 12.492,11.937 12.655,11.800 12.729 C 11=
.694 12.786,11.682 12.787,10.147 12.787 C 8.412 12.787,8.452 12.790,8.040 1=
2.588 C 7.923 12.531,7.560 12.302,7.234 12.080 C 6.788 11.776,6.597 11.629,=
6.461 11.482 C 6.244 11.250,6.081 10.954,5.995 10.640 C 5.933 10.414,5.933 =
10.411,5.934 8.680 C 5.935 6.981,5.936 6.943,5.993 6.740 C 6.122 6.280,6.34=
2 5.961,6.785 5.590 C 7.729 4.799,8.177 4.059,8.428 2.878 C 8.460 2.725,8.5=
08 2.558,8.534 2.507 C 8.599 2.381,8.735 2.255,8.880 2.187 C 9.038 2.114,9.=
346 2.123,9.509 2.207 M3.868 6.360 C 4.036 6.448,4.027 6.262,4.027 9.565 C =
4.026 12.525,4.026 12.575,3.973 12.661 C 3.944 12.709,3.889 12.760,3.852 12=
.774 C 3.812 12.789,3.401 12.800,2.877 12.800 C 2.119 12.800,1.958 12.793,1=
.892 12.760 C 1.724 12.673,1.733 12.857,1.733 9.560 C 1.733 6.274,1.725 6.4=
50,1.887 6.362 C 1.997 6.302,3.753 6.301,3.868 6.360 "></path></svg>
</div> </button><button class=3D"outline-primary like sm me-2" data-v-867a1=
798=3D""><div class=3D"app-icon size-null thumbs-down" data-v-867a1798=3D""=
><svg width=3D"16" height=3D"17" viewBox=3D"0 0 16 17" fill=3D"none" xmlns=
=3D"http://www.w3.org/2000/svg"><path d=3D"M2.000 3.229 C 1.750 3.272,1.483=
 3.418,1.284 3.620 C 1.186 3.719,1.074 3.861,1.034 3.935 C 0.874 4.236,0.88=
0 4.093,0.880 7.536 C 0.880 10.605,0.881 10.692,0.933 10.859 C 1.077 11.327=
,1.487 11.699,1.969 11.801 C 2.191 11.848,3.928 11.856,4.175 11.811 C 4.577=
 11.738,4.975 11.430,5.164 11.047 C 5.202 10.970,5.237 10.907,5.242 10.907 =
C 5.247 10.907,5.299 11.009,5.357 11.133 C 5.575 11.598,5.830 11.912,6.336 =
12.336 C 6.687 12.630,6.888 12.842,7.064 13.102 C 7.326 13.491,7.444 13.789=
,7.587 14.427 C 7.739 15.103,7.936 15.423,8.382 15.715 C 8.735 15.947,8.981=
 16.025,9.362 16.026 C 10.051 16.028,10.662 15.653,10.968 15.040 C 11.105 1=
4.766,11.172 14.508,11.173 14.253 C 11.173 13.938,10.955 13.008,10.763 12.5=
12 L 10.674 12.280 12.264 12.265 C 13.802 12.250,13.860 12.247,14.049 12.19=
1 C 14.305 12.114,14.464 12.031,14.660 11.874 C 15.102 11.520,15.321 11.098=
,15.352 10.546 C 15.375 10.130,15.256 9.740,14.998 9.385 L 14.910 9.263 14.=
976 9.124 C 15.089 8.889,15.123 8.745,15.138 8.440 C 15.148 8.223,15.141 8.=
100,15.110 7.967 C 15.019 7.581,14.800 7.231,14.488 6.974 L 14.307 6.824 14=
.304 6.512 C 14.300 6.059,14.200 5.740,13.961 5.414 C 13.863 5.281,13.656 5=
.078,13.543 5.004 C 13.485 4.966,13.472 4.933,13.459 4.793 C 13.406 4.241,1=
3.024 3.682,12.520 3.422 C 12.120 3.215,12.159 3.219,10.573 3.207 C 9.046 3=
.195,8.732 3.211,8.330 3.318 C 7.947 3.421,7.688 3.559,6.960 4.046 C 6.262 =
4.512,5.958 4.756,5.754 5.013 C 5.605 5.201,5.427 5.486,5.367 5.632 C 5.320=
 5.747,5.320 5.746,5.305 5.040 C 5.291 4.399,5.284 4.317,5.229 4.160 C 5.15=
1 3.936,5.088 3.834,4.911 3.647 C 4.736 3.462,4.577 3.358,4.347 3.276 C 4.1=
79 3.217,4.143 3.215,3.160 3.209 C 2.603 3.207,2.081 3.216,2.000 3.229 M4.0=
24 4.302 C 4.074 4.319,4.140 4.367,4.171 4.409 C 4.227 4.484,4.227 4.487,4.=
234 7.465 C 4.242 10.690,4.246 10.594,4.098 10.704 C 4.026 10.758,3.988 10.=
760,3.138 10.768 C 2.187 10.777,2.095 10.768,2.001 10.649 C 1.947 10.580,1.=
947 10.566,1.947 7.536 C 1.947 4.558,1.948 4.491,1.999 4.407 C 2.030 4.356,=
2.082 4.315,2.126 4.305 C 2.167 4.296,2.212 4.285,2.227 4.280 C 2.241 4.275=
,2.631 4.271,3.093 4.270 C 3.677 4.270,3.961 4.279,4.024 4.302 M11.973 4.33=
7 C 12.223 4.454,12.399 4.719,12.400 4.977 C 12.400 5.052,12.376 5.231,12.3=
46 5.375 C 12.317 5.518,12.296 5.637,12.300 5.638 C 12.304 5.640,12.430 5.6=
81,12.580 5.729 C 12.901 5.832,13.050 5.934,13.150 6.118 C 13.315 6.420,13.=
278 6.629,12.974 7.135 L 12.751 7.507 13.185 7.591 C 13.663 7.683,13.728 7.=
708,13.872 7.858 C 14.006 7.996,14.079 8.168,14.079 8.344 C 14.081 8.657,13=
.980 8.802,13.491 9.191 C 13.331 9.318,13.200 9.429,13.200 9.438 C 13.200 9=
.446,13.363 9.536,13.562 9.637 C 14.018 9.870,14.102 9.931,14.196 10.104 C =
14.393 10.466,14.264 10.912,13.908 11.102 L 13.800 11.160 11.329 11.173 L 8=
.858 11.187 9.059 11.520 C 9.616 12.441,9.831 12.941,10.015 13.747 C 10.137=
 14.284,10.108 14.528,9.895 14.741 C 9.740 14.896,9.613 14.946,9.373 14.946=
 C 9.124 14.946,9.000 14.894,8.847 14.724 C 8.714 14.578,8.708 14.563,8.627=
 14.187 C 8.377 13.035,7.896 12.243,6.992 11.499 C 6.661 11.227,6.485 11.01=
8,6.347 10.733 C 6.140 10.305,6.147 10.389,6.147 8.400 C 6.147 6.393,6.138 =
6.488,6.364 6.029 C 6.544 5.661,6.776 5.439,7.440 4.996 C 8.102 4.554,8.255=
 4.466,8.502 4.383 C 8.799 4.283,9.005 4.272,10.493 4.276 C 11.820 4.280,11=
.856 4.281,11.973 4.337 "></path></svg>
</div></button></div></div></div><!----><!--]--><!--]--><p class=3D"mt-2 sm=
all" data-v-bf996add=3D"">Is your question not listed? <a href=3D"https://w=
ww.usermanuals.au/hisense/hr6bmff518bw/manual?p=3D24#" data-v-bf996add=3D""=
>Ask your question here</a></p></div><!----><!----><section class=3D"relate=
d-products" data-v-8e81d043=3D"" data-v-18a9b685=3D""><h2 class=3D"pb-2" da=
ta-v-18a9b685=3D"">Related product manuals</h2><div class=3D"mb-3" data-v-1=
8a9b685=3D"" data-v-3ad21d7d=3D""><!--[--><!--]--><div class=3D"product-lis=
ting" data-v-3ad21d7d=3D""><!--[--><!--[--><!----><div class=3D"product-lis=
ting__item d-flex justify-content-between align-items-center pl-0" data-v-3=
ad21d7d=3D""><a href=3D"https://www.usermanuals.au/hisense/hr6bmff453b/manu=
al" class=3D"d-flex w-100 text-dark text-decoration-none" data-v-3ad21d7d=
=3D""><div class=3D"smart-image product-listing__img" data-v-3ad21d7d=3D"" =
data-v-1460e71d=3D""><picture data-v-1460e71d=3D""><source srcset=3D"/thumb=
s/products/s/1532134-hisense-hr6bmff453b.webp" type=3D"image/webp" data-v-1=
460e71d=3D""><img src=3D"https://www.usermanuals.au/thumbs/products/s/15321=
34-hisense-hr6bmff453b.jpg" width=3D"100" height=3D"100" alt=3D"Hisense HR6=
BMFF453B" loading=3D"lazy" data-v-1460e71d=3D""></picture></div><div class=
=3D"product-listing__info" data-v-3ad21d7d=3D""><!--[--><h5 class=3D"h6" da=
ta-v-3ad21d7d=3D"">Hisense HR6BMFF453B</h5><!--]--><!--[--><span data-v-3ad=
21d7d=3D"">manual</span><span data-v-3ad21d7d=3D"">22 pages</span><!--]--><=
div class=3D"product-listing__rating" data-v-3ad21d7d=3D""><div style=3D"wi=
dth:50%;" class=3D"product-listing__rating-hover" data-v-3ad21d7d=3D""></di=
v></div></div></a></div><!--]--><!--[--><!----><div class=3D"product-listin=
g__item d-flex justify-content-between align-items-center pl-0" data-v-3ad2=
1d7d=3D""><a href=3D"https://www.usermanuals.au/hisense/hrbm482bw/manual" c=
lass=3D"d-flex w-100 text-dark text-decoration-none" data-v-3ad21d7d=3D""><=
div class=3D"smart-image product-listing__img" data-v-3ad21d7d=3D"" data-v-=
1460e71d=3D""><picture data-v-1460e71d=3D""><source srcset=3D"/thumbs/produ=
cts/s/1695399-hisense-hrbm482bw.webp" type=3D"image/webp" data-v-1460e71d=
=3D""><img src=3D"https://www.usermanuals.au/thumbs/products/s/1695399-hise=
nse-hrbm482bw.jpg" width=3D"100" height=3D"100" alt=3D"Hisense HRBM482BW" l=
oading=3D"lazy" data-v-1460e71d=3D""></picture></div><div class=3D"product-=
listing__info" data-v-3ad21d7d=3D""><!--[--><h5 class=3D"h6" data-v-3ad21d7=
d=3D"">Hisense HRBM482BW</h5><!--]--><!--[--><span data-v-3ad21d7d=3D"">man=
ual</span><span data-v-3ad21d7d=3D"">26 pages</span><!--]--><div class=3D"p=
roduct-listing__rating" data-v-3ad21d7d=3D""><div style=3D"width:50%;" clas=
s=3D"product-listing__rating-hover" data-v-3ad21d7d=3D""></div></div></div>=
</a></div><!--]--><!--[--><!----><div class=3D"product-listing__item d-flex=
 justify-content-between align-items-center pl-0" data-v-3ad21d7d=3D""><a h=
ref=3D"https://www.usermanuals.au/hisense/hr6bmff519b/manual" class=3D"d-fl=
ex w-100 text-dark text-decoration-none" data-v-3ad21d7d=3D""><div class=3D=
"smart-image product-listing__img" data-v-3ad21d7d=3D"" data-v-1460e71d=3D"=
"><picture data-v-1460e71d=3D""><source srcset=3D"/thumbs/products/s/140214=
7-hisense-hr6bmff519b.webp" type=3D"image/webp" data-v-1460e71d=3D""><img s=
rc=3D"https://www.usermanuals.au/thumbs/products/s/1402147-hisense-hr6bmff5=
19b.jpg" width=3D"100" height=3D"100" alt=3D"Hisense HR6BMFF519B" loading=
=3D"lazy" data-v-1460e71d=3D""></picture></div><div class=3D"product-listin=
g__info" data-v-3ad21d7d=3D""><!--[--><h5 class=3D"h6" data-v-3ad21d7d=3D""=
>Hisense HR6BMFF519B</h5><!--]--><!--[--><span data-v-3ad21d7d=3D"">manual<=
/span><span data-v-3ad21d7d=3D"">24 pages</span><!--]--><div class=3D"produ=
ct-listing__rating" data-v-3ad21d7d=3D""><div style=3D"width:50%;" class=3D=
"product-listing__rating-hover" data-v-3ad21d7d=3D""></div></div></div></a>=
</div><!--]--><!--[--><!----><div class=3D"product-listing__item d-flex jus=
tify-content-between align-items-center pl-0" data-v-3ad21d7d=3D""><a href=
=3D"https://www.usermanuals.au/hisense/rl475n4bc2/manual" class=3D"d-flex w=
-100 text-dark text-decoration-none" data-v-3ad21d7d=3D""><div class=3D"sma=
rt-image product-listing__img" data-v-3ad21d7d=3D"" data-v-1460e71d=3D""><p=
icture data-v-1460e71d=3D""><source srcset=3D"/thumbs/products/s/893301-his=
ense-rl475n4bc2.webp" type=3D"image/webp" data-v-1460e71d=3D""><img src=3D"=
https://www.usermanuals.au/thumbs/products/s/893301-hisense-rl475n4bc2.jpg"=
 width=3D"100" height=3D"100" alt=3D"Hisense RL475N4BC2" loading=3D"lazy" d=
ata-v-1460e71d=3D""></picture></div><div class=3D"product-listing__info" da=
ta-v-3ad21d7d=3D""><!--[--><h5 class=3D"h6" data-v-3ad21d7d=3D"">Hisense RL=
475N4BC2</h5><!--]--><!--[--><span data-v-3ad21d7d=3D"">manual</span><span =
data-v-3ad21d7d=3D"">90 pages</span><!--]--><div class=3D"product-listing__=
rating" data-v-3ad21d7d=3D""><div style=3D"width:50%;" class=3D"product-lis=
ting__rating-hover" data-v-3ad21d7d=3D""></div></div></div></a></div><!--]-=
-><!--[--><!----><div class=3D"product-listing__item d-flex justify-content=
-between align-items-center pl-0" data-v-3ad21d7d=3D""><a href=3D"https://w=
ww.usermanuals.au/hisense/mtz55183ff/manual" class=3D"d-flex w-100 text-dar=
k text-decoration-none" data-v-3ad21d7d=3D""><div class=3D"smart-image prod=
uct-listing__img" data-v-3ad21d7d=3D"" data-v-1460e71d=3D""><picture data-v=
-1460e71d=3D""><source srcset=3D"/thumbs/brands/s/2376-hisense_logo.webp" t=
ype=3D"image/webp" data-v-1460e71d=3D""><img src=3D"https://www.usermanuals=
.au/thumbs/brands/s/2376-hisense_logo.jpg" width=3D"100" height=3D"100" alt=
=3D"Hisense MTZ55183FF" loading=3D"lazy" data-v-1460e71d=3D""></picture></d=
iv><div class=3D"product-listing__info" data-v-3ad21d7d=3D""><!--[--><h5 cl=
ass=3D"h6" data-v-3ad21d7d=3D"">Hisense MTZ55183FF</h5><!--]--><!--[--><spa=
n data-v-3ad21d7d=3D"">manual</span><span data-v-3ad21d7d=3D"">12 pages</sp=
an><!--]--><div class=3D"product-listing__rating" data-v-3ad21d7d=3D""><div=
 style=3D"width:50%;" class=3D"product-listing__rating-hover" data-v-3ad21d=
7d=3D""></div></div></div></a></div><!--]--><!--[--><!----><div class=3D"pr=
oduct-listing__item d-flex justify-content-between align-items-center pl-0"=
 data-v-3ad21d7d=3D""><a href=3D"https://www.usermanuals.au/hisense/mcf98/m=
anual" class=3D"d-flex w-100 text-dark text-decoration-none" data-v-3ad21d7=
d=3D""><div class=3D"smart-image product-listing__img" data-v-3ad21d7d=3D""=
 data-v-1460e71d=3D""><picture data-v-1460e71d=3D""><source srcset=3D"/thum=
bs/brands/s/2376-hisense_logo.webp" type=3D"image/webp" data-v-1460e71d=3D"=
"><img src=3D"https://www.usermanuals.au/thumbs/brands/s/2376-hisense_logo.=
jpg" width=3D"100" height=3D"100" alt=3D"Hisense MCF98" loading=3D"lazy" da=
ta-v-1460e71d=3D""></picture></div><div class=3D"product-listing__info" dat=
a-v-3ad21d7d=3D""><!--[--><h5 class=3D"h6" data-v-3ad21d7d=3D"">Hisense MCF=
98</h5><!--]--><!--[--><span data-v-3ad21d7d=3D"">manual</span><span data-v=
-3ad21d7d=3D"">12 pages</span><!--]--><div class=3D"product-listing__rating=
" data-v-3ad21d7d=3D""><div style=3D"width:50%;" class=3D"product-listing__=
rating-hover" data-v-3ad21d7d=3D""></div></div></div></a></div><!--]--><!--=
[--><!----><div class=3D"product-listing__item d-flex justify-content-betwe=
en align-items-center pl-0" data-v-3ad21d7d=3D""><a href=3D"https://www.use=
rmanuals.au/hisense/mc55244db/manual" class=3D"d-flex w-100 text-dark text-=
decoration-none" data-v-3ad21d7d=3D""><div class=3D"smart-image product-lis=
ting__img" data-v-3ad21d7d=3D"" data-v-1460e71d=3D""><picture data-v-1460e7=
1d=3D""><source srcset=3D"/thumbs/brands/s/2376-hisense_logo.webp" type=3D"=
image/webp" data-v-1460e71d=3D""><img src=3D"https://www.usermanuals.au/thu=
mbs/brands/s/2376-hisense_logo.jpg" width=3D"100" height=3D"100" alt=3D"His=
ense MC55244DB" loading=3D"lazy" data-v-1460e71d=3D""></picture></div><div =
class=3D"product-listing__info" data-v-3ad21d7d=3D""><!--[--><h5 class=3D"h=
6" data-v-3ad21d7d=3D"">Hisense MC55244DB</h5><!--]--><!--[--><span data-v-=
3ad21d7d=3D"">manual</span><span data-v-3ad21d7d=3D"">26 pages</span><!--]-=
-><div class=3D"product-listing__rating" data-v-3ad21d7d=3D""><div style=3D=
"width:50%;" class=3D"product-listing__rating-hover" data-v-3ad21d7d=3D""><=
/div></div></div></a></div><!--]--><!--[--><!----><div class=3D"product-lis=
ting__item d-flex justify-content-between align-items-center pl-0" data-v-3=
ad21d7d=3D""><a href=3D"https://www.usermanuals.au/hisense/mtz55145ff/manua=
l" class=3D"d-flex w-100 text-dark text-decoration-none" data-v-3ad21d7d=3D=
""><div class=3D"smart-image product-listing__img" data-v-3ad21d7d=3D"" dat=
a-v-1460e71d=3D""><picture data-v-1460e71d=3D""><source srcset=3D"/thumbs/b=
rands/s/2376-hisense_logo.webp" type=3D"image/webp" data-v-1460e71d=3D""><i=
mg src=3D"https://www.usermanuals.au/thumbs/brands/s/2376-hisense_logo.jpg"=
 width=3D"100" height=3D"100" alt=3D"Hisense MTZ55145FF" loading=3D"lazy" d=
ata-v-1460e71d=3D""></picture></div><div class=3D"product-listing__info" da=
ta-v-3ad21d7d=3D""><!--[--><h5 class=3D"h6" data-v-3ad21d7d=3D"">Hisense MT=
Z55145FF</h5><!--]--><!--[--><span data-v-3ad21d7d=3D"">manual</span><span =
data-v-3ad21d7d=3D"">16 pages</span><!--]--><div class=3D"product-listing__=
rating" data-v-3ad21d7d=3D""><div style=3D"width:50%;" class=3D"product-lis=
ting__rating-hover" data-v-3ad21d7d=3D""></div></div></div></a></div><!--]-=
-><!--[--><!----><div class=3D"product-listing__item d-flex justify-content=
-between align-items-center pl-0" data-v-3ad21d7d=3D""><a href=3D"https://w=
ww.usermanuals.au/hisense/ksnf-360-vr/manual" class=3D"d-flex w-100 text-da=
rk text-decoration-none" data-v-3ad21d7d=3D""><div class=3D"smart-image pro=
duct-listing__img" data-v-3ad21d7d=3D"" data-v-1460e71d=3D""><picture data-=
v-1460e71d=3D""><source srcset=3D"/thumbs/brands/s/2376-hisense_logo.webp" =
type=3D"image/webp" data-v-1460e71d=3D""><img src=3D"https://www.usermanual=
s.au/thumbs/brands/s/2376-hisense_logo.jpg" width=3D"100" height=3D"100" al=
t=3D"Hisense KSNF 360 VR" loading=3D"lazy" data-v-1460e71d=3D""></picture><=
/div><div class=3D"product-listing__info" data-v-3ad21d7d=3D""><!--[--><h5 =
class=3D"h6" data-v-3ad21d7d=3D"">Hisense KSNF 360 VR</h5><!--]--><!--[--><=
span data-v-3ad21d7d=3D"">manual</span><span data-v-3ad21d7d=3D"">17 pages<=
/span><!--]--><div class=3D"product-listing__rating" data-v-3ad21d7d=3D""><=
div style=3D"width:50%;" class=3D"product-listing__rating-hover" data-v-3ad=
21d7d=3D""></div></div></div></a></div><!--]--><!--[--><!----><div class=3D=
"product-listing__item d-flex justify-content-between align-items-center pl=
-0" data-v-3ad21d7d=3D""><a href=3D"https://www.usermanuals.au/hisense/ks-2=
50/manual" class=3D"d-flex w-100 text-dark text-decoration-none" data-v-3ad=
21d7d=3D""><div class=3D"smart-image product-listing__img" data-v-3ad21d7d=
=3D"" data-v-1460e71d=3D""><picture data-v-1460e71d=3D""><source srcset=3D"=
/thumbs/brands/s/2376-hisense_logo.webp" type=3D"image/webp" data-v-1460e71=
d=3D""><img src=3D"https://www.usermanuals.au/thumbs/brands/s/2376-hisense_=
logo.jpg" width=3D"100" height=3D"100" alt=3D"Hisense KS 250" loading=3D"la=
zy" data-v-1460e71d=3D""></picture></div><div class=3D"product-listing__inf=
o" data-v-3ad21d7d=3D""><!--[--><h5 class=3D"h6" data-v-3ad21d7d=3D"">Hisen=
se KS 250</h5><!--]--><!--[--><span data-v-3ad21d7d=3D"">manual</span><span=
 data-v-3ad21d7d=3D"">11 pages</span><!--]--><div class=3D"product-listing_=
_rating" data-v-3ad21d7d=3D""><div style=3D"width:50%;" class=3D"product-li=
sting__rating-hover" data-v-3ad21d7d=3D""></div></div></div></a></div><!--]=
--><!--]--><!----></div></div><a href=3D"https://www.usermanuals.au/hisense=
" class=3D"d-block" data-v-18a9b685=3D""><div class=3D"app-icon size-null c=
hevron.right" data-v-18a9b685=3D""><svg width=3D"100" height=3D"100" viewBo=
x=3D"0 0 100 100" fill=3D"none" xmlns=3D"http://www.w3.org/2000/svg">
<path d=3D"M72.4902 50.3223C72.4902 48.9209 71.9775 47.7588 70.8838 46.665L=
44.5654 20.9277C43.6768 20.0391 42.6172 19.6289 41.3525 19.6289C38.7891 19.=
6289 36.7041 21.6455 36.7041 24.209C36.7041 25.4736 37.251 26.6357 38.1738 =
27.5586L61.5527 50.2881L38.1738 73.0859C37.251 74.0088 36.7041 75.1367 36.7=
041 76.4355C36.7041 78.999 38.7891 81.0498 41.3525 81.0498C42.6172 81.0498 =
43.6768 80.6055 44.5654 79.7168L70.8838 53.9795C72.0117 52.8857 72.4902 51.=
7236 72.4902 50.3223Z"></path>
</svg>
</div> View all Hisense manuals</a><a href=3D"https://www.usermanuals.au/re=
frigerators/hisense" class=3D"" data-v-18a9b685=3D""><div class=3D"app-icon=
 size-null chevron.right" data-v-18a9b685=3D""><svg width=3D"100" height=3D=
"100" viewBox=3D"0 0 100 100" fill=3D"none" xmlns=3D"http://www.w3.org/2000=
/svg">
<path d=3D"M72.4902 50.3223C72.4902 48.9209 71.9775 47.7588 70.8838 46.665L=
44.5654 20.9277C43.6768 20.0391 42.6172 19.6289 41.3525 19.6289C38.7891 19.=
6289 36.7041 21.6455 36.7041 24.209C36.7041 25.4736 37.251 26.6357 38.1738 =
27.5586L61.5527 50.2881L38.1738 73.0859C37.251 74.0088 36.7041 75.1367 36.7=
041 76.4355C36.7041 78.999 38.7891 81.0498 41.3525 81.0498C42.6172 81.0498 =
43.6768 80.6055 44.5654 79.7168L70.8838 53.9795C72.0117 52.8857 72.4902 51.=
7236 72.4902 50.3223Z"></path>
</svg>
</div> View all Hisense refrigerator manuals</a></section><div data-v-8e81d=
043=3D"" data-v-a411a370=3D""><!----></div><div class=3D"ad-placeholder my-=
3" data-v-8e81d043=3D"" data-v-5e860da6=3D""><!----><ins data-ad-unit=3D"hz=
_manual_bottom_5" data-ad-channel=3D"4152078287" data-ad-region=3D"page-0.0=
25326126493820045" data-ad-format=3D"auto" data-ad-slot=3D"6457866712" data=
-ad-client=3D"ca-pub-4193547076534677" data-ad-layout-key=3D"" data-full-wi=
dth-responsive=3D"true" style=3D"display:block;" class=3D"adsbygoogle" data=
-v-8e81d043=3D"" data-v-5e860da6=3D""></ins></div></div><!--]--></div></div=
></div><!--]--></div><footer class=3D"footer" data-v-a1a4be57=3D""><div cla=
ss=3D"container py-5 text-center" data-v-a1a4be57=3D""><div class=3D"lang-s=
witcher text-start" data-v-a1a4be57=3D""><div class=3D"lang-switcher" data-=
v-a1a4be57=3D"" data-v-9b698a5b=3D""><button class=3D"lang-switcher__label =
btn" data-v-9b698a5b=3D"">EN_AU <div class=3D"app-icon size-null chevron.do=
wn" data-v-9b698a5b=3D""><svg width=3D"100" height=3D"100" viewBox=3D"0 0 1=
00 100" fill=3D"none" xmlns=3D"http://www.w3.org/2000/svg">
<path d=3D"M50 69.4629C51.4014 69.4629 52.5977 68.916 53.6914 67.7881L79.39=
45 41.5039C80.2832 40.6152 80.7275 39.5557 80.7275 38.291C80.7275 35.7275 7=
8.6768 33.6426 76.1133 33.6426C74.8828 33.6426 73.6865 34.1895 72.7637 35.1=
123L50.0342 58.4912L27.2363 35.1123C26.3135 34.1895 25.1514 33.6426 23.8525=
 33.6426C21.3232 33.6426 19.2725 35.7275 19.2725 38.291C19.2725 39.5557 19.=
751 40.6152 20.6055 41.5039L46.3086 67.8223C47.4365 68.916 48.5986 69.4629 =
50 69.4629Z"></path>
</svg>
</div></button><!----></div></div><h4 data-v-a1a4be57=3D"">UserManuals<span=
>.</span>au</h4><p class=3D"bv-d-sm-down-none" data-v-a1a4be57=3D"">Looking=
 for a manual? UserManuals.au ensures that you will find the manual you are=
 looking for in no time.=20
Our database contains more than 1 million PDF manuals from more than 10,000=
 brands. Every day we add the latest manuals so that you will always find t=
he product you are looking for.
It's very simple: just type the brand name and the type of product in the s=
earch bar and you can instantly view the manual of your choice online for f=
ree.</p><p data-v-a1a4be57=3D"">=C2=A9 Copyright 2025 UserManuals.au. All R=
ights Reserved.
</p><div class=3D"footer__links" data-v-a1a4be57=3D""><a href=3D"https://ww=
w.usermanuals.au/privacy-policy" class=3D"me-3" rel=3D"nofollow" data-v-a1a=
4be57=3D"">Privacy Policy</a><!----><a href=3D"https://www.usermanuals.au/c=
ontact" class=3D"" rel=3D"nofollow" data-v-a1a4be57=3D"">Contact</a></div><=
/div></footer></div></div></div><div id=3D"teleports"></div>
<protonpass-root-21c5 data-protonpass-role=3D"root" data-protonpass-theme=
=3D"dark"><template shadowmode=3D"open"></template></protonpass-root-21c5><=
/body></html>
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/products/s/893301-hisense-rl475n4bc2.webp

UklGRpYCAABXRUJQVlA4WAoAAAAQAAAAxwAAxwAAQUxQSB4AAAABDzD/ERFCQds2UpiP+EkGcV9E
/6NqAQCm6vu/3wBWUDggUgIAADAbAJ0BKsgAyAA+kUSaSSWqpaEskbjxUBIJaW7hb9Ebn6gIZrLi
HxyIdBDjG7u4NYt1OSuJbP9fVQc5W+gT1YZVFpwB+Ii5O6Y6xDGPQDaUEtOCHVn0Y6/Yb8hyY0SN
/lgMrTEmTAbBrxTRIljaCc/jW7r/r0vhW1ujAbO+SUXzKe5eo2oVGR9NAvjYePRl6rsZNX2WIbSg
pErLgpNi25e1mQMjGsmzQiHH0YDZ3yTAp7Vvvhy6laUS3couSBzJaKZATmQKLY3brwULgmimTkSF
7/h5zeaqNw/QZYD9s1dSbAAA/v5zBpaGnOxig9EVhJTUmuUNdppJqbdWjt/mYY5raJFEZjQH+g9m
58AQN2W5R5nXVHVC1EH//+YEGKNWAOO+eFUq2S2nT0SA+HA+nSNTgjyJjWPhtvTp6+BfKlMq2HTa
hQlQD/9SR5TwqWA4rlSn0QTXlTfbakV/8F+1nptQqLBjs0WnsjRCXb0Ju8iGFWoI73YOXzJ3DWHy
OwqH3SUFhuzFqItQFeQIIYZYZMgdhHc9LYcIQR9e1m13QLSCC5H3E8i/L1DFRna3yqKwP+VzwBy8
YVID+e2YssmXBHC6uWgPK2QoU5A16cw2825vrCcMfIFU/whFkkY3sk9CMLoa/dOzK23xzf3rhGF3
iFP3CCT8JDcaC0YpnQ6akHICz9qqm1a92S0dA5/sab4Md+oL+j/f9Hs96IYdC45u0DX8GIqYD6NE
1WJj0H9x9xacDcnYIDr27M8a4rhzLno+fzF9JFHxWex6rLkcjarHjhAAAA==

------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/products/s/1402147-hisense-hr6bmff519b.webp

UklGRiYCAABXRUJQVlA4IBoCAAAQGgCdASrIAMgAPpFImUmlpSKhJz8qsLASCWlu62AUIDY2T3Hv
4qHGAIQWVUhKCVloScRDg9skNJ23NoB21/xVjdjnFZHlAdqTRS80QqGq7tSZLR4JthbEr+VgwDJQ
mZJY8uQlD43GsMdtp5FHSpqf1RlOfVXdqTVyWwu1J0hciXcflKrCS2l+znb5yRo9+Uux7GycR/gK
wWacks0mfT8pVYSBDFZoyq5UtwzZKlJe3fifcszqkBV99tFPHuIR4M+eDA5OG9soKyETobgZPWFV
KM0xVPHmA1xAAP74TfLIyV7m7+uEVoDVhSuui63YhtvCqskVUBQzB6gXQ/wThUgjwEqBHGyzB6s1
Bkk82E8xqSFbWKE5hOMX0g9c/4i4nHBNBwbdMnZ3ZgF/AFQsoacE79vDugUteLL2FBJMdfa8emjo
A7YgAEAUrOMXFUHhOb4PnNAGrJ8gguRNopOxjXKyaqNEy8anmFmLfxfTBBiVIwBYNUFZFdRLZHBp
0QKtIYGv0a/vS/m26oeBUtV0ew7YnvslFOguD5tmTqYodaN8GmsW3VIFjPNg+5O3Coui3KjBOlGT
Iwa2Z1B9izAk/U5ghZrSLv3YYB2tbE78jQjICpCm8IbA2E+fg2YpDC/oIeOvlqSMiT6RtkuAXJC5
06Al6rWz29gesstP7dvsutT5DmZjw9cIvw76sXNuxzRD59o9emCIufeAAAAA

------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/products/s/1695399-hisense-hrbm482bw.webp

UklGRsYCAABXRUJQVlA4ILoCAACwHACdASrIAMgAPpFCm0slo6IiqJBo+LASCWlu4XCOpaT3QUXw
wVHsPMBBNJJjvkrvr5851WbcD5wSwMAnfI24Jcn89wZNO3a1ECoA6ozkxK7zQLFJnSQGbCQxh3FJ
2lDumcGAiZxHh+WB1uLk0DumcGAiZxHi9Btf2RZCjHOkCRryiTHvwJe3IIi8fWLHvuwGZX8/A15U
Ea7QF19kHVJkbCQxhzhkKC9b1bvlrpq7uSVFMikMYoEy+cbcFc2oy7y5GME82It1eZ53BXDG9hkQ
Hg5YNneBbZ+oNjJC1dCd4dM2SsCkMaBvyhsXcsEAAP7+ojpfqV7h4EG+L9r4jB02XR62MEf40SMP
6hp6br8S4PEvRli7BGnfNjnLbwDbKOAQqelPlhVh86mIVa8d2/Rhe5UVZQ1N/rP8tkaSHLD0q4cb
XdWTHN6FztxtOkxFCu7vsbXl4gqhhlGdozjSDeD3+rrjsR3fzo4WaAT66zhHiT8BXzoe9GsaXvuI
BAN5bovbZ8XLqiA6Mq06gLqLULCBuB3aVJ68fqFfuW2fIhcZocB60WMW5W6lNCUko3SfNJ/dhuKE
FMIOxV3lGVrbuM6l7xGni5p71ce/aDZsBmgBeAbEP/tRD0C6tyzX/lnHzGIxZi1BqRcu9mn+Hzpb
So3jbXf34y+JgDxZYsTzLNaCx1s001GwxAukmcpipq9PQsRcibiy9ZWVF1NU/ZHo5HeilPxUWUKT
e/aKBlhBNj1dRV22+w9z23WTY0dqWULCfA6eN5vHJgKeGHmkQ48tvK1IY61UX+sc2DiWuEvqdsVM
xajighuysaGA1UTHOSLfi7Qc6kpN0AKp3yEXjzK1iLkuz6Xjr4NC2GpzjFlNnplLBoUPb3A48xZp
ntNO2lAUKjv6ZTdrqCngDW57b2qi6q91y+P2zUBBkd8AAA==

------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/products/s/1532134-hisense-hr6bmff453b.webp

UklGRjACAABXRUJQVlA4WAoAAAAQAAAAxwAAxwAAQUxQSB0AAAABDzD/ERFCQQAQCM89bw5kRP8n
AELiKkL5/ddpAABWUDgg7AEAAJAaAJ0BKsgAyAA+kTyYSKejIqEtfXi48BIJaW7ewc5so4gTZZRE
83QZAY8VH3I3Ppp5ZHcPjMQk4wtqvqNErWIpSMVeR52hv8UL6jaD557QYHF+yA9NRonEr5tIO0u3
Lolf+LVVOUaXaryGdfUx9hAjqB+xket3zOKjrt+O8p9aWnIVYVeT8DKfTEMZrHJGL01GzCJB9MKL
K7KUIEdyH+gk7HGgIehf3F9RnTtHLw637R3pM6EWq+n8ffN4CxdOSnSrPEm8koVdFwJ3UddwL6TZ
4/QMxjcvH3FEyuAAAP74TePgN5a8BwhSHKCaxwz8hARqGBHIDXdQDRQGXGicwVt6M1awgxRCPamS
Gokk+4UmFPO47LKiIRRh9SsGaUqoaOgXIV7xUzeqbYyZ2ZXW2il6kO/k5zJ+RqxAOXRRWujTU+bC
NbyYAHL7riXWe4zU1rRWeDPyOTUR6tbXDNubqmDDkxnpiuiugnljvUAgG6C3VlxgkVmDcRjZLT6h
kblaQbzbv8a96XAQy//ANZRGXjFHiHAwvftXLaqZ0Pyj7wk5zz9QX3/KX+4aeuuMCc01kQ/aR9oV
JlhDgfpMm3UWVcj09jlcVyrRhLy7eYZZbyGk7ePdYAkWStBhQo5d1YGKtZ4r0nojduDmoAAAAA==

------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/brands/l/2376-hisense_logo.webp
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------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: image/svg+xml
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/images/comment-section.svg

<svg fill=3D"none" height=3D"159" viewBox=3D"0 0 280 159" width=3D"280" xml=
ns=3D"http://www.w3.org/2000/svg"><path d=3D"m151.925 43.1907c.061.8632-.09=
 1.7291-.433 2.4881-.342.759-.862 1.3772-1.492 1.7764-.631.3992-1.344.5615-=
2.05.4665-.707-.0951-1.374-.4433-1.918-1.0006s-.94-1.2987-1.139-2.1306c-.19=
8-.8319-.19-1.7169.023-2.5432.214-.8263.624-1.5569 1.178-2.0994s1.227-.8726=
 1.935-.9486c.471-.0506.945.0126 1.396.1859.45.1734.869.4534 1.231.8242.362=
.3707.66.8249.878 1.3365s.35 1.0705.391 1.6448z" fill=3D"#fff"/><path d=3D"=
m144.105 51.2729c.034.4809-.051.9632-.242 1.3857-.191.4226-.481.7665-.832.9=
883-.352.2217-.749.3115-1.143.2577-.393-.0537-.765-.2484-1.067-.5595-.303-.=
3111-.523-.7246-.633-1.1883s-.105-.9567.015-1.4167.349-.8664.658-1.1678.685=
-.4842 1.079-.5254c.262-.0284.527.0067.778.1034s.483.253.685.4599c.201.2069=
.367.4604.487.7457.121.2854.194.597.215.917z" fill=3D"#fff"/><path d=3D"m17=
8.77 22.8028c.334 3.3697-.442 6.7634-2.158 9.4359s-4.231 4.4054-6.994 4.818=
1c-1.818.276-3.664-.0391-5.35-.9134-.822 1.3128-1.882 2.3768-3.096 3.1091-1=
.215.7323-2.551 1.1133-3.906 1.1133s-2.691-.381-3.906-1.1133c-1.214-.7323-2=
.274-1.7963-3.096-3.1091-.387.1394-.783.2395-1.183.2993-1.371.2041-2.761-.0=
81-4.001-.8207s-2.278-1.9022-2.988-3.3467c-.709-1.4445-1.06-3.1089-1.009-4.=
7916.05-1.6826.5-3.3113 1.294-4.6886-1.546-2.3998-2.339-5.3946-2.235-8.4443=
s1.098-5.95378 2.803-8.18854c1.705-2.23475 4.008-3.65316 6.495-3.99949 2.48=
7-.34634 4.993.4022 7.068 2.11064.973-1.47521 2.248-2.61082 3.696-3.291902 =
1.449-.681079 3.019-.883453 4.552-.586639s2.975 1.082281 4.179 2.276901c1.2=
04 1.19463 2.128 2.75601 2.679 4.52612.367-.15083.747-.25374 1.133-.30703 1=
.163-.17011 2.342.09643 3.379.76409 1.036.66766 1.881 1.70472 2.422 2.97295=
.541 1.2682.752 2.7073.605 4.1255s-.645 2.7481-1.428 3.8124c.551 1.3262.904=
 2.7606 1.045 4.237z" fill=3D"#fff"/><path d=3D"m160.834 23.2024c-.013-.934=
2.124-1.8625.403-2.7326.351-.9131.943-1.6563 1.681-2.1108.258-.1919.566-.39=
92.944-.6371.382-.2455.746-.5306 1.089-.852.352-.3264.658-.7205.906-1.1667.=
248-.4541.378-.9894.371-1.5352.007-.3984-.064-.793-.207-1.1514-.13-.3208-.3=
12-.6057-.535-.8366-.228-.2311-.491-.4035-.775-.5066-.29-.1152-.594-.1748-.=
9-.1766-.349-.0079-.697.0731-1.019.238-.295.1482-.573.3444-.825.5833-.235.2=
401-.446.5132-.629.8137-.181.2707-.347.5553-.497.852l-2.216-1.8652c.215-.62=
33.513-1.1991.881-1.7041.355-.49791.771-.92523 1.234-1.26648.472-.33943.98-=
.59772 1.51-.76758.549-.17635 1.114-.26409 1.681-.26098.639.00404 1.275.117=
74 1.888.33774.618.22467 1.193.595 1.693 1.08997.516.51903.932 1.16853 1.22=
1 1.90363.326.8712.486 1.8213.466 2.7786.008.5915-.056 1.1812-.189 1.7501-.=
12.4933-.305.9598-.547 1.3816-.238.416-.52.7927-.837 1.1207-.345.3525-.711.=
6732-1.096.9595l-1.019.7138c-.327.2216-.636.4785-.925.7676-.279.2652-.516.5=
904-.699.9595-.19.4007-.283.858-.271 1.3202zm.051 7.2306v-4.2831h2.819v4.28=
31z" fill=3D"#407bff"/><path d=3D"m154.526 15.1889c.175.909.196 1.8517.063 =
2.771-.179.9774-.622 1.8532-1.258 2.487-.221.2456-.491.5296-.819.8597-.333.=
3366-.642.7063-.925 1.1053-.288.4066-.52.8682-.686 1.3663-.166.5072-.201 1.=
0643-.101 1.5965.057.3891.191.7552.391 1.067.18.2843.407.5196.667.6908.261.=
1707.548.2751.843.307.305.0338.613.0131.913-.0614.342-.0801.668-.2446.956-.=
4836.264-.2172.503-.4752.712-.7675.201-.2932.372-.6155.51-.9595.128-.3114.2=
42-.6318.339-.9595l2.48 1.2742c-.101.6646-.295 1.3031-.572 1.8882-.27.5717-=
.609 1.0894-1.008 1.5352-.404.4524-.857.8346-1.346 1.136-.507.3103-1.044.53=
74-1.599.6755-.633.164-1.282.2132-1.926.1458-.642-.0586-1.267-.2753-1.838-.=
6371-.589-.3748-1.106-.8981-1.517-1.5351-.743-1.2696-1.109-2.8101-1.039-4.3=
676.035-.5103.137-1.0105.303-1.4814.161-.4669.373-.9057.629-1.3049.278-.433=
.58-.8409.906-1.2204.29-.3224.586-.6448.875-.9518.28-.3045.535-.6413.762-1.=
0056.221-.3324.394-.7089.51-1.1129.115-.4401.128-.9102.037-1.3587zm-1.258-7=
.0617.723 4.191-2.763.7062-.724-4.19103z" fill=3D"#407bff"/><path d=3D"m238=
.637 65.7944h-70.434c-8.642 0-15.648 7.006-15.648 15.6483v57.3243c0 8.642 7=
.006 15.648 15.648 15.648h70.434c8.642 0 15.648-7.006 15.648-15.648v-57.324=
3c0-8.6423-7.006-15.6483-15.648-15.6483z" fill=3D"#407bff"/><path d=3D"m238=
.637 65.7961h-70.434c-8.642 0-15.648 7.006-15.648 15.6483v57.3246c0 8.642 7=
.006 15.648 15.648 15.648h70.434c8.642 0 15.648-7.006 15.648-15.648v-57.324=
6c0-8.6423-7.006-15.6483-15.648-15.6483z" fill=3D"#000" opacity=3D".4"/><pa=
th d=3D"m254.273 96.688h15.237l-15.58 15.584z" fill=3D"#407bff"/><path d=3D=
"m254.265 96.6892h15.237l-15.58 15.5838z" fill=3D"#000" opacity=3D".4"/><pa=
th d=3D"m150.133 106.269-6.386-7.2616c-1.726-1.7265-2.319-2.5576-1.972-2.86=
01.46-.4034.262-.2703 3.848 2.4204 0 0-5.083-3.8848-3.32-3.909.311 0 1.174.=
5002 2.098 1.1053-1.259-1.069-1.614-1.7911-1.368-2.134.404-.597 3.518 1.561=
2 4.95 2.598-1.17-.8714-3.401-2.5617-3.792-3.0942-.541-.7261.553-.6656 1.44=
-.3631.997.3389 6.503 3.1507 8.133 3.8768.131.078.277.1267.428.1428.152.016=
1.305-.0007.449-.0494s.276-.1281.386-.2326c.111-.1046.197-.2319.254-.3731.6=
01-2.5254 2.421-4.1672 3.114-4.381.694-.2138 1.231.6737.735 1.3191-.467.751=
-.828 1.5628-1.073 2.4124-.225.6665-.336 1.3662-.327 2.0695 0 .8068.056 1.5=
047.109 2.0896l5.837 8.0072-9.763 5.648z" fill=3D"#b78876"/><path d=3D"m147=
.84 99.697c-.064.0888-1.21-.6898-2.533-1.7427s-2.356-1.9807-2.28-2.0655c.07=
7-.0847 1.211.6979 2.534 1.7428 1.323 1.0448 2.336 1.9726 2.279 2.0654z" fi=
ll=3D"#aa6550"/><path d=3D"m149.091 98.4223c-.064.0928-1.319-.6737-2.804-1.=
7064-1.484-1.0327-2.638-1.9485-2.573-2.0372.064-.0888 1.319.6696 2.803 1.70=
24 1.485 1.0327 2.639 1.9484 2.574 2.0412z" fill=3D"#aa6550"/><path d=3D"m1=
49.997 97.0673c-.064.0928-1.266-.6091-2.682-1.5692-1.416-.9602-2.514-1.8113=
-2.453-1.9082.06-.0968 1.262.6092 2.682 1.5693 1.421.9601 2.514 1.8153 2.45=
3 1.9081z" fill=3D"#aa6550"/><path d=3D"m158.383 100.731 23.269 29.505-5.32=
9 12.405s-6.382-1.412-8.319-4.099c-1.936-2.687-17.871-32.272-17.871-32.272z=
" fill=3D"#b78876"/><path d=3D"m217.441 59.4534c1.674 1.4522 1.67 4.0098 1.=
477 6.2165-.194 2.2066-.343 4.7199 1.186 6.3254 1.262 1.3232 3.55 1.9001 3.=
84 3.7033.069.4237.005.8583-.183 1.244-.189.3858-.492.7038-.868.9103s-.807.=
2914-1.234.243c-.426-.0485-.828-.2278-1.148-.5134.492 1.424.153 3.2031-1.08=
1 4.0664-1.235.8633-3.304.3308-3.687-1.1255.062.2431.074.4965.035.7445-.04.=
2479-.13.4851-.265.6966-.136.2115-.313.3929-.522.5328-.208.1399-.443.2353-.=
69.2803s-.501.0387-.745-.0187c-.245-.0574-.475-.1645-.676-.3147s-.369-.3402=
-.494-.5583c-.124-.218-.203-.4594-.23-.709" fill=3D"#263238"/><path d=3D"m1=
93.481 74.3474c-.226.5403-.656.969-1.198 1.1918-.541.2228-1.149.2214-1.689-=
.0038s-.969-.6558-1.192-1.1971-.221-1.149.004-1.6893c.383-.8391 1.291-1.444=
2 1.38-2.364.105-1.1093-1.029-1.904-2.017-2.3881-.989-.4841-2.183-1.1134-2.=
292-2.2268s.855-1.9082 1.658-2.6262c.803-.7181 1.614-1.8315 1.102-2.7876-.2=
55-.4921-.783-.7705-1.126-1.2102-.754-.9601-.359-2.489.605-3.2273.964-.7382=
 2.296-.8552 3.498-.6414s2.332.714 3.501 1.065c1.94.5639 3.975.7196 5.978.4=
571 2.002-.2625 3.929-.9373 5.657-1.982" fill=3D"#263238"/><path d=3D"m200.=
618 100.363c2.084.421 4.25.005 6.031-1.1573 1.78-1.1622 3.032-2.9778 3.486-=
5.0551l5.724-26.2457c.358-1.5155.407-3.0873.147-4.6224-.261-1.5351-.827-3.0=
023-1.664-4.3149-.838-1.3126-1.93-2.4439-3.213-3.327-1.282-.8832-2.729-1.50=
02-4.254-1.8146l-.597-.0888c-2.916-.4287-5.887.257-8.321 1.9204-2.433 1.663=
4-4.151 4.1825-4.81 7.0555-1.275 5.5387-2.61 11.6746-3.014 14.688-.835 6.24=
08 5.245 8.1206 5.245 8.1206l-1.093 5.2443c-.217 1.0449-.225 2.1223-.024 3.=
1704.201 1.048.607 2.0461 1.194 2.9367.588.8907 1.346 1.6565 2.231 2.2533.8=
84.5968 1.878 1.0126 2.924 1.2246z" fill=3D"#b78876"/><path d=3D"m194.284 6=
6.8446c-.036.2316.02.4679.156.6591.135.1912.34.3222.57.3656.112.0254.227.02=
83.34.0084.113-.0198.22-.062.316-.124.097-.062.179-.1426.244-.237.065-.0945=
.11-.2009.133-.313.037-.2314-.017-.4682-.152-.6598-.135-.1915-.34-.3225-.57=
-.3649-.112-.0255-.228-.0284-.341-.0086s-.221.0619-.317.1239c-.097.062-.18.=
1426-.245.237-.065.0945-.111.201-.134.3133z" fill=3D"#263238"/><path d=3D"m=
194.04 65.9564c.092.1332.839-.2702 1.799-.125s1.585.7221 1.706.6212c.061-.0=
443-.024-.2703-.294-.5365-.372-.3477-.843-.5717-1.347-.6414-.498-.0751-1.00=
7.0096-1.453.242-.327.1856-.456.3792-.411.4397z" fill=3D"#263238"/><path d=
=3D"m203.644 68.3766c-.037.2318.019.4686.154.66.136.1915.341.3223.572.3647.=
112.0254.228.0281.341.008s.22-.0626.317-.125c.096-.0624.179-.1435.243-.2384=
s.109-.2018.132-.3143c.036-.2312-.02-.4676-.155-.6583-.136-.1908-.341-.3208=
-.571-.3623-.112-.026-.228-.0293-.341-.0097s-.221.0617-.317.1238-.179.1429-=
.244.2377c-.064.0947-.109.2014-.131.3138z" fill=3D"#263238"/><path d=3D"m20=
3.431 67.5131c.093.1291.839-.2703 1.795-.1251.956.1453 1.59.7221 1.711.6213=
.056-.0444-.024-.2703-.299-.5366-.372-.3476-.843-.5717-1.347-.6414-.498-.07=
51-1.006.0097-1.453.2421-.31.1734-.455.3792-.407.4397z" fill=3D"#263238"/><=
path d=3D"m198.906 74.1051c0-.0565-.569-.2461-1.513-.5083-.242-.0605-.464-.=
1452-.48-.3187.016-.2541.109-.4973.266-.6979.32-.5567.652-1.1416.996-1.7548=
 1.392-2.5052 2.421-4.5746 2.336-4.6311s-1.311 1.9283-2.703 4.4375l-.968 1.=
7669c-.195.2743-.29.608-.266.944.025.0891.069.1716.13.2414.06.0698.136.1251=
.221.162.129.0553.264.0946.403.117.514.1458 1.044.2271 1.578.242z" fill=3D"=
#263238"/><path d=3D"m203.72 63.7263c.056.2743 1.033.2904 2.134.5889 1.101.=
2986 1.965.7383 2.15.5285.081-.1008-.04-.3711-.367-.6737-.443-.3809-.968-.6=
555-1.533-.803-.566-.1475-1.158-.164-1.73-.0482-.452.1049-.674.2986-.654.40=
75z" fill=3D"#263238"/><path d=3D"m194.804 63.2365c.137.2421.807.113 1.614.=
2179.806.1048 1.444.363 1.613.1573.085-.1009 0-.3348-.25-.585-.358-.3043-.7=
99-.4952-1.266-.5484-.466-.0531-.939.0339-1.356.2499-.291.1816-.416.3913-.3=
55.5083z" fill=3D"#263238"/><path d=3D"m210.102 60.0826.028.0323c.879.9275 =
1.364 2.1592 1.355 3.437v.125c0 1.8154-.326 3.6307-.322 5.4259s.351 3.7073 =
1.484 5.1192c.464.585 1.368 1.0691 1.896.5446.186-.2231.305-.4945.343-.7826=
.96-4.2398 2.687-7.8705 3.336-12.3564.09-.6005.036-1.2138-.158-1.7891-.195-=
.5752-.523-1.0959-.959-1.5188-.839-.8068-1.884-1.6782-3.002-1.3353-1.117.34=
29-2.751 2.0977-3.683 2.8239" fill=3D"#263238"/><path d=3D"m213.364 70.4144=
c.113-.0323 4.466-.6414 3.704 3.5984-.763 4.2398-4.841 2.602-4.841 2.4769s1=
.137-6.0753 1.137-6.0753z" fill=3D"#b78876"/><path d=3D"m213.788 75.1175s.0=
64.0645.177.1412c.163.1077.361.1482.553.1129.476-.0847.98-.7261 1.138-1.488=
5.081-.3599.081-.7334 0-1.0933-.017-.1395-.067-.2728-.147-.3887-.079-.1159-=
.185-.2109-.309-.2769-.081-.0381-.173-.0464-.259-.0236-.086.0229-.162.0756-=
.213.1487-.077.1048-.065.1976-.089.1976s-.077-.0847 0-.2541c.042-.1005.117-=
.1833.214-.234.132-.0675.286-.0805.427-.0363.169.0672.318.1767.433.318.114.=
1413.19.3096.221.4888.112.394.127.8091.044 1.2102-.181.8512-.762 1.5693-1.4=
04 1.6379-.12.0127-.241-.0017-.355-.0422s-.217-.1061-.302-.1918c-.141-.1251=
-.145-.2219-.129-.2259z" fill=3D"#aa6550"/><path d=3D"m214.15 57.6665c1.299=
-1.5532-1.21-4.4174-3.227-5.3896s-4.922-.3509-7.117.0363c-1.383.2461-2.787.=
6657-4.167.4034-.916-.2291-1.791-.5949-2.598-1.0851-.809-.4814-1.706-.7956-=
2.638-.9238-.469-.0577-.945.0136-1.377.2062-.431.1927-.802.4993-1.072.887-.=
48.8068-.145 2.0695.758 2.3317-.272-.2067-.59-.3443-.928-.401-.337-.0566-.6=
83-.0306-1.008.0758s-.619.2901-.857.5352c-.239.2451-.414.5444-.511.8722-.09=
8.3278-.114.6743-.048 1.0098s.213.6499.427.9164c.214.2664.49.4769.803.6134.=
314.1365.656.1949.997.1702-.537-.5486-1.578.0767-1.614.8432-.036.7664.569 1=
.4159 1.21 1.8234 1.658 1.0408 3.764 1.0932 5.693.7463 1.928-.347 3.776-1.0=
65 5.696-1.4604 1.92-.3953 5.143-1.1456 6.878-.2299 1.275.6696 1.977 2.4204=
 2.788 2.1824.384-.1414.729-.3704 1.01-.6687.28-.2983.486-.6577.603-1.0498.=
219-.7872.31-1.6044.271-2.4204" fill=3D"#263238"/><path d=3D"m222.451 77.33=
23c.08-.0182.155-.051.222-.0968.2-.1282.359-.3099.46-.5245.157-.3432.204-.7=
26.137-1.0972-.104-.4882-.372-.9263-.758-1.2425-.459-.3774-.975-.6784-1.529=
-.8916-.623-.2273-1.222-.5165-1.787-.8632-.593-.4163-1.044-1.0031-1.295-1.6=
823-.276-.7313-.403-1.5102-.375-2.2913.04-1.6136.488-3.1264.597-4.5505.109-=
1.424-.073-2.7593-.678-3.6831-.455-.7429-1.177-1.2841-2.017-1.5127-.225-.05=
68-.458-.0799-.69-.0686-.081.0013-.161.0135-.238.0363.306-.002.611.0359.908=
.1129.799.2527 1.477.7913 1.904 1.5128.561.8956.754 2.1946.613 3.5863-.141 =
1.3918-.573 2.9045-.621 4.5626-.026.8097.111 1.6163.403 2.372.27.7176.753 1=
.3355 1.384 1.771.579.3476 1.191.6368 1.827.8632.54.2001 1.045.4832 1.497.8=
391.369.2916.63.6975.742 1.1538.074.3474.04.709-.096 1.0367-.125.2806-.339.=
5118-.61.6576z" fill=3D"#455a64"/><path d=3D"m192.568 54.0312c-.057-.0424-.=
119-.0764-.186-.1008-.075-.0372-.153-.0682-.234-.0928-.111-.0382-.226-.0666=
-.343-.0847-.382-.0604-.774-.0329-1.144.0803s-.71.3092-.994.5732c-.287.3498=
-.502.7532-.632 1.1867-.13.4334-.173.8885-.126 1.3386.054.5592.22 1.1018.48=
7 1.5958.268.4941.631.9295 1.07 1.2805 1.008.8123 2.236 1.3053 3.526 1.416 =
1.401.1205 2.811.0348 4.187-.2542 2.182-.4274 4.271-1.2358 6.173-2.3881.815=
-.5041 1.667-.9451 2.549-1.3192.77-.3123 1.59-.4805 2.421-.4962 1.208-.0261=
 2.404.2513 3.477.8068.388.2172.764.4556 1.126.7141-.323-.3174-.688-.5889-1=
.085-.8068-1.077-.5997-2.29-.9097-3.522-.8996-.851.0065-1.693.1692-2.485.48=
-.892.3736-1.754.8146-2.578 1.3192-1.888 1.1284-3.957 1.9214-6.116 2.3438-1=
.356.2871-2.746.3754-4.127.2622-1.25-.1037-2.442-.5754-3.425-1.3555-.423-.3=
341-.775-.7492-1.035-1.2209s-.424-.9905-.482-1.5263c-.049-.4292-.013-.864.1=
06-1.2794.119-.4153.319-.8031.588-1.141.269-.2568.592-.4504.945-.5669.353-.=
1166.727-.153 1.096-.1068.261.0571.517.1382.763.242z" fill=3D"#455a64"/><pa=
th d=3D"m194.94 50.502c-.014.3241.036.6479.149.952.315.8406.903 1.551 1.671=
 2.017.515.3359 1.088.5738 1.69.702.69.139 1.397.1757 2.098.1089 1.48-.1089=
 3.029-.6172 4.655-1.0368 1.626-.4195 3.252-.6414 4.656-.2662 1.246.3301 2.=
354 1.0495 3.162 2.0533.556.6668.969 1.4414 1.211 2.2752.161.5931.181.94.20=
9.9359.006-.0832.006-.1668 0-.2501-.022-.2378-.061-.4737-.117-.7059-.215-.8=
602-.616-1.6629-1.174-2.3519-.816-1.045-1.948-1.799-3.227-2.1502-1.452-.403=
4-3.122-.1734-4.756.2502-1.634.4235-3.171.9318-4.619 1.0488-.683.0706-1.372=
.0421-2.046-.0847-.583-.1186-1.14-.3399-1.646-.6535-.748-.4411-1.333-1.1117=
-1.67-1.9122-.108-.3032-.19-.6149-.246-.9318z" fill=3D"#455a64"/><path d=3D=
"m196.98 77.2725c.053.4165.163.8237.327 1.2102.086.1911.211.3617.367.5008.1=
57.1391.341.2434.541.3061.216.0447.44.0345.652-.0296.211-.0642.403-.1802.55=
8-.3375.309-.3259.508-.7409.569-1.1861.064-.4034 0-.9076-.404-1.0367-.162-.=
0403-.333-.0403-.496 0l-2.235.4034" fill=3D"#aa6550"/><path d=3D"m197.378 7=
8.966c.356.1367.733.2078 1.114.2097.394-.0231.765-.1929 1.04-.476.163-.1731=
.287-.3788.364-.6034s.106-.4631.084-.6996c-.012-.1212-.039-.2405-.08-.355-.=
053-.1089-.037-.1008-.13-.1129-.092-.0122-.238 0-.359.0201l-.375.0283c-.484=
.0363-.92.0645-1.287.0847-.402.0559-.81.0478-1.21-.0242.377-.1377.773-.2193=
 1.174-.2421.363-.0524.807-.1049 1.279-.1613l.371-.0404c.134-.0196.268-.031=
7.403-.0363.107.0029.21.0396.295.1049.075.0666.132.1513.165.2461.055.1453.0=
9.2972.105.4518.027.2904-.011.5831-.111.8572-.1.274-.259.5227-.466.7282-.34=
4.3274-.804.5057-1.279.4962-.31.0012-.614-.084-.879-.2461-.137-.113-.23-.21=
38-.218-.2299z" fill=3D"#263238"/><path d=3D"m195.4 85.5212s4.066 1.5411 8.=
024.4034c0 0-2.986 2.5052-8.432 1.5451z" fill=3D"#aa6550"/><path d=3D"m201.=
582 143.358c-.196-.129-.382-.272-.556-.428-.174-.141-.404-.315-.65-.5-.246-=
.186-.544-.404-.887-.625-.343-.222-.698-.464-1.114-.694-.415-.23-.855-.472-=
1.327-.714-2.07-1.019-4.295-1.684-6.584-1.969-.528-.056-1.028-.121-1.5-.133=
s-.912-.032-1.312-.028c-.399.004-.766.024-1.085.036-.318.012-.593.036-.807.=
061-.23.035-.464.051-.698.048.227-.058.458-.101.69-.129.268-.046.537-.078.8=
07-.097.319-.024.682-.069 1.089-.069.408 0 .848-.024 1.324 0 .476.025.984.0=
65 1.516.113 1.155.144 2.296.379 3.413.702 1.109.341 2.189.771 3.228 1.287.=
472.25.928.48 1.327.738.399.259.779.489 1.109.723.331.234.622.459.876.653s.=
464.379.629.533c.184.149.356.314.512.492z" fill=3D"#fafafa"/><path d=3D"m17=
7.892 122.64 3.308-14.333c1.267-5.39 2.768-10.5696 5.289-11.1827v1.666l-1.0=
81 40.9497s-6.741 7.665-15.33 3.413c-2.687-1.335-7.588-10.714-7.588-10.714s=
-11.405-20.348-10.929-21.272 7.443-9.904 8.153-10.352z" fill=3D"#407bff"/><=
path d=3D"m211.942 93.0659c6.575 1.2586 13.978 5.095 17.387 14.9261 1.242 3=
.59 10.295 30.058 10.295 30.058l-15.439-.323-2.493-5.45 1.21 8.855 1.045 12=
.627-38.986.657.274-19.025.375-37.8392s.436-.5729 7.161-4.2802c4.171-2.3034=
 19.171-.2057 19.171-.2057z" fill=3D"#407bff"/><path d=3D"m215.797 118.375c=
.642 7.988 1.376 10.457 2.017 18.444.016.756.152 1.505.404 2.219.135.355.35=
1.673.631.928.28.256.617.442.983.544 1.137.23 2.614 1.053 2.932-.06l-1.077-=
7.693c-.231-1.119-.589-2.209-1.065-3.248-2.392-5.313-3.275-5.623-4.841-11.1=
34" fill=3D"#000" opacity=3D".2"/><path d=3D"m192.75 97.3656.129-.831 1.247=
-7.9713c.036-.2317.158-.4412.342-.5864s.416-.2153.65-.1962l15.35 1.2828c.46=
4.0381.894.2579 1.196.6116.302.3536.452.8124.418 1.2764l-.343 4.6311-4.793 =
6.2524-6.902 1.078" fill=3D"#407bff"/><path d=3D"m185.05 112.352c.061 0 .10=
5 6.245.105 13.946s-.044 13.946-.105 13.946c-.06 0-.105-6.245-.105-13.946s.=
049-13.946.105-13.946z" fill=3D"#263238"/><path d=3D"m224.511 139.01s0-.028=
-.048-.089c-.048-.06-.077-.161-.129-.274l-.468-1.061-1.682-3.917-5.483-12.9=
53c-1.065-2.534-2.077-4.946-2.997-7.145-.465-1.03-.799-2.114-.993-3.227-.17=
-.973-.259-1.958-.266-2.945-.051-1.431.157-2.859.613-4.216l.125-.334.142-.2=
83c.066-.15.147-.293.242-.427l.169-.25c.02-.029.041-.056.065-.081-.266.446-=
.489.916-.666 1.404-.422 1.353-.605 2.77-.541 4.187.007 2.104.445 4.184 1.2=
87 6.112l3.014 7.136 5.426 12.978 1.613 3.941.432 1.077.109.282c.015.027.02=
7.056.036.085z" fill=3D"#263238"/><path d=3D"m216.709 94.4736c-.142.1433-.2=
91.278-.448.4034-.454.3459-.948.6345-1.473.8593-.83.3466-1.7.5904-2.59.7261=
-1.176.1827-2.361.2999-3.55.351-1.323.0565-2.767.0323-4.316.0605-1.646-.002=
8-3.285.1978-4.882.5971-1.768.4179-3.369 1.3616-4.591 2.707-.608.732-1.044 =
1.593-1.274 2.517-.247.948-.23 1.945.048 2.884.154.464.382.9.674 1.291.294.=
38.665.694 1.089.92.411.225.88.324 1.347.283.457-.047.88-.263 1.186-.606.27=
3-.36.467-.773.57-1.213s.112-.896.027-1.34c-.067-.438-.203-.863-.403-1.259-=
.199-.386-.438-.749-.714-1.085-1.078-1.243-2.531-2.102-4.139-2.449-1.394-.3=
284-2.846-.3118-4.232.049-1.14.295-2.228.761-3.227 1.383-.761.475-1.479 1.0=
15-2.146 1.614-.553.484-.944.904-1.211 1.178l-.298.323c-.033.038-.069.073-.=
109.105.475-.6 1.001-1.158 1.573-1.666.661-.611 1.376-1.16 2.138-1.638 1.00=
9-.642 2.112-1.124 3.268-1.428 1.408-.3781 2.888-.403 4.309-.073 1.649.348 =
3.141 1.225 4.248 2.497.281.337.527.702.734 1.089.203.416.339.861.403 1.319=
.071.468.062.944-.024 1.408-.087.484-.3.936-.617 1.311-.339.381-.808.621-1.=
315.674-.509.048-1.021-.057-1.469-.302-.45-.241-.845-.573-1.158-.977-.308-.=
41-.547-.868-.71-1.355-.29-.976-.306-2.013-.048-2.997.235-.957.687-1.847 1.=
319-2.602 1.252-1.373 2.888-2.3361 4.696-2.7637 1.614-.3947 3.272-.5871 4.9=
34-.5729 1.553-.0242 2.997 0 4.312-.0363 1.184-.0413 2.366-.1477 3.538-.318=
7.885-.127 1.751-.3571 2.582-.6858.521-.2117 1.017-.4824 1.477-.8068.161-.1=
089.27-.2098.35-.2662.037-.0402.078-.0767.122-.109z" fill=3D"#fafafa"/><pat=
h d=3D"m230.045 111.262c-.057.016-.115.026-.174.028-.113 0-.278.029-.496.03=
7-.609.02-1.22.005-1.827-.045-.771-.056-1.683-.173-2.691-.314-1.009-.141-2.=
114-.291-3.284-.351-1.094-.06-2.191.023-3.264.246-.877.178-1.719.498-2.493.=
948-.701.428-1.323.974-1.839 1.613.021-.053.051-.104.088-.149.085-.142.183-=
.277.291-.403.391-.478.858-.887 1.383-1.21.78-.476 1.633-.817 2.526-1.009 1=
.09-.238 2.206-.327 3.32-.266 1.182.06 2.291.222 3.3.371s1.912.282 2.675.35=
5c.762.073 1.387.105 1.815.109.224 0 .448.013.67.04z" fill=3D"#fafafa"/><pa=
th d=3D"m220.348 129.348c-.759-.176-1.539-.243-2.316-.198-.912.062-1.814.22=
3-2.691.48-1.028.279-2.178.686-3.469 1.086-1.402.475-2.869.729-4.349.754-1.=
704-.001-3.39-.351-4.954-1.029-1.738-.734-3.283-1.858-4.518-3.284-.668-.745=
-1.213-1.592-1.614-2.509-.426-.949-.587-1.996-.464-3.029.141-1.042.649-1.99=
9 1.432-2.699.39-.34.856-.578 1.36-.694.492-.104 1.002-.09 1.488.041s.934.3=
76 1.307.713c.365.335.618.772.727 1.255.104.472.066.965-.109 1.416-.17.433-=
.427.827-.755 1.158-.625.626-1.382 1.107-2.214 1.408-.393.149-.798.261-1.21=
1.334-.201.037-.403.081-.601.109l-.601.073c-1.474.146-2.962.041-4.401-.311-=
1.187-.282-2.331-.722-3.401-1.307-.809-.437-1.568-.962-2.263-1.565l-.404-.3=
51-.33-.335c-.179-.169-.346-.349-.501-.54l-.29-.347c-.033-.038-.063-.078-.0=
89-.121.039.033.076.07.109.109l.303.331c.16.185.331.36.512.524l.335.327.403=
.343c.698.586 1.457 1.096 2.263 1.52 1.065.569 2.201.995 3.377 1.267 1.425.=
339 2.896.435 4.353.283l.589-.073c.198-.032.403-.077.597-.113.403-.073.799-=
.184 1.182-.331.806-.293 1.537-.76 2.142-1.367.309-.313.55-.685.71-1.094.15=
8-.415.193-.868.101-1.303-.12-.444-.358-.847-.69-1.166-.332-.318-.745-.54-1=
.194-.641-.46-.133-.945-.146-1.412-.04-.466.107-.898.33-1.255.649-.747.666-=
1.229 1.578-1.359 2.57-.116.994.038 2.002.444 2.917.39.895.918 1.724 1.565 =
2.456 1.214 1.4 2.732 2.504 4.438 3.228 1.538.671 3.198 1.023 4.877 1.032 1=
.465-.018 2.918-.263 4.308-.726 1.287-.403 2.421-.782 3.482-1.053.886-.249 =
1.796-.399 2.715-.447.334-.021.67-.021 1.004 0 .287.044.533.064.727.108l.43=
9.109c.058.017.114.041.166.073z" fill=3D"#fafafa"/><path d=3D"m184.903 125.=
045c-.037 0-.222-.404-.613-1.081-1.024-1.72-2.518-3.113-4.305-4.014-.686-.3=
47-1.137-.501-1.125-.537.113.01.224.036.33.077.294.087.581.195.86.322.924.4=
22 1.774.99 2.517 1.683.744.692 1.37 1.501 1.852 2.396.152.268.287.545.403.=
831.041.103.068.212.081.323z" fill=3D"#fafafa"/><path d=3D"m169.258 142.309=
s-.029-.033-.065-.101c-.036-.069-.097-.178-.165-.303-.146-.266-.355-.665-.6=
14-1.182-.749-1.458-1.367-2.979-1.847-4.546-.321-1.089-.515-2.211-.577-3.34=
4-.032-.633-.006-1.268.077-1.896.1-.666.259-1.32.476-1.957.471-1.377 1.286-=
2.61 2.368-3.582.578-.53 1.257-.938 1.997-1.198s1.525-.367 2.307-.315c.813.=
061 1.573.43 2.122 1.033.262.298.452.651.556 1.034.104.382.119.783.045 1.17=
2-.087.382-.286.729-.573.997-.279.256-.617.44-.984.536-.71.185-1.464.094-2.=
11-.254-.599-.323-1.123-.77-1.537-1.311-.397-.501-.724-1.053-.972-1.642-.43=
7-1.056-.618-2.201-.529-3.34.082-.94.357-1.854.807-2.683.334-.625.761-1.197=
 1.267-1.694.316-.315.663-.598 1.037-.843.125-.085.23-.137.298-.178l.105-.0=
52s-.129.093-.379.266c-.36.257-.696.546-1.005.863-.483.502-.891 1.072-1.21 =
1.691-.429.819-.685 1.716-.754 2.638-.078 1.117.11 2.237.548 3.268.245.571.=
566 1.107.953 1.593.401.518.907.945 1.484 1.255.603.324 1.307.408 1.969.234=
.339-.08.651-.251.899-.496s.424-.554.509-.892c.064-.356.047-.723-.049-1.072=
-.097-.349-.27-.672-.508-.945-.515-.562-1.225-.905-1.985-.96-.754-.05-1.51.=
053-2.222.304-.713.25-1.368.642-1.925 1.152-1.056.95-1.854 2.152-2.32 3.494=
-.217.623-.378 1.264-.48 1.916-.083.617-.112 1.241-.084 1.864.056 1.121.24 =
2.232.548 3.312.465 1.563 1.059 3.085 1.775 4.55l.577 1.21c.061.129.109.234=
.149.315.041.081.021.089.021.089z" fill=3D"#fafafa"/><path d=3D"m164.265 10=
6.696s.021.057.037.166c.028.163.048.327.06.492.053.602.053 1.209 0 1.811-.1=
63 2.007-.849 3.935-1.99 5.594-1.141 1.658-2.697 2.989-4.513 3.858-.543.265=
-1.109.483-1.69.649-.202.065-.367.093-.476.121-.109.029-.17.033-.17.025.203=
-.087.41-.162.622-.222.566-.196 1.117-.432 1.65-.706 1.767-.893 3.281-2.216=
 4.404-3.847 1.122-1.631 1.816-3.518 2.018-5.488.064-.595.087-1.193.069-1.7=
91-.021-.22-.028-.441-.021-.662z" fill=3D"#fafafa"/><path d=3D"m233.577 124=
.189c-.6.376-1.128.857-1.557 1.42-.404.52-.807 1.21-1.263 1.936-.5.813-1.07=
7 1.576-1.723 2.279-1.181 1.276-2.581 2.33-4.135 3.111-.467.236-.95.443-1.4=
44.617-.18.071-.366.125-.557.161.167-.095.343-.175.525-.238.339-.149.831-.3=
59 1.408-.673 1.507-.811 2.87-1.864 4.034-3.119.639-.692 1.213-1.441 1.715-=
2.239.401-.666.838-1.31 1.311-1.928.328-.414.718-.775 1.157-1.069.119-.079.=
245-.148.376-.206.105-.04.153-.06.153-.052z" fill=3D"#fafafa"/><path d=3D"m=
211.8 93.5489c-.059.0254-.121.0417-.185.0484l-.537.0969c-.468.0766-1.146.17=
75-1.981.2904-1.678.2259-4.002.4922-6.579.6818-1.207.1103-2.419.1534-3.631.=
1291-1-.0244-1.99-.1943-2.941-.5043-.642-.208-1.247-.5142-1.795-.9077-.15-.=
1053-.293-.2198-.428-.3429-.089-.0887-.133-.1331-.129-.1412.004-.008.21.161=
4.601.4035.557.3582 1.162.6379 1.795.831.942.2866 1.92.4388 2.905.4518 1.21=
2.0129 2.424-.0356 3.631-.1452 2.569-.1896 4.893-.4276 6.571-.6172l1.989-.2=
26c.237-.0328.475-.049.714-.0484z" fill=3D"#263238"/><path d=3D"m239.428 14=
0.907c.304 1.56.26 3.169-.127 4.711s-1.109 2.98-2.114 4.211c-1.006 1.232-2.=
269 2.228-3.702 2.917s-3 1.054-4.59 1.07l-30.559-.339.807-9.896 26.165-3.92=
9" fill=3D"#b78876"/><path d=3D"m201.032 144.012c-.142.045-1.905-.746-3.486=
-1.476-1.677-.777-3.519-1.134-5.365-1.041l-7.286.371s.448 1.21 1.21 1.614c.=
763.403 5.333.532 5.333.532l-.234.238c-.955.985-2.259 1.555-3.63 1.59h-.698=
c-.517-.041-1.027-.141-1.521-.299l-1.795-.726c-.545-.242-1.118-.206-1.356.3=
43s-.056 1.178 1.428 1.924c.827.404 3.901 1.481 3.901 1.481.021.437.082.872=
.182 1.299.133.448 1.339.895 1.339.895.314.664.672 1.306 1.073 1.921.141.16=
1 1.235.504 2.401.831 1.879.526 3.869.51 5.74-.045l2.776-.831v-8.471" fill=
=3D"#b78876"/><path d=3D"m191.05 149.543c-1.284-.033-2.542-.364-3.675-.968 =
0-.049.807.258 1.827.52 1.021.262 1.852.387 1.848.448z" fill=3D"#aa6550"/><=
path d=3D"m190.796 151.24c-.342.071-.697.045-1.025-.076-.342-.063-.66-.22-.=
919-.452.329.066.653.159.968.278.331.058.658.142.976.25z" fill=3D"#aa6550"/=
><path d=3D"m192.121 143.354c-.159.225-.347.427-.56.601-.483.462-1.017.868-=
1.59 1.211-.577.337-1.205.579-1.859.718-.263.07-.535.1-.807.088.901-.207 1.=
772-.531 2.59-.964.56-.338 1.093-.719 1.593-1.141.196-.19.408-.361.633-.513=
z" fill=3D"#aa6550"/><path d=3D"m201.758 142.685-.742 11.191h28.783c1.955.0=
01 3.873-.53 5.547-1.537 1.896-1.139 3.395-2.835 4.293-4.856s1.151-4.269.72=
6-6.44c-.414-2.125-1.036-4.204-1.856-6.208l-14.474 2.384.464 1.799z" fill=
=3D"#407bff"/><path d=3D"m231.165 138.078c-.096.028-.193.049-.291.065l-.843=
.153-3.11.516-10.231 1.614-10.243 1.569-3.106.452-.847.113c-.099.014-.199.0=
22-.299.024.096-.033.194-.056.295-.068l.839-.15 3.098-.52 10.231-1.614 10.2=
63-1.581 3.106-.452.847-.113c.097-.013.194-.015.291-.008z" fill=3D"#263238"=
/><path d=3D"m216.43 153.884c-.053 0 .173-.807.202-2.126.006-.358-.009-.717=
-.045-1.073-.034-.407-.094-.811-.177-1.21-.2-.921-.515-1.813-.936-2.655-.43=
2-.838-.96-1.623-1.574-2.34l-.403-.472c-.137-.153-.286-.286-.424-.423-.27-.=
279-.556-.5-.806-.714-1.029-.807-1.759-1.21-1.735-1.247.178.072.35.159.512.=
262.201.105.395.222.581.351.214.154.48.303.734.521.291.217.568.452.831.702.=
142.137.299.266.44.403l.44.476c.632.721 1.174 1.516 1.613 2.368.428.86.742 =
1.771.936 2.711.083.411.137.828.162 1.247.026.362.026.726 0 1.089-.008.301-=
.035.602-.081.899-.022.225-.061.447-.117.666-.035.192-.086.381-.153.565z" f=
ill=3D"#fafafa"/><path d=3D"m233.97 153.04c-.45-.003-.897-.055-1.335-.157-.=
516-.119-1.017-.296-1.493-.529-.631-.313-1.2-.737-1.682-1.25-.585-.63-1.01-=
1.391-1.239-2.219-.127-.46-.199-.934-.214-1.412-.029-.505.012-1.011.122-1.5=
05.107-.525.31-1.027.597-1.48.299-.481.715-.878 1.21-1.154.511-.298 1.099-.=
438 1.69-.403.304.021.6.103.871.242.271.138.512.33.706.564.202.231.354.501.=
445.794.092.292.122.6.088.905-.05.295-.164.576-.336.822-.172.245-.396.45-.6=
56.598-.977.511-2.101.665-3.179.436-1.531-.239-2.929-1.005-3.954-2.166s-1.6=
11-2.644-1.658-4.192c-.026-.45.012-.901.113-1.34-.037.445-.048.89-.032 1.33=
6.075 1.214.472 2.386 1.15 3.396.505.742 1.156 1.373 1.913 1.853.758.481 1.=
606.802 2.492.943 1.03.221 2.105.079 3.042-.403.234-.133.435-.315.59-.535s.=
259-.471.305-.736c.024-.276-.007-.554-.091-.818s-.219-.509-.398-.721-.397-.=
386-.644-.513c-.246-.127-.515-.204-.791-.227-.547-.038-1.093.087-1.569.359-=
.449.268-.834.629-1.13 1.059s-.495.919-.585 1.434c-.109.478-.151.97-.125 1.=
46.011.462.078.921.198 1.368.212.806.617 1.549 1.178 2.166.461.506 1.007.92=
8 1.613 1.246.465.238.955.423 1.461.553.847.19 1.331.198 1.327.226z" fill=
=3D"#fafafa"/><path d=3D"m119.792 31.1309h-72.0878c-4.2134 0-8.2542 1.6737-=
11.2336 4.6529-2.9793 2.9793-4.6531 7.02-4.6531 11.2333v25.6891h-15.5519l15=
.5519 15.5513v17.4635c0 4.213 1.6738 8.254 4.6531 11.233 2.9794 2.98 7.0202=
 4.653 11.2336 4.653h72.0878c4.213 0 8.254-1.673 11.233-4.653 2.98-2.979 4.=
653-7.02 4.653-11.233v-58.7039c0-4.2133-1.673-8.254-4.653-11.2333-2.979-2.9=
792-7.02-4.6529-11.233-4.6529z" fill=3D"#407bff"/><path d=3D"m90.3603 33.84=
62c-.4559 1.1819.3267 2.4688 1.2102 3.3805s1.9405 1.775 2.2188 3.0094c.23 1=
.0368-.1533 2.1139-.0605 3.1708s.6777 2.0534 1.0085 3.0861c.6011 1.8718.359=
1 3.9171-.1775 5.8091-.3869 1.5722-1.1263 3.0358-2.1623 4.2801-.5218.6163-1=
.1685 1.1148-1.8973 1.4625s-1.5232.5368-2.3305.5546c-.2283.0143-.4571-.0184=
-.6722-.0961s-.412-.1988-.5784-.3558c-.2263-.3263-.3512-.7122-.359-1.1093-.=
9239-8.1227-.9239-16.3239 0-24.4465" fill=3D"#263238"/><path d=3D"m89.4894 =
60.0237c3.5097-.1533 6.7532 3.2071 6.4748 6.7087 3.5541.8673 6.0518 4.7763 =
5.3698 8.3666-.23 1.1861-.759 2.3156-.831 3.5178-.081 1.3151.403 2.5939.641=
 3.8848.457 2.3527.213 4.7878-.702 7.0031-.5525 1.3353-1.5853 2.715-3.0295 =
2.7553-.4893-.0206-.966-.1613-1.3882-.4096-.4221-.2483-.7766-.5967-1.0323-1=
.0144-.5028-.8456-.8345-1.7818-.9763-2.7553-.6253-3.1546-.8068-6.394-1.6782=
-9.4882-.8714-3.0941-2.5657-6.1439-5.3614-7.7131-1.5451-.8714-3.4008-1.2828=
-4.6313-2.5617-.6189-.6979-1.0316-1.5542-1.1919-2.4732-.1602-.919-.0618-1.8=
644.2843-2.7307.3626-.8585.9021-1.6309 1.5833-2.2669s1.4888-1.1213 2.3702-1=
.4243" fill=3D"#263238"/><path d=3D"m65.1993 36.7384c-.0403-1.5007 1.3434-2=
.6584 2.7392-3.2272 1.3959-.5689 2.949-.7585 4.1915-1.6137 1.1054-.7422 1.8=
437-1.9 2.7756-2.8238 1.1507-1.1669 2.5912-2.0066 4.1737-2.4328 1.5825-.426=
1 3.2499-.4235 4.8311.0077 1.5811.4312 3.019 1.2754 4.166 2.446s1.9618 2.62=
53 2.3607 4.2148c.5366 2.1341.1695 4.6594-1.5128 6.0794-1.9525 1.6459-4.841=
 1.2788-7.3543.7665-2.5133-.5124-5.3372-1.0166-7.4269.4558-1.2587.8835-1.98=
48 2.3277-2.7553 3.663-.7706 1.3352-1.7347 2.6947-3.1911 3.199-1.4563.5042-=
3.4371-.3671-3.4936-1.9041" fill=3D"#263238"/><path d=3D"m71.1302 37.045c-.=
6246.0675-1.2012.3669-1.6158.8389-.4145.4721-.6369 1.0825-.6232 1.7106l1.49=
67 33.8863c.0645 4.0018 5.9141 6.2487 10.1984 6.1479 4.3287-.1009 4.4658-2.=
2228 4.5223-6.031.0888-6.3577.0807-8.7458.0847-8.7095.0041.0363 5.4663-.919=
8 6.5838-8.0682.5527-3.5459.3631-9.3631.0524-14.1193-.2783-4.2801-3.1264-9.=
5244-7.3906-9.0645z" fill=3D"#ffbe9d"/><path d=3D"m90.5735 47.1946c.0023.23=
8-.0882.4677-.2524.6401-.1642.1725-.3891.2742-.627.2837-.1151.0099-.2311-.0=
032-.3412-.0385-.11-.0354-.2119-.0922-.2997-.1673-.0879-.0751-.1599-.1669-.=
212-.2701-.052-.1032-.083-.2157-.0911-.331-.0033-.2384.0869-.4686.2513-.641=
2.1644-.1727.3899-.2741.6281-.2826.1152-.0099.2311.0032.3412.0385.11.0354.2=
119.0922.2998.1673.0878.0751.1599.1669.2119.2701s.083.2157.0911.331z" fill=
=3D"#263238"/><path d=3D"m90.5483 45.7433c-.1129.121-.8068-.4034-1.7912-.40=
34-.9843 0-1.7064.5002-1.8153.3792-.0525-.0524.0645-.2703.3792-.5002.4247-.=
2958.9307-.4522 1.4482-.4478.5123-.003 1.0123.1566 1.4281.4558.3026.2381.40=
75.464.351.5164z" fill=3D"#263238"/><path d=3D"m81.1204 47.1946c.0023.238-.=
0883.4677-.2524.6401-.1642.1725-.3891.2742-.627.2837-.1152.0099-.2311-.0032=
-.3412-.0385-.11-.0354-.2119-.0922-.2998-.1673-.0878-.0751-.1599-.1669-.211=
9-.2701s-.083-.2157-.0911-.331c-.0034-.2384.0869-.4686.2512-.6412.1644-.172=
7.3899-.2741.6282-.2826.1152-.0099.2311.0032.3412.0385.11.0354.2119.0922.29=
97.1673.0879.0751.1599.1669.212.2701.052.1032.083.2157.0911.331z" fill=3D"#=
263238"/><path d=3D"m80.8961 45.9482c-.113.121-.8068-.4034-1.7912-.4034-.98=
43 0-1.7064.5002-1.8113.3792-.0525-.0524.0605-.2703.3752-.5002.4245-.2965.9=
304-.4543 1.4482-.4518.5128-.0019 1.0128.1592 1.4281.4598.2824.2461.3873.46=
4.351.5164z" fill=3D"#263238"/><path d=3D"m84.4708 53.2421c0-.0565.6052-.16=
13 1.6137-.2904.2501-.0283.4881-.0807.5325-.2501.0185-.2601-.0404-.5198-.16=
94-.7464l-.7504-1.9121c-1.0368-2.723-1.7911-4.9619-1.6782-5.0023.113-.0403 =
1.0408 2.1341 2.0776 4.8409l.7181 1.9243c.1564.303.2034.6507.1331.9843-.035=
4.088-.09.1669-.1597.2312-.0698.0642-.1531.112-.2437.1399-.1314.0363-.2671.=
0553-.4034.0565-1.0489.0726-1.6661.0807-1.6702.0242z" fill=3D"#263238"/><pa=
th d=3D"m85.186 64.8924c-3.3597.058-6.6695-.8177-9.561-2.5294 0 0 2.3277 4.=
8974 9.4682 4.2116z" fill=3D"#eb996e"/><path d=3D"m84.0562 55.614c-.1899-.2=
386-.4383-.4242-.721-.5387s-.5902-.154-.8927-.1148c-.2172.0195-.4282.0828-.=
6203.1861s-.3612.2445-.4972.415c-.1311.1645-.2085.3655-.2215.5755s.039.419.=
1489.5984c.1579.1772.3655.3027.5958.3603.2302.0576.4725.0445.6952-.0375.464=
4-.1651.891-.4216 1.2546-.7544.1117-.078.2075-.1766.2824-.2905.0367-.0541.0=
564-.1181.0564-.1835 0-.0655-.0197-.1294-.0564-.1836" fill=3D"#eb996e"/><pa=
th d=3D"m81.565 53.9348c.1614 0 .1654 1.061 1.0812 1.8153.9158.7544 2.0534.=
6374 2.0615.8069 0 .0685-.2542.2097-.7343.2218-.6261.0114-1.2361-.1986-1.72=
26-.593-.4655-.3808-.7649-.9275-.835-1.5249-.0444-.4719.0766-.7261.1492-.72=
61z" fill=3D"#263238"/><path d=3D"m81.3058 44.2214c-.0968.2703-1.0811.1452-=
2.2349.2864s-2.0897.4599-2.243.2179c-.0686-.113.0928-.3671.468-.6253.5009-.=
322 1.0685-.5256 1.6599-.5955.5913-.0699 1.1908-.0042 1.753.1921.4397.1896.=
6535.3994.597.5244z" fill=3D"#263238"/><path d=3D"m90.1742 42.8943c-.1734.2=
219-.8512 0-1.6701 0-.819 0-1.5048.1533-1.6621-.0807-.0726-.1129.0363-.3348=
.3389-.5486.4048-.2533.8756-.3812 1.3529-.3675.4774.0136.9401.1682 1.3298.4=
441.3106.2139.3953.4438.3106.5527z" fill=3D"#263238"/><path d=3D"m66.1278 6=
0.5155c.9836.1989 2.0005.161 2.9666-.1105.9661-.2714 1.8539-.7687 2.59-1.45=
08s1.2994-1.5295 1.6436-2.4721c.3441-.9427.4592-1.9537.3356-2.9496-.2218-1.=
7548-1.1658-3.5338-.5567-5.1959.8069-2.1905 3.8769-2.8642 4.6918-5.0547.472=
-1.2707.0484-2.7593.5849-4.0058.5043-1.1699 1.7387-1.8436 2.9409-2.2793s2.4=
851-.7342 3.4936-1.5168c1.0086-.7826 1.6339-2.2591.9844-3.3604-5.328.6393-1=
0.5609 1.9109-15.5881 3.788-1.1659.4357-2.3882.9601-3.0781 1.9929-.7261 1.0=
972-.6898 2.5092-.6293 3.8243.2985 6.4061-.6777 12.4048-.3792 18.8109" fill=
=3D"#263238"/><path d=3D"m64.4296 58.5064c-.0406-.0061-.08-.0184-.117-.0363=
-.1151-.0402-.2249-.0944-.3267-.1614-.393-.2542-.7019-.6192-.8875-1.0489-.3=
054-.6791-.3873-1.4376-.234-2.1663.2429-.9466.6843-1.8308 1.2949-2.5939.625=
3-.8713 1.4201-1.7548 1.9607-2.8803.2882-.5719.4492-1.1995.4719-1.8395-.023=
2-.6795-.1596-1.3502-.4034-1.9848-.2719-.6861-.4154-1.4163-.4236-2.1542.039=
1-.7896.2709-1.5576.6753-2.2369s.9689-1.2493 1.6444-1.66c.7244-.3953 1.5279=
-.6241 2.3519-.6697.7974-.0391 1.5871-.1746 2.3519-.4034.7181-.2719 1.3083-=
.803 1.6541-1.4886.3295-.6744.5916-1.3798.7826-2.1058.2041-.6965.4743-1.371=
9.8068-2.017.6066-1.2231 1.5303-2.261 2.6747-3.0054 1.0105-.6353 2.1927-.94=
25 3.3847-.8794.9872.051 1.9276.4366 2.6666 1.0932.5508.4952.9613 1.127 1.1=
9 1.8315.1487.441.205.9079.1654 1.3716-.0082.1613-.0353.321-.0807.476 0 0 .=
0283-.1654.0404-.4801.0194-.4565-.0491-.9126-.2017-1.3433-.2778-.7928-.7875=
-1.4837-1.4631-1.983-.6755-.4992-1.4856-.7838-2.325-.8167-1.159-.051-2.3055=
.256-3.2838.8795-1.1137.7318-2.0108 1.7487-2.598 2.9448-.3259.6453-.588 1.3=
209-.7827 2.0171-.197.7391-.4673 1.4567-.8068 2.1421-.187.3547-.431.6763-.7=
221.952-.301.2787-.655.4941-1.0408.6334-.786.2422-1.5991.3859-2.4205.4276-.=
7942.0418-1.5689.261-2.2673.6414-.6457.3917-1.1858.9354-1.5732 1.5837s-.610=
4 1.3815-.6496 2.1357c.0072.7152.1439 1.4232.4034 2.0897.2568.654.4 1.3471.=
4236 2.0493-.0272.6659-.1978 1.3182-.5002 1.9121-.5648 1.1538-1.3676 2.0171=
-1.9969 2.8925-.6074.7415-1.0502 1.6037-1.299 2.5293-.1608.7044-.0944 1.441=
5.1896 2.1058.1745.4233.4654.7884.8391 1.0529.2703.1372.4357.1775.4316.1937=
z" fill=3D"#455a64"/><path d=3D"m85.1641 27.3999c.0979.0072.193.0362.2783.0=
847.2421.1027.4689.2385.6737.4034.7029.5454 1.1685 1.3406 1.3 2.2205.1314.8=
798-.0813 1.7764-.594 2.5034-.149.2167-.3253.4133-.5244.585-.1453.121-.234.=
1734-.2421.1613.2275-.2577.4416-.527.6414-.8068.4547-.7089.637-1.5585.5131-=
2.3915s-.5455-1.5928-1.1868-2.1387c-.2742-.2235-.5611-.4309-.8592-.6213z" f=
ill=3D"#455a64"/><path d=3D"m100.969 76.3609c.085-.2481.153-.5014.205-.7584=
.095-.7042-.028-1.4205-.351-2.0534-.497-.9289-1.2891-1.6667-2.2508-2.0977-.=
5627-.2662-1.1506-.4757-1.7549-.6253-.6293-.1613-1.2949-.2945-1.9606-.5123-=
.6698-.1825-1.2874-.5197-1.8033-.9843-.4619-.4626-.7608-1.063-.8512-1.7105-=
.1089-.601-.1452-1.1699-.2783-1.6781-.1193-.4703-.3411-.9084-.6495-1.2829-.=
4512-.5486-1.0505-.9561-1.7267-1.1739-.48-.1452-.7665-.1452-.7665-.1694.068=
5-.0068.1374-.0068.2058 0 .1973.0136.3931.0446.585.0927.7075.1952 1.34.5986=
 1.8153 1.1578.3322.3867.5735.843.706 1.3353.1452.5285.1896 1.1053.3026 1.6=
863.0829.6123.3667 1.1798.8068 1.6136.4947.4392 1.0843.7581 1.7226.9319.649=
5.2097 1.3151.3469 1.9485.5123.6124.1498 1.2073.3634 1.775.6374.9936.4549 1=
.8042 1.2326 2.2992 2.2066.323.6618.428 1.409.299 2.134-.039.1934-.099.3816=
-.182.5608-.026.0623-.058.1218-.096.1775z" fill=3D"#455a64"/><path d=3D"m69=
.9393 63.7269c-3.6307-.9682-6.737-4.5061-8.0038-8.0279-1.2667-3.5217-1.2667=
-7.3662-.9399-11.0937.2339-2.6988.71-5.5549 2.5011-7.5881 2.7029-3.0659 5.6=
479-4.6311 9.7143-4.502" fill=3D"#263238"/><path d=3D"m91.0501 71.5923-3.36=
86-.8472c.3672-.2524.635-.6249.7572-1.0534.1222-.4286.0913-.8863-.0875-1.29=
44-.2132-.5232-.566-.9778-1.0199-1.3142-.4538-.3365-.9913-.5419-1.5539-.593=
9l-15.3541-1.7347c-.2684-.0358-.5411.0195-.7744.157s-.4137.3493-.5125.6014c=
-.6804 1.732-.7033 3.1574-.0686 4.2761h-.0726l-2.9611.589-5.0185 44.27 32.8=
705-1.537z" fill=3D"#f5f5f5"/><path d=3D"m86.2054 70.9158c-.2017.0589-.4083=
.0994-.6173.121-.4034.0646-.9803.1331-1.6984.2058-3.758.3491-7.5466.1603-11=
.2513-.5608-.71-.1412-1.2788-.2703-1.6701-.3751-.2056-.0399-.4069-.0993-.60=
11-.1775.2083.0152.4149.049.6172.1008.4034.0766.9723.1856 1.6823.3066 3.700=
9.6368 7.4652.8237 11.2109.5567.7181-.0484 1.2991-.1009 1.7025-.1412.2071-.=
0285.4162-.0406.6253-.0363z" fill=3D"#263238"/><path d=3D"m71.3438 77.9502c=
.3233-.6715.7082-1.3116 1.1497-1.9121.3853-.6386.8297-1.2396 1.3272-1.7952-=
.3239.6721-.7102 1.3123-1.1537 1.9122-.3833.6389-.8263 1.24-1.3232 1.7951z"=
 fill=3D"#e0e0e0"/><path d=3D"m81.9428 78.7012c-1.0253-1.0528-1.948-2.201-2=
.7553-3.429.5375.503 1.0264 1.0554 1.4604 1.65.4881.5497.9218 1.1455 1.2949=
 1.779z" fill=3D"#e0e0e0"/><path d=3D"m77.0092 84.3287c-.2319-.7176-.398-1.=
4548-.4962-2.2026-.1679-.7352-.2638-1.4851-.2864-2.2389.2325.7175.3986 1.45=
48.4962 2.2026.1633.7361.2592 1.4855.2864 2.2389z" fill=3D"#e0e0e0"/><path =
d=3D"m85.0996 84.6758c-.5695.7083-1.1926 1.3719-1.8638 1.9847-.6233.6637-1.=
2977 1.2774-2.017 1.8356.5694-.7085 1.1925-1.372 1.8637-1.9848.6259-.661 1.=
3002-1.2746 2.0171-1.8355z" fill=3D"#e0e0e0"/><path d=3D"m74.2711 92.2188c-=
1.4866-.8882-2.6974-2.1716-3.4977-3.7073.0565-.0363.6737.9319 1.646 1.9525.=
9722 1.0206 1.892 1.7024 1.8517 1.7548z" fill=3D"#e0e0e0"/><path d=3D"m77.7=
438 98.0444c0 .0605-.9198.355-1.9848.9238-1.0651.5688-1.8437 1.1658-1.884 1=
.1138.5078-.544 1.1142-.9862 1.7871-1.3034.6399-.377 1.3469-.6263 2.0817-.7=
342z" fill=3D"#e0e0e0"/><path d=3D"m83.4032 98.4202c-.2404-.2037-.4558-.435=
2-.6414-.6899-.3711-.4437-.8673-1.0771-1.3837-1.7992s-.9521-1.3958-1.2547-1=
.896c-.18-.2586-.329-.5375-.4437-.831.2146.2302.4052.4816.5688.7503l1.303 1=
.8557 1.3273 1.8315c.2018.2402.3776.5012.5244.7786z" fill=3D"#e0e0e0"/><pat=
h d=3D"m83.9718 103.39c-.7841.275-1.5956.464-2.4205.565-.8159.17-1.6476.252=
-2.481.246.8058-.203 1.6237-.354 2.4488-.452.8097-.168 1.6288-.288 2.4527-.=
359z" fill=3D"#e0e0e0"/><path d=3D"m74.7942 70.2893c-.0282 0 .1654-.2421.33=
08-.6697.2049-.5595.2659-1.1615.1775-1.7508-.1001-.5938-.2749-1.1726-.5204-=
1.7225-.1188-.2251-.2109-.4634-.2743-.71.1685.1934.3047.4127.4034.6495.2935=
.5466.4939 1.1383.5931 1.7508.0975.6254.0121 1.2656-.2461 1.8435-.0902.245-=
.2518.4572-.464.6092z" fill=3D"#e0e0e0"/><path d=3D"m80.9425 66.9731c.2876.=
3961.4932.8456.6048 1.3222.1115.4766.1267.9707.0447 1.4533-.0605 0-.0686-.6=
414-.2501-1.4039-.1816-.7624-.4438-1.3393-.3994-1.3716z" fill=3D"#e0e0e0"/>=
<path d=3D"m108.644 98.5158 15.29-26.8549 1.843-4.6594s4.769-10.2465 5.099-=
11.2954c.384-1.2102 1.057-1.307 1.493-1.1376.678.2703.896 1.2788.597 2.3236=
-.105.3591-1.956 5.9906-1.956 5.9906.497-.4292 1.026-.8215 1.581-1.1739 1.1=
74-.8068 3.324-.7665 5.317-.0565s2.485 1.0852 2.247 1.8678c-.053.1469-.138.=
2801-.249.3897-.111.1097-.245.1929-.392.2437-.47.1959-.996.2075-1.473.0322-=
3.203-1.0892-4.623.355-4.623.355l-.238.6092s2.42-.7705 3.776.3187c.202.1599=
.372.3571.5.5809.439.1433.832.3986 1.142.7407.31.3422.525.7592.624 1.2099s.=
078.9195-.059 1.3599c-.138.4404-.388.8372-.727 1.1512-.147.1676-.341.2866-.=
557.3413-.216.0548-.444.0427-.653-.0347-.195-.1332-.353-.3155-.456-.5285-.0=
65-.1066-.14-.2078-.222-.3025l-.911 1.6136s-2.247 3.5096-3.054 5.1515l-10.5=
29 28.0491c-.347.923-.873 1.768-1.55 2.486-.676.717-1.489 1.293-2.39 1.693s=
-1.874.616-2.859.636c-.986.021-1.966-.156-2.883-.519-.99-.39-1.884-.989-2.6=
22-1.755s-1.303-1.682-1.656-2.685c-.354-1.003-.487-2.071-.392-3.131.094-1.0=
59.416-2.0857.942-3.0102z" fill=3D"#ffbe9d"/><path d=3D"m124.977 71.5693c.5=
4.355 1.286.113 1.771-.3066.484-.4195.806-.9964 1.274-1.4522 1.36-1.3595 3.=
631-1.4886 4.934-2.9127.182-.1976.343-.5002.186-.718-.158-.2179-.448-.1775-=
.69-.1291l-6.257 1.2385" fill=3D"#ffbe9d"/><path d=3D"m126.195 67.2886c.211=
-.0955.434-.1633.662-.2017.432-.1089 1.061-.2501 1.835-.4034l2.728-.5769.80=
6-.1654c.311-.1138.654-.1022.956.0323.087.064.152.1523.188.2536s.041.2111.0=
14.3152c-.041.1708-.125.3288-.242.4599-.211.2333-.448.4417-.706.6212-1.041.=
7221-2.182 1.0166-3.094 1.4967-.435.2055-.835.4777-1.186.8068-.323.3106-.57=
7.6656-.843.9681-.192.267-.44.4887-.726.6497s-.605.2573-.932.2822c-.48 0-.6=
78-.238-.662-.242.21.072.432.1062.654.1008.297-.0492.581-.1596.834-.3242.25=
3-.1645.469-.3797.634-.6318.258-.3526.534-.6919.827-1.0166.366-.3625.786-.6=
647 1.247-.8956.94-.5083 2.077-.8068 3.037-1.4926.232-.1643.445-.3522.638-.=
5607.177-.1977.238-.4357.113-.5043s-.404-.0363-.682.0202l-.807.1492-2.743.5=
043c-.783.1371-1.416.242-1.856.3066-.227.0524-.461.0687-.694.0484z" fill=3D=
"#eb996e"/><path d=3D"m133.085 65.1578c.16-.3745.406-.7061.718-.9682.349-.2=
618.774-.4034 1.21-.4034.549.0033 1.093.0931 1.614.2663.531.1794 1.08.3037 =
1.638.3711.467.0537.941-.0187 1.371-.2098.148-.0683.278-.1702.38-.2977.101-=
.1275.172-.277.205-.4365.031-.1122.031-.2306 0-.3429-.024 0-.024.121-.085.3=
227-.044.136-.117.2606-.215.3651-.098.1046-.217.1865-.349.2401-.411.1526-.8=
53.2024-1.287.1452-.541-.0851-1.074-.2202-1.59-.4034-.539-.1774-1.103-.2646=
-1.67-.2582-.476.0111-.935.1816-1.303.4841-.244.2031-.432.4657-.545.7624-.1=
.238-.109.3591-.092.3631z" fill=3D"#eb996e"/><path d=3D"m133.073 56.4888c-.=
158.3609-.285.7347-.379 1.1174-.238.7746-.521 1.6943-.835 2.7109-.315 1.016=
6-.626 1.9283-.876 2.6988-.146.3655-.258.7436-.335 1.1296.201-.3399.366-.69=
95.493-1.0731.266-.6777.605-1.6136.944-2.6827.338-1.069.601-2.017.77-2.7391=
.113-.3785.186-.768.218-1.1618z" fill=3D"#eb996e"/><path d=3D"m133.953 66.8=
446c.123.0177.248.0177.371 0 .155-.0265.314-.0193.465.0211.152.0404.294.113=
.415.2129.07.074.12.1649.144.2638.025.099.024.2026-.003.3009-.065.2478-.162=
.4864-.287.71-.161.2806-.214.6107-.149.9279.055.168.149.3208.274.4462.124.1=
254.277.2199.444.2759.166.0524.34.0706.513.0532.173-.0173.34-.0698.492-.154=
1.282-.1443.514-.3707.665-.6495.219-.4597.368-.949.444-1.4522.059-.3377.059=
-.683 0-1.0207-.012-.1268-.061-.2473-.141-.3469-.02 0 .032.1291.052.359.018=
.3291-.011.6591-.084.9803-.096.4769-.253.9393-.468 1.3756-.132.2457-.341.44=
1-.594.5555-.254.1146-.539.142-.81.0779-.133-.043-.254-.1164-.354-.2145-.09=
9-.098-.174-.218-.219-.3503-.053-.2635-.008-.5372.125-.7705.126-.2442.22-.5=
03.282-.7705.028-.1281.021-.2609-.017-.3859-.039-.125-.11-.238-.204-.3281-.=
141-.111-.305-.1878-.48-.2242s-.356-.0315-.529.0144c-.12.012-.237.0433-.347=
.0928z" fill=3D"#eb996e"/><path d=3D"m136.593 69.669s-.077.1049-.069.3308c.=
011.1585.06.3122.141.4487.081.1366.193.2523.327.3379.207.119.441.1816.68.18=
16.238 0 .473-.0626.68-.1816.497-.2869.864-.7553 1.024-1.307.176-.5401.176-=
1.1219 0-1.662-.143-.4366-.401-.8267-.746-1.1296-.228-.2037-.492-.3625-.779=
-.4679-.102-.0489-.213-.0738-.326-.0726.359.174.693.3966.992.6615.338.3302.=
577.7487.69 1.2079s.095.9408-.053 1.3901c-.144.4957-.467.9199-.907 1.19-.17=
6.1032-.375.1602-.579.1659-.204.0056-.406-.0403-.587-.1336-.12-.0701-.224-.=
165-.305-.2784-.081-.1133-.136-.2426-.163-.3791-.041-.1775 0-.3026-.02-.302=
6z" fill=3D"#eb996e"/><path d=3D"m131.499 72.2029c-.16-.1079-.348-.1655-.54=
1-.1654-.492-.048-.986.0699-1.404.3348l.174.117c.054-.2663.092-.5358.113-.8=
068.141-1.5571-.355-2.7593-.403-2.727.155.8934.213 1.8009.173 2.7068-.02.25=
85-.053.5157-.101.7705l-.048.2623.222-.1493c.378-.2467.819-.3785 1.27-.3792=
.335.0081.537.0645.545.0363z" fill=3D"#eb996e"/><path d=3D"m134.683 62.6334=
c-.065 0 .024.4841.452.8553.427.3711.907.4034.907.3348s-.383-.1856-.75-.512=
3c-.367-.3268-.544-.702-.609-.6778z" fill=3D"#eb996e"/><path d=3D"m86.9375 =
120.801 15.9955.158-2.683-11.606.9 2.19c.829 2.023 2.241 3.753 4.057 4.97 1=
.815 1.218 3.951 1.868 6.137 1.868 5.059 0 8.557-3.764 10.695-8.347 5.002-1=
0.714 9.823-27.9678 9.823-27.9678l-9.751-8.5684c-7.059 13.3528-13.692 24.94=
68-13.692 24.9468s-1.916-5.8212-3.396-11.0171c-1.481-5.1959-1.469-7.9229-3.=
74-10.5128-3.4976-3.9857-8.4717-4.974-13.103-6.1681l-.1734 21.5581z" fill=
=3D"#407bff"/><path d=3D"m68.6354 104.765 29.1268 5.648 1.416 11.19h-44.658=
4c-1.2224.015-2.4346-.225-3.5591-.705-1.1245-.479-2.1366-1.188-2.9717-2.081=
-.8352-.893-1.475-1.95-1.8786-3.104-.4037-1.154-.5623-2.379-.4657-3.598.641=
4-8.157 1.3393-18.2019 1.8718-22.4497.4034-3.312 1.7347-6.737 3.2959-9.6818=
 2.2551-4.2439 9.7708-9.6818 16.3586-9.9642l1.2466 27.7424" fill=3D"#407bff=
"/><g fill=3D"#000" opacity=3D".4"><path d=3D"m86.9297 120.801 15.9953.158-=
2.683-11.607.9 2.191c.829 2.022 2.241 3.752 4.057 4.97 1.815 1.218 3.952 1.=
868 6.137 1.868 5.059 0 8.557-3.764 10.695-8.347 5.002-10.7143 9.823-27.968=
 9.823-27.968l-9.75-8.5684c-7.06 13.3528-13.692 24.9467-13.692 24.9467s-1.9=
17-5.8212-3.397-11.017c-1.481-5.1959-1.469-7.923-3.74-10.5129-3.4974-3.9856=
-8.4716-4.974-13.1028-6.1681l-.1735 21.5581z"/><path d=3D"m68.6354 104.766 =
26.2706 4.816c.5353.1 1.0292.355 1.4191.735s.6584.867.7714 1.399l2.0817 9.9=
12h-44.6584c-1.2224.015-2.4346-.225-3.5591-.704-1.1245-.48-2.1366-1.189-2.9=
717-2.082-.8352-.893-1.475-1.95-1.8786-3.104-.4037-1.154-.5623-2.379-.4657-=
3.598.6414-8.157 1.3393-18.2017 1.8718-22.4496.4034-3.312 1.7347-6.7369 3.2=
959-9.6818 2.2551-4.2438 9.7708-9.6818 16.3586-9.9642l1.2466 27.7424"/></g>=
<path d=3D"m63.1008 103.934c-.0104-.088-.0104-.178 0-.266 0-.198 0-.452 0-.=
763 0-.69 0-1.642 0-2.824 0-2.3676-.0444-5.6473-.2259-9.2497-.0928-1.8073-.=
2098-3.5258-.3348-5.083-.0586-1.3822-.2545-2.7552-.585-4.0986-.2307-.8967-.=
6703-1.7261-1.2829-2.4205-.2372-.2423-.4877-.4712-.7503-.6857.308.1584.5875=
.367.827.6172.6531.6893 1.1232 1.5311 1.3676 2.4487.3523 1.3541.5632 2.7412=
.6293 4.1389.1372 1.5612.2582 3.2797.351 5.087.1855 3.6307.2057 6.8942.1654=
 9.2787-.0242 1.186-.0565 2.146-.0847 2.824l-.0444.762c-.0016.079-.0124.158=
-.0323.234z" fill=3D"#263238"/><path d=3D"m97.1949 111.219c-.3072-.339-.665=
1-.629-1.061-.859-.4707-.256-.9705-.455-1.4886-.593-.6011-.173-1.3071-.306-=
2.0857-.464-3.1144-.637-7.4108-1.521-12.1711-2.42-4.7603-.9-9.093-1.614-12.=
2276-2.114l-3.7154-.593-1.0086-.17c-.1188-.014-.2362-.038-.351-.072.1191 0 =
.2378.011.355.032l1.0167.129c.8794.117 2.1542.295 3.7235.529 3.1426.468 7.4=
794 1.161 12.2437 2.053 4.7644.892 9.0608 1.795 12.1631 2.465.7786.169 1.48=
45.314 2.0897.5.5239.148 1.0268.362 1.4967.637.3018.171.5743.389.8068.646.0=
62.064.1187.132.1694.206.0283.048.0484.088.0444.088z" fill=3D"#263238"/><pa=
th d=3D"m63.1744 103.934c0 .036-.5366.161-1.3676.5-.4852.201-.9568.432-1.41=
2.694-.5504.328-1.075.698-1.5693 1.105-.4807.417-.9285.87-1.3393 1.356-.338=
1.4-.6481.823-.9279 1.266-.476.763-.6939 1.279-.7302 1.263.0251-.13.0672-.2=
56.1251-.375.1374-.332.2964-.654.476-.964.2694-.461.5728-.9.9077-1.315.8281=
-1.016 1.8411-1.866 2.9853-2.506.4635-.261.9479-.484 1.4483-.665.3365-.126.=
6816-.227 1.0327-.303.1205-.036.2454-.055.3712-.056z" fill=3D"#263238"/><pa=
th d=3D"m100.8 110.906c-.027-.091-.048-.185-.06-.279-.032-.201-.077-.468-.1=
29-.807-.113-.697-.262-1.706-.432-2.957-.3388-2.497-.7502-5.958-1.0972-9.79=
03-.3469-3.8324-.5527-7.3097-.6656-9.831-.0565-1.2587-.0888-2.2793-.1049-2.=
9853 0-.3361 0-.6051 0-.8068-.0088-.0939-.0088-.1884 0-.2824.0218.0926.0326=
.1874.0323.2824l.0524.8068c.0444.7262.1009 1.7387.1735 2.9772.1492 2.5172.3=
832 5.9906.7261 9.8189.3429 3.8285.7344 7.2895 1.0364 9.7905.15 1.235.275 2=
.239.363 2.965.037.335.069.601.093.807.017.096.021.194.012.291z" fill=3D"#2=
63238"/><path d=3D"m110.455 106.569s-.093-.089-.234-.267c-.195-.257-.375-.5=
27-.536-.807-1.083-1.847-1.582-3.979-1.432-6.1152.02-.3214.063-.6409.129-.9=
561.014-.1177.044-.233.088-.3429.037 0-.04.5002-.068 1.3071-.068 2.1061.419=
 4.1921 1.412 6.0511.379.698.673 1.105.641 1.13z" fill=3D"#263238"/><g fill=
=3D"#fafafa"><path d=3D"m62.9698 88.9435s-.0525.0202-.1493.0323c-.1456.0234=
-.2924.0382-.4397.0443-.5412.0228-1.0831-.0206-1.6137-.1291-.7881-.1584-1.5=
575-.399-2.2954-.718-.8351-.3469-1.7226-.8068-2.6505-1.2546-.8686-.4362-1.7=
679-.8084-2.6908-1.1134-.7489-.2515-1.5273-.4048-2.3156-.4559-.7302-.0237-1=
.4601.0592-2.1664.2461.0434-.0288.0911-.0506.1412-.0645.1307-.0572.2656-.10=
44.4035-.1412.5258-.1409 1.0695-.2034 1.6136-.1856.8055.0252 1.6036.1609 2.=
3721.4034.9348.3008 1.8441.6758 2.7191 1.1215.9318.4599 1.8113.9117 2.6343 =
1.2626.7245.3219 1.4783.5732 2.251.7504.7193.147 1.4525.2147 2.1866.2017z"/=
><path d=3D"m67.9957 87.9634c-.775.2359-1.5685.4059-2.3721.5083-.7961.1733-=
1.6062.2746-2.4205.3025.775-.2359 1.5685-.4059 2.3721-.5083.7961-.1735 1.60=
62-.2748 2.4205-.3025z"/><path d=3D"m62.7958 96.6204c-.1857.1047-.3817.1899=
-.585.2541-.3832.1493-.944.351-1.6419.5769-1.825.5856-3.6877 1.0465-5.5752 =
1.3796-1.1054.1937-2.1664.3349-3.1346.4317-.8587.1109-1.7265.1339-2.5899.06=
86-.5857-.0474-1.1561-.2105-1.6782-.4801-.142-.0738-.277-.1602-.4034-.2582-=
.0466-.0318-.0887-.0698-.1251-.1129 0 0 .1936.125.5608.2985.5228.2361 1.085=
1.3729 1.658.4034.8529.0442 1.708.0063 2.5536-.1129.9642-.109 2.0171-.2542 =
3.1185-.4478 1.8786-.3332 3.7372-.7709 5.5671-1.3111.702-.2098 1.2667-.4034=
 1.6581-.5123.201-.0744.4073-.1337.6172-.1775z"/><path d=3D"m67.8504 98.746=
5c-.7269.1155-1.463.1628-2.1986.1412-.7335.0463-1.4696.0233-2.1987-.0685.72=
69-.1155 1.463-.1628 2.1987-.1412.7334-.0463 1.4695-.0233 2.1986.0685z"/><p=
ath d=3D"m47.1094 117.125c.0046-.075.0196-.148.0444-.218.0403-.161.0968-.35=
9.1613-.605l.121-.436c.0525-.157.1211-.323.1856-.504.1451-.412.3121-.816.50=
03-1.21l.3267-.69c.117-.242.2663-.476.4034-.726.3043-.529.6355-1.042.9924-1=
.537 1.6481-2.279 3.797-4.149 6.2813-5.466.5402-.286 1.095-.543 1.662-.771.=
2703-.101.5205-.218.7746-.299l.7302-.229c.4183-.134.8439-.243 1.2748-.327.1=
896-.041.3631-.085.5244-.113l.4519-.061.6212-.076c.0722-.011.1457-.011.2179=
 0-.0688.026-.1407.044-.2138.052l-.6132.117-.4438.077-.5204.129c-.3711.101-=
.8068.185-1.2546.355l-.7181.242c-.2501.085-.5003.202-.7625.306-.5587.232-1.=
1053.492-1.6379.779-1.2131.671-2.3552 1.463-3.4088 2.364-1.0408.918-1.9868 =
1.939-2.824 3.046-.363.524-.7221 1.016-.9964 1.513-.1412.246-.2905.476-.403=
4.714l-.3389.677c-.2299.428-.3711.839-.5244 1.211-.0686.177-.1412.342-.1977=
.496l-.1412.427-.1977.593c-.0193.059-.045.116-.0766.17z"/><path d=3D"m67.32=
93 104.733c-.6747.587-1.2355 1.292-1.654 2.082-.44.863-.7525 1.786-.9279 2.=
739-.2086 1.172-.3433 2.357-.4034 3.546-.0502 1.198-.185 2.391-.4034 3.57-.=
1976.967-.5389 1.898-1.0126 2.764-.3163.58-.7251 1.104-1.2102 1.553-.1322.1=
22-.2738.234-.4236.335-.1009.068-.1574.104-.1614.096.687-.573 1.2592-1.271 =
1.6863-2.057.447-.858.7676-1.777.9521-2.727.2147-1.172.3495-2.356.4034-3.54=
6.0524-1.199.1872-2.393.4034-3.574.1879-.967.5197-1.9.9843-2.768.3171-.585.=
7257-1.116 1.2103-1.573.1269-.122.2617-.235.4034-.339.0466-.04.0981-.074.15=
33-.101z"/><path d=3D"m74.2168 121.672c-.0565 0 0-3.477.1493-7.762.1493-4.2=
84.3025-7.757.359-7.757s0 3.477-.1492 7.765c-.1493 4.289-.3026 7.758-.3591 =
7.754z"/><path d=3D"m85.1674 121.076c-.0759-.162-.1354-.331-.1775-.504-.065=
4-.199-.1206-.401-.1654-.605-.0524-.239-.125-.509-.1694-.807-.1255-.712-.19=
7-1.432-.2138-2.155-.0397-1.775.2334-3.543.8068-5.224.2337-.684.5183-1.35.8=
512-1.992.1331-.279.2824-.513.4034-.723.1025-.182.2157-.359.3389-.528.09-.1=
54.1969-.297.3187-.428.0363.021-.4034.646-.9319 1.747-.3136.643-.5831 1.306=
-.8068 1.985-.5526 1.663-.8253 3.407-.8069 5.159.0092.715.0644 1.427.1654 2=
.134.2018 1.203.4277 1.929.3873 1.941z"/><path d=3D"m66.5616 76.4903c0 .032=
3-.6374-.2461-1.6863-.5849-2.732-.882-5.6273-1.1384-8.4717-.7504-1.0893.149=
3-1.7549.3187-1.763.2824.1447-.0698.298-.12.4559-.1492.4221-.1168.8504-.209=
7 1.2829-.2784 2.866-.4762 5.8061-.2166 8.5443.7544.4115.1451.8154.3107 1.2=
103.4962.1515.0587.2951.1359.4276.2299z"/><path d=3D"m98.4487 93.4693c-.498=
9.026-.999.0179-1.4967-.0242-.9238-.0364-2.1987-.0848-3.6066-.117-1.4079-.0=
323-2.6827-.0525-3.6065-.0605-.4995.0158-.9995-.0017-1.4967-.0525.1322-.031=
2.2675-.0475.4034-.0484.2582 0 .6334-.0363 1.0973-.0484.9238-.0282 2.1986-.=
0363 3.6106 0s2.6868.1089 3.6308.1856c.4599.0363.831.0685 1.0892.1008.127.0=
081.2528.0297.3752.0646z"/><path d=3D"m106.392 91.9407c0 .0363-.504.2098-1.=
291.597-2.058 1.0123-3.801 2.5659-5.042 4.494-.4765.7382-.7064 1.2304-.7387=
 1.2102.0225-.1274.0661-.2501.1291-.3631.1414-.3206.303-.6318.4841-.9318.59=
15-.9975 1.3345-1.8971 2.2025-2.6666.866-.7717 1.844-1.407 2.901-1.8839.319=
-.1435.646-.2675.98-.3711.121-.0445.247-.073.375-.0847z"/><path d=3D"m102.4=
67 79.0119c-.695-.0238-1.388.0731-2.049.2864-.7143.2493-1.4065.5569-2.0699.=
9198-.819.454-1.6745.8385-2.5577 1.1497-1.6324.523-3.3686.6353-5.0548.3268-=
.5068-.092-1.0048-.2269-1.4886-.4034-.181-.0567-.3555-.1324-.5204-.2259 0-.=
0404.7584.2783 2.0171.4841 1.6626.2595 3.3629.1283 4.966-.3833.8731-.3089 1=
.7214-.6838 2.5375-1.1215.6753-.3584 1.3814-.6554 2.1098-.8875.496-.1572 1.=
013-.2322 1.533-.2218.135.0039.27.0174.403.0403.126.0162.178.0283.174.0363z=
"/><path d=3D"m100.448 106.048c-.147.093-.304.167-.4684.222-.1533.064-.3429=
.141-.5688.214-.2259.072-.4801.169-.7665.242-.6755.188-1.3628.331-2.0574.42=
7-.8506.11-1.7083.155-2.5657.133-.8572-.032-1.7108-.127-2.5537-.286-.6841-.=
138-1.358-.323-2.0171-.553-.2864-.088-.5325-.197-.7503-.286-.2179-.089-.403=
4-.174-.5567-.246-.1605-.067-.3134-.151-.4559-.25 0-.037.6818.286 1.8113.64=
1.6615.208 1.3351.375 2.0171.5 1.6686.309 3.3747.361 5.0589.154.6875-.085 1=
.3692-.212 2.0413-.38 1.1376-.29 1.8189-.568 1.8319-.532z"/><path d=3D"m109=
.713 117.989c-.058-.037-.11-.08-.157-.13-.093-.092-.247-.217-.404-.403-.487=
-.521-.915-1.095-1.275-1.71-1.164-2.063-1.783-4.389-1.799-6.757.003-2.353.3=
11-4.694.916-6.967.218-.872.403-1.574.553-2.0537.068-.2218.125-.4034.169-.5=
526.044-.1493.065-.1937.073-.1896-.001.0671-.012.1337-.033.1977-.032.1533-.=
076.3388-.129.5647-.121.5205-.286 1.2105-.488 2.0695-.557 2.265-.84 4.59-.8=
43 6.922.013 2.331.596 4.623 1.698 6.677.346.615.751 1.195 1.211 1.731.318.=
387.52.584.508.601z"/><path d=3D"m124.411 104.288c-.645-.511-1.365-.919-2.1=
34-1.21-.842-.291-1.705-.516-2.582-.673-1.089-.184-2.167-.431-3.228-.739-1.=
915-.623-3.663-1.6738-5.111-3.0737-.437-.4223-.841-.8768-1.21-1.3595-.133-.=
1694-.222-.3106-.286-.4034-.065-.0927-.093-.1452-.085-.1492.156.1571.301.32=
42.435.5002.388.4606.804.8959 1.247 1.303 1.454 1.3495 3.185 2.3666 5.071 2=
.9816 1.05.306 2.115.556 3.191.75.884.17 1.753.411 2.598.722.571.216 1.106.=
515 1.59.888.131.099.256.208.371.326.101.089.141.133.133.137z"/><path d=3D"=
m129.021 90.8278c-.155-.1111-.3-.2367-.431-.3752-.133-.125-.295-.2783-.497-=
.4477-.201-.1695-.403-.3712-.677-.5648-.596-.4807-1.221-.924-1.872-1.3272-1=
.596-.9872-3.332-1.7297-5.148-2.2026-.742-.1911-1.495-.3379-2.255-.4397-.32=
3-.0525-.617-.0727-.875-.0969s-.484-.0443-.666-.0524c-.191-.0008-.381-.0184=
-.569-.0524.188-.0291.379-.0386.569-.0283.224-.0112.449-.0112.674 0 .258 0 =
.561.0202.887.0646.768.0839 1.53.2187 2.28.4034.919.2355 1.819.5375 2.694.9=
036.868.3784 1.707.8207 2.51 1.3232.652.4163 1.276.8759 1.868 1.3756.258.20=
57.467.4034.661.5971.166.1503.323.3092.472.476.141.134.266.2828.375.4437z"/=
><path d=3D"m93.7123 72.3102c-.2368.158-.4949.2816-.7665.3671-.6364.2496-1.=
2903.4518-1.9565.6051-.6637.1563-1.3378.2642-2.0171.3227-.2808.0426-.5664.0=
426-.8472 0 0-.0645 1.2788-.1694 2.8239-.5365s2.7392-.8189 2.7634-.7584z"/>=
</g><path d=3D"m108.032 129.128c.052.18.059.37.019.553s-.126.353-.248.495c-=
.123.142-.279.25-.455.316s-.365.086-.55.06c-.216-.031-.417-.123-.581-.267-.=
163-.143-.281-.331-.339-.54l-.343-1.267h.052c.035.183.026.372-.026.55-.052.=
179-.147.343-.275.478s-.286.238-.462.3c-.175.062-.363.081-.548.056-.528 0-.=
984-.771-1.137-1.178-.399.039-.801-.033-1.163-.206-.361-.174-.668-.443-.887=
-.778l-2.3679-3.696-4.3852-3.207 2.6545-9.476 9.4356 6.108c.372.29.428.403 =
1.017.956l6.092 5.603c-.69.404-1.275.384-2.235 0-.96-.383-5.164-3.227-5.164=
-3.227l1.896 8.33" fill=3D"#ffbe9d"/><path d=3D"m99.9693 121.333s.1087.048.=
2787.165l.314.218c.15.094.286.208.404.339.139.161.249.345.322.545.073.214.1=
5.431.23.661l.517 1.517c.359 1.081.674 2.057.891 2.772.138.378.242.768.311 =
1.165-.183-.36-.334-.735-.452-1.121-.254-.702-.593-1.67-.952-2.747-.173-.53=
7-.343-1.049-.5-1.521-.073-.234-.141-.456-.21-.666-.06-.185-.152-.358-.27-.=
512-.102-.126-.219-.24-.347-.339l-.291-.246c-.173-.137-.2538-.218-.2457-.23=
z" fill=3D"#eb996e"/><path d=3D"m105.644 128.695c-.06-.128-.104-.264-.133-.=
403l-.302-1.081c-.258-.912-.601-2.175-1.041-3.55-.44-1.376-.9-2.602-1.452-3=
.361-.207-.303-.468-.564-.771-.77-.222-.137-.363-.178-.359-.202.037-.001.07=
4.006.109.02.1.026.198.063.29.109.335.187.626.445.852.755.601.766 1.085 2.0=
17 1.529 3.384.443 1.368.766 2.655.984 3.578.109.46.19.836.242 ************=
************.423z" fill=3D"#eb996e"/><path d=3D"m108.079 129.269c-.073-.134=
-.128-.276-.165-.424-.101-.322-.226-.714-.375-1.178-.307-.996-.714-2.376-1.=
154-3.901-.44-1.524-.807-2.864-1.11-3.913-.104-.388-.33-.733-.645-.984-.128=
-.082-.263-.152-.403-.21.039-.009.081-.009.121 0 .***************.318.133.3=
53.242.613.596.739 1.005.33.996.722 2.376 1.169 3.901.448 1.525.827 2.916 1=
.098 3.921.121.468.226.867.306 ************.083.29.101.44z" fill=3D"#eb996e=
"/></svg>=0A
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: image/webp
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/viewer/14/2827814/24/bg18.webp
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------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/products/s/1532132-hisense-hr6bmff518bw.webp

UklGRiICAABXRUJQVlA4IBYCAAAQGQCdASrIAMgAPpFImUmrpSKhJz8qwXASCWluu7lObKOHpsZy
yy7eanF7jJDg9/saVi8ZT/3UhbeAiFEydKc5KSrxs0B+TTSVdJIt4DhLZ0YI/8cvWMt8qp/eogrb
sQ8OWH5iS9DqLu3XSXymQzBz+NUG0qxAxdJZnOzrlvHtvkrk6yLSJYucrpel6l9xYHLG8WZXUPnq
9VVeeDaTYfur8cvE4XerunbjAsM9quDc2Mxt6MUByxeQiunFyqkPKqGEKzSAiT0s6EKIH1GkBtxa
YAD++E3df7KMc06MLJftfTFx8STKwTRfLfIEjoZA+Tbq2wbjuljpH0NS/ns8bvoZZxgY6aDkUsTl
18y5CPyaJfccSZ4FAuIMp1bYF1vZlx0plMaF/xEf1kd/psS9q4Z6RYN2lcnvvhk80H8kVLqGcBiv
uRVs+aBWlMUPPKpdydbP3BpLZxbtodTohCP+8cWSyvneC58i3dEPv+3VdIJ3B3vIjTvj8qvUpVtj
oK+PEUpVloyk3cg6jPZYMEvc+ps/eZSXQXw7Y1BgCGwZOr9QaRnATeLCrCb3LsBUJpmRE0Va8EaH
56T5sJep4aWO9+CT+DLZnNScJIsGJ1ZNAFLaOi48jxBej1YJjG2FGk1PZoQl6Xm81uob7zybHMKw
qyr5j/VmzrArdq3LPv2OSPnFjvE0Kb4d9WHnD6gXYe4w7ldQIE8AAAA=

------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/products/l/1532132-hisense-hr6bmff518bw.webp
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------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: application/octet-stream
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/thumbs/brands/s/2376-hisense_logo.webp
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==

------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/_nuxt/index.C3HriZ4f.css

@charset "utf-8";

.breadcrumbs[data-v-23ead4d8] { display: flex; font-size: 1rem; font-weight=
: 600; list-style: none; margin: 0.5rem 0px; padding: 0px; }

.breadcrumbs__item[data-v-23ead4d8] { color: rgb(173, 181, 189); padding-le=
ft: 0.2rem; }

.breadcrumbs__item[data-v-23ead4d8]:first-child::before { content: ""; padd=
ing-right: 0px; }

.breadcrumbs__item[data-v-23ead4d8]::before { content: "/"; padding-right: =
0.2rem; }

.breadcrumbs__item a[data-v-23ead4d8] { color: rgb(173, 181, 189); }

.breadcrumbs__item[data-v-23ead4d8]:last-child { opacity: 0.7; }

.header[data-v-1a98f4e7] { background: rgb(245, 246, 246); min-height: 200p=
x; position: relative; }

.header[data-v-1a98f4e7], .header.header--invert[data-v-1a98f4e7] { padding=
: 0px 0px 2rem; }

.header .container[data-v-1a98f4e7] { flex-direction: column; max-width: 10=
00px; }

.header .container[data-v-1a98f4e7], .header__logo[data-v-1a98f4e7] { align=
-items: center; display: flex; justify-content: center; }

.header__logo[data-v-1a98f4e7] { color: rgb(99, 150, 198); height: 120px; m=
ix-blend-mode: multiply; padding: 1rem; width: 120px; }

.header__logo--invert[data-v-1a98f4e7] { filter: brightness(0) invert(1); }

.header--invert .header__logo[data-v-1a98f4e7] { mix-blend-mode: normal; }

.header__logo .app-icon[data-v-1a98f4e7] { height: 100%; width: 100%; }

.product .header__logo[data-v-1a98f4e7] { align-items: center; background-c=
olor: rgb(255, 255, 255); border-radius: 50%; box-shadow: rgba(0, 0, 0, 0.0=
5) 5px 10px 13px; display: flex; height: 100px; justify-content: center; ma=
rgin: 1rem; mix-blend-mode: normal; overflow: hidden; width: 100px; }

.header__text[data-v-1a98f4e7] { text-align: center; }

.header--invert .header__text[data-v-1a98f4e7], .header--invert .header__te=
xt[data-v-1a98f4e7] * { color: rgb(255, 255, 255); }

.header__text small[data-v-1a98f4e7] { font-weight: 600; opacity: 0.7; }

.header__text h1[data-v-1a98f4e7] { text-transform: capitalize; }

.header__text h1[data-v-1a98f4e7] span { color: rgb(243, 91, 58); display: =
block; font-size: 1rem; }

.header--invert .header__text h1[data-v-1a98f4e7] span { color: rgb(255, 25=
5, 255); opacity: 0.7; }

.header__breadcrumbs[data-v-1a98f4e7] { align-items: center; align-self: fl=
ex-start; display: flex; }

.header--invert .header__breadcrumbs[data-v-1a98f4e7] * { color: rgb(255, 2=
55, 255); }

@media (max-width: 768px) {
  .header__breadcrumbs[data-v-1a98f4e7] { display: none; }
}

.h1[data-v-884f83e4], .nav__item[data-v-884f83e4] { font-weight: 700; }

.nav__item[data-v-884f83e4] { align-items: center; background-color: rgb(25=
5, 255, 255); border-radius: 0.5rem; color: rgb(61, 70, 82); display: flex;=
 flex-direction: row; justify-content: flex-start; margin-bottom: 4px; padd=
ing: 0.5rem 1rem; position: relative; }

.nav__item span[data-v-884f83e4] { width: 100%; }

.nav__item[data-v-884f83e4]::after { background-image: url("data:image/svg+=
xml;utf8,<svg width=3D\"100\" height=3D\"100\" viewBox=3D\"0 0 100 100\" fi=
ll=3D\"black\" xmlns=3D\"http://www.w3.org/2000/svg\"><path d=3D\"M72.4902 =
50.3223C72.4902 48.9209 71.9775 47.7588 70.8838 46.665L44.5654 20.9277C43.6=
768 20.0391 42.6172 19.6289 41.3525 19.6289C38.7891 19.6289 36.7041 21.6455=
 36.7041 24.209C36.7041 25.4736 37.251 26.6357 38.1738 27.5586L61.5527 50.2=
881L38.1738 73.0859C37.251 74.0088 36.7041 75.1367 36.7041 76.4355C36.7041 =
78.999 38.7891 81.0498 41.3525 81.0498C42.6172 81.0498 43.6768 80.6055 44.5=
654 79.7168L70.8838 53.9795C72.0117 52.8857 72.4902 51.7236 72.4902 50.3223=
Z\"/></svg>"); background-repeat: no-repeat; background-size: contain; cont=
ent: ""; display: flex; height: 20px; width: 30px; }

.nav__img[data-v-884f83e4] { border-radius: 0.25rem; flex-shrink: 0; height=
: 50px; margin-right: 1rem; overflow: hidden; width: 50px; }

.nav__img--icon[data-v-884f83e4] { color: rgb(99, 150, 198); padding: 0.5re=
m; }

@media (min-width: 576px) {
  .nav[data-v-884f83e4] { display: grid; grid-auto-rows: 1fr; grid-template=
-columns: repeat(5, 1fr); gap: 0.8rem; }
  .nav--placeholder[data-v-884f83e4] { display: flex; }
  .nav--placeholder[data-v-884f83e4] picture { display: none; }
  .nav--placeholder[data-v-884f83e4]::before { content: attr(title); displa=
y: flex; flex-direction: column; justify-content: center; }
  .nav--product[data-v-884f83e4] { gap: 0.5rem; display: flex; flex-wrap: w=
rap; justify-content: center; }
  .nav__img[data-v-884f83e4] { height: 100px; margin-right: 0px; width: 100=
px; }
  .nav__img--placeholder[data-v-884f83e4] { display: flex; }
  .nav__img--placeholder[data-v-884f83e4] picture { display: none; }
  .nav__img--placeholder[data-v-884f83e4]::before { content: attr(title); d=
isplay: flex; flex-direction: column; justify-content: center; width: 100%;=
 }
  .nav__img--icon[data-v-884f83e4] { padding: 0.5rem; }
  .nav--product .nav__img[data-v-884f83e4] { height: 50px; margin-right: 1r=
em; width: 50px; }
  .nav__item[data-v-884f83e4] { display: flex; flex-direction: column; heig=
ht: 200px; justify-content: center; text-align: center; --color: #c2c2c2; b=
order: 1px solid transparent; border-radius: 0.5rem; position: relative; }
  .nav__item[data-v-884f83e4], .nav__item[data-v-884f83e4]::before { transi=
tion: 0.2s ease-in-out; }
  .nav__item[data-v-884f83e4]::before { border-radius: 0.5rem; inset: 0px; =
box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 13px; content: ""; opacity: 0.3; pos=
ition: absolute; }
  .nav--brand .nav__item[data-v-884f83e4] img[src*=3D"placeholder"]::before=
 { content: attr(title); }
  .nav__item[data-v-884f83e4]:hover { transform: scale(1.02); }
  .nav--brand .nav__item[data-v-884f83e4]:hover { border: 1px solid var(--c=
olor); }
  .nav--category .nav__item[data-v-884f83e4]:hover { background: rgb(99, 15=
0, 198); color: rgb(255, 255, 255); }
  .nav--category .nav__item:hover .nav__img--icon[data-v-884f83e4] { color:=
 rgb(255, 255, 255); transition: 0.2s ease-in-out; }
  .nav__item[data-v-884f83e4]:hover::before { box-shadow: 0 5px 45px var(--=
color); }
  .nav--brand .nav__item span[data-v-884f83e4], .nav__item[data-v-884f83e4]=
::after { display: none; }
  .nav--product .nav__item[data-v-884f83e4] { display: inline-flex; flex-di=
rection: row; height: auto; padding: 1rem 1.5rem; }
  .nav--category .nav__item .nav__img--icon[data-v-884f83e4] { padding: 1re=
m; }
  .nav__more[data-v-884f83e4] { background-color: rgb(99, 150, 198); color:=
 rgb(255, 255, 255); display: flex; font-weight: 700; margin: 0.5rem auto; =
opacity: 1; text-decoration: none; text-transform: capitalize; transition: =
opacity 0.2s ease-in-out; }
  .nav__more[data-v-884f83e4]:hover { opacity: 0.8; }
}

@media (min-width: 576px) {
  .lb[data-v-8c1058fb] { max-height: 90px; min-height: 90px; }
}

.filters[data-v-8c1058fb] { display: flex; flex-direction: column; }

.sticky-top[data-v-8c1058fb] { position: sticky; top: 0px; z-index: auto; }

.index__bottom-description2[data-v-ed6af2c0] { margin: 0px auto; max-width:=
 800px; text-align: center; }

.index__bottom-description[data-v-ed6af2c0] { margin: 0px auto; white-space=
: pre-wrap; }

@media (min-width: 768px) {
  .index__bottom-description[data-v-ed6af2c0] { max-width: 800px; text-alig=
n: center; }
  .index__bottom-description[data-v-ed6af2c0] ul { margin: 0px auto; text-a=
lign: left; width: fit-content; }
}

.index__sort-container[data-v-ed6af2c0] { display: flex; flex-grow: 4; }

.index__sort[data-v-ed6af2c0] { background-color: rgb(196, 196, 196); borde=
r-radius: calc(4px + 1rem); display: flex; margin: 1.5rem 0.5rem 1.5rem 0px=
; min-width: 200px; padding: 3px; }

.index__sort > a[data-v-ed6af2c0] { background-color: transparent; border-r=
adius: 1rem; color: rgb(255, 255, 255); flex-grow: 1; margin: 0px; padding:=
 0.25rem 1rem; text-align: center; transition: 0.2s ease-in-out; }

.index__sort > a[data-v-ed6af2c0]:hover { background-color: rgb(171, 171, 1=
71); }

.index__sort > a.active[data-v-ed6af2c0] { background-color: rgb(99, 150, 1=
98); color: rgb(255, 255, 255); font-weight: 600; }

.index__sort > a.active[data-v-ed6af2c0]:hover { background-color: rgb(66, =
125, 180); }

.index__brand-thumb[data-v-ed6af2c0] { display: flex; height: 60px; margin:=
 1rem auto; width: 60px; }

.index__cnt[data-v-ed6af2c0] { font-weight: 500; }

.index__title[data-v-ed6af2c0] { align-items: center; display: flex; flex-w=
rap: wrap; justify-content: space-between; width: 100%; }

.index__title .h1[data-v-ed6af2c0] { text-align: center; width: 100%; }

@media (min-width: 576px) {
  .index__title[data-v-ed6af2c0] { flex-wrap: nowrap; }
  .index__title .index__sort-container[data-v-ed6af2c0] { order: -1; width:=
 25%; }
  .index__title .h1[data-v-ed6af2c0] { width: 50%; }
  .index__title .search[data-v-ed6af2c0] { width: 25%; }
}

.index__title .search[data-v-ed6af2c0] { min-width: inherit; }

.index__title .search-button[data-v-ed6af2c0] > button { border-radius: cal=
c(2px + 1rem); height: calc(4px + 2rem); padding: 0px 1rem; }

.index .btn-group-sm .btn[data-v-ed6af2c0] { font-weight: 400; text-decorat=
ion: none; }

.index .btn-group-sm .btn-outline-secondary[data-v-ed6af2c0] { border-color=
: rgb(222, 226, 230); color: rgb(61, 70, 82); }

.index .active_filters[data-v-ed6af2c0] { display: flex; flex-wrap: wrap; }

.index .active_filters .btn[data-v-ed6af2c0] { text-decoration: none; }

.index .active_filters .btn .app-icon[data-v-ed6af2c0] { margin-right: -1re=
m; margin-top: -0.3rem; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/_nuxt/default.BLw1kusm.css

@charset "utf-8";

.resize-observer[data-v-b329ee4c] { background-color: transparent; border: =
none; opacity: 0; }

.resize-observer[data-v-b329ee4c], .resize-observer[data-v-b329ee4c] object=
 { display: block; height: 100%; left: 0px; overflow: hidden; pointer-event=
s: none; position: absolute; top: 0px; width: 100%; z-index: -1; }

.v-popper__popper { left: 0px; outline: none; top: 0px; z-index: 10000; }

.v-popper__popper.v-popper__popper--hidden { opacity: 0; pointer-events: no=
ne; transition: opacity 0.15s, visibility 0.15s; visibility: hidden; }

.v-popper__popper.v-popper__popper--shown { opacity: 1; transition: opacity=
 0.15s; visibility: visible; }

.v-popper__popper.v-popper__popper--skip-transition, .v-popper__popper.v-po=
pper__popper--skip-transition > .v-popper__wrapper { transition: none !impo=
rtant; }

.v-popper__backdrop { display: none; height: 100%; left: 0px; position: abs=
olute; top: 0px; width: 100%; }

.v-popper__inner { box-sizing: border-box; overflow-y: auto; position: rela=
tive; }

.v-popper__inner > div { max-height: inherit; max-width: inherit; position:=
 relative; z-index: 1; }

.v-popper__arrow-container { height: 10px; position: absolute; width: 10px;=
 }

.v-popper__popper--arrow-overflow .v-popper__arrow-container, .v-popper__po=
pper--no-positioning .v-popper__arrow-container { display: none; }

.v-popper__arrow-inner, .v-popper__arrow-outer { border-style: solid; heigh=
t: 0px; left: 0px; position: absolute; top: 0px; width: 0px; }

.v-popper__arrow-inner { border-width: 7px; visibility: hidden; }

.v-popper__arrow-outer { border-width: 6px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-inner=
, .v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-inner =
{ left: -2px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-outer=
, .v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-outer =
{ left: -1px; }

.v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-inner, .=
v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-outer { b=
order-bottom-width: 0px; border-bottom-color: transparent !important; borde=
r-left-color: transparent !important; border-right-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-inner { =
top: -2px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-conta=
iner { top: 0px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-inner=
, .v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-out=
er { border-top-width: 0px; border-left-color: transparent !important; bord=
er-right-color: transparent !important; border-top-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-inner=
 { top: -4px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-outer=
 { top: -6px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-inner, =
.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-inner =
{ top: -2px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-outer, =
.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-outer =
{ top: -1px; }

.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-inner,=
 .v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-outer=
 { border-left-width: 0px; border-bottom-color: transparent !important; bor=
der-left-color: transparent !important; border-top-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-inner =
{ left: -4px; }

.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-outer =
{ left: -6px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-contain=
er { right: -10px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-inner, =
.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-outer {=
 border-right-width: 0px; border-bottom-color: transparent !important; bord=
er-right-color: transparent !important; border-top-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-inner {=
 left: -2px; }

.v-popper--theme-tooltip .v-popper__inner { background: rgba(0, 0, 0, 0.8);=
 border-radius: 6px; color: rgb(255, 255, 255); padding: 7px 12px 6px; }

.v-popper--theme-tooltip .v-popper__arrow-outer { border-color: rgba(0, 0, =
0, 0.8); }

.v-popper--theme-dropdown .v-popper__inner { background: rgb(255, 255, 255)=
; border: 1px solid rgb(221, 221, 221); border-radius: 6px; box-shadow: rgb=
a(0, 0, 0, 0.1) 0px 6px 30px; color: rgb(0, 0, 0); }

.v-popper--theme-dropdown .v-popper__arrow-inner { border-color: rgb(255, 2=
55, 255); visibility: visible; }

.v-popper--theme-dropdown .v-popper__arrow-outer { border-color: rgb(221, 2=
21, 221); }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/_nuxt/manual.DMb89GAp.css

@charset "utf-8";

.rating[data-v-ff31764a] { align-items: center; display: flex; flex-grow: 0=
; flex-wrap: wrap; font-size: 0.8rem; }

.rating__cta[data-v-ff31764a], .rating__stats[data-v-ff31764a] { font-size:=
 0.875rem; font-weight: 600; margin-right: 1rem; text-transform: capitalize=
; white-space: nowrap; }

.rating .text-muted[data-v-ff31764a] { color: rgb(173, 181, 189); }

.rating__stars[data-v-ff31764a] { align-self: baseline; background: rgb(173=
, 181, 189); cursor: pointer; display: inline-flex; flex: 0 1 0px; height: =
1rem; margin-right: 0.5rem; mask-image: url("data:image/svg+xml;utf8,<svg w=
idth=3D\"100\" height=3D\"100\" viewBox=3D\"0 0 100 100\" fill=3D\"black\" =
xmlns=3D\"http://www.w3.org/2000/svg\"> <path d=3D\"M26.04 84.0234C27.8174 =
85.3906 29.9707 84.9463 32.4316 83.1689L50 70.2832L67.5342 83.1689C69.9951 =
84.9463 72.1484 85.3906 73.9258 84.0234C75.6689 82.7246 76.0107 80.5371 75.=
0537 77.7002L68.1152 57.0898L85.8203 44.4092C88.2812 42.666 89.3066 40.6836=
 88.623 38.5986C87.9053 36.5479 85.957 35.5225 82.915 35.5566L61.2451 35.72=
75L54.6143 14.9805C53.6914 12.0752 52.1875 10.5371 50 10.5371C47.8125 10.53=
71 46.2744 12.0752 45.3516 14.9805L38.7549 35.7275L17.0166 35.5566C14.043 3=
5.5225 12.0605 36.5479 11.3428 38.5986C10.625 40.6836 11.7188 42.666 14.145=
5 44.4092L31.8506 57.0898L24.9121 77.7002C23.9551 80.5371 24.2969 82.7246 2=
6.04 84.0234Z\"></path> </svg>"); mask-repeat: repeat-x; mask-size: 1rem 1r=
em; min-width: 5rem; position: relative; white-space: nowrap; width: 5rem; =
}

.rating__stars-hover[data-v-ff31764a] { background: rgb(245, 194, 0); heigh=
t: 100%; overflow: hidden; position: absolute; top: 0px; }

.rating__tooltip[data-v-ff31764a] { font-size: 1rem; }

.rating__tooltip .rating__stars[data-v-ff31764a] { height: 3rem; mask-size:=
 3rem 3rem; min-width: 15rem; width: 15rem; }

.zoom[data-v-ac8a74d0] { height: 100%; left: 0px; pointer-events: none; pos=
ition: absolute; top: 0px; width: 100%; }

.zoom__overlay[data-v-ac8a74d0] { backface-visibility: hidden; border: 2px =
solid rgb(222, 226, 230); border-radius: 50%; box-shadow: rgba(0, 0, 0, 0.3=
) 0px 0px 0px 10px; height: 300px; margin: -150px 0px 0px -150px; overflow:=
 hidden; position: absolute; width: 300px; z-index: 1000; }

.zoom__overlay-content[data-v-ac8a74d0] { position: absolute; transform-ori=
gin: 0px 0px; }

.viewer-page .h1, .viewer-page .h2, .viewer-page .h3, .viewer-page .h4, .vi=
ewer-page .h5, .viewer-page .h6, .viewer-page h1, .viewer-page h2, .viewer-=
page h3, .viewer-page h4, .viewer-page h5, .viewer-page h6 { margin-bottom:=
 auto; }

.viewer-page figure { margin: 0px; }

#page-container { border: 0px; }

@media screen {
  #sidebar.opened + #page-container { left: 250px; }
  #page-container { bottom: 0px; overflow: auto; right: 0px; }
  .loading-indicator { display: none; }
  .loading-indicator.active { display: block; height: 64px; left: 50%; marg=
in-left: -32px; margin-top: -32px; position: absolute; top: 50%; width: 64p=
x; }
  .loading-indicator img { inset: 0px; position: absolute; }
}

.t::after { content: ""; }

.t span { display: inline-block; position: relative; unicode-bidi: bidi-ove=
rride; }

.fallback[data-v-dd20de80] { padding-top: 200%; }

.loading[data-v-dd20de80] { align-items: center; background: rgba(255, 255,=
 255, 0.7); height: 100%; position: absolute; width: 100%; z-index: 100; }

.file-switcher[data-v-4aa625cc] { background-color: rgb(255, 255, 255); bor=
der-radius: 0.25rem; display: flex; flex-flow: wrap; margin: 0px 15px; over=
flow: hidden; }

.file-switcher__item[data-v-4aa625cc] { clear: both; padding: 1rem; width: =
100%; }

@media (min-width: 768px) {
  .file-switcher__item[data-v-4aa625cc] { width: 50%; }
}

.file-switcher__item[data-v-4aa625cc] .smart-image { background-color: rgb(=
255, 255, 255); border: 1px solid rgb(196, 196, 196); border-radius: 0.25re=
m; float: left; height: 100%; margin-right: 1rem; max-height: 80px; positio=
n: relative; transition: 0.4s; width: 50px; }

.file-switcher__item[data-v-4aa625cc] .smart-image::after { background-colo=
r: rgb(255, 255, 255); border-bottom: 1px solid rgb(196, 196, 196); content=
: ""; height: 16px; position: absolute; right: -8px; top: -8px; transform: =
rotate(45deg); width: 16px; }

.file-switcher__item--active[data-v-4aa625cc], .file-switcher__item[data-v-=
4aa625cc]:hover { background-color: rgb(224, 234, 244); }

.file-switcher__item--active[data-v-4aa625cc] .smart-image::after, .file-sw=
itcher__item[data-v-4aa625cc]:hover .smart-image::after { background-color:=
 rgb(224, 234, 244); }

.file-switcher h4[data-v-4aa625cc] { margin-bottom: 0px; }

.file-switcher__lang[data-v-4aa625cc] { background-color: rgb(99, 150, 198)=
; border-radius: 0.25rem; color: rgb(255, 255, 255); display: block; float:=
 left; font-size: 0.875rem; font-weight: 700; margin: 0.25rem 0.25rem 0.25r=
em 0px; padding: 0.1rem 0.5rem; }

.slider[data-v-b5a7cda6] { display: flex; flex-direction: column; position:=
 relative; width: 100%; }

.slider:hover .slider__label[data-v-b5a7cda6] { opacity: 1; }

.slider:hover .slider__track[data-v-b5a7cda6] { background-color: rgb(255, =
255, 255); height: 4px; }

.slider__label[data-v-b5a7cda6] { background-color: rgb(255, 255, 255); bor=
der-radius: 3px; box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 10px; color: rgb(6=
1, 70, 82); font-size: 12px; font-weight: 700; margin-left: 1em; min-width:=
 30px; opacity: 0; padding: 0.1rem 1rem; position: absolute; text-align: ce=
nter; top: -20px; transform: translate3d(-50%, -5px, 0px); transition: opac=
ity 0.2s; white-space: nowrap; }

.slider__label[data-v-b5a7cda6]::after { border-width: 5px; border-style: s=
olid; border-color: rgb(255, 255, 255) transparent transparent; border-imag=
e: initial; bottom: -10px; content: ""; height: 0px; left: 0px; margin: aut=
o; position: absolute; right: 0px; width: 0px; }

.slider__wrapper[data-v-b5a7cda6] { align-items: center; display: flex; pos=
ition: relative; }

.slider__track[data-v-b5a7cda6] { background: rgb(194, 194, 194); border-ra=
dius: 2px; height: 2px; position: absolute; transition: 0.2s ease-in-out; w=
idth: 100%; z-index: 0; }

.slider__input[data-v-b5a7cda6] { appearance: none; background: none; borde=
r: none; height: 30px; margin: 10px 0px; padding: 0px; position: relative; =
width: 100%; z-index: 1; }

.slider__input[data-v-b5a7cda6]:focus { outline: none; }

.slider__input[data-v-b5a7cda6]::-webkit-slider-runnable-track { cursor: po=
inter; height: 10px; width: 100%; background: transparent; border: none; bo=
rder-radius: 0px; }

.slider__input[data-v-b5a7cda6]::-webkit-slider-thumb { appearance: none; b=
ackground: rgb(255, 255, 255); border-radius: 50%; cursor: pointer; height:=
 1rem; margin-top: -3px; width: 1rem; }

.slider__input[data-v-b5a7cda6]:focus::-webkit-slider-runnable-track { back=
ground: transparent; }

.viewer-toolbar[data-v-7feed808] { align-items: center; display: flex; flex=
-direction: column; }

.viewer-toolbar__main[data-v-7feed808] { color: rgb(255, 255, 255); display=
: flex; flex-direction: row; justify-content: center; overflow: hidden; }

.viewer-toolbar a[data-v-7feed808] { align-items: center; color: rgb(61, 70=
, 82); display: flex; font-size: 2rem; }

.viewer-toolbar__slider[data-v-7feed808] { align-items: center; display: fl=
ex; flex-direction: column; justify-content: center; max-width: min(550px, =
80vw); width: 100%; }

.viewer-toolbar__dropdown-container[data-v-7feed808] { position: absolute; =
}

.viewer-toolbar__dropdown[data-v-7feed808] { align-items: center; display: =
flex; justify-content: center; width: 130px; }

.viewer-toolbar__dropdown .dropdown[data-v-7feed808] { background-color: rg=
b(52, 58, 64); border: 0px; color: rgb(255, 255, 255); filter: drop-shadow(=
rgba(0, 0, 0, 0.15) 0px 2px 10px); list-style: none; max-height: 400px; ove=
rflow-y: auto; padding: 0px; top: 40px; }

.viewer-toolbar__dropdown .dropdown li[data-v-7feed808] { padding: 0.1rem 1=
rem; }

.viewer-toolbar__dropdown .dropdown li[data-v-7feed808]:hover { background-=
color: rgb(73, 80, 87); }

.viewer-toolbar__dropdown .btn[data-v-7feed808] { background: rgb(33, 37, 4=
1); color: rgb(255, 255, 255); font-weight: 400; padding: 0.05rem 0.75rem; =
}

.viewer-toolbar__dropdown .btn span[data-v-7feed808] { opacity: 0.7; }

.viewer-toolbar__arrow[data-v-7feed808] { border-radius: 2rem; padding: 1re=
m; transform: scale(1); transition: background-color 0.2s ease-in-out; }

.viewer-toolbar__arrow .app-icon[data-v-7feed808] { color: rgb(255, 255, 25=
5); }

.viewer-toolbar__arrow[data-v-7feed808]:hover { background-color: rgba(0, 0=
, 0, 0.1); }

.viewer-toolbar__arrow[data-v-7feed808]:active { transform: scale(0.8); }

#sidebar { bottom: 0px; overflow: auto; width: 250px; }

#page-container, #sidebar { left: 0px; margin: 0px; padding: 0px; position:=
 absolute; top: 0px; }

#page-container { border: 0px; padding: 0px !important; }

@media screen {
  #sidebar.opened + #page-container { left: 250px; }
  #page-container { bottom: 0px; overflow: auto; right: 0px; }
  .loading-indicator { display: none; }
  .loading-indicator.active { background: url("/images/loading.gif"); displ=
ay: block; height: 32px; left: 50%; margin-left: -32px; margin-top: -32px; =
position: absolute; top: 50%; width: 32px; }
  .loading-indicator img { inset: 0px; position: absolute; }
}

@media print {
  @page { margin: 0px; }
  body, html { margin: 0px; }
  body { -webkit-print-color-adjust: exact; }
  #sidebar { display: none; }
  #page-container { background-color: transparent; height: auto; overflow: =
visible; width: auto; }
  .d { display: none; }
}

.pf { background-color: rgb(255, 255, 255); position: relative; }

.pc, .pf { border: 0px; margin: 0px; overflow: hidden; }

.pc { height: 100%; left: 0px; padding: 0px; position: absolute; top: 0px; =
transform-origin: 0px 0px; width: 100%; }

.pc, .pc.opened { display: block; }

.bf { bottom: 0px; height: 100%; top: 0px; width: 100%; }

.bf, .bi { border: 0px; margin: 0px; position: absolute; user-select: none;=
 }

@media print {
  .pf { box-shadow: none; margin: 0px; break-after: page; break-inside: avo=
id; }
}

.c { border: 0px; display: block; margin: 0px; overflow: hidden; padding: 0=
px; }

.c, .t { position: absolute; }

.t { font-size: 1px; transform-origin: 0px 100%; unicode-bidi: bidi-overrid=
e; white-space: pre; }

._ { color: transparent; z-index: -1; }

.pi { display: none; }

.d { position: absolute; transform-origin: 0px 100%; }

.tabs { align-items: baseline; display: flex; margin-bottom: -0.8rem; margi=
n-top: 0.5rem; overflow-x: scroll; }

.tabs .app-icon { margin-right: 0.3rem; transform: translateY(-2px); }

.tabs button { background-color: rgb(43, 47, 53); color: rgb(255, 255, 255)=
; font-size: 0.875rem; font-weight: 400; margin-bottom: 0px; margin-left: 0=
.3rem; padding: 0.2rem 0.5rem; text-transform: capitalize; transition: back=
ground-color 0.2s; }

.tabs button:first-child { background-color: rgb(61, 70, 82); border-bottom=
-left-radius: 0px; border-bottom-right-radius: 0px; padding-bottom: 0.8rem;=
 }

@media (min-width: 768px) {
  .tabs button:first-child { margin-left: 1rem; }
}

.tabs button:hover { background-color: rgb(61, 70, 82); }

.glide__track { padding-bottom: 10px; }

.glide__arrow--left, .glide__arrow--right { align-items: center; bottom: 10=
px; display: flex; font-size: 1rem; justify-content: center; opacity: 1; po=
sition: absolute; top: 10px; transform: scale(1); transition: background-co=
lor 0.2s ease-in-out; width: 80px; z-index: 10; }

.glide__arrow--left:hover, .glide__arrow--right:hover { background-color: r=
gba(0, 0, 0, 0.1); }

.glide__arrow--left:active .app-icon, .glide__arrow--right:active .app-icon=
 { transform: scale(0.8); }

.glide__arrow--left { left: 10px; }

.glide__arrow--right { right: 10px; }

.glide__arrow .app-icon { backdrop-filter: blur(3px); background: rgba(0, 0=
, 0, 0.2); border: 1px solid rgba(0, 0, 0, 0.3); border-radius: 50%; bottom=
: 50%; color: rgb(255, 255, 255); content: ""; display: flex; height: 2.5re=
m; margin: 30px 0px; position: sticky; top: 20px; width: 2.5rem; z-index: -=
1; }

@media (min-width: 768px) {
  .viewer--in-view .glide__arrow .app-icon { animation: 1.5s ease 0s 3 norm=
al none running blink-desktop; }
}

.viewer { background-color: rgb(61, 70, 82); direction: ltr; padding-top: 0=
.5rem; position: relative; }

@media (min-width: 768px) {
  .viewer { border-radius: 0.5rem; margin: 0px 0.5rem; padding: 0.5rem 0.5r=
em 0px; width: calc(100% - 1rem); }
}

.viewer__toolbar-top { bottom: 0px; display: flex; position: absolute; righ=
t: 0.5rem; }

.viewer__toolbar-top button { color: rgb(255, 255, 255); transition: backgr=
ound-color 0.3s ease-in-out; }

.viewer__toolbar-top button .app-icon { height: 1.4rem; width: 1.4rem; }

.viewer__toolbar-top button:hover { background-color: rgba(0, 0, 0, 0.2); }

.viewer__title { color: rgb(255, 255, 255); font-size: 1rem; margin: 0px; p=
adding: 0.5rem 3rem 0.5rem 0.5rem; }

.viewer-transition { display: flex; flex-direction: row; position: relative=
; }

.viewer-transition > div { width: 100vw; }

.js-only .viewer-transition > div { position: absolute; top: 0px; }

@media (max-width: 767.98px) {
  .viewer--in-view .viewer-container { animation: 1s ease 0s 3 normal none =
running blink; }
}

.viewer-container { margin: 10px; max-width: 100%; }

.viewer-container:not(.active) { display: none; }

.mounted .viewer-container { display: list-item; }

.viewer-page { background: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.=
9) 0px 0px 5px; min-height: 80px; position: relative; }

@media (max-width: 768px) {
  .viewer-page::after, .viewer-page::before { background: rgb(255, 255, 255=
); box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 5px; content: ""; height: 100%; p=
osition: absolute; top: 0px; transform: translate(calc(-100% - 5px)); width=
: 100%; }
  .viewer-page::after { transform: translate(calc(100% + 5px)); }
}

.viewer .pf { transform-origin: 0px 0px; }

.slide-left-enter-active, .slide-left-leave-active, .slide-right-enter-acti=
ve, .slide-right-leave-active { transition: 0.4s; }

@media (max-width: 767.98px) {
  .slide-left-enter { transform: translate(100%) !important; }
  .slide-left-leave-to, .slide-right-enter { transform: translate(-100%) !i=
mportant; }
  .slide-right-leave-to { transform: translate(100%) !important; }
}

@media (min-width: 768px) {
  .slide-left-enter { opacity: 1; }
  .slide-left-leave-to { opacity: 0; }
  .slide-right-enter { opacity: 1; }
  .slide-right-leave-to { opacity: 0; }
}

@keyframes blink {=20
  0% { transform: translate(0px); }
  30% { transform: translate(0px); }
  60% { transform: translate(-20px); }
}

@keyframes blink-desktop {=20
  0% { transform: scale(1); }
  80% { transform: scale(1); }
  90% { transform: scale(1.2); }
}

.toc[data-v-ed7e345c] { color: rgb(61, 70, 82); font-size: 1rem; }

.toc[data-v-ed7e345c]::-webkit-scrollbar { appearance: none; width: 10px; }

.toc[data-v-ed7e345c]::-webkit-scrollbar-thumb { background-color: rgba(0, =
0, 0, 0.5); border-radius: 5px; box-shadow: rgba(255, 255, 255, 0.5) 0px 0p=
x 1px; }

.toc__container[data-v-ed7e345c] { background-color: rgb(255, 255, 255); bo=
rder-radius: 0.5rem; max-height: 400px; overflow: scroll; padding: 1rem; po=
sition: relative; }

.toc__container[data-v-ed7e345c] ul { list-style: none; padding: 0px; }

.toc__container[data-v-ed7e345c] ul li { padding: 0.1rem 0px 0.1rem 1.5rem;=
 position: relative; }

.toc__container[data-v-ed7e345c] ul li a { padding: 0.2rem 0px; }

.toc__container[data-v-ed7e345c] ul li.active { font-weight: 700; }

.toc__container[data-v-ed7e345c] ul .app-icon { cursor: pointer; margin: 0.=
2rem 0px 0px -1.5rem; position: absolute; transform: rotate(0deg); transiti=
on: 0.4s; }

.toc__container[data-v-ed7e345c] ul .app-icon:hover { color: rgb(105, 120, =
140); }

.toc__container[data-v-ed7e345c] ul .app-icon.open { transform: rotate(90de=
g); }

.toc__container[data-v-ed7e345c] ul a { display: flex; justify-content: spa=
ce-between; line-height: 1; }

.toc__container[data-v-ed7e345c] ul a::after { border-bottom: 1.5px solid r=
gb(224, 234, 244); content: ""; flex-grow: 4; margin: 0px 0.5rem; }

.toc__container[data-v-ed7e345c] ul a::before { background: rgb(255, 255, 2=
55); color: rgb(61, 70, 82); content: attr(data-page); padding-left: 0.5rem=
; position: absolute; right: 0px; text-align: right; }

.comment-avatar[data-v-d8374c2e] { align-items: center; background: rgb(237=
, 237, 237); border-radius: 15px; color: rgb(255, 255, 255); display: flex;=
 height: 30px; justify-content: center; width: 30px; }

.comment-avatar--bot[data-v-d8374c2e] { color: transparent; background-colo=
r: transparent !important; background-image: url("data:image/svg+xml;charse=
t=3Dutf-8,%3Csvg xmlns=3D'http://www.w3.org/2000/svg' width=3D'35' height=
=3D'36' fill=3D'none' viewBox=3D'0 0 35 36'%3E%3Cpath fill=3D'%236A009C' d=
=3D'm23.106 9.564 1.417-2.834 1.417 2.834 2.834 1.417-2.834 1.417-1.417 2.8=
34-1.417-2.834-2.834-1.417zM11.77 15.232l2.834-5.668 2.834 5.668 5.668 2.83=
4-5.668 2.835-2.834 5.668L11.77 20.9l-5.668-2.835zm12.753 5.669 1.417 2.834=
 2.834 1.417-2.834 1.417-1.417 2.834-1.417-2.834-2.834-1.417 2.834-1.417z'/=
%3E%3C/svg%3E") !important; background-size: contain !important; }

.comment[data-v-282eccf6] { background: rgb(255, 255, 255); box-shadow: rgb=
a(0, 0, 0, 0.1) 0px 0px 20px; margin: 1rem 0px; padding: 1rem; position: re=
lative; }

@media (min-width: 768px) {
  .comment[data-v-282eccf6] { border-radius: 1rem; padding: 2rem; }
}

.comment--answer[data-v-282eccf6] { background: rgb(233, 235, 240); border-=
radius: 0px; box-shadow: none; margin: 0px; }

.comment__answers[data-v-282eccf6] { border-top: 1px solid rgb(196, 196, 19=
6); margin-top: 1rem; }

.comment__answers > h5[data-v-282eccf6] { font-weight: 400; }

.comment__answers > .comment[data-v-282eccf6] { padding-bottom: 0px; paddin=
g-top: 1rem; }

.comment__answers > .comment[data-v-282eccf6]:first-of-type { border-top-le=
ft-radius: 0.5rem; border-top-right-radius: 0.5rem; padding-top: 2rem; }

.comment__answers > .comment[data-v-282eccf6]:last-of-type { border-bottom-=
left-radius: 0.5rem; border-bottom-right-radius: 0.5rem; padding-bottom: 2r=
em; }

.comment__answers > .comment:last-of-type .comment__footer[data-v-282eccf6]=
 { border-bottom: 0px; padding: 0px; }

.comment__footer[data-v-282eccf6] { align-items: center; display: flex; fle=
x-flow: wrap; }

.comment--answer .comment__footer[data-v-282eccf6] { border-bottom: 1px sol=
id rgb(196, 196, 196); padding-bottom: 2rem; }

.comment__cta[data-v-282eccf6] { cursor: pointer; display: flex; font-size:=
 0.875rem; margin-top: 1rem; min-width: 100%; position: relative; transitio=
n: 0.2s ease-in-out; }

.comment__cta .input[data-v-282eccf6] { align-items: center; background: rg=
b(255, 255, 255); border-radius: 3px 4px 4px 3px; color: rgb(173, 181, 189)=
; display: flex; justify-content: space-between; line-height: 1; outline: r=
gb(68, 131, 255) solid 1px; padding: 0px 0px 0px 1rem; width: 100%; }

.comment__cta button[data-v-282eccf6] { border-radius: 3px; font-weight: 40=
0; height: 100%; }

.comment__cta[data-v-282eccf6]:hover { opacity: 0.7; }

.comment__attachment[data-v-282eccf6] { aspect-ratio: 4 / 3; border-radius:=
 0.25rem; display: flex; margin-bottom: 1rem; max-width: 400px; overflow: h=
idden; position: relative; transition: 0.2s ease-in-out; width: 100%; }

.comment__attachment small[data-v-282eccf6] { background: rgba(0, 0, 0, 0.4=
); border-radius: 0.25rem; bottom: 30px; color: rgb(255, 255, 255); padding=
: 0.2rem 0.5rem; position: absolute; right: 30px; }

.comment__attachment small .app-icon[data-v-282eccf6] { color: rgb(255, 255=
, 255); margin-right: 0.5rem; }

.comment__attachment img[data-v-282eccf6] { background: rgb(237, 237, 237);=
 height: 100%; object-fit: cover; position: absolute; width: 100%; }

.comment__attachment[data-v-282eccf6]:hover { filter: brightness(0.9); }

.comment__header[data-v-282eccf6] { align-items: center; display: flex; gap=
: 10px; line-height: 1.1; padding-bottom: 0.5rem; width: 100%; }

.comment__header--bot strong[data-v-282eccf6] { color: rgb(106, 0, 156); }

.comment__header--bot .info[data-v-282eccf6] { align-items: center; backgro=
und-color: rgb(106, 0, 156); border-radius: 0.8rem; color: rgb(255, 255, 25=
5); display: inline-flex; font-size: 0.6rem; font-weight: lighter; height: =
0.8rem; justify-content: center; margin-left: 0.5rem; padding: 0px; width: =
0.8rem; }

.comment__body[data-v-282eccf6], .comment__body p[data-v-282eccf6] { overfl=
ow: hidden; white-space: pre-line; }

.comment__body > div > small[data-v-282eccf6] { display: block; font-style:=
 italic; padding-bottom: 1rem; }

.comment__footer button.sm[data-v-282eccf6] { border-radius: 3px; font-weig=
ht: 500; padding: 0.25rem 1rem; }

.comment__footer button.sm.like[data-v-282eccf6]:first-child { border-botto=
m-right-radius: 0px; border-right: 0px; border-top-right-radius: 0px; }

.comment__footer button.sm.like ~ .like[data-v-282eccf6] { border-bottom-le=
ft-radius: 0px; border-top-left-radius: 0px; }

.comment__footer button.sm[data-v-282eccf6]:disabled { background-color: rg=
b(68, 131, 255); color: rgb(255, 255, 255); opacity: 1; }

.comment__footer button.sm .app-icon[data-v-282eccf6] { transform: scale(1.=
2); transform-origin: 50% 100%; }

.comment__footer button.sm:hover .app-icon[data-v-282eccf6] { transform: sc=
ale(1.3); }

.comment .btn-link[data-v-282eccf6] { color: rgb(68, 131, 255); font-weight=
: 400; padding: 0px 1rem; }

.comments[data-v-01df5956] { padding: 0px; }

@media (max-width: 767.98px) {
  .comments[data-v-01df5956] { padding: 0px !important; }
}

.comments[data-v-01df5956] div[data-freestar-ad] { border-bottom: 1px dotte=
d rgb(237, 237, 237); }

.comments .ad-placeholder[data-v-01df5956] { height: 50px; max-height: 50px=
; min-height: auto; }

@media (min-width: 768px) {
  .comments .ad-placeholder[data-v-01df5956] { height: 90px; max-height: 90=
px; }
}

.comments__hero[data-v-01df5956] { background-color: rgba(68, 131, 255, 0.2=
); margin-bottom: 2rem; padding: 1.5rem; text-align: center; }

.comments__hero h2[data-v-01df5956] { color: rgb(68, 131, 255); }

@media (min-width: 768px) {
  .comments__hero[data-v-01df5956] { border-radius: 1rem; }
}

.comments__hero ul[data-v-01df5956] { color: rgb(117, 131, 151); list-style=
: none; }

.comments__hero ul li[data-v-01df5956]::before { color: rgb(25, 135, 84); c=
ontent: "=E2=9C=93 "; }

.comments__hero p[data-v-01df5956] { margin: 0px auto 1rem; max-width: 320p=
x; }

.comments__hero input[data-v-01df5956] { border: 1px solid rgb(68, 131, 255=
); text-align: left !important; }

.comments button.block[data-v-01df5956], .comments input.block[data-v-01df5=
956] { display: flex; justify-content: center; margin: 0px auto; max-width:=
 300px; text-align: center; width: 100%; }

.illustration[data-v-01df5956] { margin: 0px auto; max-width: 300px; }

.description[data-v-412cb6ea] { white-space: pre-line; }

.gallery__tile-container[data-v-412cb6ea] { display: flex; flex-direction: =
row; margin-top: 1rem; }

.gallery__tile[data-v-412cb6ea] { background: rgb(255, 255, 255); border: 1=
px solid rgb(173, 181, 189); border-radius: 0.25rem; box-shadow: rgba(61, 7=
0, 82, 0.15) 0px 0px 20px; cursor: pointer; display: inline-block; height: =
100%; margin: 0.5rem 0.5rem 0.5rem 0px; overflow: hidden; padding: 5px 10px=
; position: relative; transform: scale(1); transition: 0.2s ease-in-out; wi=
dth: 15%; }

@media (max-width: 767.98px) {
  .gallery__tile[data-v-412cb6ea] { max-width: 15%; width: calc(33.3333% - =
0.5rem); }
}

.gallery__tile[data-v-412cb6ea]:hover { box-shadow: rgba(61, 70, 82, 0.35) =
0px 0px 20px; transform: scale(1.1); }

.gallery__tile.video[data-v-412cb6ea] { color: rgb(255, 255, 255); position=
: relative; }

.gallery__tile.video .app-icon[data-v-412cb6ea] { height: 50%; left: 25%; p=
osition: absolute; top: 25%; width: 50%; }

.gallery__tile > .image[data-v-412cb6ea] { background: rgb(255, 255, 255); =
padding-top: calc(100% + 1px); position: relative; }

.gallery__tile > .image img[data-v-412cb6ea] { height: 100%; max-width: non=
e; min-height: 20px; object-fit: cover; position: absolute; top: 0px; width=
: 100%; }

.gallery__more[data-v-412cb6ea] { align-items: center; background: rgba(0, =
0, 0, 0.4); inset: 0px; color: rgb(255, 255, 255); display: flex; font-size=
: 21px; justify-content: center; position: absolute; transition: 0.4s ease-=
in-out; }

.gallery__more[data-v-412cb6ea]:hover { background: rgba(0, 0, 0, 0.5); }

.carousel[data-v-412cb6ea] { align-items: center; background: rgb(255, 255,=
 255); border-radius: 0.5rem; box-shadow: rgba(0, 0, 0, 0.15) 0px 0.5rem 1r=
em; display: flex; justify-content: center; min-height: 480px; padding: 2re=
m; position: relative; }

.carousel iframe[data-v-412cb6ea], .carousel img[data-v-412cb6ea], .carouse=
l video[data-v-412cb6ea] { height: inherit; max-width: 320px; min-width: 32=
0px; width: 100%; }

.carousel[data-v-412cb6ea]::after { clear: both; content: ""; display: bloc=
k; }

.carousel__next[data-v-412cb6ea], .carousel__prev[data-v-412cb6ea] { align-=
items: center; background: rgba(0, 0, 0, 0.2); border: 1px solid rgb(173, 1=
81, 189); border-radius: 1rem; cursor: pointer; display: flex; height: 2rem=
; justify-content: center; left: 10%; position: absolute; top: calc(50% - 2=
rem); transform: scale(1); transition: 0.2s; width: 2rem; z-index: 1; }

.carousel__next[data-v-412cb6ea]:hover, .carousel__prev[data-v-412cb6ea]:ho=
ver { box-shadow: rgba(61, 70, 82, 0.35) 0px 0px 20px; transform: scale(1.1=
); }

.carousel__next .app-icon[data-v-412cb6ea], .carousel__prev .app-icon[data-=
v-412cb6ea] { color: rgb(255, 255, 255); display: flex; height: 1rem; width=
: 1rem; }

.carousel__next .app-icon[data-v-412cb6ea] svg, .carousel__prev .app-icon[d=
ata-v-412cb6ea] svg { width: 1rem; }

.carousel__next[data-v-412cb6ea] { left: inherit; right: 10%; }

.app-icon[data-v-998de295] { color: rgb(68, 131, 255); }

.card[data-v-998de295] { background: transparent; }

table[data-v-998de295] { background-color: rgb(255, 255, 255); border-radiu=
s: 0.25rem; margin-bottom: 2rem; width: 100%; }

table tr:nth-child(2n) td[data-v-998de295] { background-color: rgb(245, 246=
, 246); }

table tr > td[data-v-998de295] { overflow: hidden; padding: 0.3rem 0.5rem; =
vertical-align: top; }

table tr > td[data-v-998de295]:nth-child(2) { max-width: 0px; text-overflow=
: ellipsis; white-space: nowrap; width: 50%; }

.truncate-wrapper[data-v-998de295] { display: flex; height: 100%; min-heigh=
t: 250px; overflow: hidden; position: relative; }

.truncate-wrapper > div[data-v-998de295] { position: absolute; top: 0px; wi=
dth: 100%; }

.truncate-wrapper[data-v-998de295]::before { background-image: linear-gradi=
ent(rgba(244, 245, 245, 0) 0px, rgb(245, 246, 246)); bottom: 0px; content: =
""; height: 10%; position: absolute; width: 100%; z-index: 1; }

.specs__highlight[data-v-998de295] { display: flex; flex-wrap: wrap; gap: 0=
.5rem; margin-bottom: 2rem; }

.specs__highlight-item[data-v-998de295] { background: rgb(255, 255, 255); b=
order-radius: 0.25rem; padding: 0.5rem 3rem 0.5rem 0.5rem; position: relati=
ve; width: calc(50% - 0.25rem); }

.specs__highlight-item span[data-v-998de295] { position: absolute; right: 1=
rem; top: calc(50% - 13px); }

.feature-logos[data-v-998de295] { display: flex; flex-flow: wrap; }

.feature-logos > div[data-v-998de295] { margin: 0.5rem; width: 40px; }

.faq-item[data-v-867a1798] { background: rgb(255, 255, 255); border-radius:=
 0.5rem; margin-bottom: 0.5rem; padding: 1rem; width: 100%; }

.faq-item p[data-v-867a1798] { margin-bottom: 0.3rem; opacity: 0.8; white-s=
pace: pre-wrap; }

.faq-item h4[data-v-867a1798] { font-size: 1rem; font-weight: 600; }

.faq-item__footer button.sm[data-v-867a1798] { border-radius: 3px; font-wei=
ght: 500; padding: 0.25rem 1rem; }

.faq-item__footer button.sm.like[data-v-867a1798]:first-child { border-bott=
om-right-radius: 0px; border-right: 0px; border-top-right-radius: 0px; }

.faq-item__footer button.sm.like ~ .like[data-v-867a1798] { border-bottom-l=
eft-radius: 0px; border-top-left-radius: 0px; }

.faq-item__footer button.sm[data-v-867a1798]:disabled { background-color: r=
gb(68, 131, 255); color: rgb(255, 255, 255); opacity: 1; }

.faq-item__footer button.sm .app-icon[data-v-867a1798] { transform: scale(1=
.2); transform-origin: 50% 100%; }

.faq-item__footer button.sm:hover .app-icon[data-v-867a1798] { transform: s=
cale(1.3); }

.faq-listing iframe[data-v-bf996add], .faq-listing video[data-v-bf996add] {=
 height: inherit; width: 100%; }

.faq-listing .ad-placeholder[data-v-bf996add] { height: 50px; max-height: 5=
0px; min-height: auto; }

@media (min-width: 768px) {
  .faq-listing .ad-placeholder[data-v-bf996add] { height: 90px; max-height:=
 90px; }
}

.video-wrapper[data-v-bf996add] { padding-bottom: 56.25%; position: relativ=
e; }

.video-wrapper iframe[data-v-bf996add], .video-wrapper video[data-v-bf996ad=
d] { height: 100%; left: 0px; position: absolute; top: 0px; width: 100%; }

@media (max-width: 640px) {
  .glide__track[data-v-18a9b685] { margin: 0px -15px; padding-left: 15px; }
}

.related-products__item[data-v-18a9b685] { background: rgb(255, 255, 255); =
padding: 1rem; }

.manual__description[data-v-8e81d043] { white-space: break-spaces; }

.manual[data-v-8e81d043] .col-xl-8 { padding: 0px; }

@media (max-width: 767.98px) {
  .manual .top-desktop[data-v-8e81d043] { display: none; }
}

@media (min-width: 768px) {
  .manual .top-mobile[data-v-8e81d043] { display: none; }
}

.manual__navigation[data-v-8e81d043] { background: rgb(255, 255, 255); }

.manual__navigation .navigation[data-v-8e81d043] { box-shadow: rgba(0, 0, 0=
, 0.05) 5px 10px 10px; }

.manual__navigation-sub[data-v-8e81d043] { align-items: center; display: fl=
ex; font-weight: 600; height: 3rem; margin: 0px auto 1rem; max-width: 1000p=
x; padding: 0px 15px; }

.manual__navigation-sub--invert .smart-image[data-v-8e81d043] { filter: bri=
ghtness(0) invert(1); }

.manual__navigation-sub .smart-image[data-v-8e81d043] { height: 2rem; margi=
n-left: 1rem; max-width: 5rem; }

.manual__navigation-sub a[data-v-8e81d043] { align-items: center; border-ra=
dius: 0.5rem; color: rgb(99, 150, 198); display: flex; height: 2rem; justif=
y-content: center; margin-right: 0.5rem; padding-right: 0.5rem; }

.manual__navigation-sub a[data-v-8e81d043]:hover { backdrop-filter: brightn=
ess(0.9); }

.manual__navigation-sub--background[data-v-8e81d043], .manual__navigation-s=
ub--background a[data-v-8e81d043] { color: rgb(255, 255, 255); }

.manual__header[data-v-8e81d043] { display: flex; flex-flow: wrap; height: =
auto; line-height: 1.35; margin-bottom: 0.5rem; max-width: 1000px; }

@media (min-width: 768px) {
  .manual__header[data-v-8e81d043] { height: 90px; padding-left: 120px; pos=
ition: relative; }
}

.manual__rating[data-v-8e81d043] .rating__stars-hover svg { color: rgb(73, =
80, 87); }

.manual__rating[data-v-8e81d043] .link { color: rgb(73, 80, 87); }

.manual__title[data-v-8e81d043] { flex-shrink: 0; font-size: 1.25rem; margi=
n: 0.5rem 0px; width: 100%; }

@media (min-width: 768px) {
  .manual__title[data-v-8e81d043] { font-size: 1.5rem; margin: 0px; }
  .manual__title[data-v-8e81d043] span { color: rgb(61, 70, 82); display: b=
lock; font-size: 2.5rem; text-transform: capitalize; }
}

.manual__subtitle[data-v-8e81d043] { align-items: center; display: flex; fl=
ex-wrap: wrap; font-size: 0.875rem; font-weight: 600; }

.manual__subtitle .expand[data-v-8e81d043] { display: flex; flex-shrink: 0;=
 }

.manual__subtitle span[data-v-8e81d043] { color: rgb(73, 80, 87); white-spa=
ce: nowrap; }

.manual__subtitle > span[data-v-8e81d043]:first-child { align-items: center=
; display: flex; }

.manual__subtitle > span[data-v-8e81d043]:nth-child(2) { margin-right: 1rem=
; }

.manual__subtitle > span[data-v-8e81d043]:last-child { width: 100%; }

@media (min-width: 768px) {
  .manual__subtitle > span[data-v-8e81d043]:last-child { width: auto; }
}

.manual__info[data-v-8e81d043] { display: flex; flex-direction: column; fle=
x-grow: 2; gap: 8px; justify-content: center; width: 60%; }

.manual__info .flag[data-v-8e81d043]:not(.globe) { background-color: rgb(25=
5, 255, 255); border-radius: 2px; filter: drop-shadow(rgba(0, 0, 0, 0.15) 0=
px 2px 10px); height: 1rem; overflow: hidden; width: 1.3rem; }

.manual__main-image[data-v-8e81d043] { background-color: rgb(255, 255, 255)=
; border-radius: 0.5rem; box-shadow: rgba(61, 70, 82, 0.1) 0px 4px 20px; di=
splay: flex; float: left; height: 73px; margin: 0px 0.5rem 0px 0px; overflo=
w: hidden; padding: 0.3rem; width: 73px; }

.manual__main-image[data-v-8e81d043] img { border-radius: 0.25rem; }

@media (min-width: 768px) {
  .manual__main-image[data-v-8e81d043] { height: 90px; left: 1rem; position=
: absolute; top: 0px; width: 90px; }
}

.grid[data-v-8e81d043] > :not([class^=3D"ad"]) { margin: 0px auto 3rem; max=
-width: 758px; padding-left: 15px; padding-right: 15px; }

.grid[data-v-8e81d043] > .affiliate-products, .grid[data-v-8e81d043] > .rel=
ated-products { max-width: 1000px; }

.ad_test[data-v-8e81d043] { display: flex; }

.ad_test[data-v-8e81d043] > div, .ad_test[data-v-8e81d043] > div > ins { di=
splay: flex; width: 100%; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/_nuxt/ContainerSide.B9MGHb34.css

@charset "utf-8";

div[data-v-1460e71d] { position: relative; }

div picture[data-v-1460e71d], div picture img[data-v-1460e71d] { height: 10=
0%; max-height: 100%; max-width: 100%; width: 100%; }

.ad[data-v-a411a370] { margin: 1rem 0px; min-height: 100px; text-align: cen=
ter; }

.ad.type-display[data-v-a411a370]::before, .ad.type-link[data-v-a411a370]::=
before { color: rgb(194, 194, 194); content: "advertisement"; display: bloc=
k; font-size: 0.8rem; margin-top: -1.1rem; }

.debug[data-v-a411a370] { background: rgb(255, 255, 255); border: 1px solid=
 rgb(194, 194, 194); opacity: 0.95; position: absolute; text-align: left; z=
-index: 1000; height: 100px !important; width: 300px !important; }

.debug pre[data-v-a411a370] { white-space: pre-line; }

.ad-placeholder__debug[data-v-b25fdf40] { background: rgb(151, 234, 138); b=
order: 1px solid rgb(194, 194, 194); bottom: 0px; font-size: 11px; opacity:=
 0.85; padding: 0.5rem; position: absolute; right: 0px; text-align: left; t=
op: 0px; z-index: 1000; width: 200px !important; }

.ad-placeholder__debug pre[data-v-b25fdf40] { white-space: pre-wrap; }

.ad-placeholder[data-v-5e860da6] { clear: both; height: 83.2vw; margin-left=
: auto; margin-right: auto; overflow: hidden; text-align: center; width: 10=
0%; }

.ad-placeholder.ad_debug[data-v-5e860da6] { background: rgb(237, 237, 237);=
 border: 1px solid rgb(194, 194, 194); }

.ad-placeholder[data-v-5e860da6] ins.adsbygoogle[data-ad-status=3D"unfilled=
"] { border: 1px solid rgb(237, 237, 237); }

.ad-placeholder.in-feed[data-v-5e860da6] { min-height: 100px; }

.ad-placeholder__debug[data-v-5e860da6] { background: rgb(231, 159, 159); b=
order: 1px solid rgb(194, 194, 194); font-size: 11px; opacity: 0.85; paddin=
g: 0.5rem; position: absolute; text-align: left; z-index: 1000; height: 200=
px !important; width: 200px !important; }

.ad-placeholder__debug pre[data-v-5e860da6] { white-space: pre-wrap; }

@media (min-width: 768px) {
  .ad-placeholder[data-v-5e860da6] { background: transparent; height: 280px=
; }
}

@media (max-width: 768px) {
  .ad-placeholder[data-v-5e860da6] { margin-left: calc(50% - 50vw); width: =
100vw; }
  .ad-placeholder.desktopOnly[data-v-5e860da6] { min-height: 0px; }
}

.pagination { display: inline-flex; flex-direction: row; list-style: none; =
margin: 0px -15px; overflow: hidden; padding: 1rem 0px; }

.pagination li { padding: 0px 0.2rem; }

.pagination a, .pagination span { background: rgb(255, 255, 255); border-ra=
dius: 50%; color: rgb(99, 150, 198); display: flex; font-size: 0.875rem; fo=
nt-weight: 700; justify-content: center; line-height: 1; overflow: hidden; =
padding: 0.5rem; transition: background-color 0.2s ease-in-out; width: 1.87=
5rem; }

.pagination a.active, .pagination a:hover, .pagination span.active, .pagina=
tion span:hover { background: rgb(99, 150, 198); color: rgb(255, 255, 255);=
 }

.pagination a.alt, .pagination span.alt { background: rgb(61, 70, 82); colo=
r: rgb(255, 255, 255); }

.pagination a.alt:hover, .pagination span.alt:hover { background: rgb(132, =
146, 164); }

.product-listing[data-v-3ad21d7d] > :first-child { border-top-left-radius: =
0.5rem; border-top-right-radius: 0.5rem; }

.product-listing[data-v-3ad21d7d] > :last-child { border-bottom-left-radius=
: 0.5rem; border-bottom-right-radius: 0.5rem; }

.product-listing__item[data-v-3ad21d7d] { background: rgb(255, 255, 255); b=
order-radius: 0px; line-height: 1.2; margin-bottom: 2px; padding: 0.85rem 1=
rem 0.5rem; }

.product-listing__item h5[data-v-3ad21d7d], .product-listing__item p[data-v=
-3ad21d7d] { color: rgb(61, 70, 82); margin-bottom: 0px; overflow-wrap: any=
where; }

.product-listing__item h5[data-v-3ad21d7d] { font-weight: 700; }

.product-listing__item[data-v-3ad21d7d] span + span::before { content: " =
=C2=B7 "; }

.product-listing__item[data-v-3ad21d7d] span { color: rgb(196, 196, 196); f=
ont-size: 0.875rem; text-transform: capitalize; }

.product-listing__img[data-v-3ad21d7d] { margin-right: 1rem; }

.product-listing__img[data-v-3ad21d7d] img { border-radius: 0.25rem; height=
: 60px; width: 60px; }

.product-listing__rating[data-v-3ad21d7d] { align-self: center; background:=
 rgb(196, 196, 196); cursor: pointer; display: flex; margin-bottom: 0.3rem;=
 margin-left: -2px; mask-image: url("data:image/svg+xml;utf8,<svg width=3D\=
"100\" height=3D\"100\" viewBox=3D\"0 0 100 100\" fill=3D\"black\" xmlns=3D=
\"http://www.w3.org/2000/svg\"> <path d=3D\"M26.04 84.0234C27.8174 85.3906 =
29.9707 84.9463 32.4316 83.1689L50 70.2832L67.5342 83.1689C69.9951 84.9463 =
72.1484 85.3906 73.9258 84.0234C75.6689 82.7246 76.0107 80.5371 75.0537 77.=
7002L68.1152 57.0898L85.8203 44.4092C88.2812 42.666 89.3066 40.6836 88.623 =
38.5986C87.9053 36.5479 85.957 35.5225 82.915 35.5566L61.2451 35.7275L54.61=
43 14.9805C53.6914 12.0752 52.1875 10.5371 50 10.5371C47.8125 10.5371 46.27=
44 12.0752 45.3516 14.9805L38.7549 35.7275L17.0166 35.5566C14.043 35.5225 1=
2.0605 36.5479 11.3428 38.5986C10.625 40.6836 11.7188 42.666 14.1455 44.409=
2L31.8506 57.0898L24.9121 77.7002C23.9551 80.5371 24.2969 82.7246 26.04 84.=
0234Z\"></path> </svg>"); mask-position: center bottom; mask-repeat: repeat=
-x; mask-size: 1rem 1rem; min-height: 1rem; min-width: 5rem; position: rela=
tive; white-space: nowrap; width: 5rem; }

.product-listing__rating-hover[data-v-3ad21d7d] { background: rgb(255, 205,=
 0); }

.product-listing__ad-lb[data-v-3ad21d7d] { align-items: center; display: fl=
ex; grid-column: 1 / 6; justify-content: center; }

.product-listing__ad-lb ins[data-v-3ad21d7d], .product-listing__ad-lb > div=
[data-v-3ad21d7d] { align-items: center; display: flex; justify-content: ce=
nter; min-height: 100px; width: 100%; }

@media (min-width: 576px) {
  .product-listing[data-v-3ad21d7d] { display: grid; grid-auto-rows: auto; =
grid-template-columns: repeat(5, 1fr); gap: 0.8rem; }
  .product-listing__ad[data-v-3ad21d7d] { align-items: center; display: fle=
x; grid-area: 1 / 1 / 2 / 3; justify-content: center; }
  .product-listing__ad ins[data-v-3ad21d7d], .product-listing__ad > div[dat=
a-v-3ad21d7d] { min-height: 100px; width: 100%; }
  .product-listing__img[data-v-3ad21d7d] { height: 80px; justify-self: cent=
er; margin: 1rem 0px; width: 80px; }
  .product-listing__img[data-v-3ad21d7d] img { height: 80px; width: 80px; }
  .product-listing__info[data-v-3ad21d7d] { display: flex; flex-direction: =
column; height: 100%; }
  .product-listing__rating[data-v-3ad21d7d] { height: 100%; }
  .product-listing__item[data-v-3ad21d7d] { box-shadow: rgba(61, 70, 82, 0.=
1) 0px 4px 20px; display: flex; height: 100%; padding: 0px 0.8rem 0.8rem; t=
ransition: 0.2s ease-in-out; border-radius: 0.5rem !important; }
  .product-listing__item[data-v-3ad21d7d]:hover { box-shadow: rgba(61, 70, =
82, 0.3) 0px 4px 20px; transform: scale(1.04); }
  .product-listing__item span + span[data-v-3ad21d7d] { display: block; }
  .product-listing__item span + span[data-v-3ad21d7d]::before { content: ""=
; }
  .product-listing__item a[data-v-3ad21d7d] { align-items: center; flex-dir=
ection: column; height: 100%; padding: 0.5rem; text-align: center; }
  .product-listing__item a h5[data-v-3ad21d7d] { margin: 0.5rem 0px; }
}

.clearfix[data-v-0eadb2a6] { clear: both; }

.col-xl-2.sticky-side[data-v-0eadb2a6] { flex-grow: 1; order: 3; }

.col-xl-2.sticky-side[data-v-0eadb2a6]:first-child { order: 1; }

.col-xl-2.sticky-side + .col-xl-8[data-v-0eadb2a6] { max-width: 1000px; ord=
er: 1; }

.sticky-top[data-v-0eadb2a6] { position: sticky; top: 0px; z-index: auto; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/_nuxt/useConfig.C6h2z7Th.css

@charset "utf-8";

.search-button[data-v-75dbeedd] { display: flex; flex-direction: column; ju=
stify-content: center; }

.search-button__overlay[data-v-75dbeedd] { place-content: center; backdrop-=
filter: blur(5px); background: rgba(0, 0, 0, 0.8); display: flex; height: 1=
00vh; left: 0px; padding-top: 5rem; position: fixed; top: 0px; width: 100vw=
; z-index: 1055; }

.search-button__overlay .container[data-v-75dbeedd] { flex-direction: colum=
n; justify-content: flex-start; margin-top: 2rem; max-width: 500px; positio=
n: relative; }

.search-button__overlay .app-icon.close[data-v-75dbeedd] { color: rgb(255, =
255, 255); position: absolute; right: 30px; top: 30px; }

.navigation[data-v-95f55862] { background-color: rgb(255, 255, 255); box-sh=
adow: rgba(0, 0, 0, 0.2) 0px 0px 4px; position: relative; z-index: 1; }

.navigation--inverted[data-v-95f55862] { background-color: rgba(255, 255, 2=
55, 0.25); width: 100%; }

.navigation .container[data-v-95f55862] { align-items: center; display: fle=
x; flex-flow: row; height: 45px; justify-content: space-between; max-width:=
 1000px; }

.navigation--inverted .navigation__search[data-v-95f55862] .app-icon { colo=
r: rgb(255, 255, 255); }

.navigation__logo[data-v-95f55862] { color: rgb(99, 150, 198); font-weight:=
 700; letter-spacing: 0.05rem; max-width: calc(-60px + 100vw); overflow: hi=
dden; text-decoration: none; text-overflow: ellipsis; transition: color 0.3=
s; }

.navigation__logo[data-v-95f55862]:focus, .navigation__logo[data-v-95f55862=
]:hover { color: rgb(66, 125, 180); }

.navigation__logo[data-v-95f55862] span { color: rgb(243, 91, 58); }

.navigation--inverted .navigation__logo[data-v-95f55862], .navigation--inve=
rted .navigation__logo[data-v-95f55862] span { color: rgb(255, 255, 255); }

.dropdown[data-v-95f55862] { z-index: 1040; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/_nuxt/entry.h33TVmZ5.css

@charset "utf-8";

.app-icon { box-sizing: border-box; display: inline-flex; height: 1em; outl=
ine: none; position: relative; vertical-align: middle; width: 1em; }

.app-icon svg { fill: currentcolor; height: 100%; width: 100%; }

.app-icon.size-lg { height: 1.5rem; width: 1.5rem; }

.app-icon.size-xl { height: 2rem; width: 2rem; }

.app-icon.size-xxl { height: 3rem; width: 3rem; }

.search[data-v-4203ab74] { max-width: 400px; min-width: 280px; position: re=
lative; width: 100%; }

.search .dropdown[data-v-4203ab74] { list-style: none; margin-top: 5px; ove=
rflow: hidden; padding-left: 0px; width: 100%; }

.search .dropdown a[data-v-4203ab74] { color: rgb(61, 70, 82); }

.search__placeholder[data-v-4203ab74] { line-height: 2.25rem; opacity: 0.7;=
 overflow: hidden; pointer-events: none; position: absolute; text-overflow:=
 ellipsis; white-space: nowrap; width: calc(100% - 45px); z-index: 1; }

.search--large .search__placeholder[data-v-4203ab74] { line-height: 2.8rem;=
 width: calc(100% - 60px); }

.search__input[data-v-4203ab74], .search__placeholder[data-v-4203ab74] { co=
lor: rgb(61, 70, 82); height: 2.25rem; padding: 0px 1rem; }

.search--large .search__input[data-v-4203ab74], .search--large .search__pla=
ceholder[data-v-4203ab74] { height: 2.8rem; padding: 0px 1.5rem; }

.search__input[data-v-4203ab74] { border: 1px solid rgb(222, 226, 230); bor=
der-radius: 1.5rem 0px 0px 1.5rem; flex-grow: 1; width: 100%; }

.search--large .search__input[data-v-4203ab74] { border: 0px; box-shadow: r=
gba(0, 0, 0, 0.1) 0px 10px 20px; }

.search__button[data-v-4203ab74] { border-radius: 1.5rem; height: 2.25rem; =
margin-left: -1.5rem; padding: 0px 1.5rem; transition: 0.2s ease-in-out; }

.search--large .search__button[data-v-4203ab74] { height: 2.8rem; transform=
: scale(1.2); }

.search__button[data-v-4203ab74]:hover { transform: scale(1.3); }

.search__close[data-v-4203ab74] { cursor: pointer; height: 50%; left: calc(=
100% - 95px); opacity: 0.25; position: absolute; transition: opacity 0.2s e=
ase-in-out; z-index: 1; }

.search__close[data-v-4203ab74]:hover { opacity: 0.3; }

.search--large .search__close[data-v-4203ab74] { left: calc(100% - 110px); =
}

.modal[data-v-740f5b8e] { background: rgb(255, 255, 255); position: fixed; =
top: 100px; z-index: 1040; }

.modal__backdrop[data-v-740f5b8e] { background: rgba(0, 0, 0, 0.3); content=
: ""; height: 100vh; left: 0px; position: fixed; top: 0px; width: 100vw; z-=
index: -1040; }

.modal__container[data-v-740f5b8e] { background: rgb(245, 246, 246); border=
: 1px solid rgb(222, 226, 230); border-radius: 0.5rem; left: auto; margin: =
0px auto; max-width: 600px; position: relative; right: auto; transform: tra=
nslateY(calc(-50% + 50vh)); }

@media (max-width: 768px) {
  .modal__container[data-v-740f5b8e] { max-width: 95vw; }
  .modal--full-screen .modal__container[data-v-740f5b8e] { border-radius: 0=
px; height: 100vh; max-width: 100vw; width: 100vw; }
}

.modal__header[data-v-740f5b8e] { background: rgb(255, 255, 255); border-ra=
dius: 0.5rem; min-height: 3rem; padding: 0.75rem 1rem; }

.modal__content[data-v-740f5b8e] { max-height: 80vh; overflow: scroll; padd=
ing: 1rem; position: relative; }

.modal__close[data-v-740f5b8e] { position: fixed; right: 0px; top: 0px; z-i=
ndex: 10; }

.search-panel[data-v-60f9a23b] { align-items: center; background: url("../i=
mages/header.svg") 0% 0% / cover; display: flex; flex-direction: column; ju=
stify-content: center; margin-bottom: 3rem; min-height: 200px; padding: 5re=
m 1rem 0px; }

.search-panel .search--dropdown[data-v-60f9a23b] { justify-self: flex-end; =
margin-bottom: -1rem; margin-top: 4rem; }

.search-panel h1[data-v-60f9a23b] { color: rgb(255, 255, 255); font-weight:=
 700; }

.search-panel .search-placeholder[data-v-60f9a23b] { background: rgb(255, 2=
55, 255); border-radius: 0.5rem; color: rgb(196, 196, 196); display: inline=
-flex; min-width: 300px; padding: 1rem; }

.search-panel .search-placeholder .app-icon[data-v-60f9a23b] { color: rgb(6=
1, 70, 82); margin-left: -1rem; margin-top: 0.2rem; }

.lang-switcher__modal[data-v-9b698a5b] .modal__container { max-width: 300px=
; }

.lang-switcher__label[data-v-9b698a5b] { background-color: rgb(255, 255, 25=
5); border-radius: 3px; margin-right: 1rem; padding: 0.3rem 1rem; }

.lang-switcher__label[data-v-9b698a5b]:hover { background: rgb(255, 255, 25=
5); color: rgb(243, 91, 58); }

.lang-switcher__list[data-v-9b698a5b] { text-align: left; }

.lang-switcher__list a[data-v-9b698a5b] { color: rgb(61, 70, 82); }

.lang-switcher__list li[data-v-9b698a5b] { padding: 0.3rem; }

.lang-switcher__list li[data-v-9b698a5b]:hover { background: rgb(224, 234, =
244); }

.lang-switcher__list .app-icon[data-v-9b698a5b] { background-color: rgb(255=
, 255, 255); border-radius: 2px; filter: drop-shadow(rgba(0, 0, 0, 0.15) 0p=
x 2px 10px); height: 1.4rem; margin-right: 0.5rem; overflow: hidden; width:=
 1.8rem; }

.lang-switcher .badge[data-v-9b698a5b] { background: rgb(222, 226, 230); bo=
rder-radius: 3px; display: inline-block; padding: 0.3rem 0.5rem; text-align=
: center; width: 4rem; }

.footer[data-v-a1a4be57] { background: rgb(99, 150, 198); color: rgb(255, 2=
55, 255); font-size: 0.875rem; }

.footer .lang-switcher[data-v-a1a4be57] { align-items: baseline; display: f=
lex; justify-content: center; margin-bottom: 0.5rem; }

.footer__links[data-v-a1a4be57] { padding: 1rem 0px 2rem; }

.footer a[data-v-a1a4be57], .footer p[data-v-a1a4be57] { color: rgb(255, 25=
5, 255) !important; opacity: 0.7; }

.footer h4[data-v-a1a4be57] { color: rgb(255, 255, 255); }

.footer h4 span[data-v-a1a4be57] { color: rgb(68, 131, 255); }

@media (max-width: 768px) {
  .footer .bv-d-sm-down-none[data-v-a1a4be57] { display: none; }
}
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.viewer-page .ff0 { font-family: sans-serif; visibility: hidden; }

.viewer-page .sc_ { text-shadow: none; }

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .viewer-page .sc_ { -webkit-text-stroke: 0px transparent; }
}

.viewer-page .y0 { bottom: 0px; }

.viewer-page .h0 { height: 595.275px; }

.viewer-page .h1 { height: 595.5px; }

.viewer-page .w1 { width: 419.5px; }

.viewer-page .w0 { width: 419.528px; }

.viewer-page .x0 { left: 0px; }

@media print {
  .viewer-page .y0 { bottom: 0pt; }
  .viewer-page .h0 { height: 793.701pt; }
  .viewer-page .h1 { height: 794pt; }
  .viewer-page .w1 { width: 559.333pt; }
  .viewer-page .w0 { width: 559.371pt; }
  .viewer-page .x0 { left: 0pt; }
}
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.product-listing[data-v-3ad21d7d] > :first-child { border-top-left-radius: =
0.5rem; border-top-right-radius: 0.5rem; }

.product-listing[data-v-3ad21d7d] > :last-child { border-bottom-left-radius=
: 0.5rem; border-bottom-right-radius: 0.5rem; }

.product-listing__item[data-v-3ad21d7d] { background: rgb(255, 255, 255); b=
order-radius: 0px; line-height: 1.2; margin-bottom: 2px; padding: 0.85rem 1=
rem 0.5rem; }

.product-listing__item h5[data-v-3ad21d7d], .product-listing__item p[data-v=
-3ad21d7d] { color: rgb(61, 70, 82); margin-bottom: 0px; overflow-wrap: any=
where; }

.product-listing__item h5[data-v-3ad21d7d] { font-weight: 700; }

.product-listing__item[data-v-3ad21d7d] span + span::before { content: " =
=C2=B7 "; }

.product-listing__item[data-v-3ad21d7d] span { color: rgb(196, 196, 196); f=
ont-size: 0.875rem; text-transform: capitalize; }

.product-listing__img[data-v-3ad21d7d] { margin-right: 1rem; }

.product-listing__img[data-v-3ad21d7d] img { border-radius: 0.25rem; height=
: 60px; width: 60px; }

.product-listing__rating[data-v-3ad21d7d] { align-self: center; background:=
 rgb(196, 196, 196); cursor: pointer; display: flex; margin-bottom: 0.3rem;=
 margin-left: -2px; mask-image: url("data:image/svg+xml;utf8,<svg width=3D\=
"100\" height=3D\"100\" viewBox=3D\"0 0 100 100\" fill=3D\"black\" xmlns=3D=
\"http://www.w3.org/2000/svg\"> <path d=3D\"M26.04 84.0234C27.8174 85.3906 =
29.9707 84.9463 32.4316 83.1689L50 70.2832L67.5342 83.1689C69.9951 84.9463 =
72.1484 85.3906 73.9258 84.0234C75.6689 82.7246 76.0107 80.5371 75.0537 77.=
7002L68.1152 57.0898L85.8203 44.4092C88.2812 42.666 89.3066 40.6836 88.623 =
38.5986C87.9053 36.5479 85.957 35.5225 82.915 35.5566L61.2451 35.7275L54.61=
43 14.9805C53.6914 12.0752 52.1875 10.5371 50 10.5371C47.8125 10.5371 46.27=
44 12.0752 45.3516 14.9805L38.7549 35.7275L17.0166 35.5566C14.043 35.5225 1=
2.0605 36.5479 11.3428 38.5986C10.625 40.6836 11.7188 42.666 14.1455 44.409=
2L31.8506 57.0898L24.9121 77.7002C23.9551 80.5371 24.2969 82.7246 26.04 84.=
0234Z\"></path> </svg>"); mask-position: center bottom; mask-repeat: repeat=
-x; mask-size: 1rem 1rem; min-height: 1rem; min-width: 5rem; position: rela=
tive; white-space: nowrap; width: 5rem; }

.product-listing__rating-hover[data-v-3ad21d7d] { background: rgb(255, 205,=
 0); }

.product-listing__ad-lb[data-v-3ad21d7d] { align-items: center; display: fl=
ex; grid-column: 1 / 6; justify-content: center; }

.product-listing__ad-lb ins[data-v-3ad21d7d], .product-listing__ad-lb > div=
[data-v-3ad21d7d] { align-items: center; display: flex; justify-content: ce=
nter; min-height: 100px; width: 100%; }

@media (min-width: 576px) {
  .product-listing[data-v-3ad21d7d] { display: grid; grid-auto-rows: auto; =
grid-template-columns: repeat(5, 1fr); gap: 0.8rem; }
  .product-listing__ad[data-v-3ad21d7d] { align-items: center; display: fle=
x; grid-area: 1 / 1 / 2 / 3; justify-content: center; }
  .product-listing__ad ins[data-v-3ad21d7d], .product-listing__ad > div[dat=
a-v-3ad21d7d] { min-height: 100px; width: 100%; }
  .product-listing__img[data-v-3ad21d7d] { height: 80px; justify-self: cent=
er; margin: 1rem 0px; width: 80px; }
  .product-listing__img[data-v-3ad21d7d] img { height: 80px; width: 80px; }
  .product-listing__info[data-v-3ad21d7d] { display: flex; flex-direction: =
column; height: 100%; }
  .product-listing__rating[data-v-3ad21d7d] { height: 100%; }
  .product-listing__item[data-v-3ad21d7d] { box-shadow: rgba(61, 70, 82, 0.=
1) 0px 4px 20px; display: flex; height: 100%; padding: 0px 0.8rem 0.8rem; t=
ransition: 0.2s ease-in-out; border-radius: 0.5rem !important; }
  .product-listing__item[data-v-3ad21d7d]:hover { box-shadow: rgba(61, 70, =
82, 0.3) 0px 4px 20px; transform: scale(1.04); }
  .product-listing__item span + span[data-v-3ad21d7d] { display: block; }
  .product-listing__item span + span[data-v-3ad21d7d]::before { content: ""=
; }
  .product-listing__item a[data-v-3ad21d7d] { align-items: center; flex-dir=
ection: column; height: 100%; padding: 0.5rem; text-align: center; }
  .product-listing__item a h5[data-v-3ad21d7d] { margin: 0.5rem 0px; }
}
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

@media (max-width: 640px) {
  .glide__track[data-v-18a9b685] { margin: 0px -15px; padding-left: 15px; }
}

.related-products__item[data-v-18a9b685] { background: rgb(255, 255, 255); =
padding: 1rem; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.faq-item[data-v-867a1798] { background: rgb(255, 255, 255); border-radius:=
 0.5rem; margin-bottom: 0.5rem; padding: 1rem; width: 100%; }

.faq-item p[data-v-867a1798] { margin-bottom: 0.3rem; opacity: 0.8; white-s=
pace: pre-wrap; }

.faq-item h4[data-v-867a1798] { font-size: 1rem; font-weight: 600; }

.faq-item__footer button.sm[data-v-867a1798] { border-radius: 3px; font-wei=
ght: 500; padding: 0.25rem 1rem; }

.faq-item__footer button.sm.like[data-v-867a1798]:first-child { border-bott=
om-right-radius: 0px; border-right: 0px; border-top-right-radius: 0px; }

.faq-item__footer button.sm.like ~ .like[data-v-867a1798] { border-bottom-l=
eft-radius: 0px; border-top-left-radius: 0px; }

.faq-item__footer button.sm[data-v-867a1798]:disabled { background-color: r=
gb(68, 131, 255); color: rgb(255, 255, 255); opacity: 1; }

.faq-item__footer button.sm .app-icon[data-v-867a1798] { transform: scale(1=
.2); transform-origin: 50% 100%; }

.faq-item__footer button.sm:hover .app-icon[data-v-867a1798] { transform: s=
cale(1.3); }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.faq-listing iframe[data-v-bf996add], .faq-listing video[data-v-bf996add] {=
 height: inherit; width: 100%; }

.faq-listing .ad-placeholder[data-v-bf996add] { height: 50px; max-height: 5=
0px; min-height: auto; }

@media (min-width: 768px) {
  .faq-listing .ad-placeholder[data-v-bf996add] { height: 90px; max-height:=
 90px; }
}

.video-wrapper[data-v-bf996add] { padding-bottom: 56.25%; position: relativ=
e; }

.video-wrapper iframe[data-v-bf996add], .video-wrapper video[data-v-bf996ad=
d] { height: 100%; left: 0px; position: absolute; top: 0px; width: 100%; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.app-icon[data-v-998de295] { color: rgb(68, 131, 255); }

.card[data-v-998de295] { background: transparent; }

table[data-v-998de295] { background-color: rgb(255, 255, 255); border-radiu=
s: 0.25rem; margin-bottom: 2rem; width: 100%; }

table tr:nth-child(2n) td[data-v-998de295] { background-color: rgb(245, 246=
, 246); }

table tr > td[data-v-998de295] { overflow: hidden; padding: 0.3rem 0.5rem; =
vertical-align: top; }

table tr > td[data-v-998de295]:nth-child(2) { max-width: 0px; text-overflow=
: ellipsis; white-space: nowrap; width: 50%; }

.truncate-wrapper[data-v-998de295] { display: flex; height: 100%; min-heigh=
t: 250px; overflow: hidden; position: relative; }

.truncate-wrapper > div[data-v-998de295] { position: absolute; top: 0px; wi=
dth: 100%; }

.truncate-wrapper[data-v-998de295]::before { background-image: linear-gradi=
ent(rgba(244, 245, 245, 0) 0px, rgb(245, 246, 246)); bottom: 0px; content: =
""; height: 10%; position: absolute; width: 100%; z-index: 1; }

.specs__highlight[data-v-998de295] { display: flex; flex-wrap: wrap; gap: 0=
.5rem; margin-bottom: 2rem; }

.specs__highlight-item[data-v-998de295] { background: rgb(255, 255, 255); b=
order-radius: 0.25rem; padding: 0.5rem 3rem 0.5rem 0.5rem; position: relati=
ve; width: calc(50% - 0.25rem); }

.specs__highlight-item span[data-v-998de295] { position: absolute; right: 1=
rem; top: calc(50% - 13px); }

.feature-logos[data-v-998de295] { display: flex; flex-flow: wrap; }

.feature-logos > div[data-v-998de295] { margin: 0.5rem; width: 40px; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.description[data-v-412cb6ea] { white-space: pre-line; }

.gallery__tile-container[data-v-412cb6ea] { display: flex; flex-direction: =
row; margin-top: 1rem; }

.gallery__tile[data-v-412cb6ea] { background: rgb(255, 255, 255); border: 1=
px solid rgb(173, 181, 189); border-radius: 0.25rem; box-shadow: rgba(61, 7=
0, 82, 0.15) 0px 0px 20px; cursor: pointer; display: inline-block; height: =
100%; margin: 0.5rem 0.5rem 0.5rem 0px; overflow: hidden; padding: 5px 10px=
; position: relative; transform: scale(1); transition: 0.2s ease-in-out; wi=
dth: 15%; }

@media (max-width: 767.98px) {
  .gallery__tile[data-v-412cb6ea] { max-width: 15%; width: calc(33.3333% - =
0.5rem); }
}

.gallery__tile[data-v-412cb6ea]:hover { box-shadow: rgba(61, 70, 82, 0.35) =
0px 0px 20px; transform: scale(1.1); }

.gallery__tile.video[data-v-412cb6ea] { color: rgb(255, 255, 255); position=
: relative; }

.gallery__tile.video .app-icon[data-v-412cb6ea] { height: 50%; left: 25%; p=
osition: absolute; top: 25%; width: 50%; }

.gallery__tile > .image[data-v-412cb6ea] { background: rgb(255, 255, 255); =
padding-top: calc(100% + 1px); position: relative; }

.gallery__tile > .image img[data-v-412cb6ea] { height: 100%; max-width: non=
e; min-height: 20px; object-fit: cover; position: absolute; top: 0px; width=
: 100%; }

.gallery__more[data-v-412cb6ea] { align-items: center; background: rgba(0, =
0, 0, 0.4); inset: 0px; color: rgb(255, 255, 255); display: flex; font-size=
: 21px; justify-content: center; position: absolute; transition: 0.4s ease-=
in-out; }

.gallery__more[data-v-412cb6ea]:hover { background: rgba(0, 0, 0, 0.5); }

.carousel[data-v-412cb6ea] { align-items: center; background: rgb(255, 255,=
 255); border-radius: 0.5rem; box-shadow: rgba(0, 0, 0, 0.15) 0px 0.5rem 1r=
em; display: flex; justify-content: center; min-height: 480px; padding: 2re=
m; position: relative; }

.carousel iframe[data-v-412cb6ea], .carousel img[data-v-412cb6ea], .carouse=
l video[data-v-412cb6ea] { height: inherit; max-width: 320px; min-width: 32=
0px; width: 100%; }

.carousel[data-v-412cb6ea]::after { clear: both; content: ""; display: bloc=
k; }

.carousel__next[data-v-412cb6ea], .carousel__prev[data-v-412cb6ea] { align-=
items: center; background: rgba(0, 0, 0, 0.2); border: 1px solid rgb(173, 1=
81, 189); border-radius: 1rem; cursor: pointer; display: flex; height: 2rem=
; justify-content: center; left: 10%; position: absolute; top: calc(50% - 2=
rem); transform: scale(1); transition: 0.2s; width: 2rem; z-index: 1; }

.carousel__next[data-v-412cb6ea]:hover, .carousel__prev[data-v-412cb6ea]:ho=
ver { box-shadow: rgba(61, 70, 82, 0.35) 0px 0px 20px; transform: scale(1.1=
); }

.carousel__next .app-icon[data-v-412cb6ea], .carousel__prev .app-icon[data-=
v-412cb6ea] { color: rgb(255, 255, 255); display: flex; height: 1rem; width=
: 1rem; }

.carousel__next .app-icon[data-v-412cb6ea] svg, .carousel__prev .app-icon[d=
ata-v-412cb6ea] svg { width: 1rem; }

.carousel__next[data-v-412cb6ea] { left: inherit; right: 10%; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.comment-avatar[data-v-d8374c2e] { align-items: center; background: rgb(237=
, 237, 237); border-radius: 15px; color: rgb(255, 255, 255); display: flex;=
 height: 30px; justify-content: center; width: 30px; }

.comment-avatar--bot[data-v-d8374c2e] { color: transparent; background-colo=
r: transparent !important; background-image: url("data:image/svg+xml;charse=
t=3Dutf-8,%3Csvg xmlns=3D'http://www.w3.org/2000/svg' width=3D'35' height=
=3D'36' fill=3D'none' viewBox=3D'0 0 35 36'%3E%3Cpath fill=3D'%236A009C' d=
=3D'm23.106 9.564 1.417-2.834 1.417 2.834 2.834 1.417-2.834 1.417-1.417 2.8=
34-1.417-2.834-2.834-1.417zM11.77 15.232l2.834-5.668 2.834 5.668 5.668 2.83=
4-5.668 2.835-2.834 5.668L11.77 20.9l-5.668-2.835zm12.753 5.669 1.417 2.834=
 2.834 1.417-2.834 1.417-1.417 2.834-1.417-2.834-2.834-1.417 2.834-1.417z'/=
%3E%3C/svg%3E") !important; background-size: contain !important; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.comment[data-v-282eccf6] { background: rgb(255, 255, 255); box-shadow: rgb=
a(0, 0, 0, 0.1) 0px 0px 20px; margin: 1rem 0px; padding: 1rem; position: re=
lative; }

@media (min-width: 768px) {
  .comment[data-v-282eccf6] { border-radius: 1rem; padding: 2rem; }
}

.comment--answer[data-v-282eccf6] { background: rgb(233, 235, 240); border-=
radius: 0px; box-shadow: none; margin: 0px; }

.comment__answers[data-v-282eccf6] { border-top: 1px solid rgb(196, 196, 19=
6); margin-top: 1rem; }

.comment__answers > h5[data-v-282eccf6] { font-weight: 400; }

.comment__answers > .comment[data-v-282eccf6] { padding-bottom: 0px; paddin=
g-top: 1rem; }

.comment__answers > .comment[data-v-282eccf6]:first-of-type { border-top-le=
ft-radius: 0.5rem; border-top-right-radius: 0.5rem; padding-top: 2rem; }

.comment__answers > .comment[data-v-282eccf6]:last-of-type { border-bottom-=
left-radius: 0.5rem; border-bottom-right-radius: 0.5rem; padding-bottom: 2r=
em; }

.comment__answers > .comment:last-of-type .comment__footer[data-v-282eccf6]=
 { border-bottom: 0px; padding: 0px; }

.comment__footer[data-v-282eccf6] { align-items: center; display: flex; fle=
x-flow: wrap; }

.comment--answer .comment__footer[data-v-282eccf6] { border-bottom: 1px sol=
id rgb(196, 196, 196); padding-bottom: 2rem; }

.comment__cta[data-v-282eccf6] { cursor: pointer; display: flex; font-size:=
 0.875rem; margin-top: 1rem; min-width: 100%; position: relative; transitio=
n: 0.2s ease-in-out; }

.comment__cta .input[data-v-282eccf6] { align-items: center; background: rg=
b(255, 255, 255); border-radius: 3px 4px 4px 3px; color: rgb(173, 181, 189)=
; display: flex; justify-content: space-between; line-height: 1; outline: r=
gb(68, 131, 255) solid 1px; padding: 0px 0px 0px 1rem; width: 100%; }

.comment__cta button[data-v-282eccf6] { border-radius: 3px; font-weight: 40=
0; height: 100%; }

.comment__cta[data-v-282eccf6]:hover { opacity: 0.7; }

.comment__attachment[data-v-282eccf6] { aspect-ratio: 4 / 3; border-radius:=
 0.25rem; display: flex; margin-bottom: 1rem; max-width: 400px; overflow: h=
idden; position: relative; transition: 0.2s ease-in-out; width: 100%; }

.comment__attachment small[data-v-282eccf6] { background: rgba(0, 0, 0, 0.4=
); border-radius: 0.25rem; bottom: 30px; color: rgb(255, 255, 255); padding=
: 0.2rem 0.5rem; position: absolute; right: 30px; }

.comment__attachment small .app-icon[data-v-282eccf6] { color: rgb(255, 255=
, 255); margin-right: 0.5rem; }

.comment__attachment img[data-v-282eccf6] { background: rgb(237, 237, 237);=
 height: 100%; object-fit: cover; position: absolute; width: 100%; }

.comment__attachment[data-v-282eccf6]:hover { filter: brightness(0.9); }

.comment__header[data-v-282eccf6] { align-items: center; display: flex; gap=
: 10px; line-height: 1.1; padding-bottom: 0.5rem; width: 100%; }

.comment__header--bot strong[data-v-282eccf6] { color: rgb(106, 0, 156); }

.comment__header--bot .info[data-v-282eccf6] { align-items: center; backgro=
und-color: rgb(106, 0, 156); border-radius: 0.8rem; color: rgb(255, 255, 25=
5); display: inline-flex; font-size: 0.6rem; font-weight: lighter; height: =
0.8rem; justify-content: center; margin-left: 0.5rem; padding: 0px; width: =
0.8rem; }

.comment__body[data-v-282eccf6], .comment__body p[data-v-282eccf6] { overfl=
ow: hidden; white-space: pre-line; }

.comment__body > div > small[data-v-282eccf6] { display: block; font-style:=
 italic; padding-bottom: 1rem; }

.comment__footer button.sm[data-v-282eccf6] { border-radius: 3px; font-weig=
ht: 500; padding: 0.25rem 1rem; }

.comment__footer button.sm.like[data-v-282eccf6]:first-child { border-botto=
m-right-radius: 0px; border-right: 0px; border-top-right-radius: 0px; }

.comment__footer button.sm.like ~ .like[data-v-282eccf6] { border-bottom-le=
ft-radius: 0px; border-top-left-radius: 0px; }

.comment__footer button.sm[data-v-282eccf6]:disabled { background-color: rg=
b(68, 131, 255); color: rgb(255, 255, 255); opacity: 1; }

.comment__footer button.sm .app-icon[data-v-282eccf6] { transform: scale(1.=
2); transform-origin: 50% 100%; }

.comment__footer button.sm:hover .app-icon[data-v-282eccf6] { transform: sc=
ale(1.3); }

.comment .btn-link[data-v-282eccf6] { color: rgb(68, 131, 255); font-weight=
: 400; padding: 0px 1rem; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.comments[data-v-01df5956] { padding: 0px; }

@media (max-width: 767.98px) {
  .comments[data-v-01df5956] { padding: 0px !important; }
}

.comments[data-v-01df5956] div[data-freestar-ad] { border-bottom: 1px dotte=
d rgb(237, 237, 237); }

.comments .ad-placeholder[data-v-01df5956] { height: 50px; max-height: 50px=
; min-height: auto; }

@media (min-width: 768px) {
  .comments .ad-placeholder[data-v-01df5956] { height: 90px; max-height: 90=
px; }
}

.comments__hero[data-v-01df5956] { background-color: rgba(68, 131, 255, 0.2=
); margin-bottom: 2rem; padding: 1.5rem; text-align: center; }

.comments__hero h2[data-v-01df5956] { color: rgb(68, 131, 255); }

@media (min-width: 768px) {
  .comments__hero[data-v-01df5956] { border-radius: 1rem; }
}

.comments__hero ul[data-v-01df5956] { color: rgb(117, 131, 151); list-style=
: none; }

.comments__hero ul li[data-v-01df5956]::before { color: rgb(25, 135, 84); c=
ontent: "=E2=9C=93 "; }

.comments__hero p[data-v-01df5956] { margin: 0px auto 1rem; max-width: 320p=
x; }

.comments__hero input[data-v-01df5956] { border: 1px solid rgb(68, 131, 255=
); text-align: left !important; }

.comments button.block[data-v-01df5956], .comments input.block[data-v-01df5=
956] { display: flex; justify-content: center; margin: 0px auto; max-width:=
 300px; text-align: center; width: 100%; }

.illustration[data-v-01df5956] { margin: 0px auto; max-width: 300px; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.toc[data-v-ed7e345c] { color: rgb(61, 70, 82); font-size: 1rem; }

.toc[data-v-ed7e345c]::-webkit-scrollbar { appearance: none; width: 10px; }

.toc[data-v-ed7e345c]::-webkit-scrollbar-thumb { background-color: rgba(0, =
0, 0, 0.5); border-radius: 5px; box-shadow: rgba(255, 255, 255, 0.5) 0px 0p=
x 1px; }

.toc__container[data-v-ed7e345c] { background-color: rgb(255, 255, 255); bo=
rder-radius: 0.5rem; max-height: 400px; overflow: scroll; padding: 1rem; po=
sition: relative; }

.toc__container[data-v-ed7e345c] ul { list-style: none; padding: 0px; }

.toc__container[data-v-ed7e345c] ul li { padding: 0.1rem 0px 0.1rem 1.5rem;=
 position: relative; }

.toc__container[data-v-ed7e345c] ul li a { padding: 0.2rem 0px; }

.toc__container[data-v-ed7e345c] ul li.active { font-weight: 700; }

.toc__container[data-v-ed7e345c] ul .app-icon { cursor: pointer; margin: 0.=
2rem 0px 0px -1.5rem; position: absolute; transform: rotate(0deg); transiti=
on: 0.4s; }

.toc__container[data-v-ed7e345c] ul .app-icon:hover { color: rgb(105, 120, =
140); }

.toc__container[data-v-ed7e345c] ul .app-icon.open { transform: rotate(90de=
g); }

.toc__container[data-v-ed7e345c] ul a { display: flex; justify-content: spa=
ce-between; line-height: 1; }

.toc__container[data-v-ed7e345c] ul a::after { border-bottom: 1.5px solid r=
gb(224, 234, 244); content: ""; flex-grow: 4; margin: 0px 0.5rem; }

.toc__container[data-v-ed7e345c] ul a::before { background: rgb(255, 255, 2=
55); color: rgb(61, 70, 82); content: attr(data-page); padding-left: 0.5rem=
; position: absolute; right: 0px; text-align: right; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.file-switcher[data-v-4aa625cc] { background-color: rgb(255, 255, 255); bor=
der-radius: 0.25rem; display: flex; flex-flow: wrap; margin: 0px 15px; over=
flow: hidden; }

.file-switcher__item[data-v-4aa625cc] { clear: both; padding: 1rem; width: =
100%; }

@media (min-width: 768px) {
  .file-switcher__item[data-v-4aa625cc] { width: 50%; }
}

.file-switcher__item[data-v-4aa625cc] .smart-image { background-color: rgb(=
255, 255, 255); border: 1px solid rgb(196, 196, 196); border-radius: 0.25re=
m; float: left; height: 100%; margin-right: 1rem; max-height: 80px; positio=
n: relative; transition: 0.4s; width: 50px; }

.file-switcher__item[data-v-4aa625cc] .smart-image::after { background-colo=
r: rgb(255, 255, 255); border-bottom: 1px solid rgb(196, 196, 196); content=
: ""; height: 16px; position: absolute; right: -8px; top: -8px; transform: =
rotate(45deg); width: 16px; }

.file-switcher__item--active[data-v-4aa625cc], .file-switcher__item[data-v-=
4aa625cc]:hover { background-color: rgb(224, 234, 244); }

.file-switcher__item--active[data-v-4aa625cc] .smart-image::after, .file-sw=
itcher__item[data-v-4aa625cc]:hover .smart-image::after { background-color:=
 rgb(224, 234, 244); }

.file-switcher h4[data-v-4aa625cc] { margin-bottom: 0px; }

.file-switcher__lang[data-v-4aa625cc] { background-color: rgb(99, 150, 198)=
; border-radius: 0.25rem; color: rgb(255, 255, 255); display: block; float:=
 left; font-size: 0.875rem; font-weight: 700; margin: 0.25rem 0.25rem 0.25r=
em 0px; padding: 0.1rem 0.5rem; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.slider[data-v-b5a7cda6] { display: flex; flex-direction: column; position:=
 relative; width: 100%; }

.slider:hover .slider__label[data-v-b5a7cda6] { opacity: 1; }

.slider:hover .slider__track[data-v-b5a7cda6] { background-color: rgb(255, =
255, 255); height: 4px; }

.slider__label[data-v-b5a7cda6] { background-color: rgb(255, 255, 255); bor=
der-radius: 3px; box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 10px; color: rgb(6=
1, 70, 82); font-size: 12px; font-weight: 700; margin-left: 1em; min-width:=
 30px; opacity: 0; padding: 0.1rem 1rem; position: absolute; text-align: ce=
nter; top: -20px; transform: translate3d(-50%, -5px, 0px); transition: opac=
ity 0.2s; white-space: nowrap; }

.slider__label[data-v-b5a7cda6]::after { border-width: 5px; border-style: s=
olid; border-color: rgb(255, 255, 255) transparent transparent; border-imag=
e: initial; bottom: -10px; content: ""; height: 0px; left: 0px; margin: aut=
o; position: absolute; right: 0px; width: 0px; }

.slider__wrapper[data-v-b5a7cda6] { align-items: center; display: flex; pos=
ition: relative; }

.slider__track[data-v-b5a7cda6] { background: rgb(194, 194, 194); border-ra=
dius: 2px; height: 2px; position: absolute; transition: 0.2s ease-in-out; w=
idth: 100%; z-index: 0; }

.slider__input[data-v-b5a7cda6] { appearance: none; background: none; borde=
r: none; height: 30px; margin: 10px 0px; padding: 0px; position: relative; =
width: 100%; z-index: 1; }

.slider__input[data-v-b5a7cda6]:focus { outline: none; }

.slider__input[data-v-b5a7cda6]::-webkit-slider-runnable-track { cursor: po=
inter; height: 10px; width: 100%; background: transparent; border: none; bo=
rder-radius: 0px; }

.slider__input[data-v-b5a7cda6]::-webkit-slider-thumb { appearance: none; b=
ackground: rgb(255, 255, 255); border-radius: 50%; cursor: pointer; height:=
 1rem; margin-top: -3px; width: 1rem; }

.slider__input[data-v-b5a7cda6]:focus::-webkit-slider-runnable-track { back=
ground: transparent; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.viewer-toolbar[data-v-7feed808] { align-items: center; display: flex; flex=
-direction: column; }

.viewer-toolbar__main[data-v-7feed808] { color: rgb(255, 255, 255); display=
: flex; flex-direction: row; justify-content: center; overflow: hidden; }

.viewer-toolbar a[data-v-7feed808] { align-items: center; color: rgb(61, 70=
, 82); display: flex; font-size: 2rem; }

.viewer-toolbar__slider[data-v-7feed808] { align-items: center; display: fl=
ex; flex-direction: column; justify-content: center; max-width: min(550px, =
80vw); width: 100%; }

.viewer-toolbar__dropdown-container[data-v-7feed808] { position: absolute; =
}

.viewer-toolbar__dropdown[data-v-7feed808] { align-items: center; display: =
flex; justify-content: center; width: 130px; }

.viewer-toolbar__dropdown .dropdown[data-v-7feed808] { background-color: rg=
b(52, 58, 64); border: 0px; color: rgb(255, 255, 255); filter: drop-shadow(=
rgba(0, 0, 0, 0.15) 0px 2px 10px); list-style: none; max-height: 400px; ove=
rflow-y: auto; padding: 0px; top: 40px; }

.viewer-toolbar__dropdown .dropdown li[data-v-7feed808] { padding: 0.1rem 1=
rem; }

.viewer-toolbar__dropdown .dropdown li[data-v-7feed808]:hover { background-=
color: rgb(73, 80, 87); }

.viewer-toolbar__dropdown .btn[data-v-7feed808] { background: rgb(33, 37, 4=
1); color: rgb(255, 255, 255); font-weight: 400; padding: 0.05rem 0.75rem; =
}

.viewer-toolbar__dropdown .btn span[data-v-7feed808] { opacity: 0.7; }

.viewer-toolbar__arrow[data-v-7feed808] { border-radius: 2rem; padding: 1re=
m; transform: scale(1); transition: background-color 0.2s ease-in-out; }

.viewer-toolbar__arrow .app-icon[data-v-7feed808] { color: rgb(255, 255, 25=
5); }

.viewer-toolbar__arrow[data-v-7feed808]:hover { background-color: rgba(0, 0=
, 0, 0.1); }

.viewer-toolbar__arrow[data-v-7feed808]:active { transform: scale(0.8); }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.zoom[data-v-ac8a74d0] { height: 100%; left: 0px; pointer-events: none; pos=
ition: absolute; top: 0px; width: 100%; }

.zoom__overlay[data-v-ac8a74d0] { backface-visibility: hidden; border: 2px =
solid rgb(222, 226, 230); border-radius: 50%; box-shadow: rgba(0, 0, 0, 0.3=
) 0px 0px 0px 10px; height: 300px; margin: -150px 0px 0px -150px; overflow:=
 hidden; position: absolute; width: 300px; z-index: 1000; }

.zoom__overlay-content[data-v-ac8a74d0] { position: absolute; transform-ori=
gin: 0px 0px; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.fallback[data-v-dd20de80] { padding-top: 200%; }

.loading[data-v-dd20de80] { align-items: center; background: rgba(255, 255,=
 255, 0.7); height: 100%; position: absolute; width: 100%; z-index: 100; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.viewer-page .h1, .viewer-page .h2, .viewer-page .h3, .viewer-page .h4, .vi=
ewer-page .h5, .viewer-page .h6, .viewer-page h1, .viewer-page h2, .viewer-=
page h3, .viewer-page h4, .viewer-page h5, .viewer-page h6 { margin-bottom:=
 auto; }

.viewer-page figure { margin: 0px; }

#sidebar { bottom: 0px; overflow: auto; width: 250px; }

#page-container, #sidebar { left: 0px; margin: 0px; padding: 0px; position:=
 absolute; top: 0px; }

#page-container { border: 0px; }

@media screen {
  #sidebar.opened + #page-container { left: 250px; }
  #page-container { bottom: 0px; overflow: auto; right: 0px; }
  .loading-indicator { display: none; }
  .loading-indicator.active { display: block; height: 64px; left: 50%; marg=
in-left: -32px; margin-top: -32px; position: absolute; top: 50%; width: 64p=
x; }
  .loading-indicator img { inset: 0px; position: absolute; }
}

@media print {
  @page { margin: 0px; }
  body, html { margin: 0px; }
  body { -webkit-print-color-adjust: exact; }
  #sidebar { display: none; }
  #page-container { background-color: transparent; height: auto; overflow: =
visible; width: auto; }
  .d { display: none; }
}

.pf { background-color: rgb(255, 255, 255); position: relative; }

.pc, .pf { border: 0px; margin: 0px; overflow: hidden; }

.pc { height: 100%; left: 0px; padding: 0px; position: absolute; top: 0px; =
transform-origin: 0px 0px; width: 100%; }

.pc, .pc.opened { display: block; }

.bf { bottom: 0px; height: 100%; top: 0px; width: 100%; }

.bf, .bi { border: 0px; margin: 0px; position: absolute; user-select: none;=
 }

@media print {
  .pf { box-shadow: none; margin: 0px; break-after: page; break-inside: avo=
id; }
}

.c { border: 0px; display: block; margin: 0px; overflow: hidden; padding: 0=
px; }

.c, .t { position: absolute; }

.t { font-size: 1px; transform-origin: 0px 100%; unicode-bidi: bidi-overrid=
e; white-space: pre; }

.t::after { content: ""; }

.t span { display: inline-block; position: relative; unicode-bidi: bidi-ove=
rride; }

._ { color: transparent; z-index: -1; }

.pi { display: none; }

.d { position: absolute; transform-origin: 0px 100%; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

#sidebar { bottom: 0px; overflow: auto; width: 250px; }

#page-container, #sidebar { left: 0px; margin: 0px; padding: 0px; position:=
 absolute; top: 0px; }

#page-container { border: 0px; padding: 0px !important; }

@media screen {
  #sidebar.opened + #page-container { left: 250px; }
  #page-container { bottom: 0px; overflow: auto; right: 0px; }
  .loading-indicator { display: none; }
  .loading-indicator.active { background: url("/images/loading.gif"); displ=
ay: block; height: 32px; left: 50%; margin-left: -32px; margin-top: -32px; =
position: absolute; top: 50%; width: 32px; }
  .loading-indicator img { inset: 0px; position: absolute; }
}

@media print {
  @page { margin: 0px; }
  body, html { margin: 0px; }
  body { -webkit-print-color-adjust: exact; }
  #sidebar { display: none; }
  #page-container { background-color: transparent; height: auto; overflow: =
visible; width: auto; }
  .d { display: none; }
}

.pf { background-color: rgb(255, 255, 255); position: relative; }

.pc, .pf { border: 0px; margin: 0px; overflow: hidden; }

.pc { height: 100%; left: 0px; padding: 0px; position: absolute; top: 0px; =
transform-origin: 0px 0px; width: 100%; }

.pc, .pc.opened { display: block; }

.bf { bottom: 0px; height: 100%; top: 0px; width: 100%; }

.bf, .bi { border: 0px; margin: 0px; position: absolute; user-select: none;=
 }

@media print {
  .pf { box-shadow: none; margin: 0px; break-after: page; break-inside: avo=
id; }
}

.c { border: 0px; display: block; margin: 0px; overflow: hidden; padding: 0=
px; }

.c, .t { position: absolute; }

.t { font-size: 1px; transform-origin: 0px 100%; unicode-bidi: bidi-overrid=
e; white-space: pre; }

._ { color: transparent; z-index: -1; }

.pi { display: none; }

.d { position: absolute; transform-origin: 0px 100%; }

.tabs { align-items: baseline; display: flex; margin-bottom: -0.8rem; margi=
n-top: 0.5rem; overflow-x: scroll; }

.tabs .app-icon { margin-right: 0.3rem; transform: translateY(-2px); }

.tabs button { background-color: rgb(43, 47, 53); color: rgb(255, 255, 255)=
; font-size: 0.875rem; font-weight: 400; margin-bottom: 0px; margin-left: 0=
.3rem; padding: 0.2rem 0.5rem; text-transform: capitalize; transition: back=
ground-color 0.2s; }

.tabs button:first-child { background-color: rgb(61, 70, 82); border-bottom=
-left-radius: 0px; border-bottom-right-radius: 0px; padding-bottom: 0.8rem;=
 }

@media (min-width: 768px) {
  .tabs button:first-child { margin-left: 1rem; }
}

.tabs button:hover { background-color: rgb(61, 70, 82); }

.glide__track { padding-bottom: 10px; }

.glide__arrow--left, .glide__arrow--right { align-items: center; bottom: 10=
px; display: flex; font-size: 1rem; justify-content: center; opacity: 1; po=
sition: absolute; top: 10px; transform: scale(1); transition: background-co=
lor 0.2s ease-in-out; width: 80px; z-index: 10; }

.glide__arrow--left:hover, .glide__arrow--right:hover { background-color: r=
gba(0, 0, 0, 0.1); }

.glide__arrow--left:active .app-icon, .glide__arrow--right:active .app-icon=
 { transform: scale(0.8); }

.glide__arrow--left { left: 10px; }

.glide__arrow--right { right: 10px; }

.glide__arrow .app-icon { backdrop-filter: blur(3px); background: rgba(0, 0=
, 0, 0.2); border: 1px solid rgba(0, 0, 0, 0.3); border-radius: 50%; bottom=
: 50%; color: rgb(255, 255, 255); content: ""; display: flex; height: 2.5re=
m; margin: 30px 0px; position: sticky; top: 20px; width: 2.5rem; z-index: -=
1; }

@media (min-width: 768px) {
  .viewer--in-view .glide__arrow .app-icon { animation: 1.5s ease 0s 3 norm=
al none running blink-desktop; }
}

.viewer { background-color: rgb(61, 70, 82); direction: ltr; padding-top: 0=
.5rem; position: relative; }

@media (min-width: 768px) {
  .viewer { border-radius: 0.5rem; margin: 0px 0.5rem; padding: 0.5rem 0.5r=
em 0px; width: calc(100% - 1rem); }
}

.viewer__toolbar-top { bottom: 0px; display: flex; position: absolute; righ=
t: 0.5rem; }

.viewer__toolbar-top button { color: rgb(255, 255, 255); transition: backgr=
ound-color 0.3s ease-in-out; }

.viewer__toolbar-top button .app-icon { height: 1.4rem; width: 1.4rem; }

.viewer__toolbar-top button:hover { background-color: rgba(0, 0, 0, 0.2); }

.viewer__title { color: rgb(255, 255, 255); font-size: 1rem; margin: 0px; p=
adding: 0.5rem 3rem 0.5rem 0.5rem; }

.viewer-transition { display: flex; flex-direction: row; position: relative=
; }

.viewer-transition > div { width: 100vw; }

.js-only .viewer-transition > div { position: absolute; top: 0px; }

@media (max-width: 767.98px) {
  .viewer--in-view .viewer-container { animation: 1s ease 0s 3 normal none =
running blink; }
}

.viewer-container { margin: 10px; max-width: 100%; }

.viewer-container:not(.active) { display: none; }

.mounted .viewer-container { display: list-item; }

.viewer-page { background: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.=
9) 0px 0px 5px; min-height: 80px; position: relative; }

@media (max-width: 768px) {
  .viewer-page::after, .viewer-page::before { background: rgb(255, 255, 255=
); box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 5px; content: ""; height: 100%; p=
osition: absolute; top: 0px; transform: translate(calc(-100% - 5px)); width=
: 100%; }
  .viewer-page::after { transform: translate(calc(100% + 5px)); }
}

.viewer .pf { transform-origin: 0px 0px; }

.slide-left-enter-active, .slide-left-leave-active, .slide-right-enter-acti=
ve, .slide-right-leave-active { transition: 0.4s; }

@media (max-width: 767.98px) {
  .slide-left-enter { transform: translate(100%) !important; }
  .slide-left-leave-to, .slide-right-enter { transform: translate(-100%) !i=
mportant; }
  .slide-right-leave-to { transform: translate(100%) !important; }
}

@media (min-width: 768px) {
  .slide-left-enter { opacity: 1; }
  .slide-left-leave-to { opacity: 0; }
  .slide-right-enter { opacity: 1; }
  .slide-right-leave-to { opacity: 0; }
}

@keyframes blink {=20
  0% { transform: translate(0px); }
  30% { transform: translate(0px); }
  60% { transform: translate(-20px); }
}

@keyframes blink-desktop {=20
  0% { transform: scale(1); }
  80% { transform: scale(1); }
  90% { transform: scale(1.2); }
}
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.clearfix[data-v-0eadb2a6] { clear: both; }

.col-xl-2.sticky-side[data-v-0eadb2a6] { flex-grow: 1; order: 3; }

.col-xl-2.sticky-side[data-v-0eadb2a6]:first-child { order: 1; }

.col-xl-2.sticky-side + .col-xl-8[data-v-0eadb2a6] { max-width: 1000px; ord=
er: 1; }

.sticky-top[data-v-0eadb2a6] { position: sticky; top: 0px; z-index: auto; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.ad-placeholder[data-v-5e860da6] { clear: both; height: 83.2vw; margin-left=
: auto; margin-right: auto; overflow: hidden; text-align: center; width: 10=
0%; }

.ad-placeholder.ad_debug[data-v-5e860da6] { background: rgb(237, 237, 237);=
 border: 1px solid rgb(194, 194, 194); }

.ad-placeholder[data-v-5e860da6] ins.adsbygoogle[data-ad-status=3D"unfilled=
"] { border: 1px solid rgb(237, 237, 237); }

.ad-placeholder.in-feed[data-v-5e860da6] { min-height: 100px; }

.ad-placeholder__debug[data-v-5e860da6] { background: rgb(231, 159, 159); b=
order: 1px solid rgb(194, 194, 194); font-size: 11px; opacity: 0.85; paddin=
g: 0.5rem; position: absolute; text-align: left; z-index: 1000; height: 200=
px !important; width: 200px !important; }

.ad-placeholder__debug pre[data-v-5e860da6] { white-space: pre-wrap; }

@media (min-width: 768px) {
  .ad-placeholder[data-v-5e860da6] { background: transparent; height: 280px=
; }
}

@media (max-width: 768px) {
  .ad-placeholder[data-v-5e860da6] { margin-left: calc(50% - 50vw); width: =
100vw; }
  .ad-placeholder.desktopOnly[data-v-5e860da6] { min-height: 0px; }
}
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.rating[data-v-ff31764a] { align-items: center; display: flex; flex-grow: 0=
; flex-wrap: wrap; font-size: 0.8rem; }

.rating__cta[data-v-ff31764a], .rating__stats[data-v-ff31764a] { font-size:=
 0.875rem; font-weight: 600; margin-right: 1rem; text-transform: capitalize=
; white-space: nowrap; }

.rating .text-muted[data-v-ff31764a] { color: rgb(173, 181, 189); }

.rating__stars[data-v-ff31764a] { align-self: baseline; background: rgb(173=
, 181, 189); cursor: pointer; display: inline-flex; flex: 0 1 0px; height: =
1rem; margin-right: 0.5rem; mask-image: url("data:image/svg+xml;utf8,<svg w=
idth=3D\"100\" height=3D\"100\" viewBox=3D\"0 0 100 100\" fill=3D\"black\" =
xmlns=3D\"http://www.w3.org/2000/svg\"> <path d=3D\"M26.04 84.0234C27.8174 =
85.3906 29.9707 84.9463 32.4316 83.1689L50 70.2832L67.5342 83.1689C69.9951 =
84.9463 72.1484 85.3906 73.9258 84.0234C75.6689 82.7246 76.0107 80.5371 75.=
0537 77.7002L68.1152 57.0898L85.8203 44.4092C88.2812 42.666 89.3066 40.6836=
 88.623 38.5986C87.9053 36.5479 85.957 35.5225 82.915 35.5566L61.2451 35.72=
75L54.6143 14.9805C53.6914 12.0752 52.1875 10.5371 50 10.5371C47.8125 10.53=
71 46.2744 12.0752 45.3516 14.9805L38.7549 35.7275L17.0166 35.5566C14.043 3=
5.5225 12.0605 36.5479 11.3428 38.5986C10.625 40.6836 11.7188 42.666 14.145=
5 44.4092L31.8506 57.0898L24.9121 77.7002C23.9551 80.5371 24.2969 82.7246 2=
6.04 84.0234Z\"></path> </svg>"); mask-repeat: repeat-x; mask-size: 1rem 1r=
em; min-width: 5rem; position: relative; white-space: nowrap; width: 5rem; =
}

.rating__stars-hover[data-v-ff31764a] { background: rgb(245, 194, 0); heigh=
t: 100%; overflow: hidden; position: absolute; top: 0px; }

.rating__tooltip[data-v-ff31764a] { font-size: 1rem; }

.rating__tooltip .rating__stars[data-v-ff31764a] { height: 3rem; mask-size:=
 3rem 3rem; min-width: 15rem; width: 15rem; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.ad[data-v-a411a370] { margin: 1rem 0px; min-height: 100px; text-align: cen=
ter; }

.ad.type-display[data-v-a411a370]::before, .ad.type-link[data-v-a411a370]::=
before { color: rgb(194, 194, 194); content: "advertisement"; display: bloc=
k; font-size: 0.8rem; margin-top: -1.1rem; }

.debug[data-v-a411a370] { background: rgb(255, 255, 255); border: 1px solid=
 rgb(194, 194, 194); opacity: 0.95; position: absolute; text-align: left; z=
-index: 1000; height: 100px !important; width: 300px !important; }

.debug pre[data-v-a411a370] { white-space: pre-line; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

div[data-v-1460e71d] { position: relative; }

div picture[data-v-1460e71d], div picture img[data-v-1460e71d] { height: 10=
0%; max-height: 100%; max-width: 100%; width: 100%; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.navigation[data-v-95f55862] { background-color: rgb(255, 255, 255); box-sh=
adow: rgba(0, 0, 0, 0.2) 0px 0px 4px; position: relative; z-index: 1; }

.navigation--inverted[data-v-95f55862] { background-color: rgba(255, 255, 2=
55, 0.25); width: 100%; }

.navigation .container[data-v-95f55862] { align-items: center; display: fle=
x; flex-flow: row; height: 45px; justify-content: space-between; max-width:=
 1000px; }

.navigation--inverted .navigation__search[data-v-95f55862] .app-icon { colo=
r: rgb(255, 255, 255); }

.navigation__logo[data-v-95f55862] { color: rgb(99, 150, 198); font-weight:=
 700; letter-spacing: 0.05rem; max-width: calc(-60px + 100vw); overflow: hi=
dden; text-decoration: none; text-overflow: ellipsis; transition: color 0.3=
s; }

.navigation__logo[data-v-95f55862]:focus, .navigation__logo[data-v-95f55862=
]:hover { color: rgb(66, 125, 180); }

.navigation__logo[data-v-95f55862] span { color: rgb(243, 91, 58); }

.navigation--inverted .navigation__logo[data-v-95f55862], .navigation--inve=
rted .navigation__logo[data-v-95f55862] span { color: rgb(255, 255, 255); }

.dropdown[data-v-95f55862] { z-index: 1040; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.modal[data-v-740f5b8e] { background: rgb(255, 255, 255); position: fixed; =
top: 100px; z-index: 1040; }

.modal__backdrop[data-v-740f5b8e] { background: rgba(0, 0, 0, 0.3); content=
: ""; height: 100vh; left: 0px; position: fixed; top: 0px; width: 100vw; z-=
index: -1040; }

.modal__container[data-v-740f5b8e] { background: rgb(245, 246, 246); border=
: 1px solid rgb(222, 226, 230); border-radius: 0.5rem; left: auto; margin: =
0px auto; max-width: 600px; position: relative; right: auto; transform: tra=
nslateY(calc(-50% + 50vh)); }

@media (max-width: 768px) {
  .modal__container[data-v-740f5b8e] { max-width: 95vw; }
  .modal--full-screen .modal__container[data-v-740f5b8e] { border-radius: 0=
px; height: 100vh; max-width: 100vw; width: 100vw; }
}

.modal__header[data-v-740f5b8e] { background: rgb(255, 255, 255); border-ra=
dius: 0.5rem; min-height: 3rem; padding: 0.75rem 1rem; }

.modal__content[data-v-740f5b8e] { max-height: 80vh; overflow: scroll; padd=
ing: 1rem; position: relative; }

.modal__close[data-v-740f5b8e] { position: fixed; right: 0px; top: 0px; z-i=
ndex: 10; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.lang-switcher__modal[data-v-9b698a5b] .modal__container { max-width: 300px=
; }

.lang-switcher__label[data-v-9b698a5b] { background-color: rgb(255, 255, 25=
5); border-radius: 3px; margin-right: 1rem; padding: 0.3rem 1rem; }

.lang-switcher__label[data-v-9b698a5b]:hover { background: rgb(255, 255, 25=
5); color: rgb(243, 91, 58); }

.lang-switcher__list[data-v-9b698a5b] { text-align: left; }

.lang-switcher__list a[data-v-9b698a5b] { color: rgb(61, 70, 82); }

.lang-switcher__list li[data-v-9b698a5b] { padding: 0.3rem; }

.lang-switcher__list li[data-v-9b698a5b]:hover { background: rgb(224, 234, =
244); }

.lang-switcher__list .app-icon[data-v-9b698a5b] { background-color: rgb(255=
, 255, 255); border-radius: 2px; filter: drop-shadow(rgba(0, 0, 0, 0.15) 0p=
x 2px 10px); height: 1.4rem; margin-right: 0.5rem; overflow: hidden; width:=
 1.8rem; }

.lang-switcher .badge[data-v-9b698a5b] { background: rgb(222, 226, 230); bo=
rder-radius: 3px; display: inline-block; padding: 0.3rem 0.5rem; text-align=
: center; width: 4rem; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.footer[data-v-a1a4be57] { background: rgb(99, 150, 198); color: rgb(255, 2=
55, 255); font-size: 0.875rem; }

.footer .lang-switcher[data-v-a1a4be57] { align-items: baseline; display: f=
lex; justify-content: center; margin-bottom: 0.5rem; }

.footer__links[data-v-a1a4be57] { padding: 1rem 0px 2rem; }

.footer a[data-v-a1a4be57], .footer p[data-v-a1a4be57] { color: rgb(255, 25=
5, 255) !important; opacity: 0.7; }

.footer h4[data-v-a1a4be57] { color: rgb(255, 255, 255); }

.footer h4 span[data-v-a1a4be57] { color: rgb(68, 131, 255); }

@media (max-width: 768px) {
  .footer .bv-d-sm-down-none[data-v-a1a4be57] { display: none; }
}
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.manual__description[data-v-8e81d043] { white-space: break-spaces; }

.manual[data-v-8e81d043] .col-xl-8 { padding: 0px; }

@media (max-width: 767.98px) {
  .manual .top-desktop[data-v-8e81d043] { display: none; }
}

@media (min-width: 768px) {
  .manual .top-mobile[data-v-8e81d043] { display: none; }
}

.manual__navigation[data-v-8e81d043] { background: rgb(255, 255, 255); }

.manual__navigation .navigation[data-v-8e81d043] { box-shadow: rgba(0, 0, 0=
, 0.05) 5px 10px 10px; }

.manual__navigation-sub[data-v-8e81d043] { align-items: center; display: fl=
ex; font-weight: 600; height: 3rem; margin: 0px auto 1rem; max-width: 1000p=
x; padding: 0px 15px; }

.manual__navigation-sub--invert .smart-image[data-v-8e81d043] { filter: bri=
ghtness(0) invert(1); }

.manual__navigation-sub .smart-image[data-v-8e81d043] { height: 2rem; margi=
n-left: 1rem; max-width: 5rem; }

.manual__navigation-sub a[data-v-8e81d043] { align-items: center; border-ra=
dius: 0.5rem; color: rgb(99, 150, 198); display: flex; height: 2rem; justif=
y-content: center; margin-right: 0.5rem; padding-right: 0.5rem; }

.manual__navigation-sub a[data-v-8e81d043]:hover { backdrop-filter: brightn=
ess(0.9); }

.manual__navigation-sub--background[data-v-8e81d043], .manual__navigation-s=
ub--background a[data-v-8e81d043] { color: rgb(255, 255, 255); }

.manual__header[data-v-8e81d043] { display: flex; flex-flow: wrap; height: =
auto; line-height: 1.35; margin-bottom: 0.5rem; max-width: 1000px; }

@media (min-width: 768px) {
  .manual__header[data-v-8e81d043] { height: 90px; padding-left: 120px; pos=
ition: relative; }
}

.manual__rating[data-v-8e81d043] .rating__stars-hover svg { color: rgb(73, =
80, 87); }

.manual__rating[data-v-8e81d043] .link { color: rgb(73, 80, 87); }

.manual__title[data-v-8e81d043] { flex-shrink: 0; font-size: 1.25rem; margi=
n: 0.5rem 0px; width: 100%; }

@media (min-width: 768px) {
  .manual__title[data-v-8e81d043] { font-size: 1.5rem; margin: 0px; }
  .manual__title[data-v-8e81d043] span { color: rgb(61, 70, 82); display: b=
lock; font-size: 2.5rem; text-transform: capitalize; }
}

.manual__subtitle[data-v-8e81d043] { align-items: center; display: flex; fl=
ex-wrap: wrap; font-size: 0.875rem; font-weight: 600; }

.manual__subtitle .expand[data-v-8e81d043] { display: flex; flex-shrink: 0;=
 }

.manual__subtitle span[data-v-8e81d043] { color: rgb(73, 80, 87); white-spa=
ce: nowrap; }

.manual__subtitle > span[data-v-8e81d043]:first-child { align-items: center=
; display: flex; }

.manual__subtitle > span[data-v-8e81d043]:nth-child(2) { margin-right: 1rem=
; }

.manual__subtitle > span[data-v-8e81d043]:last-child { width: 100%; }

@media (min-width: 768px) {
  .manual__subtitle > span[data-v-8e81d043]:last-child { width: auto; }
}

.manual__info[data-v-8e81d043] { display: flex; flex-direction: column; fle=
x-grow: 2; gap: 8px; justify-content: center; width: 60%; }

.manual__info .flag[data-v-8e81d043]:not(.globe) { background-color: rgb(25=
5, 255, 255); border-radius: 2px; filter: drop-shadow(rgba(0, 0, 0, 0.15) 0=
px 2px 10px); height: 1rem; overflow: hidden; width: 1.3rem; }

.manual__main-image[data-v-8e81d043] { background-color: rgb(255, 255, 255)=
; border-radius: 0.5rem; box-shadow: rgba(61, 70, 82, 0.1) 0px 4px 20px; di=
splay: flex; float: left; height: 73px; margin: 0px 0.5rem 0px 0px; overflo=
w: hidden; padding: 0.3rem; width: 73px; }

.manual__main-image[data-v-8e81d043] img { border-radius: 0.25rem; }

@media (min-width: 768px) {
  .manual__main-image[data-v-8e81d043] { height: 90px; left: 1rem; position=
: absolute; top: 0px; width: 90px; }
}

.grid[data-v-8e81d043] > :not([class^=3D"ad"]) { margin: 0px auto 3rem; max=
-width: 758px; padding-left: 15px; padding-right: 15px; }

.grid[data-v-8e81d043] > .affiliate-products, .grid[data-v-8e81d043] > .rel=
ated-products { max-width: 1000px; }

.ad_test[data-v-8e81d043] { display: flex; }

.ad_test[data-v-8e81d043] > div, .ad_test[data-v-8e81d043] > div > ins { di=
splay: flex; width: 100%; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.resize-observer[data-v-b329ee4c] { background-color: transparent; border: =
none; opacity: 0; }

.resize-observer[data-v-b329ee4c], .resize-observer[data-v-b329ee4c] object=
 { display: block; height: 100%; left: 0px; overflow: hidden; pointer-event=
s: none; position: absolute; top: 0px; width: 100%; z-index: -1; }

.v-popper__popper { left: 0px; outline: none; top: 0px; z-index: 10000; }

.v-popper__popper.v-popper__popper--hidden { opacity: 0; pointer-events: no=
ne; transition: opacity 0.15s, visibility 0.15s; visibility: hidden; }

.v-popper__popper.v-popper__popper--shown { opacity: 1; transition: opacity=
 0.15s; visibility: visible; }

.v-popper__popper.v-popper__popper--skip-transition, .v-popper__popper.v-po=
pper__popper--skip-transition > .v-popper__wrapper { transition: none !impo=
rtant; }

.v-popper__backdrop { display: none; height: 100%; left: 0px; position: abs=
olute; top: 0px; width: 100%; }

.v-popper__inner { box-sizing: border-box; overflow-y: auto; position: rela=
tive; }

.v-popper__inner > div { max-height: inherit; max-width: inherit; position:=
 relative; z-index: 1; }

.v-popper__arrow-container { height: 10px; position: absolute; width: 10px;=
 }

.v-popper__popper--arrow-overflow .v-popper__arrow-container, .v-popper__po=
pper--no-positioning .v-popper__arrow-container { display: none; }

.v-popper__arrow-inner, .v-popper__arrow-outer { border-style: solid; heigh=
t: 0px; left: 0px; position: absolute; top: 0px; width: 0px; }

.v-popper__arrow-inner { border-width: 7px; visibility: hidden; }

.v-popper__arrow-outer { border-width: 6px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-inner=
, .v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-inner =
{ left: -2px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-outer=
, .v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-outer =
{ left: -1px; }

.v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-inner, .=
v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-outer { b=
order-bottom-width: 0px; border-bottom-color: transparent !important; borde=
r-left-color: transparent !important; border-right-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"top"] .v-popper__arrow-inner { =
top: -2px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-conta=
iner { top: 0px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-inner=
, .v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-out=
er { border-top-width: 0px; border-left-color: transparent !important; bord=
er-right-color: transparent !important; border-top-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-inner=
 { top: -4px; }

.v-popper__popper[data-popper-placement^=3D"bottom"] .v-popper__arrow-outer=
 { top: -6px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-inner, =
.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-inner =
{ top: -2px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-outer, =
.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-outer =
{ top: -1px; }

.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-inner,=
 .v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-outer=
 { border-left-width: 0px; border-bottom-color: transparent !important; bor=
der-left-color: transparent !important; border-top-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-inner =
{ left: -4px; }

.v-popper__popper[data-popper-placement^=3D"right"] .v-popper__arrow-outer =
{ left: -6px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-contain=
er { right: -10px; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-inner, =
.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-outer {=
 border-right-width: 0px; border-bottom-color: transparent !important; bord=
er-right-color: transparent !important; border-top-color: transparent !impo=
rtant; }

.v-popper__popper[data-popper-placement^=3D"left"] .v-popper__arrow-inner {=
 left: -2px; }

.v-popper--theme-tooltip .v-popper__inner { background: rgba(0, 0, 0, 0.8);=
 border-radius: 6px; color: rgb(255, 255, 255); padding: 7px 12px 6px; }

.v-popper--theme-tooltip .v-popper__arrow-outer { border-color: rgba(0, 0, =
0, 0.8); }

.v-popper--theme-dropdown .v-popper__inner { background: rgb(255, 255, 255)=
; border: 1px solid rgb(221, 221, 221); border-radius: 6px; box-shadow: rgb=
a(0, 0, 0, 0.1) 0px 6px 30px; color: rgb(0, 0, 0); }

.v-popper--theme-dropdown .v-popper__arrow-inner { border-color: rgb(255, 2=
55, 255); visibility: visible; }

.v-popper--theme-dropdown .v-popper__arrow-outer { border-color: rgb(221, 2=
21, 221); }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.app-icon { box-sizing: border-box; display: inline-flex; height: 1em; outl=
ine: none; position: relative; vertical-align: middle; width: 1em; }

.app-icon svg { fill: currentcolor; height: 100%; width: 100%; }

.app-icon.size-lg { height: 1.5rem; width: 1.5rem; }

.app-icon.size-xl { height: 2rem; width: 2rem; }

.app-icon.size-xxl { height: 3rem; width: 3rem; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.row { --bs-gutter-x: 1.5rem; --bs-gutter-y: 0; display: flex; flex-wrap: w=
rap; margin-left: calc(var(--bs-gutter-x)*-.5); margin-right: calc(var(--bs=
-gutter-x)*-.5); margin-top: calc(var(--bs-gutter-y)*-1); }

.row > * { flex-shrink: 0; margin-top: var(--bs-gutter-y); max-width: 100%;=
 padding-left: calc(var(--bs-gutter-x)*.5); padding-right: calc(var(--bs-gu=
tter-x)*.5); width: 100%; }

.col { flex: 1 0 0%; }

.row-cols-auto > * { flex: 0 0 auto; width: auto; }

.row-cols-1 > * { flex: 0 0 auto; width: 100%; }

.row-cols-2 > * { flex: 0 0 auto; width: 50%; }

.row-cols-3 > * { flex: 0 0 auto; width: 33.3333%; }

.row-cols-4 > * { flex: 0 0 auto; width: 25%; }

.row-cols-5 > * { flex: 0 0 auto; width: 20%; }

.row-cols-6 > * { flex: 0 0 auto; width: 16.6667%; }

.col-auto { flex: 0 0 auto; width: auto; }

.col-1 { flex: 0 0 auto; width: 8.33333%; }

.col-2 { flex: 0 0 auto; width: 16.6667%; }

.col-3 { flex: 0 0 auto; width: 25%; }

.col-4 { flex: 0 0 auto; width: 33.3333%; }

.col-5 { flex: 0 0 auto; width: 41.6667%; }

.col-6 { flex: 0 0 auto; width: 50%; }

.col-7 { flex: 0 0 auto; width: 58.3333%; }

.col-8 { flex: 0 0 auto; width: 66.6667%; }

.col-9 { flex: 0 0 auto; width: 75%; }

.col-10 { flex: 0 0 auto; width: 83.3333%; }

.col-11 { flex: 0 0 auto; width: 91.6667%; }

.col-12 { flex: 0 0 auto; width: 100%; }

.offset-1 { margin-left: 8.33333%; }

.offset-2 { margin-left: 16.6667%; }

.offset-3 { margin-left: 25%; }

.offset-4 { margin-left: 33.3333%; }

.offset-5 { margin-left: 41.6667%; }

.offset-6 { margin-left: 50%; }

.offset-7 { margin-left: 58.3333%; }

.offset-8 { margin-left: 66.6667%; }

.offset-9 { margin-left: 75%; }

.offset-10 { margin-left: 83.3333%; }

.offset-11 { margin-left: 91.6667%; }

.g-0, .gx-0 { --bs-gutter-x: 0; }

.g-0, .gy-0 { --bs-gutter-y: 0; }

.g-1, .gx-1 { --bs-gutter-x: .25rem; }

.g-1, .gy-1 { --bs-gutter-y: .25rem; }

.g-2, .gx-2 { --bs-gutter-x: .5rem; }

.g-2, .gy-2 { --bs-gutter-y: .5rem; }

.g-3, .gx-3 { --bs-gutter-x: 1rem; }

.g-3, .gy-3 { --bs-gutter-y: 1rem; }

.g-4, .gx-4 { --bs-gutter-x: 1.5rem; }

.g-4, .gy-4 { --bs-gutter-y: 1.5rem; }

.g-5, .gx-5 { --bs-gutter-x: 3rem; }

.g-5, .gy-5 { --bs-gutter-y: 3rem; }

@media (min-width: 576px) {
  .col-sm { flex: 1 0 0%; }
  .row-cols-sm-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-sm-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-sm-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-sm-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-sm-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-sm-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-sm-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-sm-auto { flex: 0 0 auto; width: auto; }
  .col-sm-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-sm-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-sm-3 { flex: 0 0 auto; width: 25%; }
  .col-sm-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-sm-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-sm-6 { flex: 0 0 auto; width: 50%; }
  .col-sm-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-sm-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-sm-9 { flex: 0 0 auto; width: 75%; }
  .col-sm-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-sm-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-sm-12 { flex: 0 0 auto; width: 100%; }
  .offset-sm-0 { margin-left: 0px; }
  .offset-sm-1 { margin-left: 8.33333%; }
  .offset-sm-2 { margin-left: 16.6667%; }
  .offset-sm-3 { margin-left: 25%; }
  .offset-sm-4 { margin-left: 33.3333%; }
  .offset-sm-5 { margin-left: 41.6667%; }
  .offset-sm-6 { margin-left: 50%; }
  .offset-sm-7 { margin-left: 58.3333%; }
  .offset-sm-8 { margin-left: 66.6667%; }
  .offset-sm-9 { margin-left: 75%; }
  .offset-sm-10 { margin-left: 83.3333%; }
  .offset-sm-11 { margin-left: 91.6667%; }
  .g-sm-0, .gx-sm-0 { --bs-gutter-x: 0; }
  .g-sm-0, .gy-sm-0 { --bs-gutter-y: 0; }
  .g-sm-1, .gx-sm-1 { --bs-gutter-x: .25rem; }
  .g-sm-1, .gy-sm-1 { --bs-gutter-y: .25rem; }
  .g-sm-2, .gx-sm-2 { --bs-gutter-x: .5rem; }
  .g-sm-2, .gy-sm-2 { --bs-gutter-y: .5rem; }
  .g-sm-3, .gx-sm-3 { --bs-gutter-x: 1rem; }
  .g-sm-3, .gy-sm-3 { --bs-gutter-y: 1rem; }
  .g-sm-4, .gx-sm-4 { --bs-gutter-x: 1.5rem; }
  .g-sm-4, .gy-sm-4 { --bs-gutter-y: 1.5rem; }
  .g-sm-5, .gx-sm-5 { --bs-gutter-x: 3rem; }
  .g-sm-5, .gy-sm-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 768px) {
  .col-md { flex: 1 0 0%; }
  .row-cols-md-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-md-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-md-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-md-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-md-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-md-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-md-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-md-auto { flex: 0 0 auto; width: auto; }
  .col-md-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-md-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-md-3 { flex: 0 0 auto; width: 25%; }
  .col-md-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-md-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-md-6 { flex: 0 0 auto; width: 50%; }
  .col-md-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-md-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-md-9 { flex: 0 0 auto; width: 75%; }
  .col-md-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-md-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-md-12 { flex: 0 0 auto; width: 100%; }
  .offset-md-0 { margin-left: 0px; }
  .offset-md-1 { margin-left: 8.33333%; }
  .offset-md-2 { margin-left: 16.6667%; }
  .offset-md-3 { margin-left: 25%; }
  .offset-md-4 { margin-left: 33.3333%; }
  .offset-md-5 { margin-left: 41.6667%; }
  .offset-md-6 { margin-left: 50%; }
  .offset-md-7 { margin-left: 58.3333%; }
  .offset-md-8 { margin-left: 66.6667%; }
  .offset-md-9 { margin-left: 75%; }
  .offset-md-10 { margin-left: 83.3333%; }
  .offset-md-11 { margin-left: 91.6667%; }
  .g-md-0, .gx-md-0 { --bs-gutter-x: 0; }
  .g-md-0, .gy-md-0 { --bs-gutter-y: 0; }
  .g-md-1, .gx-md-1 { --bs-gutter-x: .25rem; }
  .g-md-1, .gy-md-1 { --bs-gutter-y: .25rem; }
  .g-md-2, .gx-md-2 { --bs-gutter-x: .5rem; }
  .g-md-2, .gy-md-2 { --bs-gutter-y: .5rem; }
  .g-md-3, .gx-md-3 { --bs-gutter-x: 1rem; }
  .g-md-3, .gy-md-3 { --bs-gutter-y: 1rem; }
  .g-md-4, .gx-md-4 { --bs-gutter-x: 1.5rem; }
  .g-md-4, .gy-md-4 { --bs-gutter-y: 1.5rem; }
  .g-md-5, .gx-md-5 { --bs-gutter-x: 3rem; }
  .g-md-5, .gy-md-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 992px) {
  .col-lg { flex: 1 0 0%; }
  .row-cols-lg-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-lg-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-lg-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-lg-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-lg-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-lg-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-lg-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-lg-auto { flex: 0 0 auto; width: auto; }
  .col-lg-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-lg-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-lg-3 { flex: 0 0 auto; width: 25%; }
  .col-lg-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-lg-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-lg-6 { flex: 0 0 auto; width: 50%; }
  .col-lg-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-lg-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-lg-9 { flex: 0 0 auto; width: 75%; }
  .col-lg-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-lg-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-lg-12 { flex: 0 0 auto; width: 100%; }
  .offset-lg-0 { margin-left: 0px; }
  .offset-lg-1 { margin-left: 8.33333%; }
  .offset-lg-2 { margin-left: 16.6667%; }
  .offset-lg-3 { margin-left: 25%; }
  .offset-lg-4 { margin-left: 33.3333%; }
  .offset-lg-5 { margin-left: 41.6667%; }
  .offset-lg-6 { margin-left: 50%; }
  .offset-lg-7 { margin-left: 58.3333%; }
  .offset-lg-8 { margin-left: 66.6667%; }
  .offset-lg-9 { margin-left: 75%; }
  .offset-lg-10 { margin-left: 83.3333%; }
  .offset-lg-11 { margin-left: 91.6667%; }
  .g-lg-0, .gx-lg-0 { --bs-gutter-x: 0; }
  .g-lg-0, .gy-lg-0 { --bs-gutter-y: 0; }
  .g-lg-1, .gx-lg-1 { --bs-gutter-x: .25rem; }
  .g-lg-1, .gy-lg-1 { --bs-gutter-y: .25rem; }
  .g-lg-2, .gx-lg-2 { --bs-gutter-x: .5rem; }
  .g-lg-2, .gy-lg-2 { --bs-gutter-y: .5rem; }
  .g-lg-3, .gx-lg-3 { --bs-gutter-x: 1rem; }
  .g-lg-3, .gy-lg-3 { --bs-gutter-y: 1rem; }
  .g-lg-4, .gx-lg-4 { --bs-gutter-x: 1.5rem; }
  .g-lg-4, .gy-lg-4 { --bs-gutter-y: 1.5rem; }
  .g-lg-5, .gx-lg-5 { --bs-gutter-x: 3rem; }
  .g-lg-5, .gy-lg-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 1200px) {
  .col-xl { flex: 1 0 0%; }
  .row-cols-xl-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-xl-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-xl-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-xl-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-xl-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-xl-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-xl-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-xl-auto { flex: 0 0 auto; width: auto; }
  .col-xl-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-xl-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-xl-3 { flex: 0 0 auto; width: 25%; }
  .col-xl-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-xl-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-xl-6 { flex: 0 0 auto; width: 50%; }
  .col-xl-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-xl-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-xl-9 { flex: 0 0 auto; width: 75%; }
  .col-xl-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-xl-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-xl-12 { flex: 0 0 auto; width: 100%; }
  .offset-xl-0 { margin-left: 0px; }
  .offset-xl-1 { margin-left: 8.33333%; }
  .offset-xl-2 { margin-left: 16.6667%; }
  .offset-xl-3 { margin-left: 25%; }
  .offset-xl-4 { margin-left: 33.3333%; }
  .offset-xl-5 { margin-left: 41.6667%; }
  .offset-xl-6 { margin-left: 50%; }
  .offset-xl-7 { margin-left: 58.3333%; }
  .offset-xl-8 { margin-left: 66.6667%; }
  .offset-xl-9 { margin-left: 75%; }
  .offset-xl-10 { margin-left: 83.3333%; }
  .offset-xl-11 { margin-left: 91.6667%; }
  .g-xl-0, .gx-xl-0 { --bs-gutter-x: 0; }
  .g-xl-0, .gy-xl-0 { --bs-gutter-y: 0; }
  .g-xl-1, .gx-xl-1 { --bs-gutter-x: .25rem; }
  .g-xl-1, .gy-xl-1 { --bs-gutter-y: .25rem; }
  .g-xl-2, .gx-xl-2 { --bs-gutter-x: .5rem; }
  .g-xl-2, .gy-xl-2 { --bs-gutter-y: .5rem; }
  .g-xl-3, .gx-xl-3 { --bs-gutter-x: 1rem; }
  .g-xl-3, .gy-xl-3 { --bs-gutter-y: 1rem; }
  .g-xl-4, .gx-xl-4 { --bs-gutter-x: 1.5rem; }
  .g-xl-4, .gy-xl-4 { --bs-gutter-y: 1.5rem; }
  .g-xl-5, .gx-xl-5 { --bs-gutter-x: 3rem; }
  .g-xl-5, .gy-xl-5 { --bs-gutter-y: 3rem; }
}

@media (min-width: 1400px) {
  .col-xxl { flex: 1 0 0%; }
  .row-cols-xxl-auto > * { flex: 0 0 auto; width: auto; }
  .row-cols-xxl-1 > * { flex: 0 0 auto; width: 100%; }
  .row-cols-xxl-2 > * { flex: 0 0 auto; width: 50%; }
  .row-cols-xxl-3 > * { flex: 0 0 auto; width: 33.3333%; }
  .row-cols-xxl-4 > * { flex: 0 0 auto; width: 25%; }
  .row-cols-xxl-5 > * { flex: 0 0 auto; width: 20%; }
  .row-cols-xxl-6 > * { flex: 0 0 auto; width: 16.6667%; }
  .col-xxl-auto { flex: 0 0 auto; width: auto; }
  .col-xxl-1 { flex: 0 0 auto; width: 8.33333%; }
  .col-xxl-2 { flex: 0 0 auto; width: 16.6667%; }
  .col-xxl-3 { flex: 0 0 auto; width: 25%; }
  .col-xxl-4 { flex: 0 0 auto; width: 33.3333%; }
  .col-xxl-5 { flex: 0 0 auto; width: 41.6667%; }
  .col-xxl-6 { flex: 0 0 auto; width: 50%; }
  .col-xxl-7 { flex: 0 0 auto; width: 58.3333%; }
  .col-xxl-8 { flex: 0 0 auto; width: 66.6667%; }
  .col-xxl-9 { flex: 0 0 auto; width: 75%; }
  .col-xxl-10 { flex: 0 0 auto; width: 83.3333%; }
  .col-xxl-11 { flex: 0 0 auto; width: 91.6667%; }
  .col-xxl-12 { flex: 0 0 auto; width: 100%; }
  .offset-xxl-0 { margin-left: 0px; }
  .offset-xxl-1 { margin-left: 8.33333%; }
  .offset-xxl-2 { margin-left: 16.6667%; }
  .offset-xxl-3 { margin-left: 25%; }
  .offset-xxl-4 { margin-left: 33.3333%; }
  .offset-xxl-5 { margin-left: 41.6667%; }
  .offset-xxl-6 { margin-left: 50%; }
  .offset-xxl-7 { margin-left: 58.3333%; }
  .offset-xxl-8 { margin-left: 66.6667%; }
  .offset-xxl-9 { margin-left: 75%; }
  .offset-xxl-10 { margin-left: 83.3333%; }
  .offset-xxl-11 { margin-left: 91.6667%; }
  .g-xxl-0, .gx-xxl-0 { --bs-gutter-x: 0; }
  .g-xxl-0, .gy-xxl-0 { --bs-gutter-y: 0; }
  .g-xxl-1, .gx-xxl-1 { --bs-gutter-x: .25rem; }
  .g-xxl-1, .gy-xxl-1 { --bs-gutter-y: .25rem; }
  .g-xxl-2, .gx-xxl-2 { --bs-gutter-x: .5rem; }
  .g-xxl-2, .gy-xxl-2 { --bs-gutter-y: .5rem; }
  .g-xxl-3, .gx-xxl-3 { --bs-gutter-x: 1rem; }
  .g-xxl-3, .gy-xxl-3 { --bs-gutter-y: 1rem; }
  .g-xxl-4, .gx-xxl-4 { --bs-gutter-x: 1.5rem; }
  .g-xxl-4, .gy-xxl-4 { --bs-gutter-y: 1.5rem; }
  .g-xxl-5, .gx-xxl-5 { --bs-gutter-x: 3rem; }
  .g-xxl-5, .gy-xxl-5 { --bs-gutter-y: 3rem; }
}

.container, .container-fluid, .container-xl { --bs-gutter-x: 1.5rem; --bs-g=
utter-y: 0; margin-left: auto; margin-right: auto; padding-left: calc(var(-=
-bs-gutter-x)*.5); padding-right: calc(var(--bs-gutter-x)*.5); width: 100%;=
 }

@media (min-width: 1200px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl { =
max-width: 1280px; }
}

.align-baseline { vertical-align: baseline !important; }

.align-top { vertical-align: top !important; }

.align-middle { vertical-align: middle !important; }

.align-bottom { vertical-align: bottom !important; }

.align-text-bottom { vertical-align: text-bottom !important; }

.align-text-top { vertical-align: text-top !important; }

.float-start { float: left !important; }

.float-end { float: right !important; }

.float-none { float: none !important; }

.d-inline { display: inline !important; }

.d-inline-block { display: inline-block !important; }

.d-block { display: block !important; }

.d-grid { display: grid !important; }

.d-inline-grid { display: inline-grid !important; }

.d-table { display: table !important; }

.d-table-row { display: table-row !important; }

.d-table-cell { display: table-cell !important; }

.d-flex { display: flex !important; }

.d-inline-flex { display: inline-flex !important; }

.d-none { display: none !important; }

.position-static { position: static !important; }

.position-relative { position: relative !important; }

.position-absolute { position: absolute !important; }

.position-fixed { position: fixed !important; }

.position-sticky { position: sticky !important; }

.top-0 { top: 0px !important; }

.top-50 { top: 50% !important; }

.top-100 { top: 100% !important; }

.bottom-0 { bottom: 0px !important; }

.bottom-50 { bottom: 50% !important; }

.bottom-100 { bottom: 100% !important; }

.start-0 { left: 0px !important; }

.start-50 { left: 50% !important; }

.start-100 { left: 100% !important; }

.end-0 { right: 0px !important; }

.end-50 { right: 50% !important; }

.end-100 { right: 100% !important; }

.border { border: var(--bs-border-width) var(--bs-border-style) var(--bs-bo=
rder-color) !important; }

.border-0 { border: 0px !important; }

.border-top { border-top: var(--bs-border-width) var(--bs-border-style) var=
(--bs-border-color) !important; }

.border-top-0 { border-top: 0px !important; }

.border-end { border-right: var(--bs-border-width) var(--bs-border-style) v=
ar(--bs-border-color) !important; }

.border-end-0 { border-right: 0px !important; }

.border-bottom { border-bottom: var(--bs-border-width) var(--bs-border-styl=
e) var(--bs-border-color) !important; }

.border-bottom-0 { border-bottom: 0px !important; }

.border-start { border-left: var(--bs-border-width) var(--bs-border-style) =
var(--bs-border-color) !important; }

.border-start-0 { border-left: 0px !important; }

.w-25 { width: 25% !important; }

.w-50 { width: 50% !important; }

.w-75 { width: 75% !important; }

.w-100 { width: 100% !important; }

.w-auto { width: auto !important; }

.mw-100 { max-width: 100% !important; }

.vw-100 { width: 100vw !important; }

.min-vw-100 { min-width: 100vw !important; }

.h-25 { height: 25% !important; }

.h-50 { height: 50% !important; }

.h-75 { height: 75% !important; }

.h-100 { height: 100% !important; }

.h-auto { height: auto !important; }

.mh-100 { max-height: 100% !important; }

.vh-100 { height: 100vh !important; }

.min-vh-100 { min-height: 100vh !important; }

.flex-fill { flex: 1 1 auto !important; }

.flex-row { flex-direction: row !important; }

.flex-column { flex-direction: column !important; }

.flex-row-reverse { flex-direction: row-reverse !important; }

.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-grow-0 { flex-grow: 0 !important; }

.flex-grow-1 { flex-grow: 1 !important; }

.flex-shrink-0 { flex-shrink: 0 !important; }

.flex-shrink-1 { flex-shrink: 1 !important; }

.flex-wrap { flex-wrap: wrap !important; }

.flex-nowrap { flex-wrap: nowrap !important; }

.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }

.justify-content-end { justify-content: flex-end !important; }

.justify-content-center { justify-content: center !important; }

.justify-content-between { justify-content: space-between !important; }

.justify-content-around { justify-content: space-around !important; }

.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }

.align-items-end { align-items: flex-end !important; }

.align-items-center { align-items: center !important; }

.align-items-baseline { align-items: baseline !important; }

.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }

.align-content-end { align-content: flex-end !important; }

.align-content-center { align-content: center !important; }

.align-content-between { align-content: space-between !important; }

.align-content-around { align-content: space-around !important; }

.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }

.align-self-start { align-self: flex-start !important; }

.align-self-end { align-self: flex-end !important; }

.align-self-center { align-self: center !important; }

.align-self-baseline { align-self: baseline !important; }

.align-self-stretch { align-self: stretch !important; }

.order-first { order: -1 !important; }

.order-0 { order: 0 !important; }

.order-1 { order: 1 !important; }

.order-2 { order: 2 !important; }

.order-3 { order: 3 !important; }

.order-4 { order: 4 !important; }

.order-5 { order: 5 !important; }

.order-last { order: 6 !important; }

.m-0 { margin: 0px !important; }

.m-1 { margin: 0.25rem !important; }

.m-2 { margin: 0.5rem !important; }

.m-3 { margin: 1rem !important; }

.m-4 { margin: 1.5rem !important; }

.m-5 { margin: 3rem !important; }

.m-auto { margin: auto !important; }

.mx-0 { margin-left: 0px !important; margin-right: 0px !important; }

.mx-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !important; =
}

.mx-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !important; }

.mx-3 { margin-left: 1rem !important; margin-right: 1rem !important; }

.mx-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !important; }

.mx-5 { margin-left: 3rem !important; margin-right: 3rem !important; }

.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-bottom: 0px !important; margin-top: 0px !important; }

.my-1 { margin-bottom: 0.25rem !important; margin-top: 0.25rem !important; =
}

.my-2 { margin-bottom: 0.5rem !important; margin-top: 0.5rem !important; }

.my-3 { margin-bottom: 1rem !important; margin-top: 1rem !important; }

.my-4 { margin-bottom: 1.5rem !important; margin-top: 1.5rem !important; }

.my-5 { margin-bottom: 3rem !important; margin-top: 3rem !important; }

.my-auto { margin-bottom: auto !important; margin-top: auto !important; }

.mt-0 { margin-top: 0px !important; }

.mt-1 { margin-top: 0.25rem !important; }

.mt-2 { margin-top: 0.5rem !important; }

.mt-3 { margin-top: 1rem !important; }

.mt-4 { margin-top: 1.5rem !important; }

.mt-5 { margin-top: 3rem !important; }

.mt-auto { margin-top: auto !important; }

.me-0 { margin-right: 0px !important; }

.me-1 { margin-right: 0.25rem !important; }

.me-2 { margin-right: 0.5rem !important; }

.me-3 { margin-right: 1rem !important; }

.me-4 { margin-right: 1.5rem !important; }

.me-5 { margin-right: 3rem !important; }

.me-auto { margin-right: auto !important; }

.mb-0 { margin-bottom: 0px !important; }

.mb-1 { margin-bottom: 0.25rem !important; }

.mb-2 { margin-bottom: 0.5rem !important; }

.mb-3 { margin-bottom: 1rem !important; }

.mb-4 { margin-bottom: 1.5rem !important; }

.mb-5 { margin-bottom: 3rem !important; }

.mb-auto { margin-bottom: auto !important; }

.ms-0 { margin-left: 0px !important; }

.ms-1 { margin-left: 0.25rem !important; }

.ms-2 { margin-left: 0.5rem !important; }

.ms-3 { margin-left: 1rem !important; }

.ms-4 { margin-left: 1.5rem !important; }

.ms-5 { margin-left: 3rem !important; }

.ms-auto { margin-left: auto !important; }

.p-0 { padding: 0px !important; }

.p-1 { padding: 0.25rem !important; }

.p-2 { padding: 0.5rem !important; }

.p-3 { padding: 1rem !important; }

.p-4 { padding: 1.5rem !important; }

.p-5 { padding: 3rem !important; }

.px-0 { padding-left: 0px !important; padding-right: 0px !important; }

.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important=
; }

.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; =
}

.px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }

.px-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; =
}

.px-5 { padding-left: 3rem !important; padding-right: 3rem !important; }

.py-0 { padding-bottom: 0px !important; padding-top: 0px !important; }

.py-1 { padding-bottom: 0.25rem !important; padding-top: 0.25rem !important=
; }

.py-2 { padding-bottom: 0.5rem !important; padding-top: 0.5rem !important; =
}

.py-3 { padding-bottom: 1rem !important; padding-top: 1rem !important; }

.py-4 { padding-bottom: 1.5rem !important; padding-top: 1.5rem !important; =
}

.py-5 { padding-bottom: 3rem !important; padding-top: 3rem !important; }

.pt-0 { padding-top: 0px !important; }

.pt-1 { padding-top: 0.25rem !important; }

.pt-2 { padding-top: 0.5rem !important; }

.pt-3 { padding-top: 1rem !important; }

.pt-4 { padding-top: 1.5rem !important; }

.pt-5 { padding-top: 3rem !important; }

.pe-0 { padding-right: 0px !important; }

.pe-1 { padding-right: 0.25rem !important; }

.pe-2 { padding-right: 0.5rem !important; }

.pe-3 { padding-right: 1rem !important; }

.pe-4 { padding-right: 1.5rem !important; }

.pe-5 { padding-right: 3rem !important; }

.pb-0 { padding-bottom: 0px !important; }

.pb-1 { padding-bottom: 0.25rem !important; }

.pb-2 { padding-bottom: 0.5rem !important; }

.pb-3 { padding-bottom: 1rem !important; }

.pb-4 { padding-bottom: 1.5rem !important; }

.pb-5 { padding-bottom: 3rem !important; }

.ps-0 { padding-left: 0px !important; }

.ps-1 { padding-left: 0.25rem !important; }

.ps-2 { padding-left: 0.5rem !important; }

.ps-3 { padding-left: 1rem !important; }

.ps-4 { padding-left: 1.5rem !important; }

.ps-5 { padding-left: 3rem !important; }

.gap-0 { gap: 0px !important; }

.gap-1 { gap: 0.25rem !important; }

.gap-2 { gap: 0.5rem !important; }

.gap-3 { gap: 1rem !important; }

.gap-4 { gap: 1.5rem !important; }

.gap-5 { gap: 3rem !important; }

.row-gap-0 { row-gap: 0px !important; }

.row-gap-1 { row-gap: 0.25rem !important; }

.row-gap-2 { row-gap: 0.5rem !important; }

.row-gap-3 { row-gap: 1rem !important; }

.row-gap-4 { row-gap: 1.5rem !important; }

.row-gap-5 { row-gap: 3rem !important; }

.column-gap-0 { column-gap: 0px !important; }

.column-gap-1 { column-gap: 0.25rem !important; }

.column-gap-2 { column-gap: 0.5rem !important; }

.column-gap-3 { column-gap: 1rem !important; }

.column-gap-4 { column-gap: 1.5rem !important; }

.column-gap-5 { column-gap: 3rem !important; }

.font-monospace { font-family: var(--bs-font-monospace) !important; }

.fs-1 { font-size: 2.5rem !important; }

.fs-2 { font-size: 2rem !important; }

.fs-3 { font-size: 1.75rem !important; }

.fs-4 { font-size: 1.5rem !important; }

.fs-5 { font-size: 1.25rem !important; }

.fs-6 { font-size: 1rem !important; }

.fst-italic { font-style: italic !important; }

.fst-normal { font-style: normal !important; }

.fw-lighter { font-weight: lighter !important; }

.fw-light { font-weight: 300 !important; }

.fw-normal { font-weight: 400 !important; }

.fw-medium { font-weight: 500 !important; }

.fw-bold, .fw-semibold { font-weight: 600 !important; }

.fw-bolder { font-weight: 700 !important; }

.text-start { text-align: left !important; }

.text-end { text-align: right !important; }

.text-center { text-align: center !important; }

.text-decoration-none { text-decoration: none !important; }

.text-decoration-underline { text-decoration: underline !important; }

.text-decoration-line-through { text-decoration: line-through !important; }

.text-lowercase { text-transform: lowercase !important; }

.text-uppercase { text-transform: uppercase !important; }

.text-capitalize { text-transform: capitalize !important; }

.text-wrap { white-space: normal !important; }

.text-nowrap { white-space: nowrap !important; }

.text-break { overflow-wrap: break-word !important; word-break: break-word =
!important; }

.visible { visibility: visible !important; }

.invisible { visibility: hidden !important; }

@media (min-width: 576px) {
  .float-sm-start { float: left !important; }
  .float-sm-end { float: right !important; }
  .float-sm-none { float: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-grid { display: grid !important; }
  .d-sm-inline-grid { display: inline-grid !important; }
  .d-sm-table { display: table !important; }
  .d-sm-table-row { display: table-row !important; }
  .d-sm-table-cell { display: table-cell !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline-flex { display: inline-flex !important; }
  .d-sm-none { display: none !important; }
  .flex-sm-fill { flex: 1 1 auto !important; }
  .flex-sm-row { flex-direction: row !important; }
  .flex-sm-column { flex-direction: column !important; }
  .flex-sm-row-reverse { flex-direction: row-reverse !important; }
  .flex-sm-column-reverse { flex-direction: column-reverse !important; }
  .flex-sm-grow-0 { flex-grow: 0 !important; }
  .flex-sm-grow-1 { flex-grow: 1 !important; }
  .flex-sm-shrink-0 { flex-shrink: 0 !important; }
  .flex-sm-shrink-1 { flex-shrink: 1 !important; }
  .flex-sm-wrap { flex-wrap: wrap !important; }
  .flex-sm-nowrap { flex-wrap: nowrap !important; }
  .flex-sm-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .justify-content-sm-start { justify-content: flex-start !important; }
  .justify-content-sm-end { justify-content: flex-end !important; }
  .justify-content-sm-center { justify-content: center !important; }
  .justify-content-sm-between { justify-content: space-between !important; =
}
  .justify-content-sm-around { justify-content: space-around !important; }
  .justify-content-sm-evenly { justify-content: space-evenly !important; }
  .align-items-sm-start { align-items: flex-start !important; }
  .align-items-sm-end { align-items: flex-end !important; }
  .align-items-sm-center { align-items: center !important; }
  .align-items-sm-baseline { align-items: baseline !important; }
  .align-items-sm-stretch { align-items: stretch !important; }
  .align-content-sm-start { align-content: flex-start !important; }
  .align-content-sm-end { align-content: flex-end !important; }
  .align-content-sm-center { align-content: center !important; }
  .align-content-sm-between { align-content: space-between !important; }
  .align-content-sm-around { align-content: space-around !important; }
  .align-content-sm-stretch { align-content: stretch !important; }
  .align-self-sm-auto { align-self: auto !important; }
  .align-self-sm-start { align-self: flex-start !important; }
  .align-self-sm-end { align-self: flex-end !important; }
  .align-self-sm-center { align-self: center !important; }
  .align-self-sm-baseline { align-self: baseline !important; }
  .align-self-sm-stretch { align-self: stretch !important; }
  .order-sm-first { order: -1 !important; }
  .order-sm-0 { order: 0 !important; }
  .order-sm-1 { order: 1 !important; }
  .order-sm-2 { order: 2 !important; }
  .order-sm-3 { order: 3 !important; }
  .order-sm-4 { order: 4 !important; }
  .order-sm-5 { order: 5 !important; }
  .order-sm-last { order: 6 !important; }
  .m-sm-0 { margin: 0px !important; }
  .m-sm-1 { margin: 0.25rem !important; }
  .m-sm-2 { margin: 0.5rem !important; }
  .m-sm-3 { margin: 1rem !important; }
  .m-sm-4 { margin: 1.5rem !important; }
  .m-sm-5 { margin: 3rem !important; }
  .m-sm-auto { margin: auto !important; }
  .mx-sm-0 { margin-left: 0px !important; margin-right: 0px !important; }
  .mx-sm-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !import=
ant; }
  .mx-sm-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !importan=
t; }
  .mx-sm-3 { margin-left: 1rem !important; margin-right: 1rem !important; }
  .mx-sm-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !importan=
t; }
  .mx-sm-5 { margin-left: 3rem !important; margin-right: 3rem !important; }
  .mx-sm-auto { margin-left: auto !important; margin-right: auto !important=
; }
  .my-sm-0 { margin-bottom: 0px !important; margin-top: 0px !important; }
  .my-sm-1 { margin-bottom: 0.25rem !important; margin-top: 0.25rem !import=
ant; }
  .my-sm-2 { margin-bottom: 0.5rem !important; margin-top: 0.5rem !importan=
t; }
  .my-sm-3 { margin-bottom: 1rem !important; margin-top: 1rem !important; }
  .my-sm-4 { margin-bottom: 1.5rem !important; margin-top: 1.5rem !importan=
t; }
  .my-sm-5 { margin-bottom: 3rem !important; margin-top: 3rem !important; }
  .my-sm-auto { margin-bottom: auto !important; margin-top: auto !important=
; }
  .mt-sm-0 { margin-top: 0px !important; }
  .mt-sm-1 { margin-top: 0.25rem !important; }
  .mt-sm-2 { margin-top: 0.5rem !important; }
  .mt-sm-3 { margin-top: 1rem !important; }
  .mt-sm-4 { margin-top: 1.5rem !important; }
  .mt-sm-5 { margin-top: 3rem !important; }
  .mt-sm-auto { margin-top: auto !important; }
  .me-sm-0 { margin-right: 0px !important; }
  .me-sm-1 { margin-right: 0.25rem !important; }
  .me-sm-2 { margin-right: 0.5rem !important; }
  .me-sm-3 { margin-right: 1rem !important; }
  .me-sm-4 { margin-right: 1.5rem !important; }
  .me-sm-5 { margin-right: 3rem !important; }
  .me-sm-auto { margin-right: auto !important; }
  .mb-sm-0 { margin-bottom: 0px !important; }
  .mb-sm-1 { margin-bottom: 0.25rem !important; }
  .mb-sm-2 { margin-bottom: 0.5rem !important; }
  .mb-sm-3 { margin-bottom: 1rem !important; }
  .mb-sm-4 { margin-bottom: 1.5rem !important; }
  .mb-sm-5 { margin-bottom: 3rem !important; }
  .mb-sm-auto { margin-bottom: auto !important; }
  .ms-sm-0 { margin-left: 0px !important; }
  .ms-sm-1 { margin-left: 0.25rem !important; }
  .ms-sm-2 { margin-left: 0.5rem !important; }
  .ms-sm-3 { margin-left: 1rem !important; }
  .ms-sm-4 { margin-left: 1.5rem !important; }
  .ms-sm-5 { margin-left: 3rem !important; }
  .ms-sm-auto { margin-left: auto !important; }
  .p-sm-0 { padding: 0px !important; }
  .p-sm-1 { padding: 0.25rem !important; }
  .p-sm-2 { padding: 0.5rem !important; }
  .p-sm-3 { padding: 1rem !important; }
  .p-sm-4 { padding: 1.5rem !important; }
  .p-sm-5 { padding: 3rem !important; }
  .px-sm-0 { padding-left: 0px !important; padding-right: 0px !important; }
  .px-sm-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !impo=
rtant; }
  .px-sm-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !import=
ant; }
  .px-sm-3 { padding-left: 1rem !important; padding-right: 1rem !important;=
 }
  .px-sm-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !import=
ant; }
  .px-sm-5 { padding-left: 3rem !important; padding-right: 3rem !important;=
 }
  .py-sm-0 { padding-bottom: 0px !important; padding-top: 0px !important; }
  .py-sm-1 { padding-bottom: 0.25rem !important; padding-top: 0.25rem !impo=
rtant; }
  .py-sm-2 { padding-bottom: 0.5rem !important; padding-top: 0.5rem !import=
ant; }
  .py-sm-3 { padding-bottom: 1rem !important; padding-top: 1rem !important;=
 }
  .py-sm-4 { padding-bottom: 1.5rem !important; padding-top: 1.5rem !import=
ant; }
  .py-sm-5 { padding-bottom: 3rem !important; padding-top: 3rem !important;=
 }
  .pt-sm-0 { padding-top: 0px !important; }
  .pt-sm-1 { padding-top: 0.25rem !important; }
  .pt-sm-2 { padding-top: 0.5rem !important; }
  .pt-sm-3 { padding-top: 1rem !important; }
  .pt-sm-4 { padding-top: 1.5rem !important; }
  .pt-sm-5 { padding-top: 3rem !important; }
  .pe-sm-0 { padding-right: 0px !important; }
  .pe-sm-1 { padding-right: 0.25rem !important; }
  .pe-sm-2 { padding-right: 0.5rem !important; }
  .pe-sm-3 { padding-right: 1rem !important; }
  .pe-sm-4 { padding-right: 1.5rem !important; }
  .pe-sm-5 { padding-right: 3rem !important; }
  .pb-sm-0 { padding-bottom: 0px !important; }
  .pb-sm-1 { padding-bottom: 0.25rem !important; }
  .pb-sm-2 { padding-bottom: 0.5rem !important; }
  .pb-sm-3 { padding-bottom: 1rem !important; }
  .pb-sm-4 { padding-bottom: 1.5rem !important; }
  .pb-sm-5 { padding-bottom: 3rem !important; }
  .ps-sm-0 { padding-left: 0px !important; }
  .ps-sm-1 { padding-left: 0.25rem !important; }
  .ps-sm-2 { padding-left: 0.5rem !important; }
  .ps-sm-3 { padding-left: 1rem !important; }
  .ps-sm-4 { padding-left: 1.5rem !important; }
  .ps-sm-5 { padding-left: 3rem !important; }
  .gap-sm-0 { gap: 0px !important; }
  .gap-sm-1 { gap: 0.25rem !important; }
  .gap-sm-2 { gap: 0.5rem !important; }
  .gap-sm-3 { gap: 1rem !important; }
  .gap-sm-4 { gap: 1.5rem !important; }
  .gap-sm-5 { gap: 3rem !important; }
  .row-gap-sm-0 { row-gap: 0px !important; }
  .row-gap-sm-1 { row-gap: 0.25rem !important; }
  .row-gap-sm-2 { row-gap: 0.5rem !important; }
  .row-gap-sm-3 { row-gap: 1rem !important; }
  .row-gap-sm-4 { row-gap: 1.5rem !important; }
  .row-gap-sm-5 { row-gap: 3rem !important; }
  .column-gap-sm-0 { column-gap: 0px !important; }
  .column-gap-sm-1 { column-gap: 0.25rem !important; }
  .column-gap-sm-2 { column-gap: 0.5rem !important; }
  .column-gap-sm-3 { column-gap: 1rem !important; }
  .column-gap-sm-4 { column-gap: 1.5rem !important; }
  .column-gap-sm-5 { column-gap: 3rem !important; }
  .text-sm-start { text-align: left !important; }
  .text-sm-end { text-align: right !important; }
  .text-sm-center { text-align: center !important; }
}

@media (min-width: 768px) {
  .float-md-start { float: left !important; }
  .float-md-end { float: right !important; }
  .float-md-none { float: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-grid { display: grid !important; }
  .d-md-inline-grid { display: inline-grid !important; }
  .d-md-table { display: table !important; }
  .d-md-table-row { display: table-row !important; }
  .d-md-table-cell { display: table-cell !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline-flex { display: inline-flex !important; }
  .d-md-none { display: none !important; }
  .flex-md-fill { flex: 1 1 auto !important; }
  .flex-md-row { flex-direction: row !important; }
  .flex-md-column { flex-direction: column !important; }
  .flex-md-row-reverse { flex-direction: row-reverse !important; }
  .flex-md-column-reverse { flex-direction: column-reverse !important; }
  .flex-md-grow-0 { flex-grow: 0 !important; }
  .flex-md-grow-1 { flex-grow: 1 !important; }
  .flex-md-shrink-0 { flex-shrink: 0 !important; }
  .flex-md-shrink-1 { flex-shrink: 1 !important; }
  .flex-md-wrap { flex-wrap: wrap !important; }
  .flex-md-nowrap { flex-wrap: nowrap !important; }
  .flex-md-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .justify-content-md-start { justify-content: flex-start !important; }
  .justify-content-md-end { justify-content: flex-end !important; }
  .justify-content-md-center { justify-content: center !important; }
  .justify-content-md-between { justify-content: space-between !important; =
}
  .justify-content-md-around { justify-content: space-around !important; }
  .justify-content-md-evenly { justify-content: space-evenly !important; }
  .align-items-md-start { align-items: flex-start !important; }
  .align-items-md-end { align-items: flex-end !important; }
  .align-items-md-center { align-items: center !important; }
  .align-items-md-baseline { align-items: baseline !important; }
  .align-items-md-stretch { align-items: stretch !important; }
  .align-content-md-start { align-content: flex-start !important; }
  .align-content-md-end { align-content: flex-end !important; }
  .align-content-md-center { align-content: center !important; }
  .align-content-md-between { align-content: space-between !important; }
  .align-content-md-around { align-content: space-around !important; }
  .align-content-md-stretch { align-content: stretch !important; }
  .align-self-md-auto { align-self: auto !important; }
  .align-self-md-start { align-self: flex-start !important; }
  .align-self-md-end { align-self: flex-end !important; }
  .align-self-md-center { align-self: center !important; }
  .align-self-md-baseline { align-self: baseline !important; }
  .align-self-md-stretch { align-self: stretch !important; }
  .order-md-first { order: -1 !important; }
  .order-md-0 { order: 0 !important; }
  .order-md-1 { order: 1 !important; }
  .order-md-2 { order: 2 !important; }
  .order-md-3 { order: 3 !important; }
  .order-md-4 { order: 4 !important; }
  .order-md-5 { order: 5 !important; }
  .order-md-last { order: 6 !important; }
  .m-md-0 { margin: 0px !important; }
  .m-md-1 { margin: 0.25rem !important; }
  .m-md-2 { margin: 0.5rem !important; }
  .m-md-3 { margin: 1rem !important; }
  .m-md-4 { margin: 1.5rem !important; }
  .m-md-5 { margin: 3rem !important; }
  .m-md-auto { margin: auto !important; }
  .mx-md-0 { margin-left: 0px !important; margin-right: 0px !important; }
  .mx-md-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !import=
ant; }
  .mx-md-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !importan=
t; }
  .mx-md-3 { margin-left: 1rem !important; margin-right: 1rem !important; }
  .mx-md-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !importan=
t; }
  .mx-md-5 { margin-left: 3rem !important; margin-right: 3rem !important; }
  .mx-md-auto { margin-left: auto !important; margin-right: auto !important=
; }
  .my-md-0 { margin-bottom: 0px !important; margin-top: 0px !important; }
  .my-md-1 { margin-bottom: 0.25rem !important; margin-top: 0.25rem !import=
ant; }
  .my-md-2 { margin-bottom: 0.5rem !important; margin-top: 0.5rem !importan=
t; }
  .my-md-3 { margin-bottom: 1rem !important; margin-top: 1rem !important; }
  .my-md-4 { margin-bottom: 1.5rem !important; margin-top: 1.5rem !importan=
t; }
  .my-md-5 { margin-bottom: 3rem !important; margin-top: 3rem !important; }
  .my-md-auto { margin-bottom: auto !important; margin-top: auto !important=
; }
  .mt-md-0 { margin-top: 0px !important; }
  .mt-md-1 { margin-top: 0.25rem !important; }
  .mt-md-2 { margin-top: 0.5rem !important; }
  .mt-md-3 { margin-top: 1rem !important; }
  .mt-md-4 { margin-top: 1.5rem !important; }
  .mt-md-5 { margin-top: 3rem !important; }
  .mt-md-auto { margin-top: auto !important; }
  .me-md-0 { margin-right: 0px !important; }
  .me-md-1 { margin-right: 0.25rem !important; }
  .me-md-2 { margin-right: 0.5rem !important; }
  .me-md-3 { margin-right: 1rem !important; }
  .me-md-4 { margin-right: 1.5rem !important; }
  .me-md-5 { margin-right: 3rem !important; }
  .me-md-auto { margin-right: auto !important; }
  .mb-md-0 { margin-bottom: 0px !important; }
  .mb-md-1 { margin-bottom: 0.25rem !important; }
  .mb-md-2 { margin-bottom: 0.5rem !important; }
  .mb-md-3 { margin-bottom: 1rem !important; }
  .mb-md-4 { margin-bottom: 1.5rem !important; }
  .mb-md-5 { margin-bottom: 3rem !important; }
  .mb-md-auto { margin-bottom: auto !important; }
  .ms-md-0 { margin-left: 0px !important; }
  .ms-md-1 { margin-left: 0.25rem !important; }
  .ms-md-2 { margin-left: 0.5rem !important; }
  .ms-md-3 { margin-left: 1rem !important; }
  .ms-md-4 { margin-left: 1.5rem !important; }
  .ms-md-5 { margin-left: 3rem !important; }
  .ms-md-auto { margin-left: auto !important; }
  .p-md-0 { padding: 0px !important; }
  .p-md-1 { padding: 0.25rem !important; }
  .p-md-2 { padding: 0.5rem !important; }
  .p-md-3 { padding: 1rem !important; }
  .p-md-4 { padding: 1.5rem !important; }
  .p-md-5 { padding: 3rem !important; }
  .px-md-0 { padding-left: 0px !important; padding-right: 0px !important; }
  .px-md-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !impo=
rtant; }
  .px-md-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !import=
ant; }
  .px-md-3 { padding-left: 1rem !important; padding-right: 1rem !important;=
 }
  .px-md-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !import=
ant; }
  .px-md-5 { padding-left: 3rem !important; padding-right: 3rem !important;=
 }
  .py-md-0 { padding-bottom: 0px !important; padding-top: 0px !important; }
  .py-md-1 { padding-bottom: 0.25rem !important; padding-top: 0.25rem !impo=
rtant; }
  .py-md-2 { padding-bottom: 0.5rem !important; padding-top: 0.5rem !import=
ant; }
  .py-md-3 { padding-bottom: 1rem !important; padding-top: 1rem !important;=
 }
  .py-md-4 { padding-bottom: 1.5rem !important; padding-top: 1.5rem !import=
ant; }
  .py-md-5 { padding-bottom: 3rem !important; padding-top: 3rem !important;=
 }
  .pt-md-0 { padding-top: 0px !important; }
  .pt-md-1 { padding-top: 0.25rem !important; }
  .pt-md-2 { padding-top: 0.5rem !important; }
  .pt-md-3 { padding-top: 1rem !important; }
  .pt-md-4 { padding-top: 1.5rem !important; }
  .pt-md-5 { padding-top: 3rem !important; }
  .pe-md-0 { padding-right: 0px !important; }
  .pe-md-1 { padding-right: 0.25rem !important; }
  .pe-md-2 { padding-right: 0.5rem !important; }
  .pe-md-3 { padding-right: 1rem !important; }
  .pe-md-4 { padding-right: 1.5rem !important; }
  .pe-md-5 { padding-right: 3rem !important; }
  .pb-md-0 { padding-bottom: 0px !important; }
  .pb-md-1 { padding-bottom: 0.25rem !important; }
  .pb-md-2 { padding-bottom: 0.5rem !important; }
  .pb-md-3 { padding-bottom: 1rem !important; }
  .pb-md-4 { padding-bottom: 1.5rem !important; }
  .pb-md-5 { padding-bottom: 3rem !important; }
  .ps-md-0 { padding-left: 0px !important; }
  .ps-md-1 { padding-left: 0.25rem !important; }
  .ps-md-2 { padding-left: 0.5rem !important; }
  .ps-md-3 { padding-left: 1rem !important; }
  .ps-md-4 { padding-left: 1.5rem !important; }
  .ps-md-5 { padding-left: 3rem !important; }
  .gap-md-0 { gap: 0px !important; }
  .gap-md-1 { gap: 0.25rem !important; }
  .gap-md-2 { gap: 0.5rem !important; }
  .gap-md-3 { gap: 1rem !important; }
  .gap-md-4 { gap: 1.5rem !important; }
  .gap-md-5 { gap: 3rem !important; }
  .row-gap-md-0 { row-gap: 0px !important; }
  .row-gap-md-1 { row-gap: 0.25rem !important; }
  .row-gap-md-2 { row-gap: 0.5rem !important; }
  .row-gap-md-3 { row-gap: 1rem !important; }
  .row-gap-md-4 { row-gap: 1.5rem !important; }
  .row-gap-md-5 { row-gap: 3rem !important; }
  .column-gap-md-0 { column-gap: 0px !important; }
  .column-gap-md-1 { column-gap: 0.25rem !important; }
  .column-gap-md-2 { column-gap: 0.5rem !important; }
  .column-gap-md-3 { column-gap: 1rem !important; }
  .column-gap-md-4 { column-gap: 1.5rem !important; }
  .column-gap-md-5 { column-gap: 3rem !important; }
  .text-md-start { text-align: left !important; }
  .text-md-end { text-align: right !important; }
  .text-md-center { text-align: center !important; }
}

@media (min-width: 992px) {
  .float-lg-start { float: left !important; }
  .float-lg-end { float: right !important; }
  .float-lg-none { float: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-grid { display: grid !important; }
  .d-lg-inline-grid { display: inline-grid !important; }
  .d-lg-table { display: table !important; }
  .d-lg-table-row { display: table-row !important; }
  .d-lg-table-cell { display: table-cell !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline-flex { display: inline-flex !important; }
  .d-lg-none { display: none !important; }
  .flex-lg-fill { flex: 1 1 auto !important; }
  .flex-lg-row { flex-direction: row !important; }
  .flex-lg-column { flex-direction: column !important; }
  .flex-lg-row-reverse { flex-direction: row-reverse !important; }
  .flex-lg-column-reverse { flex-direction: column-reverse !important; }
  .flex-lg-grow-0 { flex-grow: 0 !important; }
  .flex-lg-grow-1 { flex-grow: 1 !important; }
  .flex-lg-shrink-0 { flex-shrink: 0 !important; }
  .flex-lg-shrink-1 { flex-shrink: 1 !important; }
  .flex-lg-wrap { flex-wrap: wrap !important; }
  .flex-lg-nowrap { flex-wrap: nowrap !important; }
  .flex-lg-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .justify-content-lg-start { justify-content: flex-start !important; }
  .justify-content-lg-end { justify-content: flex-end !important; }
  .justify-content-lg-center { justify-content: center !important; }
  .justify-content-lg-between { justify-content: space-between !important; =
}
  .justify-content-lg-around { justify-content: space-around !important; }
  .justify-content-lg-evenly { justify-content: space-evenly !important; }
  .align-items-lg-start { align-items: flex-start !important; }
  .align-items-lg-end { align-items: flex-end !important; }
  .align-items-lg-center { align-items: center !important; }
  .align-items-lg-baseline { align-items: baseline !important; }
  .align-items-lg-stretch { align-items: stretch !important; }
  .align-content-lg-start { align-content: flex-start !important; }
  .align-content-lg-end { align-content: flex-end !important; }
  .align-content-lg-center { align-content: center !important; }
  .align-content-lg-between { align-content: space-between !important; }
  .align-content-lg-around { align-content: space-around !important; }
  .align-content-lg-stretch { align-content: stretch !important; }
  .align-self-lg-auto { align-self: auto !important; }
  .align-self-lg-start { align-self: flex-start !important; }
  .align-self-lg-end { align-self: flex-end !important; }
  .align-self-lg-center { align-self: center !important; }
  .align-self-lg-baseline { align-self: baseline !important; }
  .align-self-lg-stretch { align-self: stretch !important; }
  .order-lg-first { order: -1 !important; }
  .order-lg-0 { order: 0 !important; }
  .order-lg-1 { order: 1 !important; }
  .order-lg-2 { order: 2 !important; }
  .order-lg-3 { order: 3 !important; }
  .order-lg-4 { order: 4 !important; }
  .order-lg-5 { order: 5 !important; }
  .order-lg-last { order: 6 !important; }
  .m-lg-0 { margin: 0px !important; }
  .m-lg-1 { margin: 0.25rem !important; }
  .m-lg-2 { margin: 0.5rem !important; }
  .m-lg-3 { margin: 1rem !important; }
  .m-lg-4 { margin: 1.5rem !important; }
  .m-lg-5 { margin: 3rem !important; }
  .m-lg-auto { margin: auto !important; }
  .mx-lg-0 { margin-left: 0px !important; margin-right: 0px !important; }
  .mx-lg-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !import=
ant; }
  .mx-lg-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !importan=
t; }
  .mx-lg-3 { margin-left: 1rem !important; margin-right: 1rem !important; }
  .mx-lg-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !importan=
t; }
  .mx-lg-5 { margin-left: 3rem !important; margin-right: 3rem !important; }
  .mx-lg-auto { margin-left: auto !important; margin-right: auto !important=
; }
  .my-lg-0 { margin-bottom: 0px !important; margin-top: 0px !important; }
  .my-lg-1 { margin-bottom: 0.25rem !important; margin-top: 0.25rem !import=
ant; }
  .my-lg-2 { margin-bottom: 0.5rem !important; margin-top: 0.5rem !importan=
t; }
  .my-lg-3 { margin-bottom: 1rem !important; margin-top: 1rem !important; }
  .my-lg-4 { margin-bottom: 1.5rem !important; margin-top: 1.5rem !importan=
t; }
  .my-lg-5 { margin-bottom: 3rem !important; margin-top: 3rem !important; }
  .my-lg-auto { margin-bottom: auto !important; margin-top: auto !important=
; }
  .mt-lg-0 { margin-top: 0px !important; }
  .mt-lg-1 { margin-top: 0.25rem !important; }
  .mt-lg-2 { margin-top: 0.5rem !important; }
  .mt-lg-3 { margin-top: 1rem !important; }
  .mt-lg-4 { margin-top: 1.5rem !important; }
  .mt-lg-5 { margin-top: 3rem !important; }
  .mt-lg-auto { margin-top: auto !important; }
  .me-lg-0 { margin-right: 0px !important; }
  .me-lg-1 { margin-right: 0.25rem !important; }
  .me-lg-2 { margin-right: 0.5rem !important; }
  .me-lg-3 { margin-right: 1rem !important; }
  .me-lg-4 { margin-right: 1.5rem !important; }
  .me-lg-5 { margin-right: 3rem !important; }
  .me-lg-auto { margin-right: auto !important; }
  .mb-lg-0 { margin-bottom: 0px !important; }
  .mb-lg-1 { margin-bottom: 0.25rem !important; }
  .mb-lg-2 { margin-bottom: 0.5rem !important; }
  .mb-lg-3 { margin-bottom: 1rem !important; }
  .mb-lg-4 { margin-bottom: 1.5rem !important; }
  .mb-lg-5 { margin-bottom: 3rem !important; }
  .mb-lg-auto { margin-bottom: auto !important; }
  .ms-lg-0 { margin-left: 0px !important; }
  .ms-lg-1 { margin-left: 0.25rem !important; }
  .ms-lg-2 { margin-left: 0.5rem !important; }
  .ms-lg-3 { margin-left: 1rem !important; }
  .ms-lg-4 { margin-left: 1.5rem !important; }
  .ms-lg-5 { margin-left: 3rem !important; }
  .ms-lg-auto { margin-left: auto !important; }
  .p-lg-0 { padding: 0px !important; }
  .p-lg-1 { padding: 0.25rem !important; }
  .p-lg-2 { padding: 0.5rem !important; }
  .p-lg-3 { padding: 1rem !important; }
  .p-lg-4 { padding: 1.5rem !important; }
  .p-lg-5 { padding: 3rem !important; }
  .px-lg-0 { padding-left: 0px !important; padding-right: 0px !important; }
  .px-lg-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !impo=
rtant; }
  .px-lg-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !import=
ant; }
  .px-lg-3 { padding-left: 1rem !important; padding-right: 1rem !important;=
 }
  .px-lg-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !import=
ant; }
  .px-lg-5 { padding-left: 3rem !important; padding-right: 3rem !important;=
 }
  .py-lg-0 { padding-bottom: 0px !important; padding-top: 0px !important; }
  .py-lg-1 { padding-bottom: 0.25rem !important; padding-top: 0.25rem !impo=
rtant; }
  .py-lg-2 { padding-bottom: 0.5rem !important; padding-top: 0.5rem !import=
ant; }
  .py-lg-3 { padding-bottom: 1rem !important; padding-top: 1rem !important;=
 }
  .py-lg-4 { padding-bottom: 1.5rem !important; padding-top: 1.5rem !import=
ant; }
  .py-lg-5 { padding-bottom: 3rem !important; padding-top: 3rem !important;=
 }
  .pt-lg-0 { padding-top: 0px !important; }
  .pt-lg-1 { padding-top: 0.25rem !important; }
  .pt-lg-2 { padding-top: 0.5rem !important; }
  .pt-lg-3 { padding-top: 1rem !important; }
  .pt-lg-4 { padding-top: 1.5rem !important; }
  .pt-lg-5 { padding-top: 3rem !important; }
  .pe-lg-0 { padding-right: 0px !important; }
  .pe-lg-1 { padding-right: 0.25rem !important; }
  .pe-lg-2 { padding-right: 0.5rem !important; }
  .pe-lg-3 { padding-right: 1rem !important; }
  .pe-lg-4 { padding-right: 1.5rem !important; }
  .pe-lg-5 { padding-right: 3rem !important; }
  .pb-lg-0 { padding-bottom: 0px !important; }
  .pb-lg-1 { padding-bottom: 0.25rem !important; }
  .pb-lg-2 { padding-bottom: 0.5rem !important; }
  .pb-lg-3 { padding-bottom: 1rem !important; }
  .pb-lg-4 { padding-bottom: 1.5rem !important; }
  .pb-lg-5 { padding-bottom: 3rem !important; }
  .ps-lg-0 { padding-left: 0px !important; }
  .ps-lg-1 { padding-left: 0.25rem !important; }
  .ps-lg-2 { padding-left: 0.5rem !important; }
  .ps-lg-3 { padding-left: 1rem !important; }
  .ps-lg-4 { padding-left: 1.5rem !important; }
  .ps-lg-5 { padding-left: 3rem !important; }
  .gap-lg-0 { gap: 0px !important; }
  .gap-lg-1 { gap: 0.25rem !important; }
  .gap-lg-2 { gap: 0.5rem !important; }
  .gap-lg-3 { gap: 1rem !important; }
  .gap-lg-4 { gap: 1.5rem !important; }
  .gap-lg-5 { gap: 3rem !important; }
  .row-gap-lg-0 { row-gap: 0px !important; }
  .row-gap-lg-1 { row-gap: 0.25rem !important; }
  .row-gap-lg-2 { row-gap: 0.5rem !important; }
  .row-gap-lg-3 { row-gap: 1rem !important; }
  .row-gap-lg-4 { row-gap: 1.5rem !important; }
  .row-gap-lg-5 { row-gap: 3rem !important; }
  .column-gap-lg-0 { column-gap: 0px !important; }
  .column-gap-lg-1 { column-gap: 0.25rem !important; }
  .column-gap-lg-2 { column-gap: 0.5rem !important; }
  .column-gap-lg-3 { column-gap: 1rem !important; }
  .column-gap-lg-4 { column-gap: 1.5rem !important; }
  .column-gap-lg-5 { column-gap: 3rem !important; }
  .text-lg-start { text-align: left !important; }
  .text-lg-end { text-align: right !important; }
  .text-lg-center { text-align: center !important; }
}

@media (min-width: 1200px) {
  .float-xl-start { float: left !important; }
  .float-xl-end { float: right !important; }
  .float-xl-none { float: none !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-block { display: block !important; }
  .d-xl-grid { display: grid !important; }
  .d-xl-inline-grid { display: inline-grid !important; }
  .d-xl-table { display: table !important; }
  .d-xl-table-row { display: table-row !important; }
  .d-xl-table-cell { display: table-cell !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline-flex { display: inline-flex !important; }
  .d-xl-none { display: none !important; }
  .flex-xl-fill { flex: 1 1 auto !important; }
  .flex-xl-row { flex-direction: row !important; }
  .flex-xl-column { flex-direction: column !important; }
  .flex-xl-row-reverse { flex-direction: row-reverse !important; }
  .flex-xl-column-reverse { flex-direction: column-reverse !important; }
  .flex-xl-grow-0 { flex-grow: 0 !important; }
  .flex-xl-grow-1 { flex-grow: 1 !important; }
  .flex-xl-shrink-0 { flex-shrink: 0 !important; }
  .flex-xl-shrink-1 { flex-shrink: 1 !important; }
  .flex-xl-wrap { flex-wrap: wrap !important; }
  .flex-xl-nowrap { flex-wrap: nowrap !important; }
  .flex-xl-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .justify-content-xl-start { justify-content: flex-start !important; }
  .justify-content-xl-end { justify-content: flex-end !important; }
  .justify-content-xl-center { justify-content: center !important; }
  .justify-content-xl-between { justify-content: space-between !important; =
}
  .justify-content-xl-around { justify-content: space-around !important; }
  .justify-content-xl-evenly { justify-content: space-evenly !important; }
  .align-items-xl-start { align-items: flex-start !important; }
  .align-items-xl-end { align-items: flex-end !important; }
  .align-items-xl-center { align-items: center !important; }
  .align-items-xl-baseline { align-items: baseline !important; }
  .align-items-xl-stretch { align-items: stretch !important; }
  .align-content-xl-start { align-content: flex-start !important; }
  .align-content-xl-end { align-content: flex-end !important; }
  .align-content-xl-center { align-content: center !important; }
  .align-content-xl-between { align-content: space-between !important; }
  .align-content-xl-around { align-content: space-around !important; }
  .align-content-xl-stretch { align-content: stretch !important; }
  .align-self-xl-auto { align-self: auto !important; }
  .align-self-xl-start { align-self: flex-start !important; }
  .align-self-xl-end { align-self: flex-end !important; }
  .align-self-xl-center { align-self: center !important; }
  .align-self-xl-baseline { align-self: baseline !important; }
  .align-self-xl-stretch { align-self: stretch !important; }
  .order-xl-first { order: -1 !important; }
  .order-xl-0 { order: 0 !important; }
  .order-xl-1 { order: 1 !important; }
  .order-xl-2 { order: 2 !important; }
  .order-xl-3 { order: 3 !important; }
  .order-xl-4 { order: 4 !important; }
  .order-xl-5 { order: 5 !important; }
  .order-xl-last { order: 6 !important; }
  .m-xl-0 { margin: 0px !important; }
  .m-xl-1 { margin: 0.25rem !important; }
  .m-xl-2 { margin: 0.5rem !important; }
  .m-xl-3 { margin: 1rem !important; }
  .m-xl-4 { margin: 1.5rem !important; }
  .m-xl-5 { margin: 3rem !important; }
  .m-xl-auto { margin: auto !important; }
  .mx-xl-0 { margin-left: 0px !important; margin-right: 0px !important; }
  .mx-xl-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !import=
ant; }
  .mx-xl-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !importan=
t; }
  .mx-xl-3 { margin-left: 1rem !important; margin-right: 1rem !important; }
  .mx-xl-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !importan=
t; }
  .mx-xl-5 { margin-left: 3rem !important; margin-right: 3rem !important; }
  .mx-xl-auto { margin-left: auto !important; margin-right: auto !important=
; }
  .my-xl-0 { margin-bottom: 0px !important; margin-top: 0px !important; }
  .my-xl-1 { margin-bottom: 0.25rem !important; margin-top: 0.25rem !import=
ant; }
  .my-xl-2 { margin-bottom: 0.5rem !important; margin-top: 0.5rem !importan=
t; }
  .my-xl-3 { margin-bottom: 1rem !important; margin-top: 1rem !important; }
  .my-xl-4 { margin-bottom: 1.5rem !important; margin-top: 1.5rem !importan=
t; }
  .my-xl-5 { margin-bottom: 3rem !important; margin-top: 3rem !important; }
  .my-xl-auto { margin-bottom: auto !important; margin-top: auto !important=
; }
  .mt-xl-0 { margin-top: 0px !important; }
  .mt-xl-1 { margin-top: 0.25rem !important; }
  .mt-xl-2 { margin-top: 0.5rem !important; }
  .mt-xl-3 { margin-top: 1rem !important; }
  .mt-xl-4 { margin-top: 1.5rem !important; }
  .mt-xl-5 { margin-top: 3rem !important; }
  .mt-xl-auto { margin-top: auto !important; }
  .me-xl-0 { margin-right: 0px !important; }
  .me-xl-1 { margin-right: 0.25rem !important; }
  .me-xl-2 { margin-right: 0.5rem !important; }
  .me-xl-3 { margin-right: 1rem !important; }
  .me-xl-4 { margin-right: 1.5rem !important; }
  .me-xl-5 { margin-right: 3rem !important; }
  .me-xl-auto { margin-right: auto !important; }
  .mb-xl-0 { margin-bottom: 0px !important; }
  .mb-xl-1 { margin-bottom: 0.25rem !important; }
  .mb-xl-2 { margin-bottom: 0.5rem !important; }
  .mb-xl-3 { margin-bottom: 1rem !important; }
  .mb-xl-4 { margin-bottom: 1.5rem !important; }
  .mb-xl-5 { margin-bottom: 3rem !important; }
  .mb-xl-auto { margin-bottom: auto !important; }
  .ms-xl-0 { margin-left: 0px !important; }
  .ms-xl-1 { margin-left: 0.25rem !important; }
  .ms-xl-2 { margin-left: 0.5rem !important; }
  .ms-xl-3 { margin-left: 1rem !important; }
  .ms-xl-4 { margin-left: 1.5rem !important; }
  .ms-xl-5 { margin-left: 3rem !important; }
  .ms-xl-auto { margin-left: auto !important; }
  .p-xl-0 { padding: 0px !important; }
  .p-xl-1 { padding: 0.25rem !important; }
  .p-xl-2 { padding: 0.5rem !important; }
  .p-xl-3 { padding: 1rem !important; }
  .p-xl-4 { padding: 1.5rem !important; }
  .p-xl-5 { padding: 3rem !important; }
  .px-xl-0 { padding-left: 0px !important; padding-right: 0px !important; }
  .px-xl-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !impo=
rtant; }
  .px-xl-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !import=
ant; }
  .px-xl-3 { padding-left: 1rem !important; padding-right: 1rem !important;=
 }
  .px-xl-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !import=
ant; }
  .px-xl-5 { padding-left: 3rem !important; padding-right: 3rem !important;=
 }
  .py-xl-0 { padding-bottom: 0px !important; padding-top: 0px !important; }
  .py-xl-1 { padding-bottom: 0.25rem !important; padding-top: 0.25rem !impo=
rtant; }
  .py-xl-2 { padding-bottom: 0.5rem !important; padding-top: 0.5rem !import=
ant; }
  .py-xl-3 { padding-bottom: 1rem !important; padding-top: 1rem !important;=
 }
  .py-xl-4 { padding-bottom: 1.5rem !important; padding-top: 1.5rem !import=
ant; }
  .py-xl-5 { padding-bottom: 3rem !important; padding-top: 3rem !important;=
 }
  .pt-xl-0 { padding-top: 0px !important; }
  .pt-xl-1 { padding-top: 0.25rem !important; }
  .pt-xl-2 { padding-top: 0.5rem !important; }
  .pt-xl-3 { padding-top: 1rem !important; }
  .pt-xl-4 { padding-top: 1.5rem !important; }
  .pt-xl-5 { padding-top: 3rem !important; }
  .pe-xl-0 { padding-right: 0px !important; }
  .pe-xl-1 { padding-right: 0.25rem !important; }
  .pe-xl-2 { padding-right: 0.5rem !important; }
  .pe-xl-3 { padding-right: 1rem !important; }
  .pe-xl-4 { padding-right: 1.5rem !important; }
  .pe-xl-5 { padding-right: 3rem !important; }
  .pb-xl-0 { padding-bottom: 0px !important; }
  .pb-xl-1 { padding-bottom: 0.25rem !important; }
  .pb-xl-2 { padding-bottom: 0.5rem !important; }
  .pb-xl-3 { padding-bottom: 1rem !important; }
  .pb-xl-4 { padding-bottom: 1.5rem !important; }
  .pb-xl-5 { padding-bottom: 3rem !important; }
  .ps-xl-0 { padding-left: 0px !important; }
  .ps-xl-1 { padding-left: 0.25rem !important; }
  .ps-xl-2 { padding-left: 0.5rem !important; }
  .ps-xl-3 { padding-left: 1rem !important; }
  .ps-xl-4 { padding-left: 1.5rem !important; }
  .ps-xl-5 { padding-left: 3rem !important; }
  .gap-xl-0 { gap: 0px !important; }
  .gap-xl-1 { gap: 0.25rem !important; }
  .gap-xl-2 { gap: 0.5rem !important; }
  .gap-xl-3 { gap: 1rem !important; }
  .gap-xl-4 { gap: 1.5rem !important; }
  .gap-xl-5 { gap: 3rem !important; }
  .row-gap-xl-0 { row-gap: 0px !important; }
  .row-gap-xl-1 { row-gap: 0.25rem !important; }
  .row-gap-xl-2 { row-gap: 0.5rem !important; }
  .row-gap-xl-3 { row-gap: 1rem !important; }
  .row-gap-xl-4 { row-gap: 1.5rem !important; }
  .row-gap-xl-5 { row-gap: 3rem !important; }
  .column-gap-xl-0 { column-gap: 0px !important; }
  .column-gap-xl-1 { column-gap: 0.25rem !important; }
  .column-gap-xl-2 { column-gap: 0.5rem !important; }
  .column-gap-xl-3 { column-gap: 1rem !important; }
  .column-gap-xl-4 { column-gap: 1.5rem !important; }
  .column-gap-xl-5 { column-gap: 3rem !important; }
  .text-xl-start { text-align: left !important; }
  .text-xl-end { text-align: right !important; }
  .text-xl-center { text-align: center !important; }
}

@media (min-width: 1400px) {
  .float-xxl-start { float: left !important; }
  .float-xxl-end { float: right !important; }
  .float-xxl-none { float: none !important; }
  .d-xxl-inline { display: inline !important; }
  .d-xxl-inline-block { display: inline-block !important; }
  .d-xxl-block { display: block !important; }
  .d-xxl-grid { display: grid !important; }
  .d-xxl-inline-grid { display: inline-grid !important; }
  .d-xxl-table { display: table !important; }
  .d-xxl-table-row { display: table-row !important; }
  .d-xxl-table-cell { display: table-cell !important; }
  .d-xxl-flex { display: flex !important; }
  .d-xxl-inline-flex { display: inline-flex !important; }
  .d-xxl-none { display: none !important; }
  .flex-xxl-fill { flex: 1 1 auto !important; }
  .flex-xxl-row { flex-direction: row !important; }
  .flex-xxl-column { flex-direction: column !important; }
  .flex-xxl-row-reverse { flex-direction: row-reverse !important; }
  .flex-xxl-column-reverse { flex-direction: column-reverse !important; }
  .flex-xxl-grow-0 { flex-grow: 0 !important; }
  .flex-xxl-grow-1 { flex-grow: 1 !important; }
  .flex-xxl-shrink-0 { flex-shrink: 0 !important; }
  .flex-xxl-shrink-1 { flex-shrink: 1 !important; }
  .flex-xxl-wrap { flex-wrap: wrap !important; }
  .flex-xxl-nowrap { flex-wrap: nowrap !important; }
  .flex-xxl-wrap-reverse { flex-wrap: wrap-reverse !important; }
  .justify-content-xxl-start { justify-content: flex-start !important; }
  .justify-content-xxl-end { justify-content: flex-end !important; }
  .justify-content-xxl-center { justify-content: center !important; }
  .justify-content-xxl-between { justify-content: space-between !important;=
 }
  .justify-content-xxl-around { justify-content: space-around !important; }
  .justify-content-xxl-evenly { justify-content: space-evenly !important; }
  .align-items-xxl-start { align-items: flex-start !important; }
  .align-items-xxl-end { align-items: flex-end !important; }
  .align-items-xxl-center { align-items: center !important; }
  .align-items-xxl-baseline { align-items: baseline !important; }
  .align-items-xxl-stretch { align-items: stretch !important; }
  .align-content-xxl-start { align-content: flex-start !important; }
  .align-content-xxl-end { align-content: flex-end !important; }
  .align-content-xxl-center { align-content: center !important; }
  .align-content-xxl-between { align-content: space-between !important; }
  .align-content-xxl-around { align-content: space-around !important; }
  .align-content-xxl-stretch { align-content: stretch !important; }
  .align-self-xxl-auto { align-self: auto !important; }
  .align-self-xxl-start { align-self: flex-start !important; }
  .align-self-xxl-end { align-self: flex-end !important; }
  .align-self-xxl-center { align-self: center !important; }
  .align-self-xxl-baseline { align-self: baseline !important; }
  .align-self-xxl-stretch { align-self: stretch !important; }
  .order-xxl-first { order: -1 !important; }
  .order-xxl-0 { order: 0 !important; }
  .order-xxl-1 { order: 1 !important; }
  .order-xxl-2 { order: 2 !important; }
  .order-xxl-3 { order: 3 !important; }
  .order-xxl-4 { order: 4 !important; }
  .order-xxl-5 { order: 5 !important; }
  .order-xxl-last { order: 6 !important; }
  .m-xxl-0 { margin: 0px !important; }
  .m-xxl-1 { margin: 0.25rem !important; }
  .m-xxl-2 { margin: 0.5rem !important; }
  .m-xxl-3 { margin: 1rem !important; }
  .m-xxl-4 { margin: 1.5rem !important; }
  .m-xxl-5 { margin: 3rem !important; }
  .m-xxl-auto { margin: auto !important; }
  .mx-xxl-0 { margin-left: 0px !important; margin-right: 0px !important; }
  .mx-xxl-1 { margin-left: 0.25rem !important; margin-right: 0.25rem !impor=
tant; }
  .mx-xxl-2 { margin-left: 0.5rem !important; margin-right: 0.5rem !importa=
nt; }
  .mx-xxl-3 { margin-left: 1rem !important; margin-right: 1rem !important; =
}
  .mx-xxl-4 { margin-left: 1.5rem !important; margin-right: 1.5rem !importa=
nt; }
  .mx-xxl-5 { margin-left: 3rem !important; margin-right: 3rem !important; =
}
  .mx-xxl-auto { margin-left: auto !important; margin-right: auto !importan=
t; }
  .my-xxl-0 { margin-bottom: 0px !important; margin-top: 0px !important; }
  .my-xxl-1 { margin-bottom: 0.25rem !important; margin-top: 0.25rem !impor=
tant; }
  .my-xxl-2 { margin-bottom: 0.5rem !important; margin-top: 0.5rem !importa=
nt; }
  .my-xxl-3 { margin-bottom: 1rem !important; margin-top: 1rem !important; =
}
  .my-xxl-4 { margin-bottom: 1.5rem !important; margin-top: 1.5rem !importa=
nt; }
  .my-xxl-5 { margin-bottom: 3rem !important; margin-top: 3rem !important; =
}
  .my-xxl-auto { margin-bottom: auto !important; margin-top: auto !importan=
t; }
  .mt-xxl-0 { margin-top: 0px !important; }
  .mt-xxl-1 { margin-top: 0.25rem !important; }
  .mt-xxl-2 { margin-top: 0.5rem !important; }
  .mt-xxl-3 { margin-top: 1rem !important; }
  .mt-xxl-4 { margin-top: 1.5rem !important; }
  .mt-xxl-5 { margin-top: 3rem !important; }
  .mt-xxl-auto { margin-top: auto !important; }
  .me-xxl-0 { margin-right: 0px !important; }
  .me-xxl-1 { margin-right: 0.25rem !important; }
  .me-xxl-2 { margin-right: 0.5rem !important; }
  .me-xxl-3 { margin-right: 1rem !important; }
  .me-xxl-4 { margin-right: 1.5rem !important; }
  .me-xxl-5 { margin-right: 3rem !important; }
  .me-xxl-auto { margin-right: auto !important; }
  .mb-xxl-0 { margin-bottom: 0px !important; }
  .mb-xxl-1 { margin-bottom: 0.25rem !important; }
  .mb-xxl-2 { margin-bottom: 0.5rem !important; }
  .mb-xxl-3 { margin-bottom: 1rem !important; }
  .mb-xxl-4 { margin-bottom: 1.5rem !important; }
  .mb-xxl-5 { margin-bottom: 3rem !important; }
  .mb-xxl-auto { margin-bottom: auto !important; }
  .ms-xxl-0 { margin-left: 0px !important; }
  .ms-xxl-1 { margin-left: 0.25rem !important; }
  .ms-xxl-2 { margin-left: 0.5rem !important; }
  .ms-xxl-3 { margin-left: 1rem !important; }
  .ms-xxl-4 { margin-left: 1.5rem !important; }
  .ms-xxl-5 { margin-left: 3rem !important; }
  .ms-xxl-auto { margin-left: auto !important; }
  .p-xxl-0 { padding: 0px !important; }
  .p-xxl-1 { padding: 0.25rem !important; }
  .p-xxl-2 { padding: 0.5rem !important; }
  .p-xxl-3 { padding: 1rem !important; }
  .p-xxl-4 { padding: 1.5rem !important; }
  .p-xxl-5 { padding: 3rem !important; }
  .px-xxl-0 { padding-left: 0px !important; padding-right: 0px !important; =
}
  .px-xxl-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !imp=
ortant; }
  .px-xxl-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !impor=
tant; }
  .px-xxl-3 { padding-left: 1rem !important; padding-right: 1rem !important=
; }
  .px-xxl-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !impor=
tant; }
  .px-xxl-5 { padding-left: 3rem !important; padding-right: 3rem !important=
; }
  .py-xxl-0 { padding-bottom: 0px !important; padding-top: 0px !important; =
}
  .py-xxl-1 { padding-bottom: 0.25rem !important; padding-top: 0.25rem !imp=
ortant; }
  .py-xxl-2 { padding-bottom: 0.5rem !important; padding-top: 0.5rem !impor=
tant; }
  .py-xxl-3 { padding-bottom: 1rem !important; padding-top: 1rem !important=
; }
  .py-xxl-4 { padding-bottom: 1.5rem !important; padding-top: 1.5rem !impor=
tant; }
  .py-xxl-5 { padding-bottom: 3rem !important; padding-top: 3rem !important=
; }
  .pt-xxl-0 { padding-top: 0px !important; }
  .pt-xxl-1 { padding-top: 0.25rem !important; }
  .pt-xxl-2 { padding-top: 0.5rem !important; }
  .pt-xxl-3 { padding-top: 1rem !important; }
  .pt-xxl-4 { padding-top: 1.5rem !important; }
  .pt-xxl-5 { padding-top: 3rem !important; }
  .pe-xxl-0 { padding-right: 0px !important; }
  .pe-xxl-1 { padding-right: 0.25rem !important; }
  .pe-xxl-2 { padding-right: 0.5rem !important; }
  .pe-xxl-3 { padding-right: 1rem !important; }
  .pe-xxl-4 { padding-right: 1.5rem !important; }
  .pe-xxl-5 { padding-right: 3rem !important; }
  .pb-xxl-0 { padding-bottom: 0px !important; }
  .pb-xxl-1 { padding-bottom: 0.25rem !important; }
  .pb-xxl-2 { padding-bottom: 0.5rem !important; }
  .pb-xxl-3 { padding-bottom: 1rem !important; }
  .pb-xxl-4 { padding-bottom: 1.5rem !important; }
  .pb-xxl-5 { padding-bottom: 3rem !important; }
  .ps-xxl-0 { padding-left: 0px !important; }
  .ps-xxl-1 { padding-left: 0.25rem !important; }
  .ps-xxl-2 { padding-left: 0.5rem !important; }
  .ps-xxl-3 { padding-left: 1rem !important; }
  .ps-xxl-4 { padding-left: 1.5rem !important; }
  .ps-xxl-5 { padding-left: 3rem !important; }
  .gap-xxl-0 { gap: 0px !important; }
  .gap-xxl-1 { gap: 0.25rem !important; }
  .gap-xxl-2 { gap: 0.5rem !important; }
  .gap-xxl-3 { gap: 1rem !important; }
  .gap-xxl-4 { gap: 1.5rem !important; }
  .gap-xxl-5 { gap: 3rem !important; }
  .row-gap-xxl-0 { row-gap: 0px !important; }
  .row-gap-xxl-1 { row-gap: 0.25rem !important; }
  .row-gap-xxl-2 { row-gap: 0.5rem !important; }
  .row-gap-xxl-3 { row-gap: 1rem !important; }
  .row-gap-xxl-4 { row-gap: 1.5rem !important; }
  .row-gap-xxl-5 { row-gap: 3rem !important; }
  .column-gap-xxl-0 { column-gap: 0px !important; }
  .column-gap-xxl-1 { column-gap: 0.25rem !important; }
  .column-gap-xxl-2 { column-gap: 0.5rem !important; }
  .column-gap-xxl-3 { column-gap: 1rem !important; }
  .column-gap-xxl-4 { column-gap: 1.5rem !important; }
  .column-gap-xxl-5 { column-gap: 3rem !important; }
  .text-xxl-start { text-align: left !important; }
  .text-xxl-end { text-align: right !important; }
  .text-xxl-center { text-align: center !important; }
}

*, ::after, ::before { box-sizing: border-box; }

p { margin-bottom: 1rem; margin-top: 0px; }

picture { display: flex; justify-content: center; }

picture img { display: flex; max-width: 100%; object-fit: contain; }

body.scroll-lock { overflow: hidden; }

.slide-fade-enter-active { transition: 0.4s; }

.slide-fade-leave-active { transition: 0.4s cubic-bezier(1, 0.5, 0.8, 1); }

.slide-fade-enter, .slide-fade-leave-to { opacity: 0; transform: translateY=
(20px); }

.fade-enter-active, .fade-leave-active { transition: 0.1s cubic-bezier(1, 0=
.5, 0.8, 1); }

.fade-enter, .fade-leave-to { opacity: 0; }

body { background: rgb(245, 246, 246); color: rgb(61, 70, 82); font-family:=
 "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 1rem; line-heig=
ht: 1.5; margin: 0px; text-size-adjust: 100%; -webkit-tap-highlight-color: =
rgba(0, 0, 0, 0); }

h1, h2, h3, h4, h5 { color: rgb(61, 70, 82); font-weight: 700; line-height:=
 1.2; margin-bottom: 0.5rem; margin-top: 0px; }

.h1, h1 { font-size: calc(1.375rem + 1.5vw); }

@media (min-width: 1200px) {
  .h1, h1 { font-size: 2.5rem; }
}

.h2, h2 { font-size: calc(1.325rem + 0.9vw); }

@media (min-width: 1200px) {
  .h2, h2 { font-size: 2rem; }
}

.h3, h3 { font-size: calc(1.3rem + 0.6vw); }

@media (min-width: 1200px) {
  .h3, h3 { font-size: 1.75rem; }
}

.h4, h4 { font-size: calc(1.275rem + 0.3vw); }

@media (min-width: 1200px) {
  .h4, h4 { font-size: 1.5rem; }
}

.h5, h5 { font-size: 1.25rem; }

.h6, h6 { font-size: 1rem; }

.container { margin-left: auto; margin-right: auto; max-width: 1280px; padd=
ing-left: 15px; padding-right: 15px; width: 100%; }

a { color: rgb(99, 150, 198); text-decoration: none; }

input { appearance: none; }

input:focus-visible { outline: none; }

.question__textarea::after, input, input[type=3D"text"], select, textarea {=
 border: 1px solid rgb(224, 234, 244); border-radius: 0.3rem; font-size: 16=
px; padding: 0.375rem 0.75rem; }

.button, button { background: transparent; border: none; border-radius: 0.3=
rem; color: rgb(61, 70, 82); font-size: 16px; font-weight: 700; line-height=
: inherit; padding: calc(1px + 0.5rem) 1rem; transition: 0.55s cubic-bezier=
(0.19, 1, 0.22, 1); white-space: nowrap; }

.button:focus, button:focus { outline: none; }

.button.primary, button.primary { background-color: rgb(68, 131, 255); colo=
r: rgb(255, 255, 255); }

.button.primary:hover, button.primary:hover { background-color: rgb(17, 97,=
 255); }

.button.red, button.red { background-color: rgb(243, 91, 58); color: rgb(25=
5, 255, 255); }

.button.red:hover, button.red:hover { background-color: rgb(236, 54, 14); }

.button.secondary, button.secondary { background-color: rgb(243, 91, 58); c=
olor: rgb(255, 255, 255); }

.button.secondary:hover, button.secondary:hover { background-color: rgb(0, =
0, 0); }

.button.outline-primary, button.outline-primary { border: 1px solid rgb(68,=
 131, 255); color: rgb(68, 131, 255); padding-left: 1rem; padding-right: 1r=
em; }

.button.outline-primary:hover, button.outline-primary:hover { background: r=
gb(68, 131, 255); color: rgb(255, 255, 255); text-decoration: none; }

.button.outline-primary.lg, button.outline-primary.lg { border-radius: calc=
(1px + 1.75rem); }

.button.outline-dark, button.outline-dark { border: 1px solid rgb(61, 70, 8=
2); border-radius: calc(17px + 0.375rem); color: rgb(61, 70, 82); }

.button.outline-dark:hover, button.outline-dark:hover { background: rgb(61,=
 70, 82); color: rgb(255, 255, 255); }

.button.block, button.block { width: 100%; }

.button.link, button.link { color: rgb(99, 150, 198); font-weight: 300; tex=
t-decoration: underline; }

.button.lg, button.lg { font-size: 1.25rem; line-height: 1.5; padding: 0.5r=
em 1rem; }

.button.sm, button.sm { font-size: 0.875rem; }

.button:disabled, button:disabled { opacity: 0.6; }

.button.block:hover, button.block:hover { box-shadow: rgba(61, 70, 82, 0.3)=
 0px 4px 20px; transform: scale(1.04); }

.button.block:active, button.block:active { box-shadow: rgba(61, 70, 82, 0.=
3) 0px 4px 20px; transform: scale(1); }

.dropdown { background: rgb(255, 255, 255); border: 1px solid rgb(237, 237,=
 237); border-radius: 0.5rem; box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 20px =
7px; min-width: 100px; position: absolute; top: calc(100% + 4px); z-index: =
1400; }

.dropdown li.active { background: rgb(237, 237, 237); font-weight: 700; }

.dropdown li:hover { background: rgb(237, 237, 237); }

.float-left { float: left; }

.float-right { float: right; }

.clearfix::after { clear: both; content: ""; display: block; }

.text-dark { color: rgb(61, 70, 82); }

.text-truncate { overflow: hidden; text-overflow: ellipsis; white-space: no=
wrap; }

.w-100 { width: 100%; }

.alert { border-radius: 0.5rem; padding: 1rem; }

.alert-warning { background: rgb(255, 244, 211); border: 1px solid rgb(255,=
 193, 7); color: rgb(58, 44, 0); }

.alert-success { background: rgb(175, 240, 210); border: 1px solid rgb(89, =
224, 161); color: rgb(17, 92, 57); }

.alert ul { margin: 0px; }

.v-popper { display: block !important; z-index: 10000; }

.v-popper__inner { background: rgb(73, 80, 87); border-radius: 0.5rem; colo=
r: rgb(255, 255, 255); max-width: 300px; padding: 0.5rem; }

.v-popper .tooltip-arrow { border-color: rgb(73, 80, 87); border-style: sol=
id; height: 0px; margin: 5px; position: absolute; width: 0px; z-index: 1; }

.v-popper[x-placement^=3D"top"] { margin-bottom: 5px; }

.v-popper[x-placement^=3D"top"] .tooltip-arrow { border-width: 5px 5px 0px;=
 bottom: -5px; left: calc(50% - 5px); margin-bottom: 0px; margin-top: 0px; =
border-bottom-color: transparent !important; border-left-color: transparent=
 !important; border-right-color: transparent !important; }

.v-popper[x-placement^=3D"bottom"] { margin-top: 5px; }

.v-popper[x-placement^=3D"bottom"] .tooltip-arrow { border-width: 0px 5px 5=
px; left: calc(50% - 5px); margin-bottom: 0px; margin-top: 0px; top: -5px; =
border-left-color: transparent !important; border-right-color: transparent =
!important; border-top-color: transparent !important; }

.v-popper[x-placement^=3D"right"] { margin-left: 5px; }

.v-popper[x-placement^=3D"right"] .tooltip-arrow { border-width: 5px 5px 5p=
x 0px; left: -5px; margin-left: 0px; margin-right: 0px; top: calc(50% - 5px=
); border-bottom-color: transparent !important; border-left-color: transpar=
ent !important; border-top-color: transparent !important; }

.v-popper[x-placement^=3D"left"] { margin-right: 5px; }

.v-popper[x-placement^=3D"left"] .tooltip-arrow { border-width: 5px 0px 5px=
 5px; margin-left: 0px; margin-right: 0px; right: -5px; top: calc(50% - 5px=
); border-bottom-color: transparent !important; border-right-color: transpa=
rent !important; border-top-color: transparent !important; }

.v-popper[aria-hidden=3D"true"] { opacity: 0; transition: opacity 0.15s, vi=
sibility 0.15s; visibility: hidden; }

.v-popper[aria-hidden=3D"false"] { opacity: 1; transition: opacity 0.15s; v=
isibility: visible; }

.border { border: 1px solid rgb(224, 234, 244); }

.border-left { border-left: 1px solid rgb(224, 234, 244); }

.text-muted { color: rgb(173, 181, 189); }

.text-center { text-align: center; }

.mw-100 { max-width: 100%; }

.qc-cmp2-footer .qc-cmp2-summary-buttons > button, .qc-usp-ui-form-content =
> button { border: 0px; border-radius: 0.5rem; box-shadow: none; outline: n=
one; }

.qc-cmp2-footer .qc-cmp2-summary-buttons > button:first-of-type, .qc-usp-ui=
-form-content > button:first-of-type { background-color: rgb(237, 237, 237)=
 !important; color: gray !important; }

.qc-cmp2-footer .qc-cmp2-summary-buttons > button:last-of-type, .qc-usp-ui-=
form-content > button:last-of-type { min-height: 40px; background-color: rg=
b(75, 192, 82) !important; color: rgb(255, 255, 255) !important; }

.qc-cmp2-footer .qc-cmp2-summary-buttons > button:last-of-type:hover, .qc-u=
sp-ui-form-content > button:last-of-type:hover { background-color: rgb(56, =
160, 62) !important; }

.fc-consent-root .fc-secondary-button p.fc-button-label { color: gray !impo=
rtant; }

.fc-consent-root .fc-secondary-button div.fc-button-background { background=
-color: rgb(255, 255, 255) !important; }

.fc-consent-root .fc-secondary-button div.fc-button-background:hover { back=
ground-color: rgb(212, 212, 212) !important; }

.fc-consent-root .fc-primary-button div.fc-button-background { background-c=
olor: rgb(75, 192, 82) !important; }

.fc-consent-root .fc-primary-button div.fc-button-background:hover { backgr=
ound-color: rgb(56, 160, 62) !important; }

.fc-consent-root div.fc-header-image-container { display: none !important; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable

<html><head><meta http-equiv=3D"Content-Type" content=3D"text/html; charset=
=3DUTF-8"><link rel=3D"stylesheet" type=3D"text/css" href=3D"cid:css-5890d1=
<EMAIL>" /></head><body><div class=3D"vi=
ewer-page"><!----><div style=3D"transform: scale(2.21429);" class=3D"page-4=
 pf w0 h0" data-v-dd20de80=3D""><div id=3D"pf4" class=3D"pf w0 h0" data-pag=
e-no=3D"4"><div class=3D"pc pc4 w0 h0"><div class=3D"bi x0 y0 w1 h1" alt=3D=
"" style=3D"background: url('/viewer/14/2827814/4/bg4.webp'); background-si=
ze: contain;"></div><div class=3D"t m0 x1 h2 y1 ff1 fs0 fc0 sc0 ls0 ws0">4 =
</div><div class=3D"t m1 x2 h3 y2 ff1 fs1 fc0 sc0 ls0 ws0">WARNING<span cla=
ss=3D"_ _0"></span> =E2=80=94 Refrigerators contain refrigerant and gases i=
n the insulation. </div><div class=3D"t m1 x2 h3 y3 ff1 fs1 fc0 sc0 ls0 ws0=
">Refrigerant and gases must be disposed of profession<span class=3D"_ _1">=
</span>ally as they may cause </div><div class=3D"t m1 x2 h3 y4 ff1 fs1 fc0=
 sc0 ls0 ws0">eye injuries or ignition.Ensure that tubing of the refrigeran=
t circuit <span class=3D"_ _1"></span>is not </div><div class=3D"t m1 x2 h3=
 y5 ff1 fs1 fc0 sc0 ls0 ws0">damage prior to proper disposal. </div><div cl=
ass=3D"t m2 x3 h4 y6 ff1 fs2 fc0 sc0 ls0 ws0"> </div><h2 class=3D"t m3 x3 h=
5 y7 ff2 fs3 fc0 sc0 ls0 ws0">WARNING: <span class=3D"fc1">Risk of fire/fla=
mmable materials<span class=3D"fc0"> </span></span></h2><div class=3D"t m1 =
x2 h3 y8 ff1 fs1 fc0 sc0 ls0 ws0">If the refrigerant circuit should be dama=
ged: </div><div class=3D"t m1 x2 h3 y9 ff1 fs1 fc0 sc0 ls0 ws0">-Avoid open=
ing flames and sources of ignition. </div><div class=3D"t m1 x2 h3 ya ff1 f=
s1 fc0 sc0 ls0 ws0">-Thoroughly ventilate the room in which the appliance i=
s situated. </div><div class=3D"t m1 x2 h3 yb ff1 fs1 fc0 sc0 ls0 ws0">It i=
s dangerous to alter the specifications or modify this product <span class=
=3D"_ _1"></span>in any way<span class=3D"_ _2"></span>. </div><div class=
=3D"t m1 x2 h3 yc ff1 fs1 fc0 sc0 ls0 ws0">Any damage to the cord may cause=
 a short circuit, fire, <span class=3D"_ _1"></span>and/or electric shock.<=
/div><div class=3D"t m3 x4 h5 yc ff2 fs3 fc0 sc0 ls0 ws0"> </div><div class=
=3D"t m1 x3 h3 yd ff1 fs1 fc0 sc0 ls0 ws0"> </div><h2 class=3D"t m3 x3 h5 y=
e ff2 fs3 fc0 sc0 ls0 ws0">Electrical safety </h2><div class=3D"t m1 x2 h3 =
yf ff1 fs1 fc0 sc0 ls0 ws0">1.The power cord must not be lengthened. </div>=
<div class=3D"t m1 x2 h3 y10 ff1 fs1 fc0 sc0 ls0 ws0">2. Make sure that the=
 power plug is not crushed o<span class=3D"_ _1"></span>r damaged. <span cl=
ass=3D"_ _0"></span>A<span class=3D"_ _2"></span> crushed or </div><div cla=
ss=3D"t m1 x2 h3 y11 ff1 fs1 fc0 sc0 ls0 ws0">damaged power plug may overhe=
at and cause a<span class=3D"_ _1"></span> fire. </div><div class=3D"t m1 x=
2 h3 y12 ff1 fs1 fc0 sc0 ls0 ws0">3. Make sure that you can access the main=
 plug of the appliance. </div><div class=3D"t m1 x2 h3 y13 ff1 fs1 fc0 sc0 =
ls0 ws0">4. Do not pull the main cable. </div><div class=3D"t m1 x2 h3 y14 =
ff1 fs1 fc0 sc0 ls0 ws0">5. If <span class=3D"_ _0"></span>the <span class=
=3D"_ _0"></span>power <span class=3D"_ _0"></span>plug <span class=3D"_ _0=
"></span>socket <span class=3D"_ _0"></span>is <span class=3D"_ _0"></span>=
loose, do <span class=3D"_ _0"></span>not <span class=3D"_ _0"></span>inser=
t <span class=3D"_ _0"></span>the <span class=3D"_ _0"></span>power <span c=
lass=3D"_ _0"></span>plug. <span class=3D"_ _0"></span>There is <span class=
=3D"_ _0"></span>a <span class=3D"_ _0"></span>risk </div><div class=3D"t m=
1 x2 h3 y15 ff1 fs1 fc0 sc0 ls0 ws0">of electric shock or fire. </div><div =
class=3D"t m1 x2 h3 y16 ff1 fs1 fc0 sc0 ls0 ws0">6.Y<span class=3D"_ _2"></=
span>ou must not operate the appliance without the<span class=3D"_ _1"></sp=
an> interior lighting lamp cover<span class=3D"_ _3"></span>. </div><div cl=
ass=3D"t m1 x2 h3 y17 ff1 fs1 fc0 sc0 ls0 ws0">7.The fridge is only applied=
 with power supply of sin<span class=3D"_ _1"></span>gle phase alternating =
</div><div class=3D"t m1 x2 h3 y18 ff1 fs1 fc0 sc0 ls0 ws0">current of 220~=
240V/50Hz. If fluctuation of voltage in the district of user is so </div><d=
iv class=3D"t m1 x2 h3 y19 ff1 fs1 fc0 sc0 ls0 ws0">large that the voltage =
exceeds the above scope, for safety sake, be sure to </div><div class=3D"t =
m1 x2 h3 y1a ff1 fs1 fc0 sc0 ls0 ws0">apply <span class=3D"_ _3"></span>A.C=
. <span class=3D"_ _3"></span>Automa<span class=3D"_ _1"></span>tic voltage=
 regulator of more than 350W to the fridge. The </div><div class=3D"t m1 x2=
 h3 y1b ff1 fs1 fc0 sc0 ls0 ws0">fridge must employ a special power socket =
instead of <span class=3D"_ _1"></span>common one with other </div><div cla=
ss=3D"t m1 x2 h3 y1c ff1 fs1 fc0 sc0 ls0 ws0">electric appliances. Its plug=
 must match the socket with ground wire. </div><h2 class=3D"t m3 x2 h5 y1d =
ff2 fs3 fc0 sc0 ls0 ws0">Daily use<span class=3D"ff3"> </span></h2><div cla=
ss=3D"t m1 x2 h3 y1e ff4 fs1 fc0 sc0 ls0 ws0">=EE=98=81<span class=3D"ff1">=
 <span class=3D"_ _4"> </span>Do not st<span class=3D"_ _0"></span>ore flam=
mable g<span class=3D"_ _3"></span>a<span class=3D"_ _1"></span>s or <span =
class=3D"_ _3"></span>liquids in the appliance, <span class=3D"_ _3"></span=
>T<span class=3D"_ _1"></span>here is <span class=3D"_ _3"></span>a risk of=
 a<span class=3D"_ _0"></span>n </span></div><div class=3D"t m1 x5 h3 y1f f=
f1 fs1 fc0 sc0 ls0 ws0">explosion. </div><div class=3D"t m1 x2 h6 y20 ff4 f=
s1 fc0 sc0 ls0 ws0">=EE=98=81<span class=3D"ff1"> <span class=3D"_ _4"> </s=
pan>Do not operate any electrical appliances in the applian<span class=3D"_=
 _1"></span>ce (e<span class=3D"ff5">.</span>g. electric ice </span></div><=
div class=3D"t m1 x5 h3 y21 ff1 fs1 fc0 sc0 ls0 ws0">cream makers, mixers e=
tc.). <span class=3D"_ _5"> </span> </div><div class=3D"t m1 x2 h3 y22 ff4 =
fs1 fc0 sc0 ls0 ws0">=EE=98=81<span class=3D"ff1"> <span class=3D"_ _4"> </=
span>When unplugging always pull the plug from the mains so<span class=3D"_=
 _1"></span>cket, do not pull </span></div><div class=3D"t m1 x5 h3 y23 ff1=
 fs1 fc0 sc0 ls0 ws0">on the cable. </div><div class=3D"t m1 x2 h3 y24 ff4 =
fs1 fc0 sc0 ls0 ws0">=EE=98=81<span class=3D"ff1"> <span class=3D"_ _4"> </=
span>Do not place hot items near the plastic components of this appliance. =
</span></div><div class=3D"t m1 x2 h3 y25 ff4 fs1 fc0 sc0 ls0 ws0">=EE=98=
=81<span class=3D"ff1"> <span class=3D"_ _4"> </span>Do not place food prod=
ucts directly against the air outlet on the <span class=3D"_ _1"></span>rea=
r wall. </span></div><div class=3D"t m1 x2 h3 y26 ff4 fs1 fc0 sc0 ls0 ws0">=
=EE=98=81<span class=3D"ff1"> <span class=3D"_ _4"> </span>Store pre-packed=
 frozen food in accordance with the frozen food </span></div><div class=3D"=
t m1 x5 h3 y27 ff1 fs1 fc0 sc0 ls0 ws0">manufacture=E2=80=99s instructions.=
 </div><div class=3D"t m1 x2 h3 y28 ff4 fs1 fc0 sc0 ls0 ws0">=EE=98=81<span=
 class=3D"ff1"> <span class=3D"_ _4"> </span>The appliances manufactures st=
orage recommendat<span class=3D"_ _1"></span>ions should be strictly </span=
></div><div class=3D"t m1 x5 h3 y29 ff1 fs1 fc0 sc0 ls0 ws0">adhered to. Re=
fer to relevant instructions for storage. </div><div class=3D"t m1 x2 h3 y2=
a ff4 fs1 fc0 sc0 ls0 ws0">=EE=98=81<span class=3D"ff1"> <span class=3D"_ _=
4"> </span>Do not place carbonated or fizzy drinks in the freezer compartme=
nt as it </span></div><h3 class=3D"t m4 x6 h7 y2b ff6 fs4 fc2 sc0 ls0 ws0">=
a Gas Work</h3><h3 class=3D"t m4 x7 h7 y2c ff6 fs4 fc2 sc0 ls0 ws0">this ap=
pliance.</h3><h3 class=3D"t m4 x8 h7 y2d ff6 fs4 fc2 sc0 ls0 ws0"> It is ha=
zardous for</h3><h3 class=3D"t m4 x9 h7 y2e ff6 fs4 fc2 sc0 ls0 ws0">involv=
e removal of covers.  </h3><h3 class=3D"t m4 xa h7 y2f ff6 fs4 fc2 sc0 ls1 =
ws0">Authorization for </h3><h3 class=3D"t m4 xb h7 y30 ff6 fs4 fc2 sc0 ls1=
 ws0">hydrocarbon refrigerants</h3><div class=3D"t m4 xc h7 y31 ff6 fs4 fc2=
 sc0 ls0 ws0">In</div><h3 class=3D"t m4 xd h7 y32 ff6 fs4 fc2 sc0 ls0 ws0">=
       </h3><h3 class=3D"t m4 xe h7 y31 ff6 fs4 fc2 sc0 ls0 ws0">Authorized=
   Queensland  the</h3><h3 class=3D"t m4 xf h7 y33 ff6 fs4 fc2 sc0 ls0 ws0"=
>or   repairs   which</h3><h3 class=3D"t m4 x10 h7 y34 ff6 fs4 fc2 sc0 ls0 =
ws0">to carry out   servicing </h3><h3 class=3D"t m4 x8 h7 y35 ff6 fs4 fc2 =
sc0 ls0 ws0"> to service</h3><h3 class=3D"t m4 x11 h7 y36 ff6 fs4 fc2 sc0 l=
s0 ws0">anyone  other than  an  Authorized Service Person</h3><h3 class=3D"=
t m4 x10 h7 y37 ff6 fs4 fc2 sc0 ls0 ws0">MUST hold </h3><h3 class=3D"t m4 x=
12 h7 y38 ff6 fs4 fc2 sc0 ls0 ws0">Service<span class=3D"_"> </span>Person =
</h3></div><div class=3D"pi" data-data=3D"{&quot;ctm&quot;:[1.000000,0.0000=
00,0.000000,1.000000,0.000000,0.795609]}"></div></div>
</div><div class=3D"zoom" data-v-dd20de80=3D"" data-v-ac8a74d0=3D""><!---->=
</div></div><link rel=3D"stylesheet" href=3D"https://www.usermanuals.au/css=
/base.css"></body></html>
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.usermanuals.au/css/base.css

@charset "utf-8";

#sidebar { position: absolute; top: 0px; left: 0px; bottom: 0px; width: 250=
px; padding: 0px; margin: 0px; overflow: auto; }

#page-container { position: absolute; top: 0px; left: 0px; margin: 0px; bor=
der: 0px; padding: 0px !important; }

#page-container, #page-container > * { }

@media screen {
  #sidebar.opened + #page-container { left: 250px; }
  #page-container { bottom: 0px; right: 0px; overflow: auto; }
  .loading-indicator { display: none; }
  .loading-indicator.active { display: block; position: absolute; top: 50%;=
 left: 50%; margin-top: -32px; margin-left: -32px; width: 32px; height: 32p=
x; background: url("/images/loading.gif"); }
  .loading-indicator img { position: absolute; inset: 0px; }
}

@media print {
  @page { margin: 0px; }
  html { margin: 0px; }
  body { margin: 0px; -webkit-print-color-adjust: exact; }
  #sidebar { display: none; }
  #page-container { width: auto; height: auto; overflow: visible; backgroun=
d-color: transparent; }
  .d { display: none; }
}

.pf { position: relative; background-color: white; overflow: hidden; margin=
: 0px; border: 0px; transform: none !important; }

.pc { position: absolute; border: 0px; padding: 0px; margin: 0px; top: 0px;=
 left: 0px; width: 100%; height: 100%; overflow: hidden; display: block; tr=
ansform-origin: 0% 0%; }

.pc.opened { display: block; }

.bf { position: absolute; border: 0px; margin: 0px; top: 0px; bottom: 0px; =
width: 100%; height: 100%; user-select: none; }

.bi { position: absolute; border: 0px; margin: 0px; user-select: none; }

@media print {
  .pf { margin: 0px; box-shadow: none; break-after: page; break-inside: avo=
id; }
}

.c { position: absolute; border: 0px; padding: 0px; margin: 0px; overflow: =
hidden; display: block; }

.t { position: absolute; white-space: pre; font-size: 1px; transform-origin=
: 0% 100%; unicode-bidi: bidi-override; }

._ { color: transparent; z-index: -1; }

.pi { display: none; }

.l { }

.d { position: absolute; transform-origin: 0% 100%; }

.h1, h1, .h2, h2, .h3, h3, .h4, h4, .h5, h5, .h6, h6 { margin-bottom: auto;=
 }

figure { margin: 0px; }
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: image/webp
Content-Transfer-Encoding: base64
Content-Location: https://www.usermanuals.au/viewer/14/2827814/4/bg4.webp
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------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.viewer-page .ff0 { font-family: sans-serif; visibility: hidden; }

@font-face { font-family: ff1; font-display: swap; src: url("/viewer/14/282=
7814/4/f1.woff") format("woff"); }

.viewer-page .ff1 { font-family: ff1; line-height: 0.938965; font-style: no=
rmal; font-weight: normal; visibility: visible; }

@font-face { font-family: ff2; font-display: swap; src: url("/viewer/14/282=
7814/4/f2.woff") format("woff"); }

.viewer-page .ff2 { font-family: ff2; line-height: 0.938477; font-style: no=
rmal; font-weight: normal; visibility: visible; }

@font-face { font-family: ff3; font-display: swap; src: url("/viewer/14/282=
7814/4/f3.woff") format("woff"); }

.viewer-page .ff3 { font-family: ff3; line-height: 0.666504; font-style: no=
rmal; font-weight: normal; visibility: visible; }

@font-face { font-family: ff4; font-display: swap; src: url("/viewer/14/282=
7814/4/f4.woff") format("woff"); }

.viewer-page .ff4 { font-family: ff4; line-height: 0.722656; font-style: no=
rmal; font-weight: normal; visibility: visible; }

@font-face { font-family: ff5; font-display: swap; src: url("/viewer/14/282=
7814/4/f5.woff") format("woff"); }

.viewer-page .ff5 { font-family: ff5; line-height: 0.929688; font-style: no=
rmal; font-weight: normal; visibility: visible; }

@font-face { font-family: ff6; font-display: swap; src: url("/viewer/14/282=
7814/4/f6.woff") format("woff"); }

.viewer-page .ff6 { font-family: ff6; line-height: 0.94; font-style: normal=
; font-weight: normal; visibility: visible; }

.viewer-page .m1 { transform: matrix(0.249853, 0, 0, 0.25, 0, 0); }

.viewer-page .m2 { transform: matrix(0.249855, 0, 0, 0.25, 0, 0); }

.viewer-page .m0 { transform: matrix(0.249857, 0, 0, 0.25, 0, 0); }

.viewer-page .m3 { transform: matrix(0.24986, 0, 0, 0.25, 0, 0); }

.viewer-page .m4 { transform: matrix(0.25, 0, 0, 0.25, 0, 0); }

.viewer-page .v0 { vertical-align: 0px; }

.viewer-page .ls0 { letter-spacing: 0px; }

.viewer-page .ls1 { letter-spacing: 0.002228px; }

.viewer-page .sc_ { text-shadow: none; }

.viewer-page .sc0 { text-shadow: transparent -0.015em 0px, transparent 0px =
0.015em, transparent 0.015em 0px, transparent 0px -0.015em; }

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .viewer-page .sc_ { -webkit-text-stroke: 0px transparent; }
  .viewer-page .sc0 { -webkit-text-stroke: 0.015em transparent; text-shadow=
: none; }
}

.viewer-page .ws0 { word-spacing: 0px; }

.viewer-page ._7 { margin-left: -475.126px; }

.viewer-page ._9 { margin-left: -15.5567px; }

.viewer-page ._a { margin-left: -12.445px; }

.viewer-page ._8 { margin-left: -9.33373px; }

.viewer-page ._2 { margin-left: -3.24448px; }

.viewer-page ._3 { margin-left: -2.08141px; }

.viewer-page ._0 { margin-left: -1.02125px; }

.viewer-page ._1 { width: 1.13944px; }

.viewer-page ._5 { width: 7.60769px; }

.viewer-page ._b { width: 8.71779px; }

.viewer-page ._4 { width: 24.5781px; }

.viewer-page ._6 { width: 243.85px; }

.viewer-page .fc2 { color: rgb(229, 31, 31); }

.viewer-page .fc1 { color: rgb(255, 0, 0); }

.viewer-page .fc0 { color: rgb(0, 0, 0); }

.viewer-page .fs0 { font-size: 25.3836px; }

.viewer-page .fs2 { font-size: 29.7834px; }

.viewer-page .fs1 { font-size: 33.8448px; }

.viewer-page .fs4 { font-size: 36.1991px; }

.viewer-page .fs3 { font-size: 39.5984px; }

.viewer-page .y0 { bottom: 0px; }

.viewer-page .y2a { bottom: 45.358px; }

.viewer-page .y29 { bottom: 56.3576px; }

.viewer-page .y28 { bottom: 67.3571px; }

.viewer-page .y27 { bottom: 78.3567px; }

.viewer-page .y26 { bottom: 89.3563px; }

.viewer-page .y25 { bottom: 100.356px; }

.viewer-page .y24 { bottom: 111.355px; }

.viewer-page .y23 { bottom: 122.355px; }

.viewer-page .y22 { bottom: 133.355px; }

.viewer-page .y21 { bottom: 144.354px; }

.viewer-page .y20 { bottom: 155.354px; }

.viewer-page .y1f { bottom: 166.353px; }

.viewer-page .y1e { bottom: 177.353px; }

.viewer-page .y1d { bottom: 191.295px; }

.viewer-page .y1c { bottom: 203.77px; }

.viewer-page .y1b { bottom: 214.77px; }

.viewer-page .y1a { bottom: 225.77px; }

.viewer-page .y19 { bottom: 236.769px; }

.viewer-page .y18 { bottom: 247.769px; }

.viewer-page .y17 { bottom: 258.768px; }

.viewer-page .y16 { bottom: 269.768px; }

.viewer-page .y15 { bottom: 280.767px; }

.viewer-page .y14 { bottom: 291.767px; }

.viewer-page .y13 { bottom: 302.766px; }

.viewer-page .y12 { bottom: 313.766px; }

.viewer-page .y11 { bottom: 324.766px; }

.viewer-page .y10 { bottom: 335.765px; }

.viewer-page .yf { bottom: 346.765px; }

.viewer-page .ye { bottom: 362.587px; }

.viewer-page .yd { bottom: 374.123px; }

.viewer-page .yc { bottom: 384.182px; }

.viewer-page .yb { bottom: 395.182px; }

.viewer-page .ya { bottom: 406.182px; }

.viewer-page .y9 { bottom: 417.181px; }

.viewer-page .y8 { bottom: 428.181px; }

.viewer-page .y7 { bottom: 442.123px; }

.viewer-page .y6 { bottom: 461.518px; }

.viewer-page .y2e { bottom: 467.252px; }

.viewer-page .y33 { bottom: 467.386px; }

.viewer-page .y34 { bottom: 468.305px; }

.viewer-page .y2f { bottom: 479.318px; }

.viewer-page .y2b { bottom: 479.41px; }

.viewer-page .y37 { bottom: 479.433px; }

.viewer-page .y30 { bottom: 480.208px; }

.viewer-page .y38 { bottom: 491.612px; }

.viewer-page .y2c { bottom: 492.435px; }

.viewer-page .y31 { bottom: 492.536px; }

.viewer-page .y35 { bottom: 492.866px; }

.viewer-page .y32 { bottom: 494.046px; }

.viewer-page .y2d { bottom: 502.894px; }

.viewer-page .y36 { bottom: 503.113px; }

.viewer-page .y5 { bottom: 513.31px; }

.viewer-page .y4 { bottom: 524.309px; }

.viewer-page .y3 { bottom: 535.309px; }

.viewer-page .y2 { bottom: 546.308px; }

.viewer-page .y1 { bottom: 561.61px; }

.viewer-page .h2 { height: 18.4923px; }

.viewer-page .h4 { height: 21.6977px; }

.viewer-page .h3 { height: 24.6565px; }

.viewer-page .h7 { height: 26.3892px; }

.viewer-page .h6 { height: 27.8955px; }

.viewer-page .h5 { height: 28.8287px; }

.viewer-page .h0 { height: 595.276px; }

.viewer-page .h1 { height: 595.5px; }

.viewer-page .w1 { width: 419.5px; }

.viewer-page .w0 { width: 419.528px; }

.viewer-page .x0 { left: 0px; }

.viewer-page .x8 { left: 62.3396px; }

.viewer-page .x2 { left: 63.459px; }

.viewer-page .x10 { left: 64.6941px; }

.viewer-page .x5 { left: 78.266px; }

.viewer-page .x3 { left: 92.058px; }

.viewer-page .x7 { left: 107.058px; }

.viewer-page .x6 { left: 113.212px; }

.viewer-page .x11 { left: 139.286px; }

.viewer-page .xf { left: 153.857px; }

.viewer-page .xa { left: 164.02px; }

.viewer-page .xc { left: 168.072px; }

.viewer-page .xd { left: 175.62px; }

.viewer-page .x9 { left: 223.653px; }

.viewer-page .xb { left: 232.804px; }

.viewer-page .xe { left: 245.698px; }

.viewer-page .x12 { left: 291.74px; }

.viewer-page .x1 { left: 349.058px; }

.viewer-page .x4 { left: 350.378px; }

@media print {
  .viewer-page .v0 { vertical-align: 0pt; }
  .viewer-page .ls0 { letter-spacing: 0pt; }
  .viewer-page .ls1 { letter-spacing: 0.00297pt; }
  .viewer-page .ws0 { word-spacing: 0pt; }
  .viewer-page ._7 { margin-left: -633.501pt; }
  .viewer-page ._9 { margin-left: -20.7422pt; }
  .viewer-page ._a { margin-left: -16.5933pt; }
  .viewer-page ._8 { margin-left: -12.445pt; }
  .viewer-page ._2 { margin-left: -4.32597pt; }
  .viewer-page ._3 { margin-left: -2.77521pt; }
  .viewer-page ._0 { margin-left: -1.36167pt; }
  .viewer-page ._1 { width: 1.51926pt; }
  .viewer-page ._5 { width: 10.1436pt; }
  .viewer-page ._b { width: 11.6237pt; }
  .viewer-page ._4 { width: 32.7709pt; }
  .viewer-page ._6 { width: 325.134pt; }
  .viewer-page .fs0 { font-size: 33.8448pt; }
  .viewer-page .fs2 { font-size: 39.7112pt; }
  .viewer-page .fs1 { font-size: 45.1264pt; }
  .viewer-page .fs4 { font-size: 48.2655pt; }
  .viewer-page .fs3 { font-size: 52.7979pt; }
  .viewer-page .y0 { bottom: 0pt; }
  .viewer-page .y2a { bottom: 60.4773pt; }
  .viewer-page .y29 { bottom: 75.1434pt; }
  .viewer-page .y28 { bottom: 89.8095pt; }
  .viewer-page .y27 { bottom: 104.476pt; }
  .viewer-page .y26 { bottom: 119.142pt; }
  .viewer-page .y25 { bottom: 133.808pt; }
  .viewer-page .y24 { bottom: 148.474pt; }
  .viewer-page .y23 { bottom: 163.14pt; }
  .viewer-page .y22 { bottom: 177.806pt; }
  .viewer-page .y21 { bottom: 192.472pt; }
  .viewer-page .y20 { bottom: 207.138pt; }
  .viewer-page .y1f { bottom: 221.804pt; }
  .viewer-page .y1e { bottom: 236.47pt; }
  .viewer-page .y1d { bottom: 255.059pt; }
  .viewer-page .y1c { bottom: 271.694pt; }
  .viewer-page .y1b { bottom: 286.36pt; }
  .viewer-page .y1a { bottom: 301.026pt; }
  .viewer-page .y19 { bottom: 315.692pt; }
  .viewer-page .y18 { bottom: 330.358pt; }
  .viewer-page .y17 { bottom: 345.024pt; }
  .viewer-page .y16 { bottom: 359.69pt; }
  .viewer-page .y15 { bottom: 374.356pt; }
  .viewer-page .y14 { bottom: 389.023pt; }
  .viewer-page .y13 { bottom: 403.689pt; }
  .viewer-page .y12 { bottom: 418.355pt; }
  .viewer-page .y11 { bottom: 433.021pt; }
  .viewer-page .y10 { bottom: 447.687pt; }
  .viewer-page .yf { bottom: 462.353pt; }
  .viewer-page .ye { bottom: 483.45pt; }
  .viewer-page .yd { bottom: 498.83pt; }
  .viewer-page .yc { bottom: 512.243pt; }
  .viewer-page .yb { bottom: 526.909pt; }
  .viewer-page .ya { bottom: 541.575pt; }
  .viewer-page .y9 { bottom: 556.242pt; }
  .viewer-page .y8 { bottom: 570.908pt; }
  .viewer-page .y7 { bottom: 589.497pt; }
  .viewer-page .y6 { bottom: 615.357pt; }
  .viewer-page .y2e { bottom: 623.003pt; }
  .viewer-page .y33 { bottom: 623.182pt; }
  .viewer-page .y34 { bottom: 624.407pt; }
  .viewer-page .y2f { bottom: 639.091pt; }
  .viewer-page .y2b { bottom: 639.213pt; }
  .viewer-page .y37 { bottom: 639.244pt; }
  .viewer-page .y30 { bottom: 640.277pt; }
  .viewer-page .y38 { bottom: 655.483pt; }
  .viewer-page .y2c { bottom: 656.58pt; }
  .viewer-page .y31 { bottom: 656.714pt; }
  .viewer-page .y35 { bottom: 657.154pt; }
  .viewer-page .y32 { bottom: 658.728pt; }
  .viewer-page .y2d { bottom: 670.525pt; }
  .viewer-page .y36 { bottom: 670.817pt; }
  .viewer-page .y5 { bottom: 684.413pt; }
  .viewer-page .y4 { bottom: 699.079pt; }
  .viewer-page .y3 { bottom: 713.745pt; }
  .viewer-page .y2 { bottom: 728.411pt; }
  .viewer-page .y1 { bottom: 748.814pt; }
  .viewer-page .h2 { height: 24.6565pt; }
  .viewer-page .h4 { height: 28.9303pt; }
  .viewer-page .h3 { height: 32.8753pt; }
  .viewer-page .h7 { height: 35.1855pt; }
  .viewer-page .h6 { height: 37.194pt; }
  .viewer-page .h5 { height: 38.4383pt; }
  .viewer-page .h0 { height: 793.701pt; }
  .viewer-page .h1 { height: 794pt; }
  .viewer-page .w1 { width: 559.333pt; }
  .viewer-page .w0 { width: 559.371pt; }
  .viewer-page .x0 { left: 0pt; }
  .viewer-page .x8 { left: 83.1195pt; }
  .viewer-page .x2 { left: 84.612pt; }
  .viewer-page .x10 { left: 86.2588pt; }
  .viewer-page .x5 { left: 104.355pt; }
  .viewer-page .x3 { left: 122.744pt; }
  .viewer-page .x7 { left: 142.744pt; }
  .viewer-page .x6 { left: 150.95pt; }
  .viewer-page .x11 { left: 185.714pt; }
  .viewer-page .xf { left: 205.143pt; }
  .viewer-page .xa { left: 218.693pt; }
  .viewer-page .xc { left: 224.096pt; }
  .viewer-page .xd { left: 234.159pt; }
  .viewer-page .x9 { left: 298.204pt; }
  .viewer-page .xb { left: 310.406pt; }
  .viewer-page .xe { left: 327.598pt; }
  .viewer-page .x12 { left: 388.987pt; }
  .viewer-page .x1 { left: 465.411pt; }
  .viewer-page .x4 { left: 467.171pt; }
}
------MultipartBoundary--g3rkOKIbdAn9ODSWX6IjVRlI3UCDnCsyOBVF4rItax------
