---
creation_date: 2025-04-28
modification_date: 2025-04-28
type: note
aliases:
  - YendorCats Documentation
tags:
  - pacey/documentation
  - yendorcats
  - software-dev
area: Software-Development
project: YendorCats
resource: 
archive: 
status: active
priority: high
links:
  - "[[1-Projects]]"
  - "[[YendorCats Project Documentation]]"
related:
  - "[[YendorCats S3 Metadata Implementation]]"
  - "[[YendorCats File Uploader Service]]"
  - "[[YendorCats Deployment Guide]]"
  - "[[YendorCats Testing Guide]]"
---

# YendorCats Project: Table of Contents

## Overview
This document provides a comprehensive table of contents for the YendorCats project documentation. It serves as a central hub for accessing all project-related documentation, guides, and resources.

## Project Documentation

### Core Documentation
| Document | Description | Link |
|----------|-------------|------|
| Project Overview | High-level overview of the YendorCats project | [[YendorCats Project Documentation]] |
| README | Quick start guide and project structure | [README.md](README.md) |

### Technical Implementation
| Document | Description | Link |
|----------|-------------|------|
| S3 Metadata Implementation | Detailed documentation of the S3 metadata system | [[YendorCats S3 Metadata Implementation]] |
| File Uploader Service | Documentation for the Node.js file uploader service | [[YendorCats File Uploader Service]] |
| API Documentation | Reference for the YendorCats API endpoints | [API.md](backend/YendorCats.API/API.md) |
| Database Schema | Documentation of the database structure | [Schema.md](backend/YendorCats.API/Schema.md) |

### Deployment and Operations
| Document | Description | Link |
|----------|-------------|------|
| Deployment Guide | Instructions for deploying on Enhance Control Panel | [[YendorCats Deployment Guide]] |
| Testing Guide | Comprehensive testing procedures and guidelines | [[YendorCats Testing Guide]] |
| Backup and Recovery | Procedures for backup and disaster recovery | [Backup.md](docs/Backup.md) |
| Monitoring | Guidelines for monitoring and alerting | [Monitoring.md](docs/Monitoring.md) |

### User Guides
| Document | Description | Link |
|----------|-------------|------|
| Admin Guide | Guide for administrative users | [Admin.md](docs/Admin.md) |
| Content Management | Instructions for managing cat images and metadata | [Content.md](docs/Content.md) |
| Troubleshooting | Common issues and solutions | [Troubleshooting.md](docs/Troubleshooting.md) |

## Component Documentation

### Backend Components
| Component | Description | Link |
|-----------|-------------|------|
| API Controllers | Documentation for API controllers | [Controllers.md](backend/YendorCats.API/Controllers.md) |
| Data Models | Documentation for data models | [Models.md](backend/YendorCats.API/Models.md) |
| Services | Documentation for business logic services | [Services.md](backend/YendorCats.API/Services.md) |
| Middleware | Documentation for custom middleware | [Middleware.md](backend/YendorCats.API/Middleware.md) |

### Frontend Components
| Component | Description | Link |
|-----------|-------------|------|
| Gallery | Documentation for the gallery component | [Gallery.md](frontend/docs/Gallery.md) |
| Image Viewer | Documentation for the image viewer component | [ImageViewer.md](frontend/docs/ImageViewer.md) |
| Search and Filter | Documentation for search and filter functionality | [Search.md](frontend/docs/Search.md) |
| Admin Interface | Documentation for the admin interface | [AdminUI.md](frontend/docs/AdminUI.md) |

### Tools and Utilities
| Tool | Description | Link |
|------|-------------|------|
| File Uploader | Documentation for the file uploader tool | [[YendorCats File Uploader Service]] |
| Deployment Scripts | Documentation for deployment scripts | [DeploymentScripts.md](tools/DeploymentScripts.md) |
| Database Utilities | Documentation for database utilities | [DatabaseUtils.md](tools/DatabaseUtils.md) |

## Development Guides

### Setup and Configuration
| Guide | Description | Link |
|-------|-------------|------|
| Development Environment | Setting up the development environment | [DevEnv.md](docs/DevEnv.md) |
| Configuration | Configuration options and environment variables | [Configuration.md](docs/Configuration.md) |
| Dependencies | External dependencies and third-party services | [Dependencies.md](docs/Dependencies.md) |

### Coding Standards and Practices
| Guide | Description | Link |
|-------|-------------|------|
| Coding Style | Coding conventions and style guidelines | [CodingStyle.md](docs/CodingStyle.md) |
| Git Workflow | Git branching strategy and workflow | [GitWorkflow.md](docs/GitWorkflow.md) |
| Code Review | Code review process and checklist | [CodeReview.md](docs/CodeReview.md) |
| Testing Standards | Testing standards and best practices | [[YendorCats Testing Guide]] |

## External Resources

### Third-Party Services
| Service | Description | Link |
|---------|-------------|------|
| Enhance Control Panel | Documentation for Enhance Control Panel | [Enhance Docs](https://enhance.com/docs) |
| Backblaze B2 | Documentation for Backblaze B2 S3-compatible storage | [Backblaze B2 Docs](https://www.backblaze.com/b2/docs/) |
| Cloudflare | Documentation for Cloudflare CDN and DNS | [Cloudflare Docs](https://developers.cloudflare.com/) |
| MariaDB | Documentation for MariaDB database | [MariaDB Docs](https://mariadb.com/kb/en/documentation/) |

### Reference Materials
| Resource | Description | Link |
|----------|-------------|------|
| .NET 8 Documentation | Official documentation for .NET 8 | [.NET Docs](https://docs.microsoft.com/en-us/dotnet/) |
| Node.js Documentation | Official documentation for Node.js | [Node.js Docs](https://nodejs.org/en/docs/) |
| AWS SDK for JavaScript | Documentation for AWS SDK used with Backblaze B2 | [AWS SDK Docs](https://docs.aws.amazon.com/sdk-for-javascript/) |
| Docker Documentation | Official documentation for Docker | [Docker Docs](https://docs.docker.com/) |

## Project Management

### Planning and Tracking
| Resource | Description | Link |
|----------|-------------|------|
| Project Plan | Overall project plan and timeline | [ProjectPlan.md](docs/ProjectPlan.md) |
| Milestones | Project milestones and deliverables | [Milestones.md](docs/Milestones.md) |
| Issue Tracker | Link to the project issue tracker | [Issues](https://github.com/yourusername/yendorcats/issues) |

### Meeting Notes and Decisions
| Resource | Description | Link |
|----------|-------------|------|
| Meeting Notes | Archive of project meeting notes | [MeetingNotes.md](docs/MeetingNotes.md) |
| Decision Log | Record of key project decisions | [DecisionLog.md](docs/DecisionLog.md) |
| Change Log | Record of significant changes | [ChangeLog.md](docs/ChangeLog.md) |

## How to Use This Documentation

### For New Team Members
1. Start with the [[YendorCats Project Documentation]] for a high-level overview
2. Set up your development environment using [DevEnv.md](docs/DevEnv.md)
3. Familiarize yourself with the coding standards in [CodingStyle.md](docs/CodingStyle.md)
4. Explore the component documentation relevant to your work

### For Administrators
1. Review the [[YendorCats Deployment Guide]] for setup instructions
2. Consult [Admin.md](docs/Admin.md) for day-to-day administration tasks
3. Refer to [Troubleshooting.md](docs/Troubleshooting.md) for common issues
4. Use [Monitoring.md](docs/Monitoring.md) for monitoring and maintenance

### For Content Managers
1. Start with [Content.md](docs/Content.md) for content management guidelines
2. Review the [[YendorCats File Uploader Service]] documentation for uploading images
3. Consult the [[YendorCats S3 Metadata Implementation]] for understanding metadata

## Document Maintenance

This table of contents is maintained by the YendorCats project team. If you find missing or outdated information, please contact the project owner or submit a pull request.

Last updated: 2025-04-28

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats]]") OR contains(tags, "yendorcats")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "yendorcats") OR contains(tags, "software-dev")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project Overview]]
- [[1-Projects|All Projects]]
- [[Software-Development Overview|Software Development Area]]
