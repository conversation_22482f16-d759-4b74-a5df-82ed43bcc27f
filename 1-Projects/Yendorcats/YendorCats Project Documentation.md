---
creation_date: 2025-04-27
modification_date: 2025-04-27
type: project
status: active
priority: high
deadline: 2025-06-30
project_owner: Jordan
project_client: Personal
completion_percentage: 35
estimated_hours: 120
tags: [para/projects, software-dev, cat-breeder, gallery-website]
related: [YendorCats S3 Metadata Implementation, YendorCats File Uploader Service, YendorCats Deployment Guide]
area: Software-Development
start_date: 2025-04-26
stakeholders: []
---

# YendorCats Project Documentation

## Overview
YendorCats is a visually appealing, streamlined, gallery-focused website for a Maine Coon cat breeder. The project uses a modern tech stack with a JavaScript frontend for DOM manipulation, CSS-heavy design for visual appeal, and a .NET 8 backend with Kestrel server. The site focuses on showcasing cats with rich metadata and provides an intuitive admin interface for managing cat galleries.

## Objectives
- Create a visually stunning, image-focused gallery website for Maine Coon cats
- Implement a lightweight, efficient frontend using plain JavaScript and CSS
- Build a secure, robust backend using .NET 8 and Kestrel server
- Develop an intuitive file upload system for managing cat images and metadata
- Ensure good security practices throughout the application
- Provide comprehensive documentation for future maintenance
- Deploy using Enhance Control Panel with Cloudflare integration

## Success Criteria
- Website loads quickly and displays cat images beautifully
- Admin can easily upload and manage cat images with metadata
- Site is secure against common web vulnerabilities
- Documentation is clear and comprehensive
- Deployment process is streamlined and repeatable
- Site is optimized for both desktop and mobile viewing

## Technical Architecture

### Frontend
- **Plain JavaScript** for DOM manipulation
- **CSS-heavy design** for visual appeal and performance
- **Responsive layouts** for mobile and desktop
- **Gallery components** with metadata display
- **No frameworks** to keep the site lightweight and fast

### Backend
- **.NET 8** with Kestrel server
- **MariaDB** database for data storage
- **S3-compatible storage** (Backblaze B2) for image hosting
- **RESTful API** for frontend communication
- **JWT authentication** for admin access

### File Upload Service
- **Node.js** microservice for file uploads
- **Express** for API endpoints
- **Multer** for file handling
- **AWS SDK** for S3 integration
- **Docker** container for deployment

### Deployment
- **Enhance Control Panel** for managing deployments
- **Docker containers** for all services
- **Cloudflare** for CDN, WAF, and DNS
- **HTTPS** for secure communication

## Tasks
- [x] Initial project setup
- [x] Backend API implementation
- [x] Frontend gallery implementation
- [x] S3 storage integration
- [x] File uploader service implementation
- [x] S3 metadata implementation
- [ ] Admin authentication system
- [ ] Batch upload functionality
- [ ] Deployment configuration
- [ ] Cloudflare integration
- [ ] Testing and QA
- [ ] Documentation finalization

## Timeline
- **Start Date**: 2025-04-26
- **Deadline**: 2025-06-30
- **Milestones**:
  - [x] Initial Planning - 2025-05-03
  - [x] Core Backend Development - 2025-05-17
  - [x] Frontend Gallery Implementation - 2025-05-31
  - [x] File Upload Service - 2025-06-07
  - [ ] Testing and Refinement - 2025-06-21
  - [ ] Deployment and Launch - 2025-06-30

## Key Components

### S3 Metadata Implementation
The project uses S3 object metadata as the primary source of information for cat images, with filename parsing as a fallback. This approach provides flexibility and extensibility while maintaining backward compatibility.

[See detailed documentation: [[YendorCats S3 Metadata Implementation]]]

### File Uploader Service
A dedicated Node.js microservice handles file uploads to Backblaze B2, with a user-friendly interface for adding metadata to images. The service is containerized for easy deployment.

[See detailed documentation: [[YendorCats File Uploader Service]]]

### Deployment Process
The project is deployed using Enhance Control Panel, which manages Docker containers for all services. Cloudflare provides CDN, WAF, and DNS services.

[See detailed documentation: [[YendorCats Deployment Guide]]]

## Resources
- [Backblaze B2 Documentation](https://www.backblaze.com/b2/docs/)
- [.NET 8 Documentation](https://docs.microsoft.com/en-us/dotnet/core/whats-new/dotnet-8)
- [Enhance Control Panel](https://enhance.com/docs)
- [Cloudflare Documentation](https://developers.cloudflare.com/fundamentals/)
- [Docker Documentation](https://docs.docker.com/)

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "Software-Development")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "software-dev") OR contains(tags, "cat-breeder") OR contains(tags, "gallery-website")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "YendorCats") OR contains(file.name, "YendorCats")
SORT date DESC
```

## Progress Updates
### 2025-04-26 - Initial Setup
- Project created
- Initial planning started
- Tech stack decisions made

### 2025-04-27 - Core Implementation
- Backend API implemented
- S3 storage integration completed
- Frontend gallery implemented
- File uploader service created
- S3 metadata implementation completed

## Quick Links
- [[YendorCats Meeting|New Meeting]]
- [[YendorCats Resource|New Resource]]
- [[1-Projects|All Projects]]
- [[yendorcats|Original Project Note]]
