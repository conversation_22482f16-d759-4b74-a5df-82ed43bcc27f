---
creation_date: <% tp.date.now("YYYY-<PERSON><PERSON>-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: database
aliases: [Notes DB, Database View]
tags: [database, dataview]
---

# Notes Database

This page provides dynamic database views of all notes using Dataview.

## Notes by PARA Category

### Projects
```dataview
TABLE
  project as "Project",
  status as "Status",
  priority as "Priority",
  file.mtime as "Last Modified"
FROM #para/projects
SORT status ASC, priority ASC
```

### Areas
```dataview
TABLE
  area as "Area",
  status as "Status",
  file.mtime as "Last Modified"
FROM #para/areas
SORT area ASC
```

### Resources
```dataview
TABLE
  resource as "Resource",
  type as "Type",
  file.mtime as "Last Modified"
FROM #para/resources
SORT resource ASC
```

### Archive
```dataview
TABLE
  archive as "Item",
  type as "Type",
  creation_date as "Created"
FROM #para/archive
SORT creation_date DESC
```

## Notes by Type

### Tasks
```dataview
TABLE
  file.folder as "Location",
  status as "Status",
  priority as "Priority"
FROM #tasks OR "Tasks"
SORT priority ASC, file.mtime DESC
```

### References
```dataview
TABLE
  type as "Type",
  file.folder as "Location",
  file.mtime as "Last Modified"
FROM #reference
SORT file.mtime DESC
```

## Notes by Creation Date
```dataview
TABLE
  type as "Type",
  file.folder as "Location",
  tags as "Tags"
FROM "Brain"
SORT file.ctime DESC
LIMIT 20
```

## Notes Without PARA Classification
```dataview
TABLE
  file.folder as "Location",
  file.mtime as "Last Modified"
FROM "Brain"
WHERE !contains(tags, "para/projects") AND !contains(tags, "para/areas") AND !contains(tags, "para/resources") AND !contains(tags, "para/archive") AND file.name != "Notes Database" AND file.name != "Home"
SORT file.mtime DESC
```

## Related
- [[Home]]
- [[1-Projects]]
- [[2-Areas]]
- [[3-Resources]]
- [[4-Archive]] 