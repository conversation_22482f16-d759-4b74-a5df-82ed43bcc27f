---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: index
aliases: [Archive]
tags: [para/archive, index]
---

# Archive

> Archive contains items that are inactive but might be referenced in the future.

## Completed Projects
```dataview
TABLE
  completion_date as "Completed",
  type as "Type"
FROM "4-Archive"
WHERE contains(status, "completed")
SORT completion_date DESC
```

## Software Development Archive
```dataview
TABLE
  completion_date as "Completed",
  type as "Type"
FROM "4-Archive"
WHERE contains(tags, "software-dev") OR contains(tags, "programming")
SORT completion_date DESC
```

## Administration Archive
```dataview
TABLE
  completion_date as "Completed",
  type as "Type"
FROM "4-Archive"
WHERE contains(tags, "admin") OR contains(tags, "administration")
SORT completion_date DESC
```

## All Archived Items
```dataview
TABLE
  type as "Type",
  creation_date as "Created",
  completion_date as "Completed"
FROM "4-Archive"
SORT completion_date DESC
```

## Related
- [[1-Projects]]
- [[2-Areas]]
- [[3-Resources]]
- [[Home]]