﻿TYPE,CONTENT,DESCRIPTION,PRIORITY,INDENT,AUTHOR,RESPONSIBLE,DATE,DATE_LANG,TIMEZONE,DURATION,DURATION_UNIT,DEADLINE,DEADLINE_LANG
meta,view_style=board,,,,,,,,,,,,
,,,,,,,,,,,,,
task,Call Optus to get authorised on acc,,4,1,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,Duty management planning,,4,1,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,sell items for new laptop,,3,1,jordan.pacey (53351952),,18 Apr,en,Australia/Brisbane,,,,
task,xbox series x?,,4,2,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,controllers,,4,3,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,<PERSON> s21,,4,2,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,look through tools,,4,2,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,"4/4/2025, 8:23 PM : Make a routine for maths, make a personal roster",,4,1,jordan.pacey (53351952),,every day,en,Australia/Brisbane,,,,
task,"8/4/2025, 12:20 AM : Mazda needs service,",,4,1,jordan.pacey (53351952),,8 May,en,Australia/Brisbane,,,,
,,,,,,,,,,,,,
section,Routines 🔁,,,,,,,,,,,,
task,Do a weekly review of my tasks and goals,,3,1,jordan.pacey (53351952),,ev sun,en,Australia/Brisbane,,,,
task,Add more _personal_ routines,"e.g.: pay taxes yearly, empty the bins weekly, meditate for 10 mins ev weekday at 9am",4,1,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,Book Easter camping by,,4,1,jordan.pacey (53351952),,every year,en,Australia/Brisbane,,,,
,,,,,,,,,,,,,
section,Inspiration ✨,,,,,,,,,,,,
task,[7 Real-Life Todoist Setups to Steal](https://blog.doist.com/todoist-setup/?utm_source=todoist&utm_medium=in_app&utm_campaign=onboarding_project&utm_content=personal) 💡 @read,,4,1,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,[Organizing Your Family's To-do List with Todoist (Lessons From Users)](https://blog.doist.com/family-todo-list/?utm_source=todoist&utm_medium=in_app&utm_campaign=onboarding_project&utm_content=personal) @read,,4,1,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,Explore Todoist's curated [personal templates](https://todoist.com/templates/category/personal?utm_source=todoist&utm_medium=in_app&utm_campaign=onboarding_project&utm_content=personal),,4,1,jordan.pacey (53351952),,,,Australia/Brisbane,,,,
task,Use Smart Connections Obsidian Plugins with llama local model on noobslayer to keep notes files updated and utilise offline ai model reminder,,2,1,jordan.pacey (53351952),,22 Apr,en,Australia/Brisbane,,,,
,,,,,,,,,,,,,