{"main": {"id": "5228970d74fe2926", "type": "split", "children": [{"id": "6486b0afd511db3b", "type": "tabs", "children": [{"id": "c3a470d00d524226", "type": "leaf", "state": {"type": "markdown", "state": {"file": "notes-uca-obsidian/1-Projects/Yendorcats/YendorCats_Service_Level_Agreement.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "YendorCats_Service_Level_Agreement"}}, {"id": "320cbaba0563d759", "type": "leaf", "state": {"type": "markdown", "state": {"file": "notes-uca-obsidian/3-Resources/Cheatsheets/Untitled 1.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Untitled 1"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "ce742fd65136057f", "type": "split", "children": [{"id": "a39896947e070fef", "type": "tabs", "children": [{"id": "8cc09e8697afa151", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "9753e606c9432d95", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "75511520541a9b7c", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "c9c359111d5df5f7", "type": "split", "children": [{"id": "132dfb3934733c01", "type": "tabs", "children": [{"id": "173c2069505aabd9", "type": "leaf", "state": {"type": "backlink", "state": {"file": "notes-uca-obsidian/Templates/Meeting.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Meeting"}}, {"id": "31fd66fd249361b1", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "notes-uca-obsidian/Templates/Meeting.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Meeting"}}, {"id": "cb02381cf3215cc3", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "d378a84e1ad24905", "type": "leaf", "state": {"type": "outline", "state": {"file": "notes-uca-obsidian/Templates/Meeting.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Meeting"}}, {"id": "fbbaefaa26021971", "type": "leaf", "state": {"type": "calendar", "state": {}, "icon": "calendar-with-checkmark", "title": "Calendar"}}, {"id": "b53336185c16155f", "type": "leaf", "state": {"type": "git-view", "state": {}, "icon": "git-pull-request", "title": "Source Control"}}], "currentTab": 5}], "direction": "horizontal", "width": 300}, "left-ribbon": {"hiddenItems": {"omnisearch:Omnisearch": false, "remotely-save:Remotely Save": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "obsidian-git:Open Git source control": false, "templater-obsidian:Templater": false, "obsidian-kanban:Create new board": false, "table-editor-obsidian:Advanced Tables Toolbar": false}}, "active": "320cbaba0563d759", "lastOpenFiles": ["notes-uca-obsidian/3-Resources/Cheatsheets/Rsync.md", "notes-uca-obsidian/3-Resources/Cheatsheets/Untitled 1.md", "notes-uca-obsidian/3-Resources/Cheatsheets/Untitled 2.md", "notes-uca-obsidian/3-Resources/Cheatsheets/Filesystem extensions (aws)-1.md", "notes-uca-obsidian/3-Resources/Attachments", "Pasted image 20250527141021.png", "Pasted image 20250527141004.png", "notes-uca-obsidian/3-Resources/Cheatsheets/Bluetooth Remote  Control from SSH on cli.md", "notes-uca-obsidian/3-Resources/YendorCats Deployment Guide.md", "Pasted image 20250527140914.png", "notes-uca-obsidian/1-Projects/CRUCA Website/CRUCA Website Updates 1.md", "notes-uca-obsidian/3-Resources/Cheatsheets/Arc.md", "Pasted image 20250524043728.png", "2025-05-23.md", "notes-uca-obsidian/1-Projects/CRUCA Website/Procedure to renew ssl certificates with certbot - Auto or Manually.md", "notes-uca-obsidian/Templates/Meeting.md", "Untitled 1.md", "notes-uca-obsidian/1-Projects/CRUCA Website/CRUCA Website Updates.md", "notes-uca-obsidian/1-Projects/MCP-Server/MCP-Server Start Project.md", "notes-uca-obsidian/1-Projects/MCP-Server/MCP Server Project.md", "notes-uca-obsidian/1-Projects/Yendorcats/YendorCats_Service_Level_Agreement.md", "notes-uca-obsidian/1-Projects/Yendorcats/~$ndorCats_Service_Level_Agreement.docx", "notes-uca-obsidian/1-Projects/Yendorcats/YendorCats_Service_Level_Agreement.docx", "notes-uca-obsidian/1-Projects/Yendorcats/Yendorcats Project Finance.md", "notes-uca-obsidian/Templates/Enhance Metadata.md", "notes-uca-obsidian/1-Projects/Yendorcats/YendorCats_Service_Level_Agreement.html", "notes-uca-obsidian/1-Projects/Yendorcats/yendorcats.md", "notes-uca-obsidian/1-Projects/Yendorcats/YendorCats File Uploader Service.md", "YendorCats_Service_Level_Agreement.md", "notes-uca-obsidian/1-Projects/Yendorcats/Yendor Queensland Business and Registration details;.md", "notes-uca-obsidian/1-Projects/Website Redesign Meeting 2023-06-01.md", "notes-uca-obsidian/1-Projects/Yendorcats/1-Meetings.md", "notes-uca-obsidian/Website/cruca/cline_task_apr-16-2025_12-36-54-am.md", "notes-uca-obsidian/0-Daily Notes/23rd April 2025.md"]}