.zt-format {
  border: 1px solid var(--background-modifier-border);
  padding: 1rem;
  background-color: var(--background-primary);
  border-radius: 10px;
  margin-bottom: 10px;
}

.zt-format__form {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-bottom: 1rem;
  max-width: 600px;
}

.zt-format__form:last-child {
  margin-bottom: 0;
}

.zt-format__label {
  font-size: 0.9em;
  font-weight: 600;
  margin-bottom: 5px;
}

.is-deprecated .zt-format__label {
  color: var(--text-error);
}

.zt-format__input-wrapper {
  display: flex;
  align-items: center;
}

.zt-format__input-wrapper textarea {
  resize: vertical;
}

.zt-format__input-wrapper > *:not(.checkbox-container) {
  width: 100% !important;
}

.is-deprecated .zt-format__input-wrapper button {
  width: auto !important;
  flex-grow: 0;
  flex-shrink: 0;
  margin-left: 5px;
}

.zt-format__delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 7px 9px;
  margin-left: 10px;
  flex-shrink: 0;
  flex-grow: 0;
}

.zt-json-viewer {
  font-size: 13px;
}

.zt-json-viewer .react-json-view {
  padding: 1em;
  border-radius: 10px;
  margin-top: 1em;
  overflow: auto;
  font-family: var(--font-monospace) !important;
}

.zt-json-viewer__btns {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.zt-json-viewer__btns label {
  display: block;
  font-weight: bold;
  padding-top: 1em;
}

.zt-json-viewer__btns select {
  font-size: 1em;
}

.zt-json-viewer__btns button {
  font-size: 1em;
  margin-right: 5px;
}

.zt-json-viewer__preview,
.zt-json-viewer__data {
  border: 1px solid var(--background-modifier-border);
  border-radius: 10px;
  padding: 1em;
  margin-top: 1em;
}

.zt-json-viewer__preview.error {
  background-color: #ff000011;
  font-family: var(--font-monospace);
}

.zt-json-viewer__preview pre {
  overflow: auto;
  white-space: pre-wrap;
  margin: 0;
}

.zt-json-viewer__preview pre,
.zt-json-viewer__preview code {
  font-family: inherit;
}

.zt-json-viewer__preview:not(.error) pre {
  font-family: var(--font-text, --font-default, --default-font);
  max-height: 70vh;
  min-height: 400px;
}

.zt-multiselect {
  width: 300px;
  text-align: left;
}

.zt-multiselect input {
  outline: none !important;
  box-shadow: none !important;
}

.zt-format__input-note {
  font-style: italic;
  font-size: 0.9em;
  padding-top: 10px;
  margin-bottom: 10px;
}

.zt-setting-item pre,
.zt-format__input-note pre {
  display: inline-block;
  margin: 0;
  padding: 0 6px;
  background-color: var(--background-secondary-alt);
  border-radius: 4px;
}

.zt-asset-success {
  text-align: left;
  display: flex;
}

.zt-asset-success__icon {
  color: var(--interactive-success);
  font-size: 24px;
  margin-right: 5px;
}

.zt-asset-success__icon svg {
  width: 1em !important;
  height: 1em !important;
}

.zt-asset-success__message {
  font-size: 0.9em;
}

.zt-suggest-title {
  font-size: var(--font-ui-small);
  color: var(--text-muted);
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-top: var(--size-4-1);
}

.zt-suggest-loading-wrapper {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  padding: var(--size-4-2) 0;
}

.zt-suggest-loading,
.zt-suggest-loading:before,
.zt-suggest-loading:after {
  border-radius: 999px;
  width: 1em;
  height: 1em;
  animation-fill-mode: both;
  animation: bblFadInOut 1.6s infinite ease-in-out;
}

.zt-suggest-loading {
  display: block;
  color: var(--text-muted);
  font-size: 7px;
  position: relative;
  animation-delay: -0.16s;
  top: -1em;
}
.zt-suggest-loading:before,
.zt-suggest-loading:after {
  content: '';
  position: absolute;
}
.zt-suggest-loading:before {
  left: -2em;
  animation-delay: -0.32s;
}
.zt-suggest-loading:after {
  left: 2em;
}

.zt-color-chip {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  margin-right: var(--size-4-1);
}

@keyframes bblFadInOut {
  0%,
  80%,
  100% {
    box-shadow: 0 1em 0 -1.3em;
  }
  40% {
    box-shadow: 0 1em 0 0;
  }
}
