---
creation_date: 2025-04-18
modification_date: 2025-04-18
type: daily
date: 2025-04-18
day_of_week: Friday
week: 2025-W16
month: 2025-04
tags:
  - daily
  - 2025-04
  - email
  - errors
  - obsidian
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-04-18 - Friday

<< [[2025-04-17]] | [[2025-04-19]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- <PERSON> told me he sent extra bulletin inserts from Pastroal care but there where errors with the new email.
	- have nmopt ionvestigated why yet:
	-  (could be old errors left in the error log on PC)
	- could also be still getting quota exceeded erorr, in which case need to call namecheap
- graphics card has issues, need to swap it with mine

## Tasks
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-18)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-18
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-18
due before 2025-04-25
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-18 Meeting|Create New Meeting]]
- [[2025-04-18 Task|Create New Task]]
- [[2025-04-18 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W16|Weekly Overview]]
- [[Tasks]]
- [[Home]]
