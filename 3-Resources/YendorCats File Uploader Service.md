---
creation_date: 2025-04-27
modification_date: 2025-04-27
type: resource
source: Project documentation
tags: [para/resources, guide, software-dev, file-uploader, node-js, docker]
area: Software-Development
difficulty: medium
url: 
---

# YendorCats File Uploader Service

## Overview
The YendorCats File Uploader Service is a dedicated Node.js microservice that handles the uploading of cat images to Backblaze B2 storage. It provides a user-friendly web interface for adding metadata to images and ensures that all uploaded files follow the project's naming conventions while storing comprehensive metadata in S3 object metadata.

## Key Points
- Node.js/Express microservice for file uploads
- User-friendly web interface for adding metadata
- Uploads files to Backblaze B2 with S3-compatible API
- Stores comprehensive metadata in S3 object metadata
- Maintains filename convention for backward compatibility
- Dockerized for easy deployment with Enhance Control Panel

## Implementation Details

### Service Architecture

The file uploader service consists of the following components:

1. **Express Server**: Handles HTTP requests and serves the web interface
2. **Multer Middleware**: Processes file uploads
3. **AWS SDK**: Communicates with Backblaze B2 using the S3-compatible API
4. **Web Interface**: Provides a user-friendly form for adding metadata

### Server Implementation

The core server implementation is in `server.js`:

```javascript
/**
 * YendorCats File Uploader Service
 * 
 * This service provides a simple web interface for uploading cat images to Backblaze B2.
 * It extracts metadata from filenames and allows adding additional information.
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const cors = require('cors');
const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
require('dotenv').config();

// Create Express app
const app = express();
const port = process.env.PORT || 80;

// Configure AWS SDK for Backblaze B2
const s3 = new AWS.S3({
  endpoint: process.env.AWS_S3_ENDPOINT,
  accessKeyId: process.env.AWS_S3_ACCESS_KEY,
  secretAccessKey: process.env.AWS_S3_SECRET_KEY,
  s3ForcePathStyle: true,
  signatureVersion: 'v4',
  region: process.env.AWS_S3_REGION
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate a unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    // Accept only image files
    const filetypes = /jpeg|jpg|png|gif|webp/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    
    if (mimetype && extname) {
      return cb(null, true);
    }
    
    cb(new Error('Only image files are allowed!'));
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Upload endpoint
app.post('/upload', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { name, age, category, description, gender, color, traits, mother, father } = req.body;
    
    if (!name || !category) {
      return res.status(400).json({ error: 'Name and category are required' });
    }

    // Format the filename according to the convention: Name-Age-Date-Number.ext
    const date = new Date().toISOString().slice(2, 10).replace(/-/g, '');
    const formattedAge = age || 'unknown';
    const formattedName = name.replace(/\s+/g, '_');
    
    // Get a random number for the image sequence
    const randomNum = Math.floor(Math.random() * 9) + 1;
    
    // Create the formatted filename
    const formattedFilename = `${formattedName}-${formattedAge}-${date}-${randomNum}${path.extname(req.file.originalname)}`;
    
    // Determine the S3 key based on category
    const s3Key = `resources/${category.toLowerCase()}/${formattedFilename}`;
    
    // Upload to Backblaze B2
    const fileContent = fs.readFileSync(req.file.path);
    
    const params = {
      Bucket: process.env.AWS_S3_BUCKET_NAME,
      Key: s3Key,
      Body: fileContent,
      ContentType: req.file.mimetype,
      ACL: 'public-read',
      Metadata: {
        'name': name,
        'age': formattedAge,
        'description': description || '',
        'category': category,
        'gender': gender || '',
        'color': color || '',
        'traits': traits || '',
        'mother': mother || '',
        'father': father || ''
      }
    };
    
    const uploadResult = await s3.upload(params).promise();
    
    // Clean up the temporary file
    fs.unlinkSync(req.file.path);
    
    res.json({
      success: true,
      message: 'File uploaded successfully',
      fileUrl: uploadResult.Location,
      fileName: formattedFilename
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({ error: 'Failed to upload file', details: error.message });
  }
});

// Get categories endpoint
app.get('/categories', (req, res) => {
  // Return the available categories
  const categories = ['studs', 'queens', 'kittens', 'gallery'];
  res.json(categories);
});

// Start the server
app.listen(port, () => {
  console.log(`File uploader service running on port ${port}`);
});
```

### Web Interface

The web interface provides a user-friendly form for adding metadata to images:

```html
<form id="upload-form" enctype="multipart/form-data">
    <div class="form-group">
        <label for="category">Category <span class="required">*</span></label>
        <select id="category" name="category" required>
            <option value="" disabled selected>Select a category</option>
            <option value="studs">Studs</option>
            <option value="queens">Queens</option>
            <option value="kittens">Kittens</option>
            <option value="gallery">Gallery</option>
        </select>
    </div>

    <div class="form-group">
        <label for="name">Cat Name <span class="required">*</span></label>
        <input type="text" id="name" name="name" placeholder="e.g., Fluffy" required>
    </div>

    <div class="form-group">
        <label for="age">Age (years.months)</label>
        <input type="text" id="age" name="age" placeholder="e.g., 2.5">
        <small>Format: years.months (e.g., 2.5 for 2 years and 5 months)</small>
    </div>
    
    <div class="form-group">
        <label for="gender">Gender</label>
        <select id="gender" name="gender">
            <option value="" disabled selected>Select gender</option>
            <option value="M">Male</option>
            <option value="F">Female</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="color">Color/Pattern</label>
        <input type="text" id="color" name="color" placeholder="e.g., Brown Tabby">
    </div>
    
    <div class="form-group">
        <label for="traits">Special Traits</label>
        <input type="text" id="traits" name="traits" placeholder="e.g., Long fur, Green eyes">
    </div>
    
    <div class="form-group">
        <label for="mother">Mother</label>
        <input type="text" id="mother" name="mother" placeholder="Mother's name">
    </div>
    
    <div class="form-group">
        <label for="father">Father</label>
        <input type="text" id="father" name="father" placeholder="Father's name">
    </div>

    <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" name="description" rows="4" placeholder="Enter a description of the cat..."></textarea>
    </div>

    <div class="form-group file-input-group">
        <label for="image">Image <span class="required">*</span></label>
        <div class="file-input-container">
            <input type="file" id="image" name="image" accept="image/*" required>
            <div class="file-preview-container">
                <img id="image-preview" src="#" alt="Preview" style="display: none;">
            </div>
        </div>
        <small>Max file size: 10MB. Supported formats: JPG, PNG, GIF, WebP</small>
    </div>

    <div class="form-group">
        <label>Filename Preview</label>
        <div class="filename-preview" id="filename-preview">
            <code>Name-Age-Date-Number.jpg</code>
        </div>
        <small>The file will be renamed according to this format</small>
    </div>

    <div class="form-actions">
        <button type="submit" id="upload-button">
            <i class="fas fa-upload"></i> Upload Image
        </button>
    </div>
</form>
```

### Docker Configuration

The service is containerized using Docker for easy deployment:

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Expose the port
EXPOSE 80

# Start the application
CMD ["node", "server.js"]
```

### Environment Configuration

The service uses environment variables for configuration:

```
# S3 Configuration
AWS_S3_BUCKET_NAME=yendorcats-images
AWS_S3_REGION=us-west-004
AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
AWS_S3_ACCESS_KEY=your-backblaze-b2-application-key-id
AWS_S3_SECRET_KEY=your-backblaze-b2-application-key

# API Configuration
API_BASE_URL=http://localhost:5000

# Server Configuration
PORT=80
```

## Deployment

The service is deployed as part of the YendorCats application using Docker Compose:

```yaml
# File Upload Service
uploader:
  build:
    context: ./tools/file-uploader
    dockerfile: Dockerfile
  image: yendorcats/uploader:latest
  container_name: yendorcats-uploader
  restart: unless-stopped
  ports:
    - "5002:80"
  environment:
    - AWS_S3_BUCKET_NAME=yendorcats-images
    - AWS_S3_REGION=us-west-004
    - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
    - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
    - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
    - API_BASE_URL=http://api
  depends_on:
    - api
  networks:
    - yendorcats-network
```

## Future Enhancements

1. **Batch Upload**: Add support for uploading multiple files at once
2. **Image Optimization**: Automatically resize and optimize images before upload
3. **Advanced Metadata**: Add support for more complex metadata like pedigree information
4. **Authentication**: Add admin authentication to secure the upload interface
5. **Progress Tracking**: Add progress tracking for large uploads

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats File Uploader Service]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats File Uploader Service]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "file-uploader") OR contains(tags, "node-js") OR contains(tags, "docker")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats S3 Metadata Implementation|S3 Metadata Documentation]]
- [[3-Resources|All Resources]]
