---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: YouTube Video Analysis
tags: [para/resources, prompt-engineering, ai, llm, mcp, server, setup, integration, cursor, augment]
related: ["MCP Setup Guide", "MCP Customization", "Developer Profile", "AI Assistant Memory Management Guidelines"]
area: Software-Development
difficulty: medium
keywords: [mcp, model-context-protocol, server, setup, integration, cursor, augment, context-management]
last_used: 2025-04-16
video_source: "https://www.youtube.com/watch?v=kfTDFkhNDFU"
---
[[]]
# MCP Server Setup and Utilization

## Overview
A comprehensive guide for setting up and utilizing a Model Context Protocol (MCP) server to achieve unified context history across different development environments like Cursor AI and Augment AI.

## What is MCP?

The Model Context Protocol (MCP) is a framework that allows you to create custom tools that AI assistants can use, providing:

- **Unified Context**: Maintain context across different development environments
- **Custom Tools**: Create specialized tools for your specific workflow
- **Personalized Rules**: Configure how AI assistants interact with your tools
- **Documentation Integration**: Convert documentation into LLM-readable format

## Step-by-Step Setup Process

### 1. Understanding MCP Basics

1. **What is MCP**: The Model Context Protocol is a framework that allows you to create custom tools for AI assistants
2. **How it works**: MCP servers act as an input-output system where:
   - The AI assistant sends input to your custom tool
   - Your tool processes the input and returns output
   - The AI assistant incorporates this output into its response

### 2. Setting Up the Python Environment

1. **Install Python**: Ensure Python 3.8+ is installed on your system
   ```bash
   # Check Python version
   python --version

   # On Arch Linux, Python should already be installed
   # If needed, install with pacman
   sudo pacman -S python python-pip
   ```

2. **Create a Virtual Environment** (recommended):
   ```bash
   # Create a project directory
   mkdir mcp-server
   cd mcp-server

   # Create a virtual environment
   python -m venv venv

   # Activate the virtual environment
   source venv/bin/activate  # On Linux/macOS
   ```

3. **Install the MCP SDK**:
   ```bash
   pip install mcp

   # Install additional dependencies as needed
   pip install requests  # For API calls
   ```

### 3. Creating Your First Python MCP Server

1. **Create a Python file**: Create a new file (e.g., `mcp_server.py`)

2. **Import the MCP library and add type hints**:
   ```python
   from mcp import MCP
   from typing import Dict, List, Any, Optional, Union, float
   ```

3. **Initialize the MCP server**:
   ```python
   mcp = MCP()
   ```

4. **Define your first tool with proper Python docstrings**:
   ```python
   @mcp.tool("Convert dollars to pounds")
   def dollars_to_pounds(amount: float) -> float:
       """
       Converts a dollar amount to pounds using the current exchange rate.

       Args:
           amount: The amount in dollars to convert

       Returns:
           The equivalent amount in pounds
       """
       # Fixed exchange rate for example
       exchange_rate = 0.77
       return amount * exchange_rate
   ```

5. **Add a more complex tool example**:
   ```python
   @mcp.tool("Get weather information")
   def get_weather(city: str, country: Optional[str] = None) -> Dict[str, Any]:
       """
       Gets current weather information for a specified city.

       Args:
           city: The name of the city
           country: Optional country code (e.g., 'US', 'UK')

       Returns:
           A dictionary containing weather information
       """
       # In a real implementation, you would call a weather API
       # This is a simplified example
       return {
           "city": city,
           "country": country or "Unknown",
           "temperature": 22,  # Celsius
           "conditions": "Sunny",
           "humidity": 65,  # Percentage
       }
   ```

6. **Run the server**:
   ```python
   if __name__ == "__main__":
       # You can configure the server with additional options
       mcp.run(
           host="127.0.0.1",  # Default is localhost
           port=8000,         # Default port
           debug=True         # Enable debug mode for development
       )
   ```

7. **Complete example file**:
   ```python
   from mcp import MCP
   from typing import Dict, List, Any, Optional, Union, float

   # Initialize the MCP server
   mcp = MCP()

   @mcp.tool("Convert dollars to pounds")
   def dollars_to_pounds(amount: float) -> float:
       """
       Converts a dollar amount to pounds using the current exchange rate.

       Args:
           amount: The amount in dollars to convert

       Returns:
           The equivalent amount in pounds
       """
       exchange_rate = 0.77
       return amount * exchange_rate

   @mcp.tool("Get weather information")
   def get_weather(city: str, country: Optional[str] = None) -> Dict[str, Any]:
       """
       Gets current weather information for a specified city.

       Args:
           city: The name of the city
           country: Optional country code (e.g., 'US', 'UK')

       Returns:
           A dictionary containing weather information
       """
       return {
           "city": city,
           "country": country or "Unknown",
           "temperature": 22,
           "conditions": "Sunny",
           "humidity": 65,
       }

   if __name__ == "__main__":
       print("Starting MCP server...")
       mcp.run(debug=True)
   ```

### 4. Configuring MCP in Cursor AI

1. **Copy the file path**: Get the full path to your MCP server file

2. **Open Cursor AI settings**:
   - Navigate to Settings
   - Find the MCP section

3. **Add your MCP server**:
   - Click "Add MCP Server"
   - Name: Give it a descriptive name (e.g., "My First MCP")
   - Type: Select "command"
   - Command: Enter `mcp run [your_file_path]`
   - Click "Add"

4. **Test your MCP server**:
   - Open the Cursor AI agent
   - Ask it to use your tool (e.g., "Convert $20 to pounds")
   - Verify that it correctly uses your tool and returns the result

### 5. Adding Documentation Context

1. **Find SDK documentation**:
   - Locate the README file of the MCP SDK repository
   - Copy the URL to the raw README file (not the entire repository)

2. **Add documentation to Cursor**:
   - Go to Features > Docs in Cursor
   - Add a new Doc
   - Paste the README URL
   - Give it a name (e.g., "Python MCP SDK")
   - Click "Save"

3. **Use documentation in Composer**:
   - When using Composer, reference the doc you added
   - This provides context for the AI to help you build MCP tools

### 6. Converting Documentation to LLM-Readable Data

1. **Use Git-Ingest**:
   - Take any GitHub repository URL
   - Replace "github.com" with "gitingest.com" in the URL
   - This converts the repository into LLM-readable data

2. **Copy the output**:
   - Copy the processed data from Git-Ingest
   - Provide it to any LLM for better context

### 7. Building Advanced MCP Tools

#### Web Scraping Tool Example with Python

1. **Install Crawl for AI and BeautifulSoup**:
   ```bash
   pip install crawlforAI beautifulsoup4 requests
   ```

2. **Create a web scraping MCP tool**:
   ```python
   @mcp.tool("Crawl website")
   def crawl_website(url: str, max_depth: int = 1) -> str:
       """
       Crawls a website and extracts its content.

       Args:
           url: The URL of the website to crawl
           max_depth: Maximum depth of pages to crawl (default: 1)

       Returns:
           The extracted content in markdown format
       """
       # Import the crawler
       from crawlforAI import Crawler

       # Initialize the crawler with configuration
       crawler = Crawler(
           max_depth=max_depth,
           user_agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
           timeout=30,
           respect_robots_txt=True
       )

       # Crawl the website
       result = crawler.crawl(url)

       # Return the result
       return result
   ```

3. **Alternative implementation using BeautifulSoup**:
   ```python
   @mcp.tool("Extract webpage content")
   def extract_webpage_content(url: str) -> Dict[str, Any]:
       """
       Extracts the main content from a webpage.

       Args:
           url: The URL of the webpage to extract content from

       Returns:
           A dictionary containing the extracted content
       """
       import requests
       from bs4 import BeautifulSoup

       # Send a request to the URL
       headers = {
           "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
       }
       response = requests.get(url, headers=headers)
       response.raise_for_status()  # Raise an exception for HTTP errors

       # Parse the HTML content
       soup = BeautifulSoup(response.text, "html.parser")

       # Extract the title
       title = soup.title.text if soup.title else "No title found"

       # Extract the main content (this is a simple example)
       main_content = ""
       for paragraph in soup.find_all("p"):
           main_content += paragraph.text + "\n\n"

       # Extract headings
       headings = []
       for heading in soup.find_all(["h1", "h2", "h3"]):
           headings.append(heading.text.strip())

       # Return the extracted content
       return {
           "title": title,
           "url": url,
           "headings": headings,
           "main_content": main_content,
           "word_count": len(main_content.split())
       }
   ```

#### API Integration Examples with Python

1. **Figma API Integration**:
   ```python
   @mcp.tool("Get Figma file")
   def get_figma_file(figma_url: str, token: Optional[str] = None) -> Dict[str, Any]:
       """
       Extracts data from a Figma file using the Figma API.

       Args:
           figma_url: The URL of the Figma file
           token: Optional Figma API token (if not provided, uses environment variable)

       Returns:
           The file data from the Figma API
       """
       import re
       import os
       import requests
       from typing import Dict, Any

       # Extract file key from URL
       match = re.search(r'file/([a-zA-Z0-9]+)', figma_url)
       if not match:
           raise ValueError("Invalid Figma URL format. Expected format: https://www.figma.com/file/FILEID/FILENAME")

       file_key = match.group(1)

       # Get token from parameter or environment variable
       api_token = token or os.environ.get("FIGMA_API_TOKEN")
       if not api_token:
           raise ValueError("Figma API token not provided and FIGMA_API_TOKEN environment variable not set")

       # Make API request
       headers = {"X-Figma-Token": api_token}
       response = requests.get(f"https://api.figma.com/v1/files/{file_key}", headers=headers)

       # Check for errors
       response.raise_for_status()

       # Return the result
       return response.json()
   ```

2. **Weather API Integration**:
   ```python
   @mcp.tool("Get weather forecast")
   def get_weather_forecast(city: str, days: int = 3) -> Dict[str, Any]:
       """
       Gets weather forecast for a city using the OpenWeatherMap API.

       Args:
           city: The name of the city
           days: Number of days for the forecast (default: 3)

       Returns:
           Weather forecast data
       """
       import os
       import requests
       from typing import Dict, Any, List

       # Get API key from environment variable
       api_key = os.environ.get("OPENWEATHER_API_KEY")
       if not api_key:
           raise ValueError("OPENWEATHER_API_KEY environment variable not set")

       # Make API request
       url = f"https://api.openweathermap.org/data/2.5/forecast"
       params = {
           "q": city,
           "appid": api_key,
           "units": "metric",
           "cnt": days * 8  # API returns data in 3-hour intervals (8 per day)
       }

       response = requests.get(url, params=params)
       response.raise_for_status()
       data = response.json()

       # Process and format the response
       forecast = []
       for item in data.get("list", [])[:days * 8:8]:  # Get one entry per day
           forecast.append({
               "date": item["dt_txt"].split()[0],
               "temperature": item["main"]["temp"],
               "conditions": item["weather"][0]["description"],
               "humidity": item["main"]["humidity"],
               "wind_speed": item["wind"]["speed"]
           })

       return {
           "city": data["city"]["name"],
           "country": data["city"]["country"],
           "forecast": forecast
       }
   ```

3. **GitHub API Integration**:
   ```python
   @mcp.tool("Get GitHub repository info")
   def get_github_repo_info(repo_url: str) -> Dict[str, Any]:
       """
       Gets information about a GitHub repository.

       Args:
           repo_url: The URL of the GitHub repository

       Returns:
           Repository information
       """
       import re
       import requests
       from typing import Dict, Any

       # Extract owner and repo from URL
       match = re.search(r'github\.com/([^/]+)/([^/]+)', repo_url)
       if not match:
           raise ValueError("Invalid GitHub repository URL")

       owner, repo = match.groups()

       # Make API request
       url = f"https://api.github.com/repos/{owner}/{repo}"
       headers = {"Accept": "application/vnd.github.v3+json"}

       response = requests.get(url, headers=headers)
       response.raise_for_status()
       data = response.json()

       # Extract relevant information
       return {
           "name": data["name"],
           "full_name": data["full_name"],
           "description": data["description"],
           "stars": data["stargazers_count"],
           "forks": data["forks_count"],
           "open_issues": data["open_issues_count"],
           "language": data["language"],
           "created_at": data["created_at"],
           "updated_at": data["updated_at"],
           "license": data["license"]["name"] if data["license"] else None,
           "url": data["html_url"]
       }
   ```

## Advanced Configuration for Unified Context

To achieve a unified contextual experience across different development environments:

### 1. Create a Centralized MCP Server

1. **Set up a dedicated server** or use a cloud service to host your MCP server
2. **Ensure the server is accessible** from all your development environments
3. **Configure authentication** if needed to secure your MCP server

### 2. Implement Context Management with Python

1. **Create a simple in-memory context storage**:
   ```python
   # At the top of your file, define a context storage dictionary
   context_storage = {}

   @mcp.tool("Store context")
   def store_context(key: str, value: str) -> str:
       """
       Stores context information for later retrieval.

       Args:
           key: The key to store the context under
           value: The context value to store

       Returns:
           Confirmation message
       """
       context_storage[key] = value
       return f"Context stored under key: {key}"

   @mcp.tool("Retrieve context")
   def retrieve_context(key: str) -> str:
       """
       Retrieves previously stored context information.

       Args:
           key: The key to retrieve context for

       Returns:
           The stored context value
       """
       return context_storage.get(key, "No context found for this key")
   ```

2. **Implement persistent context storage with SQLite**:
   ```python
   import sqlite3
   import json
   from typing import Any, Dict, Optional

   class ContextStorage:
       def __init__(self, db_path: str = "context.db"):
           """
           Initialize the context storage with a SQLite database.

           Args:
               db_path: Path to the SQLite database file
           """
           self.db_path = db_path
           self._init_db()

       def _init_db(self) -> None:
           """
           Initialize the database schema if it doesn't exist.
           """
           conn = sqlite3.connect(self.db_path)
           cursor = conn.cursor()
           cursor.execute("""
           CREATE TABLE IF NOT EXISTS context (
               key TEXT PRIMARY KEY,
               value TEXT,
               created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
               updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
           )
           """)
           conn.commit()
           conn.close()

       def store(self, key: str, value: Any) -> None:
           """
           Store a value in the context database.

           Args:
               key: The key to store the value under
               value: The value to store (will be JSON serialized)
           """
           conn = sqlite3.connect(self.db_path)
           cursor = conn.cursor()
           serialized_value = json.dumps(value)
           cursor.execute("""
           INSERT OR REPLACE INTO context (key, value, updated_at)
           VALUES (?, ?, CURRENT_TIMESTAMP)
           """, (key, serialized_value))
           conn.commit()
           conn.close()

       def retrieve(self, key: str) -> Optional[Any]:
           """
           Retrieve a value from the context database.

           Args:
               key: The key to retrieve the value for

           Returns:
               The deserialized value, or None if the key doesn't exist
           """
           conn = sqlite3.connect(self.db_path)
           cursor = conn.cursor()
           cursor.execute("SELECT value FROM context WHERE key = ?", (key,))
           result = cursor.fetchone()
           conn.close()

           if result:
               return json.loads(result[0])
           return None

       def delete(self, key: str) -> bool:
           """
           Delete a value from the context database.

           Args:
               key: The key to delete

           Returns:
               True if the key was deleted, False if it didn't exist
           """
           conn = sqlite3.connect(self.db_path)
           cursor = conn.cursor()
           cursor.execute("DELETE FROM context WHERE key = ?", (key,))
           deleted = cursor.rowcount > 0
           conn.commit()
           conn.close()
           return deleted

       def list_keys(self) -> list[str]:
           """
           List all keys in the context database.

           Returns:
               A list of all keys
           """
           conn = sqlite3.connect(self.db_path)
           cursor = conn.cursor()
           cursor.execute("SELECT key FROM context")
           keys = [row[0] for row in cursor.fetchall()]
           conn.close()
           return keys

   # Initialize the context storage
   context_db = ContextStorage()

   @mcp.tool("Store persistent context")
   def store_persistent_context(key: str, value: Any) -> str:
       """
       Stores context information in a persistent SQLite database.

       Args:
           key: The key to store the context under
           value: The context value to store

       Returns:
           Confirmation message
       """
       context_db.store(key, value)
       return f"Context stored under key: {key}"

   @mcp.tool("Retrieve persistent context")
   def retrieve_persistent_context(key: str) -> Any:
       """
       Retrieves context information from a persistent SQLite database.

       Args:
           key: The key to retrieve context for

       Returns:
           The stored context value, or None if not found
       """
       value = context_db.retrieve(key)
       if value is None:
           return "No context found for this key"
       return value

   @mcp.tool("List context keys")
   def list_context_keys() -> list[str]:
       """
       Lists all keys in the context database.

       Returns:
           A list of all context keys
       """
       return context_db.list_keys()
   ```

### 3. Implement Personalized Rules

1. **Create a configuration file** for your personalized rules
2. **Load the configuration** in your MCP server
3. **Apply the rules** to the tools and responses

### 4. Add Documentation Tools with Python

1. **Implement a Git-Ingest tool**:
   ```python
   @mcp.tool("Ingest GitHub repository")
   def ingest_repo(repo_url: str, branch: str = "main", path: str = "") -> str:
       """
       Converts a GitHub repository into LLM-readable data using Git-Ingest.

       Args:
           repo_url: The URL of the GitHub repository
           branch: The branch to ingest (default: main)
           path: Optional specific path within the repository

       Returns:
           The processed repository data in a format suitable for LLMs
       """
       import requests
       from urllib.parse import quote

       # Validate the URL format
       if not repo_url.startswith("https://github.com/"):
           raise ValueError("URL must be a valid GitHub repository URL")

       # Replace github.com with gitingest.com
       ingest_url = repo_url.replace("github.com", "gitingest.com")

       # Add branch and path parameters if provided
       if branch != "main":
           ingest_url += f"/tree/{branch}"

       if path:
           # Ensure path doesn't start with a slash
           path = path.lstrip("/")
           if branch == "main":
               ingest_url += f"/tree/main/{quote(path)}"
           else:
               ingest_url += f"/{quote(path)}"

       # Fetch the processed data
       headers = {
           "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
       }

       try:
           response = requests.get(ingest_url, headers=headers, timeout=60)
           response.raise_for_status()
           return response.text
       except requests.RequestException as e:
           return f"Error fetching repository data: {str(e)}"
   ```

2. **Implement a documentation converter tool**:
   ```python
   @mcp.tool("Convert documentation to markdown")
   def convert_docs_to_markdown(url: str, format: str = "auto") -> str:
       """
       Converts documentation from various formats to markdown.

       Args:
           url: The URL of the documentation to convert
           format: The format of the documentation (auto, html, pdf, rst, etc.)

       Returns:
           The documentation converted to markdown
       """
       import requests
       from bs4 import BeautifulSoup
       import re

       # Determine the format if auto
       if format == "auto":
           if url.endswith(".pdf"):
               format = "pdf"
           elif url.endswith(".rst") or url.endswith(".rest"):
               format = "rst"
           else:
               format = "html"

       # Handle different formats
       if format == "html":
           # Fetch the HTML content
           headers = {
               "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
           }
           response = requests.get(url, headers=headers)
           response.raise_for_status()

           # Parse the HTML
           soup = BeautifulSoup(response.text, "html.parser")

           # Remove script and style elements
           for script in soup(["script", "style"]):
               script.extract()

           # Extract the main content (this is a simplified example)
           main_content = ""

           # Try to find the main content container
           main_element = soup.find("main") or soup.find("article") or soup.find("div", class_=re.compile(r"content|main|documentation"))

           if main_element:
               # Process headings
               for i in range(1, 7):
                   for heading in main_element.find_all(f"h{i}"):
                       heading_text = heading.get_text().strip()
                       main_content += f"{'#' * i} {heading_text}\n\n"

               # Process paragraphs
               for p in main_element.find_all("p"):
                   main_content += f"{p.get_text().strip()}\n\n"

               # Process lists
               for ul in main_element.find_all("ul"):
                   for li in ul.find_all("li"):
                       main_content += f"- {li.get_text().strip()}\n"
                   main_content += "\n"

               for ol in main_element.find_all("ol"):
                   for i, li in enumerate(ol.find_all("li"), 1):
                       main_content += f"{i}. {li.get_text().strip()}\n"
                   main_content += "\n"

               # Process code blocks
               for pre in main_element.find_all("pre"):
                   code = pre.get_text().strip()
                   main_content += f"```\n{code}\n```\n\n"
           else:
               # Fallback to basic text extraction
               main_content = soup.get_text()

           return main_content

       elif format == "pdf":
           return "PDF conversion requires additional libraries. Please install PyPDF2 and implement the conversion logic."

       elif format == "rst":
           return "RST conversion requires additional libraries. Please install docutils and implement the conversion logic."

       else:
           return f"Unsupported format: {format}"
   ```

## Additional Resources Needed

To build a comprehensive MCP implementation, we should gather:

1. **Official MCP Documentation**:
   - Complete SDK documentation for all supported languages
   - API reference for the MCP protocol
   - Best practices for MCP server implementation

2. **Integration Guides**:
   - Detailed guides for integrating MCP with Cursor AI
   - Detailed guides for integrating MCP with Augment AI
   - Instructions for cross-IDE integration

3. **Advanced MCP Examples**:
   - Database integration examples
   - File system access examples
   - Version control integration examples
   - Code analysis tools

4. **Deployment Guides**:
   - How to deploy MCP servers to cloud platforms
   - How to set up authentication for MCP servers
   - How to monitor and maintain MCP servers

5. **Context Management Strategies**:
   - Best practices for managing context across sessions
   - Techniques for efficient context storage and retrieval
   - Methods for context prioritization and pruning

## Next Steps

1. **Find the official MCP SDK repository** and study its documentation
2. **Experiment with basic MCP tools** to understand the framework
3. **Identify specific use cases** for your development workflow
4. **Implement a centralized MCP server** for unified context
5. **Integrate with your preferred AI coding tools** (Cursor AI, Augment AI)
6. **Develop custom tools** for your specific needs
7. **Set up context management** for persistent context across sessions

## Tasks
- [ ] Find and review the official MCP SDK documentation
- [ ] Set up a basic MCP server with a simple tool
- [ ] Test integration with Cursor AI
- [ ] Test integration with Augment AI
- [ ] Implement a context storage mechanism
- [ ] Create tools for documentation conversion
- [ ] Set up a centralized MCP server for unified context

## Related
- [[Prompt Engineering TOC]]
- [[MCP Setup Guide]]
- [[MCP Customization]]
- [[Developer Profile]]
- [[AI Assistant Memory Management Guidelines]]
