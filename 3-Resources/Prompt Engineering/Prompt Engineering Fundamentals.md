---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Personal Research
tags: [para/resources, prompt-engineering, ai, llm, fundamentals, guide]
related: ["LLM Capabilities and Limitations", "Context Window Management", "System Prompt Templates"]
area: Software-Development
difficulty: medium
keywords: [prompt-engineering, llm, ai, chatgpt, claude, gemini, instruction]
last_used: 2025-04-16
---

# Prompt Engineering Fundamentals

## Overview
Core concepts and principles for crafting effective prompts that elicit optimal responses from large language models (LLMs).

## Key Principles

### 1. Clarity and Specificity
- **Be Explicit**: Clearly state what you want the LLM to do
- **Provide Context**: Include relevant background information
- **Define Scope**: Set boundaries for the response
- **Specify Format**: Indicate how you want the information presented

### 2. Structured Instructions
- **Sequential Steps**: Break complex tasks into ordered steps
- **Numbered Lists**: Use numbered lists for multi-part instructions
- **Hierarchical Organization**: Group related instructions together
- **Priority Indicators**: Highlight the most important aspects

### 3. Role and Persona Assignment
- **Expert Roles**: Assign specific expert roles to the LLM
- **Audience Awareness**: Specify the intended audience for the response
- **Tone Setting**: Define the desired tone (formal, casual, technical)
- **Perspective Guidance**: Indicate what perspective to adopt

### 4. Constraints and Parameters
- **Length Constraints**: Specify desired response length
- **Depth vs. Breadth**: Indicate whether to go deep on fewer topics or cover more topics briefly
- **Complexity Level**: Specify the technical level (beginner, intermediate, expert)
- **Time Constraints**: Indicate if there are time limitations

## Prompt Structure Templates

### Basic Prompt Template
```
[TASK]: Briefly describe the specific task
[CONTEXT]: Provide relevant background information
[FORMAT]: Specify the desired output format
[CONSTRAINTS]: Mention any limitations or requirements
```

### Expert Consultation Template
```
I want you to act as [EXPERT ROLE].
[CONTEXT]: Describe the situation or problem
[QUESTION]: Ask specific questions
[OUTPUT]: Specify what kind of response you want
```

### Comparative Analysis Template
```
Compare and contrast [TOPIC A] and [TOPIC B] with respect to:
1. [ASPECT 1]
2. [ASPECT 2]
3. [ASPECT 3]
Format your response as a table with pros and cons for each.
```

### Step-by-Step Guide Template
```
Create a comprehensive step-by-step guide for [TASK].
For each step, include:
- A clear instruction
- The rationale behind it
- Common pitfalls to avoid
Target audience: [AUDIENCE]
```

## Common Pitfalls to Avoid

### 1. Vague Instructions
- **Problem**: "Tell me about programming" is too broad
- **Improved**: "Explain object-oriented programming principles with C# examples for a beginner developer"

### 2. Contradictory Requirements
- **Problem**: Asking for "detailed but brief" explanations creates confusion
- **Improved**: "Provide a concise overview of key concepts, then elaborate on the most important one"

### 3. Insufficient Context
- **Problem**: Asking for code without specifying language, purpose, or constraints
- **Improved**: "Write a Python function that validates Australian phone numbers, optimized for readability"

### 4. Overloading with Instructions
- **Problem**: Including too many requirements in a single prompt
- **Improved**: Break into multiple prompts or clearly prioritize requirements

## Advanced Techniques

### Chain of Thought Prompting
Guide the LLM through a reasoning process by asking it to think step by step:
```
Think through this problem step by step:
[PROBLEM DESCRIPTION]
```

### Few-Shot Learning
Provide examples of the desired input-output pattern:
```
Convert these requirements to user stories:
Requirement: The system must allow users to reset their password
User Story: As a user, I want to reset my password so that I can regain access to my account if I forget it.

Requirement: [YOUR REQUIREMENT]
```

### Self-Reflection Prompting
Ask the LLM to evaluate its own response:
```
[YOUR PROMPT]

After providing your answer, critique your response and identify any potential improvements or missing information.
```

## Adapting to Different LLMs

### ChatGPT (OpenAI)
- Responds well to detailed, structured prompts
- Benefits from explicit formatting instructions
- Handles role-playing prompts effectively

### Claude (Anthropic)
- Excels with nuanced, conversational prompts
- Strong at following complex, multi-step instructions
- Good at maintaining context over longer conversations

### Gemini (Google)
- Works well with concise, clear instructions
- Effective with technical and code-related prompts
- Benefits from explicit examples

## Related
- [[Prompt Engineering TOC]]
- [[LLM Capabilities and Limitations]]
- [[Context Window Management]]
- [[3System Prompt Templates]]
- [[Chain of Thought Prompting]]
