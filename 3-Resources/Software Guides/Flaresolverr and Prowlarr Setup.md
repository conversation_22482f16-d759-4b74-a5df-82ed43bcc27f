---
creation_date: 2025-04-06
modification_date: 2025-04-16
type: resource
source: Personal research
tags: [para/resources, guide, software, docker, media-server, self-hosted, flaresolverr, prowlarr, cloudflare, bypass, indexer, linux, pop-os]
related: ["Media Streaming Server Setup", "Docker Configuration Files", "Managing Docker Folder Permissions"]
area: "Media-Management"
difficulty: medium
operating_system: "Linux (Pop-OS)"
services: ["Flaresolverr", "Prowlarr"]
ports: ["8080", "9696"]
installation_methods: ["Docker", "Direct"]
---

# Flaresolverr and Prowlarr Setup

## Overview
A comprehensive guide for setting up FlareSolverr to work with Prowlarr on Linux (specifically Pop!_OS) to bypass Cloudflare protection for indexers.

## Prerequisites
- Docker (Recommended): This is the easiest and most isolated way to run FlareSolverr
- Docker Compose (Recommended): Simplifies managing Docker containers
- Prowlarr Installed: You should already have Prowlarr running on your system

## Method 1: Using Docker and Docker Compose (Recommended)

This is the preferred method due to its ease of setup and management.

### Step 1: Install Docker and Docker Compose

```bash
sudo apt update
sudo apt install docker.io
sudo systemctl start docker
sudo systemctl enable docker

# Install Docker Compose
sudo apt install docker-compose
```

Verify the installations:
```bash
docker --version
docker-compose --version
```

### Step 2: Create a docker-compose.yml file

Create a new directory for your FlareSolverr setup:
```bash
mkdir ~/flaresolverr
cd ~/flaresolverr
```

Create a docker-compose.yml file:
```bash
vim docker-compose.yml
```

Paste the following content:
```yaml
version: '3.8'
services:
  flaresolverr:
    image: flaresolverr/flaresolverr:latest
    container_name: flaresolverr
    restart: unless-stopped
    ports:
      - "8080:8080" # Maps host port 8080 to container port 8080
    environment:
      - LOG_LEVEL=info # Optional: Set log level (debug, info, warning, error)
      - TZ=Australia/Brisbane # Optional: Set your timezone
```

### Step 3: Start FlareSolverr using Docker Compose

```bash
docker-compose up -d
```

### Step 4: Verify FlareSolverr is running

```bash
docker ps
```

You can also try accessing the FlareSolverr health endpoint:
```
http://localhost:8080/
```

### Step 5: Configure Prowlarr to use FlareSolverr

1. Open your Prowlarr web interface
2. Navigate to Settings > Indexers
3. Edit an indexer that requires Cloudflare bypassing
4. Look for a section related to Cloudflare Solver
5. Enable the Cloudflare Solver option
6. Enter the FlareSolverr URL: `http://localhost:8080/`
7. Click Save on the indexer settings

### Step 6: Test the Indexer

After configuring FlareSolverr in Prowlarr, test the affected indexer. Prowlarr should now be able to use FlareSolverr to bypass Cloudflare protection.

## Method 2: Running FlareSolverr Directly (Less Recommended)

This method involves downloading and running FlareSolverr directly on your system.

### Step 1: Install Node.js and npm

```bash
sudo apt update
sudo apt install nodejs npm
```

Verify the installations:
```bash
node -v
npm -v
```

### Step 2: Download FlareSolverr

```bash
git clone https://github.com/FlareSolverr/FlareSolverr.git
cd FlareSolverr
```

### Step 3: Install Dependencies

```bash
npm install
```

### Step 4: Run FlareSolverr

```bash
node server.js
```

### Step 5: Configure Prowlarr

Follow the same steps as in Method 1 to configure Prowlarr.

### Step 6: Keep FlareSolverr Running

Create a systemd service:
```bash
sudo nano /etc/systemd/system/flaresolverr.service
```

Paste the following content:
```ini
[Unit]
Description=FlareSolverr
After=network.target

[Service]
User=<your_username>  # Replace with your actual username
WorkingDirectory=/path/to/FlareSolverr # Replace with the actual path
ExecStart=/usr/bin/node server.js
Restart=on-failure

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl enable flaresolverr.service
sudo systemctl start flaresolverr.service
```

## Choosing a Method

The Docker method is highly recommended for its simplicity, isolation, and ease of updates. Running FlareSolverr directly can be more involved in terms of dependency management and ensuring it runs reliably in the background.

## Related
- [[Media Streaming Server Setup]]
- [[Network Architecture Overview]]
- [[3-Resources]]
- [[Resources TOC]]
