---
creation_date: 2025-04-07
modification_date: 2025-04-16
type: resource
source: Personal configuration
tags: [para/resources, docker, docker-compose, configuration, yaml, mediaserver, nginx, software, containers, virtualization, linux, networking]
related: ["Managing Docker Folder Permissions", "Media Streaming Server Setup", "Network Architecture Overview"]
area: "Network-Administration"
difficulty: medium
operating_system: "Linux"
services: ["nginx-proxy-manager", "flaresolverr", "jellyfin", "radarr"]
networks: ["proxy-manager"]
volumes: ["npm_data", "letsencrypt"]
---

# Docker Configuration Files

## Overview
This document provides an overview and explanation of the Docker Compose configuration files used in the media server and network setup.

## Core Concepts

- **Docker Compose**: A tool for defining and running multi-container Docker applications. It uses YAML files (`docker-compose.yml`) to configure application services, networks, and volumes.
- **`.env` File**: A file used to store environment variables that can be referenced within the `docker-compose.yml` file. This is useful for managing sensitive information or configuration parameters like User/Group IDs (`PUID`/`PGID`), timezones (`TZ`), and file paths.

## Configuration Files

### 1. Nginx Proxy Manager Configuration

**Purpose**: Defines the services for Nginx Proxy Manager (NPM) and related utilities like Flaresolverr.

**Key Sections**:
- `services`: Defines individual containers (e.g., `nginx-proxy-manager`, `flaresolverr`).
  - `image`: Specifies the Docker image to use.
  - `container_name`: Assigns a readable name.
  - `volumes`: Mounts host directories or named volumes into the container (e.g., for configuration persistence `/data`, `/etc/letsencrypt`).
  - `ports`: Maps host ports to container ports (e.g., `80:80`, `443:443`, `81:81` for NPM).
  - `networks`: Connects the container to specific Docker networks (e.g., `proxy-manager` custom bridge network).
  - `environment`: Sets environment variables within the container (e.g., `PUID`, `PGID` if applicable, `DB_SQLITE_FILE`).
  - `restart`: Defines the container restart policy (e.g., `unless-stopped`).
- `networks`: Defines custom Docker networks (e.g., `proxy-manager: bridge`).
- `volumes`: Defines named volumes managed by Docker (e.g., `npm_data`).

**Example Configuration**:
```yaml
version: '3'
services:
  nginx-proxy-manager:
    image: 'jc21/nginx-proxy-manager:latest'
    container_name: nginx-proxy-manager
    restart: unless-stopped
    ports:
      - '80:80'
      - '81:81'
      - '443:443'
    volumes:
      - ./data:/data
      - ./letsencrypt:/etc/letsencrypt
    networks:
      - proxy-manager

  flaresolverr:
    image: 'flaresolverr/flaresolverr:latest'
    container_name: flaresolverr
    restart: unless-stopped
    environment:
      - LOG_LEVEL=info
      - TZ=Australia/Brisbane
    networks:
      - proxy-manager

networks:
  proxy-manager:
    driver: bridge
```

### 2. Media Server Configuration

**Purpose**: Defines the services for the media stack (Radarr, Sonarr, qBittorrent, etc.).

**Key Sections**:
- `environment`: Crucial for setting `PUID`, `PGID`, and `TZ` consistently across media applications.
- `volumes`: Mapping configuration directories (`:/config`) and the shared media library path (e.g., `/mnt/storage/media:/media` or specific subfolders like `/mnt/storage/media/downloads:/downloads`).
- `networks`: Connects to the same `proxy-manager` network for NPM to proxy them.

**Example Configuration**:
```yaml
version: '3'
services:
  jellyfin:
    image: jellyfin/jellyfin:latest
    container_name: jellyfin
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${APPDATA_ROOT}/jellyfin:/config
      - ${MOVIES_ROOT}:/movies
      - ${TV_ROOT}:/tv
    ports:
      - 8096:8096
    restart: unless-stopped
    networks:
      - proxy-manager

  radarr:
    image: linuxserver/radarr:latest
    container_name: radarr
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ${APPDATA_ROOT}/radarr:/config
      - ${MOVIES_ROOT}:/movies
      - ${DOWNLOADS_ROOT}:/downloads
    ports:
      - 7878:7878
    restart: unless-stopped
    networks:
      - proxy-manager

networks:
  proxy-manager:
    external: true
```

### 3. Environment Variables File

**Purpose**: Stores common variables referenced in the `docker-compose.yml` files.

**Example Content**:
```dotenv
# .env - Common Environment Variables

# User/Group IDs
PUID=1000
PGID=2000 # Assuming a dedicated 'media' group

# Timezone
TZ=Australia/Brisbane

# Root Paths (Adjust to your system)
MEDIA_ROOT=/mnt/storage/media
DOWNLOADS_ROOT=${MEDIA_ROOT}/downloads
MOVIES_ROOT=${MEDIA_ROOT}/movies
TV_ROOT=${MEDIA_ROOT}/tv
APPDATA_ROOT=/opt/docker/appdata

# Docker Network Name (Ensure consistency)
PROXY_NETWORK=proxy-manager
```

## Usage

Typically, you navigate to the directory containing the specific `docker-compose.yml` file and run commands:

- `docker-compose up -d`: Start services in detached mode.
- `docker-compose down`: Stop and remove containers, networks.
- `docker-compose pull`: Update images to the latest version defined.
- `docker-compose logs -f [service_name]`: View logs for a service (e.g., `radarr`).

## Related
- [[Media Streaming Server Setup]]
- [[Network Architecture Overview]]
- [[3-Resources]]
- [[Resources TOC]]
