# Dotfiles configuration file
# Format: [component]
#         source=source_path
#         target=target_path

[fish]
source=$HOME/.config/fish
target=fish/.config/fish

[i3]
source=$HOME/.config/i3
target=i3/.config/i3

[xfce4]
source=$HOME/.config/xfce4
target=xfce4/.config/xfce4

[git]
source=$HOME/.gitconfig
target=git/.gitconfig

[x11]
source=$HOME/.Xresources
target=x11/.Xresources

[bash]
source=$HOME/.bashrc
target=bash/.bashrc

[vim]
source=$HOME/.vimrc
target=vim/.vimrc

[ssh]
source=$HOME/.ssh/config
target=ssh/.ssh/config

[alacritty]
source=$HOME/.config/alacritty
target=alacritty/.config/alacritty

[kitty]
source=$HOME/.config/kitty
target=kitty/.config/kitty

[nvim]
source=$HOME/.config/nvim
target=nvim/.config/nvim

[tmux]
source=$HOME/.tmux.conf
target=tmux/.tmux.conf

[zsh]
source=$HOME/.zshrc
target=zsh/.zshrc

[fonts]
source=$HOME/.local/share/fonts
target=fonts/.local/share/fonts

[gtk]
source=$HOME/.config/gtk-3.0
target=gtk/.config/gtk-3.0

[rofi]
source=$HOME/.config/rofi
target=rofi/.config/rofi

[dunst]
source=$HOME/.config/dunst
target=dunst/.config/dunst

[picom]
source=$HOME/.config/picom
target=picom/.config/picom

[polybar]
source=$HOME/.config/polybar
target=polybar/.config/polybar
