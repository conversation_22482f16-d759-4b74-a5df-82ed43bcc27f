#!/bin/bash

# Dotfiles Backup Script
# This script backs up the current configuration to the dotfiles repository

set -e

DOTFILES_DIR="$HOME/dotfiles"
CONFIG_FILE="$HOME/.dotfiles.conf"
LOG_FILE="$DOTFILES_DIR/backup.log"
VERBOSE=0
DRY_RUN=0
COMMIT=0
PUSH=0
COMPONENTS=()

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log message to console and log file
log() {
    local level="$1"
    local message="$2"
    local color="$NC"
    
    case "$level" in
        "INFO") color="$BLUE" ;;
        "SUCCESS") color="$GREEN" ;;
        "WARNING") color="$YELLOW" ;;
        "ERROR") color="$RED" ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
    echo "[$level] $message" >> "$LOG_FILE"
}

# Display script usage
usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  --help                 Display this help message"
    echo "  --verbose              Enable verbose output"
    echo "  --dry-run              Show what would be copied without making changes"
    echo "  --commit               Automatically commit changes to the repository"
    echo "  --push                 Push changes to the remote repository (implies --commit)"
    echo "  --component NAME       Backup only the specified component"
    echo "  --all                  Backup all configured components (default)"
    echo
    exit 1
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help)
                usage
                ;;
            --verbose)
                VERBOSE=1
                shift
                ;;
            --dry-run)
                DRY_RUN=1
                shift
                ;;
            --commit)
                COMMIT=1
                shift
                ;;
            --push)
                PUSH=1
                COMMIT=1
                shift
                ;;
            --component)
                if [[ -z "$2" || "$2" == --* ]]; then
                    log "ERROR" "Missing component name after --component"
                    usage
                fi
                COMPONENTS+=("$2")
                shift 2
                ;;
            --all)
                COMPONENTS=()
                shift
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                usage
                ;;
        esac
    done
}

# Check if the configuration file exists, create it if it doesn't
check_config_file() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log "INFO" "Configuration file not found, creating default..."
        
        cat > "$CONFIG_FILE" << EOF
# Dotfiles configuration file
# Format: [component]
#         source=source_path
#         target=target_path

[fish]
source=$HOME/.config/fish
target=fish/.config/fish

[i3]
source=$HOME/.config/i3
target=i3/.config/i3

[xfce4]
source=$HOME/.config/xfce4
target=xfce4/.config/xfce4

[git]
source=$HOME/.gitconfig
target=git/.gitconfig

[x11]
source=$HOME/.Xresources
target=x11/.Xresources

[bash]
source=$HOME/.bashrc
target=bash/.bashrc

[vim]
source=$HOME/.vimrc
target=vim/.vimrc

[ssh]
source=$HOME/.ssh/config
target=ssh/.ssh/config
EOF
        
        log "SUCCESS" "Default configuration file created at $CONFIG_FILE"
    fi
}

# Parse the configuration file
parse_config() {
    local components=()
    local current_component=""
    
    while IFS= read -r line; do
        # Skip comments and empty lines
        if [[ "$line" =~ ^[[:space:]]*# || -z "$line" ]]; then
            continue
        fi
        
        # Component section
        if [[ "$line" =~ ^\[([^]]+)\]$ ]]; then
            current_component="${BASH_REMATCH[1]}"
            components+=("$current_component")
            declare -g "${current_component}_source="
            declare -g "${current_component}_target="
        # Key-value pair
        elif [[ "$line" =~ ^([^=]+)=(.*)$ ]]; then
            local key="${BASH_REMATCH[1]}"
            local value="${BASH_REMATCH[2]}"
            
            if [[ "$key" == "source" ]]; then
                declare -g "${current_component}_source=$value"
            elif [[ "$key" == "target" ]]; then
                declare -g "${current_component}_target=$value"
            fi
        fi
    done < "$CONFIG_FILE"
    
    # If no components specified, use all from config
    if [[ ${#COMPONENTS[@]} -eq 0 ]]; then
        COMPONENTS=("${components[@]}")
    fi
}

# Backup a specific component
backup_component() {
    local component="$1"
    local source_var="${component}_source"
    local target_var="${component}_target"
    
    # Check if component exists in config
    if [[ -z "${!source_var}" || -z "${!target_var}" ]]; then
        log "ERROR" "Component not found in configuration: $component"
        return 1
    fi
    
    local source="${!source_var}"
    local target="$DOTFILES_DIR/${!target_var}"
    
    log "INFO" "Backing up component: $component"
    log "INFO" "  Source: $source"
    log "INFO" "  Target: $target"
    
    # Skip if source doesn't exist
    if [[ ! -e "$source" ]]; then
        log "WARNING" "Source does not exist, skipping: $source"
        return 0
    fi
    
    # Create target directory
    mkdir -p "$(dirname "$target")"
    
    # Copy files
    if [[ "$DRY_RUN" -eq 1 ]]; then
        log "INFO" "  [DRY RUN] Would copy $source to $target"
    else
        if [[ -d "$source" ]]; then
            rsync -av --delete "$source/" "$target/" >> "$LOG_FILE" 2>&1
        else
            cp -f "$source" "$target" >> "$LOG_FILE" 2>&1
        fi
        log "SUCCESS" "  Backup completed for: $component"
    fi
}

# Commit changes to the repository
commit_changes() {
    if [[ "$DRY_RUN" -eq 1 ]]; then
        log "INFO" "[DRY RUN] Would commit changes to repository"
        return 0
    fi
    
    log "INFO" "Committing changes to repository..."
    
    cd "$DOTFILES_DIR"
    
    # Check if there are changes to commit
    if git status --porcelain | grep -q .; then
        git add .
        git commit -m "Automatic backup: $(date +%Y-%m-%d\ %H:%M:%S)"
        log "SUCCESS" "Changes committed to repository"
    else
        log "INFO" "No changes to commit"
    fi
}

# Push changes to remote repository
push_changes() {
    if [[ "$DRY_RUN" -eq 1 ]]; then
        log "INFO" "[DRY RUN] Would push changes to remote repository"
        return 0
    fi
    
    log "INFO" "Pushing changes to remote repository..."
    
    cd "$DOTFILES_DIR"
    
    # Check if remote exists
    if git remote | grep -q origin; then
        git push origin
        log "SUCCESS" "Changes pushed to remote repository"
    else
        log "WARNING" "No remote repository configured"
    fi
}

# Main function
main() {
    # Create log file
    mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE"
    
    log "INFO" "Starting dotfiles backup..."
    
    # Parse command line arguments
    parse_args "$@"
    
    # Check configuration file
    check_config_file
    
    # Parse configuration
    parse_config
    
    # Backup components
    for component in "${COMPONENTS[@]}"; do
        backup_component "$component"
    done
    
    # Commit changes if requested
    if [[ "$COMMIT" -eq 1 ]]; then
        commit_changes
    fi
    
    # Push changes if requested
    if [[ "$PUSH" -eq 1 ]]; then
        push_changes
    fi
    
    log "SUCCESS" "Dotfiles backup completed successfully!"
}

# Run the main function
main "$@"
