#!/bin/bash

# Dotfiles Repository Setup Script
# This script sets up the initial dotfiles repository structure

set -e

DOTFILES_DIR="$HOME/dotfiles"
LOG_FILE="$DOTFILES_DIR/setup.log"
GITHUB_USERNAME=""
REPO_NAME="dotfiles"

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log message to console and log file
log() {
    local level="$1"
    local message="$2"
    local color="$NC"
    
    case "$level" in
        "INFO") color="$BLUE" ;;
        "SUCCESS") color="$GREEN" ;;
        "WARNING") color="$YELLOW" ;;
        "ERROR") color="$RED" ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
    echo "[$level] $message" >> "$LOG_FILE"
}

# Display script usage
usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  --help                 Display this help message"
    echo "  --username USERNAME    GitHub username"
    echo "  --repo REPO            Repository name (default: dotfiles)"
    echo
    exit 1
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help)
                usage
                ;;
            --username)
                if [[ -z "$2" || "$2" == --* ]]; then
                    log "ERROR" "Missing username after --username"
                    usage
                fi
                GITHUB_USERNAME="$2"
                shift 2
                ;;
            --repo)
                if [[ -z "$2" || "$2" == --* ]]; then
                    log "ERROR" "Missing repository name after --repo"
                    usage
                fi
                REPO_NAME="$2"
                shift 2
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                usage
                ;;
        esac
    done
    
    # Check if GitHub username is provided
    if [[ -z "$GITHUB_USERNAME" ]]; then
        read -p "Enter your GitHub username: " GITHUB_USERNAME
    fi
}

# Create the dotfiles directory
create_dotfiles_dir() {
    log "INFO" "Creating dotfiles directory..."
    
    if [[ -d "$DOTFILES_DIR" ]]; then
        log "WARNING" "Dotfiles directory already exists: $DOTFILES_DIR"
        read -p "Do you want to continue and potentially overwrite existing files? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "INFO" "Setup aborted."
            exit 1
        fi
    else
        mkdir -p "$DOTFILES_DIR"
    fi
    
    log "SUCCESS" "Dotfiles directory created: $DOTFILES_DIR"
}

# Initialize Git repository
init_git_repo() {
    log "INFO" "Initializing Git repository..."
    
    cd "$DOTFILES_DIR"
    
    # Check if Git repository already exists
    if [[ -d ".git" ]]; then
        log "WARNING" "Git repository already exists"
    else
        git init
        log "SUCCESS" "Git repository initialized"
    fi
    
    # Create .gitignore file
    cat > .gitignore << EOF
# Dotfiles .gitignore

# Logs
*.log

# Backup files
*.bak
*~

# Sensitive information
.ssh/id_*
.ssh/*.pem
.gnupg/private-keys-*
.gnupg/secring.gpg

# Cache files
.cache/
EOF
    
    log "SUCCESS" "Created .gitignore file"
}

# Create README file
create_readme() {
    log "INFO" "Creating README file..."
    
    cat > "$DOTFILES_DIR/README.md" << EOF
# Dotfiles

My personal dotfiles for EndeavourOS.

## Overview

This repository contains my personal dotfiles and configuration files for various applications and tools I use on EndeavourOS.

## Components

- Fish shell configuration
- i3 window manager configuration
- XFCE4 desktop environment configuration
- Git configuration
- X11 configuration
- And more...

## Installation

To install these dotfiles on a new system:

1. Clone the repository:
   \`\`\`bash
   git clone https://github.com/$GITHUB_USERNAME/$REPO_NAME.git ~/dotfiles
   cd ~/dotfiles
   \`\`\`

2. Run the installation script:
   \`\`\`bash
   ./install.sh
   \`\`\`

## Backup

To backup your current configuration:

\`\`\`bash
./backup.sh
\`\`\`

## License

MIT
EOF
    
    log "SUCCESS" "Created README.md file"
}

# Create directory structure
create_directory_structure() {
    log "INFO" "Creating directory structure..."
    
    # Create component directories
    mkdir -p "$DOTFILES_DIR/fish/.config/fish"
    mkdir -p "$DOTFILES_DIR/i3/.config/i3"
    mkdir -p "$DOTFILES_DIR/xfce4/.config/xfce4"
    mkdir -p "$DOTFILES_DIR/git"
    mkdir -p "$DOTFILES_DIR/x11"
    mkdir -p "$DOTFILES_DIR/bash"
    mkdir -p "$DOTFILES_DIR/vim"
    mkdir -p "$DOTFILES_DIR/ssh/.ssh"
    
    log "SUCCESS" "Directory structure created"
}

# Create configuration file
create_config_file() {
    log "INFO" "Creating configuration file..."
    
    cat > "$HOME/.dotfiles.conf" << EOF
# Dotfiles configuration file
# Format: [component]
#         source=source_path
#         target=target_path

[fish]
source=$HOME/.config/fish
target=fish/.config/fish

[i3]
source=$HOME/.config/i3
target=i3/.config/i3

[xfce4]
source=$HOME/.config/xfce4
target=xfce4/.config/xfce4

[git]
source=$HOME/.gitconfig
target=git/.gitconfig

[x11]
source=$HOME/.Xresources
target=x11/.Xresources

[bash]
source=$HOME/.bashrc
target=bash/.bashrc

[vim]
source=$HOME/.vimrc
target=vim/.vimrc

[ssh]
source=$HOME/.ssh/config
target=ssh/.ssh/config
EOF
    
    log "SUCCESS" "Configuration file created at $HOME/.dotfiles.conf"
}

# Copy scripts to repository
copy_scripts() {
    log "INFO" "Copying scripts to repository..."
    
    # Make scripts executable
    chmod +x "$DOTFILES_DIR/install.sh"
    chmod +x "$DOTFILES_DIR/backup.sh"
    
    log "SUCCESS" "Scripts copied and made executable"
}

# Setup GitHub repository
setup_github_repo() {
    log "INFO" "Setting up GitHub repository..."
    
    read -p "Do you want to create a GitHub repository now? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "INFO" "GitHub repository setup skipped."
        return 0
    fi
    
    cd "$DOTFILES_DIR"
    
    # Check if remote already exists
    if git remote | grep -q origin; then
        log "WARNING" "Remote 'origin' already exists"
        return 0
    fi
    
    # Create GitHub repository
    log "INFO" "Creating GitHub repository: $GITHUB_USERNAME/$REPO_NAME"
    log "INFO" "Please create a repository on GitHub manually:"
    log "INFO" "https://github.com/new"
    log "INFO" "Repository name: $REPO_NAME"
    log "INFO" "Description: My personal dotfiles for EndeavourOS"
    log "INFO" "Make it public or private as you prefer"
    log "INFO" "Do NOT initialize with README, .gitignore, or license"
    
    read -p "Press Enter once you've created the repository on GitHub..."
    
    # Add remote
    git remote add origin "https://github.com/$GITHUB_USERNAME/$REPO_NAME.git"
    
    log "SUCCESS" "GitHub repository setup completed"
}

# Initial commit and push
initial_commit_push() {
    log "INFO" "Making initial commit..."
    
    cd "$DOTFILES_DIR"
    
    git add .
    git commit -m "Initial commit"
    
    log "SUCCESS" "Initial commit created"
    
    read -p "Do you want to push to GitHub now? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "INFO" "Push to GitHub skipped."
        return 0
    fi
    
    git push -u origin master
    
    log "SUCCESS" "Pushed to GitHub"
}

# Main function
main() {
    # Create log file
    mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE"
    
    log "INFO" "Starting dotfiles repository setup..."
    
    # Parse command line arguments
    parse_args "$@"
    
    # Create dotfiles directory
    create_dotfiles_dir
    
    # Initialize Git repository
    init_git_repo
    
    # Create README file
    create_readme
    
    # Create directory structure
    create_directory_structure
    
    # Create configuration file
    create_config_file
    
    # Copy scripts to repository
    copy_scripts
    
    # Setup GitHub repository
    setup_github_repo
    
    # Initial commit and push
    initial_commit_push
    
    log "SUCCESS" "Dotfiles repository setup completed successfully!"
    log "INFO" "Next steps:"
    log "INFO" "1. Run './backup.sh' to backup your current configuration"
    log "INFO" "2. Customize your dotfiles as needed"
    log "INFO" "3. Commit and push changes to GitHub"
}

# Run the main function
main "$@"
