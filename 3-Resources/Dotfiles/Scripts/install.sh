#!/bin/bash

# Dotfiles Installation Script
# This script installs the dotfiles configuration on a new system

set -e

DOTFILES_DIR="$HOME/dotfiles"
BACKUP_DIR="$HOME/.dotfiles_backup_$(date +%Y%m%d%H%M%S)"
LOG_FILE="$DOTFILES_DIR/install.log"
VERBOSE=0
DEPENDENCIES_ONLY=0
COMPONENTS=()

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log message to console and log file
log() {
    local level="$1"
    local message="$2"
    local color="$NC"
    
    case "$level" in
        "INFO") color="$BLUE" ;;
        "SUCCESS") color="$GREEN" ;;
        "WARNING") color="$YELLOW" ;;
        "ERROR") color="$RED" ;;
    esac
    
    echo -e "${color}[$level] $message${NC}"
    echo "[$level] $message" >> "$LOG_FILE"
}

# Display script usage
usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  --help                 Display this help message"
    echo "  --verbose              Enable verbose output"
    echo "  --dependencies         Only install dependencies"
    echo "  --component NAME       Install only the specified component"
    echo "  --all                  Install all components (default)"
    echo
    exit 1
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help)
                usage
                ;;
            --verbose)
                VERBOSE=1
                shift
                ;;
            --dependencies)
                DEPENDENCIES_ONLY=1
                shift
                ;;
            --component)
                if [[ -z "$2" || "$2" == --* ]]; then
                    log "ERROR" "Missing component name after --component"
                    usage
                fi
                COMPONENTS+=("$2")
                shift 2
                ;;
            --all)
                COMPONENTS=()
                shift
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                usage
                ;;
        esac
    done
}

# Check if the script is running on EndeavourOS
check_system() {
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        if [[ "$ID" != "endeavouros" && "$ID_LIKE" != *"arch"* ]]; then
            log "WARNING" "This script is designed for EndeavourOS or Arch-based systems."
            read -p "Continue anyway? (y/N) " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log "INFO" "Installation aborted."
                exit 1
            fi
        fi
    else
        log "WARNING" "Could not determine system type."
        read -p "Continue anyway? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "INFO" "Installation aborted."
            exit 1
        fi
    fi
}

# Install required dependencies
install_dependencies() {
    log "INFO" "Installing dependencies..."
    
    local packages=(
        stow
        git
        fish
        i3-wm
        i3status
        dmenu
        xfce4
        vim
        neovim
        alacritty
        kitty
    )
    
    # Ask user which packages to install
    echo "The following packages will be installed:"
    for pkg in "${packages[@]}"; do
        echo "  - $pkg"
    done
    
    read -p "Continue with installation? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "INFO" "Package installation skipped."
        return
    fi
    
    # Install packages
    if command -v pacman &> /dev/null; then
        sudo pacman -S --needed "${packages[@]}"
    else
        log "ERROR" "Package manager not supported."
        exit 1
    fi
    
    log "SUCCESS" "Dependencies installed successfully."
}

# Create backup of existing configuration
create_backup() {
    log "INFO" "Creating backup of existing configuration..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup fish configuration
    if [[ -d "$HOME/.config/fish" ]]; then
        cp -r "$HOME/.config/fish" "$BACKUP_DIR/"
        log "INFO" "Fish configuration backed up."
    fi
    
    # Backup i3 configuration
    if [[ -d "$HOME/.config/i3" ]]; then
        cp -r "$HOME/.config/i3" "$BACKUP_DIR/"
        log "INFO" "i3 configuration backed up."
    fi
    
    # Backup xfce4 configuration
    if [[ -d "$HOME/.config/xfce4" ]]; then
        cp -r "$HOME/.config/xfce4" "$BACKUP_DIR/"
        log "INFO" "XFCE4 configuration backed up."
    fi
    
    # Backup git configuration
    if [[ -f "$HOME/.gitconfig" ]]; then
        cp "$HOME/.gitconfig" "$BACKUP_DIR/"
        log "INFO" "Git configuration backed up."
    fi
    
    # Backup X11 configuration
    if [[ -f "$HOME/.Xresources" ]]; then
        cp "$HOME/.Xresources" "$BACKUP_DIR/"
        log "INFO" "X11 configuration backed up."
    fi
    
    log "SUCCESS" "Backup created at $BACKUP_DIR"
}

# Install a specific component using stow
install_component() {
    local component="$1"
    
    if [[ ! -d "$DOTFILES_DIR/$component" ]]; then
        log "ERROR" "Component directory not found: $component"
        return 1
    fi
    
    log "INFO" "Installing component: $component"
    
    # Use stow to create symlinks
    cd "$DOTFILES_DIR"
    stow -v "$component" 2>> "$LOG_FILE"
    
    log "SUCCESS" "Component installed: $component"
}

# Install all components
install_all_components() {
    log "INFO" "Installing all components..."
    
    # Get all directories in the dotfiles directory
    local all_components=()
    for dir in "$DOTFILES_DIR"/*/; do
        component=$(basename "$dir")
        # Skip .git directory
        if [[ "$component" != ".git" ]]; then
            all_components+=("$component")
        fi
    done
    
    # Install each component
    for component in "${all_components[@]}"; do
        install_component "$component"
    done
    
    log "SUCCESS" "All components installed."
}

# Set up additional configuration
setup_additional_config() {
    log "INFO" "Setting up additional configuration..."
    
    # Set fish as default shell
    if command -v fish &> /dev/null; then
        if [[ "$SHELL" != *"fish"* ]]; then
            log "INFO" "Setting fish as default shell..."
            chsh -s "$(which fish)"
            log "SUCCESS" "Fish set as default shell."
        else
            log "INFO" "Fish is already the default shell."
        fi
    fi
    
    log "SUCCESS" "Additional configuration completed."
}

# Main function
main() {
    # Create log file
    mkdir -p "$(dirname "$LOG_FILE")"
    touch "$LOG_FILE"
    
    log "INFO" "Starting dotfiles installation..."
    
    # Parse command line arguments
    parse_args "$@"
    
    # Check system compatibility
    check_system
    
    # Install dependencies
    install_dependencies
    
    # Exit if only installing dependencies
    if [[ "$DEPENDENCIES_ONLY" -eq 1 ]]; then
        log "SUCCESS" "Dependencies installed. Exiting."
        exit 0
    fi
    
    # Create backup of existing configuration
    create_backup
    
    # Install components
    if [[ ${#COMPONENTS[@]} -gt 0 ]]; then
        for component in "${COMPONENTS[@]}"; do
            install_component "$component"
        done
    else
        install_all_components
    fi
    
    # Set up additional configuration
    setup_additional_config
    
    log "SUCCESS" "Dotfiles installation completed successfully!"
    log "INFO" "Please log out and log back in to apply all changes."
}

# Run the main function
main "$@"
